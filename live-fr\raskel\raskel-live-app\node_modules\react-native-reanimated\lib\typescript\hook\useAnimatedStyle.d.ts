import type { DefaultStyle, DependencyList } from './commonTypes';
/**
 * Lets you create a styles object, similar to StyleSheet styles, which can be animated using shared values.
 *
 * @param updater - A function returning an object with style properties you want to animate.
 * @param dependencies - An optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.
 * @returns An animated style object which has to be passed to the `style` property of an Animated component you want to animate.
 * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle
 */
export declare function useAnimatedStyle<Style extends DefaultStyle>(updater: () => Style, dependencies?: DependencyList | null): Style;
