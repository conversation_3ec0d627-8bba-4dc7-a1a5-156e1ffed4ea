{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_SLIDE_TIME", "SlideInData", "SlideInRight", "name", "style", "transform", "translateX", "duration", "SlideInLeft", "SlideInUp", "translateY", "SlideInDown", "SlideOutData", "SlideOutRight", "SlideOutLeft", "SlideOutUp", "SlideOutDown", "SlideIn", "SlideOut"], "sources": ["Slide.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_SLIDE_TIME = 0.3;\n\nexport const SlideInData = {\n  SlideInRight: {\n    name: 'SlideInRight',\n    style: {\n      0: { transform: [{ translateX: '100vw' }] },\n      100: { transform: [{ translateX: '0%' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n\n  SlideInLeft: {\n    name: 'SlideInLeft',\n    style: {\n      0: { transform: [{ translateX: '-100vw' }] },\n      100: { transform: [{ translateX: '0%' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n\n  SlideInUp: {\n    name: 'SlideInUp',\n    style: {\n      0: { transform: [{ translateY: '-100vh' }] },\n      100: { transform: [{ translateY: '0%' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n\n  SlideInDown: {\n    name: 'SlideInDown',\n    style: {\n      0: { transform: [{ translateY: '100vh' }] },\n      100: { transform: [{ translateY: '0%' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n};\n\nexport const SlideOutData = {\n  SlideOutRight: {\n    name: 'SlideOutRight',\n    style: {\n      0: { transform: [{ translateX: '0%' }] },\n      100: { transform: [{ translateX: '100vw' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n\n  SlideOutLeft: {\n    name: 'SlideOutLeft',\n    style: {\n      0: { transform: [{ translateX: '0%' }] },\n      100: { transform: [{ translateX: '-100vw' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n\n  SlideOutUp: {\n    name: 'SlideOutUp',\n    style: {\n      0: { transform: [{ translateY: '0%' }] },\n      100: { transform: [{ translateY: '-100vh' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n\n  SlideOutDown: {\n    name: 'SlideOutDown',\n    style: {\n      0: { transform: [{ translateY: '0%' }] },\n      100: { transform: [{ translateY: '100vh' }] },\n    },\n    duration: DEFAULT_SLIDE_TIME,\n  },\n};\n\nexport const SlideIn = {\n  SlideInRight: {\n    style: convertAnimationObjectToKeyframes(SlideInData.SlideInRight),\n    duration: SlideInData.SlideInRight.duration,\n  },\n  SlideInLeft: {\n    style: convertAnimationObjectToKeyframes(SlideInData.SlideInLeft),\n    duration: SlideInData.SlideInLeft.duration,\n  },\n  SlideInUp: {\n    style: convertAnimationObjectToKeyframes(SlideInData.SlideInUp),\n    duration: SlideInData.SlideInUp.duration,\n  },\n  SlideInDown: {\n    style: convertAnimationObjectToKeyframes(SlideInData.SlideInDown),\n    duration: SlideInData.SlideInDown.duration,\n  },\n};\n\nexport const SlideOut = {\n  SlideOutRight: {\n    style: convertAnimationObjectToKeyframes(SlideOutData.SlideOutRight),\n    duration: SlideOutData.SlideOutRight.duration,\n  },\n  SlideOutLeft: {\n    style: convertAnimationObjectToKeyframes(SlideOutData.SlideOutLeft),\n    duration: SlideOutData.SlideOutLeft.duration,\n  },\n  SlideOutUp: {\n    style: convertAnimationObjectToKeyframes(SlideOutData.SlideOutUp),\n    duration: SlideOutData.SlideOutUp.duration,\n  },\n  SlideOutDown: {\n    style: convertAnimationObjectToKeyframes(SlideOutData.SlideOutDown),\n    duration: SlideOutData.SlideOutDown.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,kBAAkB,GAAG,GAAG;AAE9B,OAAO,MAAMC,WAAW,GAAG;EACzBC,YAAY,EAAE;IACZC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDQ,WAAW,EAAE;IACXL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDS,SAAS,EAAE;IACTN,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDH,QAAQ,EAAEP;EACZ,CAAC;EAEDW,WAAW,EAAE;IACXR,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE;IAC3C,CAAC;IACDH,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAMY,YAAY,GAAG;EAC1BC,aAAa,EAAE;IACbV,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDc,YAAY,EAAE;IACZX,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDe,UAAU,EAAE;IACVZ,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDH,QAAQ,EAAEP;EACZ,CAAC;EAEDgB,YAAY,EAAE;IACZb,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAK,CAAC;MAAE,CAAC;MACxC,GAAG,EAAE;QAAEL,SAAS,EAAE,CAAC;UAAEK,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDH,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAMiB,OAAO,GAAG;EACrBf,YAAY,EAAE;IACZE,KAAK,EAAEL,iCAAiC,CAACE,WAAW,CAACC,YAAY,CAAC;IAClEK,QAAQ,EAAEN,WAAW,CAACC,YAAY,CAACK;EACrC,CAAC;EACDC,WAAW,EAAE;IACXJ,KAAK,EAAEL,iCAAiC,CAACE,WAAW,CAACO,WAAW,CAAC;IACjED,QAAQ,EAAEN,WAAW,CAACO,WAAW,CAACD;EACpC,CAAC;EACDE,SAAS,EAAE;IACTL,KAAK,EAAEL,iCAAiC,CAACE,WAAW,CAACQ,SAAS,CAAC;IAC/DF,QAAQ,EAAEN,WAAW,CAACQ,SAAS,CAACF;EAClC,CAAC;EACDI,WAAW,EAAE;IACXP,KAAK,EAAEL,iCAAiC,CAACE,WAAW,CAACU,WAAW,CAAC;IACjEJ,QAAQ,EAAEN,WAAW,CAACU,WAAW,CAACJ;EACpC;AACF,CAAC;AAED,OAAO,MAAMW,QAAQ,GAAG;EACtBL,aAAa,EAAE;IACbT,KAAK,EAAEL,iCAAiC,CAACa,YAAY,CAACC,aAAa,CAAC;IACpEN,QAAQ,EAAEK,YAAY,CAACC,aAAa,CAACN;EACvC,CAAC;EACDO,YAAY,EAAE;IACZV,KAAK,EAAEL,iCAAiC,CAACa,YAAY,CAACE,YAAY,CAAC;IACnEP,QAAQ,EAAEK,YAAY,CAACE,YAAY,CAACP;EACtC,CAAC;EACDQ,UAAU,EAAE;IACVX,KAAK,EAAEL,iCAAiC,CAACa,YAAY,CAACG,UAAU,CAAC;IACjER,QAAQ,EAAEK,YAAY,CAACG,UAAU,CAACR;EACpC,CAAC;EACDS,YAAY,EAAE;IACZZ,KAAK,EAAEL,iCAAiC,CAACa,YAAY,CAACI,YAAY,CAAC;IACnET,QAAQ,EAAEK,YAAY,CAACI,YAAY,CAACT;EACtC;AACF,CAAC", "ignoreList": []}