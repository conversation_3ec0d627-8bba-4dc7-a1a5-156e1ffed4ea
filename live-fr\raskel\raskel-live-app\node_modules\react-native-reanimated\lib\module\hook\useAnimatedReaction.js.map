{"version": 3, "names": ["useEffect", "startMapper", "stopMapper", "useSharedValue", "shouldBeUseWeb", "useAnimatedReaction", "prepare", "react", "dependencies", "previous", "inputs", "Object", "values", "__closure", "_dependencies", "length", "undefined", "__workletHash", "push", "fun", "input", "value", "mapperId"], "sources": ["useAnimatedReaction.ts"], "sourcesContent": ["'use strict';\nimport { useEffect } from 'react';\nimport type { WorkletFunction } from '../commonTypes';\nimport { startMapper, stopMapper } from '../core';\nimport type { DependencyList } from './commonTypes';\nimport { useSharedValue } from './useSharedValue';\nimport { shouldBeUseWeb } from '../PlatformChecker';\n\n/**\n * Lets you to respond to changes in a [shared value](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value). It's especially useful when comparing values previously stored in the shared value with the current one.\n *\n * @param prepare - A function that should return a value to which you'd like to react.\n * @param react - A function that reacts to changes in the value returned by the `prepare` function.\n * @param dependencies - an optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useAnimatedReaction\n */\n// @ts-expect-error This overload is required by our API.\nexport function useAnimatedReaction<PreparedResult>(\n  prepare: () => PreparedResult,\n  react: (prepared: PreparedResult, previous: PreparedResult | null) => void,\n  dependencies?: DependencyList\n): void;\n\nexport function useAnimatedReaction<PreparedResult>(\n  prepare: WorkletFunction<[], PreparedResult>,\n  react: WorkletFunction<\n    [prepare: PreparedResult, previous: PreparedResult | null],\n    void\n  >,\n  dependencies?: DependencyList\n) {\n  const previous = useSharedValue<PreparedResult | null>(null);\n\n  let inputs = Object.values(prepare.__closure ?? {});\n\n  if (shouldBeUseWeb()) {\n    if (!inputs.length && dependencies?.length) {\n      // let web work without a Reanimated Babel plugin\n      inputs = dependencies;\n    }\n  }\n\n  if (dependencies === undefined) {\n    dependencies = [\n      ...Object.values(prepare.__closure ?? {}),\n      ...Object.values(react.__closure ?? {}),\n      prepare.__workletHash,\n      react.__workletHash,\n    ];\n  } else {\n    dependencies.push(prepare.__workletHash, react.__workletHash);\n  }\n\n  useEffect(() => {\n    const fun = () => {\n      'worklet';\n      const input = prepare();\n      react(input, previous.value);\n      previous.value = input;\n    };\n    const mapperId = startMapper(fun, inputs);\n    return () => {\n      stopMapper(mapperId);\n    };\n  }, dependencies);\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,OAAO;AAEjC,SAASC,WAAW,EAAEC,UAAU,QAAQ,SAAS;AAEjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,oBAAoB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAOA,OAAO,SAASC,mBAAmBA,CACjCC,OAA4C,EAC5CC,KAGC,EACDC,YAA6B,EAC7B;EACA,MAAMC,QAAQ,GAAGN,cAAc,CAAwB,IAAI,CAAC;EAE5D,IAAIO,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACN,OAAO,CAACO,SAAS,IAAI,CAAC,CAAC,CAAC;EAEnD,IAAIT,cAAc,CAAC,CAAC,EAAE;IAAA,IAAAU,aAAA;IACpB,IAAI,CAACJ,MAAM,CAACK,MAAM,KAAAD,aAAA,GAAIN,YAAY,cAAAM,aAAA,eAAZA,aAAA,CAAcC,MAAM,EAAE;MAC1C;MACAL,MAAM,GAAGF,YAAY;IACvB;EACF;EAEA,IAAIA,YAAY,KAAKQ,SAAS,EAAE;IAC9BR,YAAY,GAAG,CACb,GAAGG,MAAM,CAACC,MAAM,CAACN,OAAO,CAACO,SAAS,IAAI,CAAC,CAAC,CAAC,EACzC,GAAGF,MAAM,CAACC,MAAM,CAACL,KAAK,CAACM,SAAS,IAAI,CAAC,CAAC,CAAC,EACvCP,OAAO,CAACW,aAAa,EACrBV,KAAK,CAACU,aAAa,CACpB;EACH,CAAC,MAAM;IACLT,YAAY,CAACU,IAAI,CAACZ,OAAO,CAACW,aAAa,EAAEV,KAAK,CAACU,aAAa,CAAC;EAC/D;EAEAjB,SAAS,CAAC,MAAM;IACd,MAAMmB,GAAG,GAAGA,CAAA,KAAM;MAChB,SAAS;;MACT,MAAMC,KAAK,GAAGd,OAAO,CAAC,CAAC;MACvBC,KAAK,CAACa,KAAK,EAAEX,QAAQ,CAACY,KAAK,CAAC;MAC5BZ,QAAQ,CAACY,KAAK,GAAGD,KAAK;IACxB,CAAC;IACD,MAAME,QAAQ,GAAGrB,WAAW,CAACkB,GAAG,EAAET,MAAM,CAAC;IACzC,OAAO,MAAM;MACXR,UAAU,CAACoB,QAAQ,CAAC;IACtB,CAAC;EACH,CAAC,EAAEd,YAAY,CAAC;AAClB", "ignoreList": []}