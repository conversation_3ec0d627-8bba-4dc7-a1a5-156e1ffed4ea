{"logs": [{"outputFile": "com.msrfi.liveapp-mergeDebugResources-77:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d21d690b6df8bd5196af38182335d85\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,87,88,140,150,152,154,155,156,157,158,159,160,161,162,163,164,165,166,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3782,3862,3943,4026,4135,4957,5055,5185,7926,7991,11988,12809,12975,13108,13210,13275,13350,13406,13485,13545,13599,13721,13780,13842,13896,13978,14274,14366,14441,14536,14617,14701,14845,14924,15005,15146,15239,15318,15373,15424,15490,15570,15651,15722,15802,15875,15953,16026,16098,16210,16303,16375,16467,16559,16633,16717,16809,16866,16950,17016,17099,17186,17248,17312,17375,17453,17555,17659,17756,17860,17919,17974,18682,18769,18846", "endLines": "22,51,52,53,54,55,63,64,65,87,88,140,150,152,154,155,156,157,158,159,160,161,162,163,164,165,166,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,223,224,225", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "965,3857,3938,4021,4130,4225,5050,5180,5265,7986,8052,12080,12887,13036,13205,13270,13345,13401,13480,13540,13594,13716,13775,13837,13891,13973,14108,14361,14436,14531,14612,14696,14840,14919,15000,15141,15234,15313,15368,15419,15485,15565,15646,15717,15797,15870,15948,16021,16093,16205,16298,16370,16462,16554,16628,16712,16804,16861,16945,17011,17094,17181,17243,17307,17370,17448,17550,17654,17751,17855,17914,17969,18058,18764,18841,18922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d6cd5f4345895639e9dea77e62a721a7\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1081,1196,1306,1388,1494,1624,1702,1778,1869,1962,2060,2155,2255,2348,2441,2536,2627,2718,2804,2914,3025,3128,3239,3347,3454,3613,18595", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1076,1191,1301,1383,1489,1619,1697,1773,1864,1957,2055,2150,2250,2343,2436,2531,2622,2713,2799,2909,3020,3123,3234,3342,3449,3608,3707,18677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b5b22b250867a7891421e198eb95eaa0\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "56,57,58,59,60,61,62,234", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4230,4328,4430,4529,4631,4735,4839,19565", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "4323,4425,4524,4626,4730,4834,4952,19661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5cc0c55bacd14953f77d87885006a8d9\\transformed\\play-services-basement-18.1.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6416", "endColumns": "164", "endOffsets": "6576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33d72ac5290f8957ef564231fbd82b02\\transformed\\play-services-base-18.1.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5353,5459,5639,5769,5878,6049,6182,6303,6581,6759,6871,7056,7192,7352,7531,7604,7671", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "5454,5634,5764,5873,6044,6177,6298,6411,6754,6866,7051,7187,7347,7526,7599,7666,7750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0a4fb1b26caafab2932de0398d3b5ea\\transformed\\android-image-cropper-4.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,168,246,320,383,447,508,584", "endColumns": "63,48,77,73,62,63,60,75,54", "endOffsets": "114,163,241,315,378,442,503,579,634"}, "to": {"startLines": "86,145,146,147,148,149,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7862,12481,12530,12608,12682,12745,18063,18124,18200", "endColumns": "63,48,77,73,62,63,60,75,54", "endOffsets": "7921,12525,12603,12677,12740,12804,18119,18195,18250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dea44b29830a29c4d2a116dadfb42f8b\\transformed\\exoplayer-core-2.18.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10131,10205,10270,10339,10411,10494,10571,10668,10761", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "10200,10265,10334,10406,10489,10566,10663,10756,10839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1076c57e2b6fc3bba30fdb23eb82909e\\transformed\\exoplayer-ui-2.18.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3530,3596,3648,3708,3782,3856", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3525,3591,3643,3703,3777,3851,3905"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,578,8057,8146,8237,8316,8414,8511,8590,8656,8762,8869,8934,9000,9064,9136,9256,9379,9501,9576,9664,9737,9817,9908,10001,10067,10844,10897,10957,11005,11066,11137,11208,11275,11353,11418,11477,11543,11608,11674,11726,11786,11860,11934", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "381,573,782,8141,8232,8311,8409,8506,8585,8651,8757,8864,8929,8995,9059,9131,9251,9374,9496,9571,9659,9732,9812,9903,9996,10062,10126,10892,10952,11000,11061,11132,11203,11270,11348,11413,11472,11538,11603,11669,11721,11781,11855,11929,11983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f47581e38898051b48ece556f9194c7b\\transformed\\browser-1.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "85,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "7755,12085,12187,12306", "endColumns": "106,101,118,104", "endOffsets": "7857,12182,12301,12406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,278,361,428,507,589,679,771,842,929,1004,1091,1171,1251,1326,1403,1476,1567,1646,1727,1799", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,203,273,356,423,502,584,674,766,837,924,999,1086,1166,1246,1321,1398,1471,1562,1641,1722,1794,1874"}, "to": {"startLines": "50,66,144,151,153,167,168,218,219,220,221,226,227,228,229,230,231,232,233,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3712,5270,12411,12892,13041,14113,14192,18255,18345,18437,18508,18927,19002,19089,19169,19249,19324,19401,19474,19666,19745,19826,19898", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "3777,5348,12476,12970,13103,14187,14269,18340,18432,18503,18590,18997,19084,19164,19244,19319,19396,19469,19560,19740,19821,19893,19973"}}]}]}