{"version": 3, "names": ["valueSetter", "mutable", "value", "forceUpdate", "previousAnimation", "_animation", "cancelled", "onFrame", "undefined", "animation", "_value", "current", "isHigherOrder", "callback", "initializeAnimation", "timestamp", "onStart", "currentTimestamp", "global", "__frameTimestamp", "_getAnimationTimestamp", "step", "newTimestamp", "finished", "requestAnimationFrame"], "sources": ["valueSetter.ts"], "sourcesContent": ["'use strict';\nimport type { AnimationObject, Mutable } from './commonTypes';\n\nexport function valueSetter<Value>(\n  mutable: Mutable<Value>,\n  value: Value,\n  forceUpdate = false\n): void {\n  'worklet';\n  const previousAnimation = mutable._animation;\n  if (previousAnimation) {\n    previousAnimation.cancelled = true;\n    mutable._animation = null;\n  }\n  if (\n    typeof value === 'function' ||\n    (value !== null &&\n      typeof value === 'object' &&\n      // TODO TYPESCRIPT fix this after fixing AnimationObject type\n      (value as unknown as AnimationObject).onFrame !== undefined)\n  ) {\n    const animation: AnimationObject<Value> =\n      typeof value === 'function'\n        ? // TODO TYPESCRIPT fix this after fixing AnimationObject type\n          (value as () => AnimationObject<Value>)()\n        : // TODO TYPESCRIPT fix this after fixing AnimationObject type\n          (value as unknown as AnimationObject<Value>);\n    // prevent setting again to the same value\n    // and triggering the mappers that treat this value as an input\n    // this happens when the animation's target value(stored in animation.current until animation.onStart is called) is set to the same value as a current one(this._value)\n    // built in animations that are not higher order(withTiming, withSpring) hold target value in .current\n    if (\n      mutable._value === animation.current &&\n      !animation.isHigherOrder &&\n      !forceUpdate\n    ) {\n      animation.callback && animation.callback(true);\n      return;\n    }\n    // animated set\n    const initializeAnimation = (timestamp: number) => {\n      animation.onStart(animation, mutable.value, timestamp, previousAnimation);\n    };\n    const currentTimestamp =\n      global.__frameTimestamp || global._getAnimationTimestamp();\n    initializeAnimation(currentTimestamp);\n\n    const step = (newTimestamp: number) => {\n      // Function `requestAnimationFrame` adds callback to an array, all the callbacks are flushed with function `__flushAnimationFrame`\n      // Usually we flush them inside function `nativeRequestAnimationFrame` and then the given timestamp is the timestamp of end of the current frame.\n      // However function `__flushAnimationFrame` may also be called inside `registerEventHandler` - then we get actual timestamp which is earlier than the end of the frame.\n\n      const timestamp =\n        newTimestamp < (animation.timestamp || 0)\n          ? animation.timestamp\n          : newTimestamp;\n\n      if (animation.cancelled) {\n        animation.callback && animation.callback(false /* finished */);\n        return;\n      }\n      const finished = animation.onFrame(animation, timestamp);\n      animation.finished = true;\n      animation.timestamp = timestamp;\n      // TODO TYPESCRIPT\n      // For now I'll assume that `animation.current` is always defined\n      // but actually need to dive into animations to understand it\n      mutable._value = animation.current!;\n      if (finished) {\n        animation.callback && animation.callback(true /* finished */);\n      } else {\n        requestAnimationFrame(step);\n      }\n    };\n\n    mutable._animation = animation;\n\n    step(currentTimestamp);\n  } else {\n    // prevent setting again to the same value\n    // and triggering the mappers that treat this value as an input\n    if (mutable._value === value && !forceUpdate) {\n      return;\n    }\n    mutable._value = value;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,WAAWA,CACzBC,OAAuB,EACvBC,KAAY,EACZC,WAAW,GAAG,KAAK,EACb;EACN,SAAS;;EACT,MAAMC,iBAAiB,GAAGH,OAAO,CAACI,UAAU;EAC5C,IAAID,iBAAiB,EAAE;IACrBA,iBAAiB,CAACE,SAAS,GAAG,IAAI;IAClCL,OAAO,CAACI,UAAU,GAAG,IAAI;EAC3B;EACA,IACE,OAAOH,KAAK,KAAK,UAAU,IAC1BA,KAAK,KAAK,IAAI,IACb,OAAOA,KAAK,KAAK,QAAQ;EACzB;EACCA,KAAK,CAAgCK,OAAO,KAAKC,SAAU,EAC9D;IACA,MAAMC,SAAiC,GACrC,OAAOP,KAAK,KAAK,UAAU;IACvB;IACCA,KAAK,CAAkC,CAAC;IACzC;IACCA,KAA2C;IAClD;IACA;IACA;IACA;IACA,IACED,OAAO,CAACS,MAAM,KAAKD,SAAS,CAACE,OAAO,IACpC,CAACF,SAAS,CAACG,aAAa,IACxB,CAACT,WAAW,EACZ;MACAM,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC;MAC9C;IACF;IACA;IACA,MAAMC,mBAAmB,GAAIC,SAAiB,IAAK;MACjDN,SAAS,CAACO,OAAO,CAACP,SAAS,EAAER,OAAO,CAACC,KAAK,EAAEa,SAAS,EAAEX,iBAAiB,CAAC;IAC3E,CAAC;IACD,MAAMa,gBAAgB,GACpBC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;IAC5DN,mBAAmB,CAACG,gBAAgB,CAAC;IAErC,MAAMI,IAAI,GAAIC,YAAoB,IAAK;MACrC;MACA;MACA;;MAEA,MAAMP,SAAS,GACbO,YAAY,IAAIb,SAAS,CAACM,SAAS,IAAI,CAAC,CAAC,GACrCN,SAAS,CAACM,SAAS,GACnBO,YAAY;MAElB,IAAIb,SAAS,CAACH,SAAS,EAAE;QACvBG,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC;QAC9D;MACF;MACA,MAAMU,QAAQ,GAAGd,SAAS,CAACF,OAAO,CAACE,SAAS,EAAEM,SAAS,CAAC;MACxDN,SAAS,CAACc,QAAQ,GAAG,IAAI;MACzBd,SAAS,CAACM,SAAS,GAAGA,SAAS;MAC/B;MACA;MACA;MACAd,OAAO,CAACS,MAAM,GAAGD,SAAS,CAACE,OAAQ;MACnC,IAAIY,QAAQ,EAAE;QACZd,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;MAC/D,CAAC,MAAM;QACLW,qBAAqB,CAACH,IAAI,CAAC;MAC7B;IACF,CAAC;IAEDpB,OAAO,CAACI,UAAU,GAAGI,SAAS;IAE9BY,IAAI,CAACJ,gBAAgB,CAAC;EACxB,CAAC,MAAM;IACL;IACA;IACA,IAAIhB,OAAO,CAACS,MAAM,KAAKR,KAAK,IAAI,CAACC,WAAW,EAAE;MAC5C;IACF;IACAF,OAAO,CAACS,MAAM,GAAGR,KAAK;EACxB;AACF", "ignoreList": []}