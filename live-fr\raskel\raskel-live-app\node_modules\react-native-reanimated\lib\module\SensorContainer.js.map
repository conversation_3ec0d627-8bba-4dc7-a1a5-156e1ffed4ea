{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "Sensor", "SensorContainer", "constructor", "Map", "getSensorId", "sensorType", "config", "iosReferenceFrame", "adjustToInterfaceOrientation", "initializeSensor", "sensorId", "nativeSensors", "has", "newSensor", "set", "sensor", "get", "getSharedValue", "registerSensor", "handler", "isAvailable", "isRunning", "register", "listenersNumber", "unregisterSensor", "unregister"], "sources": ["SensorContainer.ts"], "sourcesContent": ["'use strict';\nimport type {\n  SensorType,\n  SensorConfig,\n  Value3D,\n  ValueRotation,\n  ShareableRef,\n  SharedValue,\n} from './commonTypes';\nimport Sensor from './Sensor';\n\nexport class SensorContainer {\n  private nativeSensors: Map<number, Sensor> = new Map();\n\n  getSensorId(sensorType: SensorType, config: SensorConfig) {\n    return (\n      sensorType * 100 +\n      config.iosReferenceFrame * 10 +\n      Number(config.adjustToInterfaceOrientation)\n    );\n  }\n\n  initializeSensor(\n    sensorType: SensorType,\n    config: SensorConfig\n  ): SharedValue<Value3D | ValueRotation> {\n    const sensorId = this.getSensorId(sensorType, config);\n\n    if (!this.nativeSensors.has(sensorId)) {\n      const newSensor = new Sensor(sensorType, config);\n      this.nativeSensors.set(sensorId, newSensor);\n    }\n\n    const sensor = this.nativeSensors.get(sensorId);\n    return sensor!.getSharedValue();\n  }\n\n  registerSensor(\n    sensorType: SensorType,\n    config: SensorConfig,\n    handler: ShareableRef<(data: Value3D | ValueRotation) => void>\n  ): number {\n    const sensorId = this.getSensorId(sensorType, config);\n\n    if (!this.nativeSensors.has(sensorId)) {\n      return -1;\n    }\n\n    const sensor = this.nativeSensors.get(sensorId);\n    if (\n      sensor &&\n      sensor.isAvailable() &&\n      (sensor.isRunning() || sensor.register(handler))\n    ) {\n      sensor.listenersNumber++;\n      return sensorId;\n    }\n    return -1;\n  }\n\n  unregisterSensor(sensorId: number) {\n    if (this.nativeSensors.has(sensorId)) {\n      const sensor = this.nativeSensors.get(sensorId);\n      if (sensor && sensor.isRunning()) {\n        sensor.listenersNumber--;\n        if (sensor.listenersNumber === 0) {\n          sensor.unregister();\n        }\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AASb,OAAOW,MAAM,MAAM,UAAU;AAE7B,OAAO,MAAMC,eAAe,CAAC;EAAAC,YAAA;IAAAvB,eAAA,wBACkB,IAAIwB,GAAG,CAAC,CAAC;EAAA;EAEtDC,WAAWA,CAACC,UAAsB,EAAEC,MAAoB,EAAE;IACxD,OACED,UAAU,GAAG,GAAG,GAChBC,MAAM,CAACC,iBAAiB,GAAG,EAAE,GAC7BR,MAAM,CAACO,MAAM,CAACE,4BAA4B,CAAC;EAE/C;EAEAC,gBAAgBA,CACdJ,UAAsB,EACtBC,MAAoB,EACkB;IACtC,MAAMI,QAAQ,GAAG,IAAI,CAACN,WAAW,CAACC,UAAU,EAAEC,MAAM,CAAC;IAErD,IAAI,CAAC,IAAI,CAACK,aAAa,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;MACrC,MAAMG,SAAS,GAAG,IAAIb,MAAM,CAACK,UAAU,EAAEC,MAAM,CAAC;MAChD,IAAI,CAACK,aAAa,CAACG,GAAG,CAACJ,QAAQ,EAAEG,SAAS,CAAC;IAC7C;IAEA,MAAME,MAAM,GAAG,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACN,QAAQ,CAAC;IAC/C,OAAOK,MAAM,CAAEE,cAAc,CAAC,CAAC;EACjC;EAEAC,cAAcA,CACZb,UAAsB,EACtBC,MAAoB,EACpBa,OAA8D,EACtD;IACR,MAAMT,QAAQ,GAAG,IAAI,CAACN,WAAW,CAACC,UAAU,EAAEC,MAAM,CAAC;IAErD,IAAI,CAAC,IAAI,CAACK,aAAa,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;MACrC,OAAO,CAAC,CAAC;IACX;IAEA,MAAMK,MAAM,GAAG,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACN,QAAQ,CAAC;IAC/C,IACEK,MAAM,IACNA,MAAM,CAACK,WAAW,CAAC,CAAC,KACnBL,MAAM,CAACM,SAAS,CAAC,CAAC,IAAIN,MAAM,CAACO,QAAQ,CAACH,OAAO,CAAC,CAAC,EAChD;MACAJ,MAAM,CAACQ,eAAe,EAAE;MACxB,OAAOb,QAAQ;IACjB;IACA,OAAO,CAAC,CAAC;EACX;EAEAc,gBAAgBA,CAACd,QAAgB,EAAE;IACjC,IAAI,IAAI,CAACC,aAAa,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;MACpC,MAAMK,MAAM,GAAG,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACN,QAAQ,CAAC;MAC/C,IAAIK,MAAM,IAAIA,MAAM,CAACM,SAAS,CAAC,CAAC,EAAE;QAChCN,MAAM,CAACQ,eAAe,EAAE;QACxB,IAAIR,MAAM,CAACQ,eAAe,KAAK,CAAC,EAAE;UAChCR,MAAM,CAACU,UAAU,CAAC,CAAC;QACrB;MACF;IACF;EACF;AACF", "ignoreList": []}