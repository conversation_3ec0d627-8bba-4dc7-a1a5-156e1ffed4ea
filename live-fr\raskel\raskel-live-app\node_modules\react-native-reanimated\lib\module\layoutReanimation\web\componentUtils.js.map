{"version": 3, "names": ["Animations", "TransitionType", "WebEasings", "getEasingByName", "TransitionGenerator", "scheduleAnimationCleanup", "_updatePropsJS", "ReduceMotion", "LayoutAnimationType", "setElementPosition", "snapshots", "Keyframe", "ReducedMotionManager", "prepareCurvedTransition", "EasingNameSymbol", "getEasingFromConfig", "config", "easingV", "easingName", "console", "warn", "getRandomDelay", "max<PERSON><PERSON><PERSON>", "Math", "floor", "random", "getDelayFromConfig", "shouldRandomizeDelay", "randomizeDelay", "delay", "delayV", "getReducedMotionFromConfig", "reduceMotionV", "jsValue", "Never", "Always", "getDurationFromConfig", "animationName", "defaultDuration", "duration", "durationV", "undefined", "getCallbackFromConfig", "callbackV", "getReversedFromConfig", "reversed", "getProcessedConfig", "animationType", "easing", "callback", "maybeModifyStyleForKeyframe", "element", "style", "animationFillMode", "timestampRules", "Object", "values", "definitions", "position", "saveSnapshot", "rect", "getBoundingClientRect", "snapshot", "top", "left", "width", "height", "scrollOffsets", "getElementScrollValue", "set", "setElementAnimation", "animationConfig", "shouldSavePosition", "parent", "configureAnimation", "animationDuration", "animationDelay", "animationTimingFunction", "ENTERING", "requestAnimationFrame", "onanimationend", "_animationConfig$call", "contains", "removedAfterAnimation", "<PERSON><PERSON><PERSON><PERSON>", "call", "removeEventListener", "animationCancelHandler", "_animationConfig$call2", "onanimationstart", "visibility", "addEventListener", "get", "handleLayoutTransition", "transitionData", "LINEAR", "SEQUENCED", "FADING", "JUMPING", "CURVED", "ENTRY_EXIT", "transitionKeyframeName", "dummyTransitionKeyframeName", "dummy", "dummyAnimationConfig", "current", "scrollTopOffset", "scrollLeftOffset", "scrollTop", "scrollLeft", "parentElement", "handleExitingAnimation", "offsetParent", "cloneNode", "reanimatedDummy", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "currentScrollTopOffset", "lastScrollTopOffset", "currentScrollLeftOffset", "lastScrollLeftOffset"], "sources": ["componentUtils.ts"], "sourcesContent": ["'use strict';\n\nimport { Animations, TransitionType } from './config';\nimport type {\n  AnimationCallback,\n  AnimationConfig,\n  AnimationNames,\n  CustomConfig,\n  KeyframeDefinitions,\n} from './config';\nimport { WebEasings, getEasingByName } from './Easing.web';\nimport type { WebEasingsNames } from './Easing.web';\nimport type { TransitionData } from './animationParser';\nimport { TransitionGenerator } from './createAnimation';\nimport { scheduleAnimationCleanup } from './domUtils';\nimport { _updatePropsJS } from '../../js-reanimated';\nimport type { ReanimatedHTMLElement } from '../../js-reanimated';\nimport { ReduceMotion } from '../../commonTypes';\nimport { LayoutAnimationType } from '../animationBuilder/commonTypes';\nimport type { ReanimatedSnapshot, ScrollOffsets } from './componentStyle';\nimport { setElementPosition, snapshots } from './componentStyle';\nimport { Keyframe } from '../animationBuilder';\nimport { ReducedMotionManager } from '../../ReducedMotion';\nimport { prepareCurvedTransition } from './transition/Curved.web';\nimport { EasingNameSymbol } from '../../Easing';\n\nfunction getEasingFromConfig(config: CustomConfig): string {\n  if (!config.easingV) {\n    return getEasingByName('linear');\n  }\n\n  const easingName = config.easingV[EasingNameSymbol];\n\n  if (!(easingName in WebEasings)) {\n    console.warn(\n      `[Reanimated] Selected easing is not currently supported on web.`\n    );\n\n    return getEasingByName('linear');\n  }\n\n  return getEasingByName(easingName as WebEasingsNames);\n}\n\nfunction getRandomDelay(maxDelay = 1000) {\n  return Math.floor(Math.random() * (maxDelay + 1)) / 1000;\n}\n\nfunction getDelayFromConfig(config: CustomConfig): number {\n  const shouldRandomizeDelay = config.randomizeDelay;\n\n  const delay = shouldRandomizeDelay ? getRandomDelay() : 0;\n\n  if (!config.delayV) {\n    return delay;\n  }\n\n  return shouldRandomizeDelay\n    ? getRandomDelay(config.delayV)\n    : config.delayV / 1000;\n}\n\nexport function getReducedMotionFromConfig(config: CustomConfig) {\n  if (!config.reduceMotionV) {\n    return ReducedMotionManager.jsValue;\n  }\n\n  switch (config.reduceMotionV) {\n    case ReduceMotion.Never:\n      return false;\n    case ReduceMotion.Always:\n      return true;\n    default:\n      return ReducedMotionManager.jsValue;\n  }\n}\n\nfunction getDurationFromConfig(\n  config: CustomConfig,\n  animationName: string\n): number {\n  // Duration in keyframe has to be in seconds. However, when using `.duration()` modifier we pass it in miliseconds.\n  // If `duration` was specified in config, we have to divide it by `1000`, otherwise we return value that is already in seconds.\n\n  const defaultDuration =\n    animationName in Animations\n      ? Animations[animationName as AnimationNames].duration\n      : 0.3;\n\n  return config.durationV !== undefined\n    ? config.durationV / 1000\n    : defaultDuration;\n}\n\nfunction getCallbackFromConfig(config: CustomConfig): AnimationCallback {\n  return config.callbackV !== undefined ? config.callbackV : null;\n}\n\nfunction getReversedFromConfig(config: CustomConfig) {\n  return !!config.reversed;\n}\n\nexport function getProcessedConfig(\n  animationName: string,\n  animationType: LayoutAnimationType,\n  config: CustomConfig\n): AnimationConfig {\n  return {\n    animationName,\n    animationType,\n    duration: getDurationFromConfig(config, animationName),\n    delay: getDelayFromConfig(config),\n    easing: getEasingFromConfig(config),\n    callback: getCallbackFromConfig(config),\n    reversed: getReversedFromConfig(config),\n  };\n}\n\nexport function maybeModifyStyleForKeyframe(\n  element: HTMLElement,\n  config: CustomConfig\n) {\n  if (!(config instanceof Keyframe)) {\n    return;\n  }\n\n  // We need to set `animationFillMode` to `forwards`, otherwise component will go back to its position.\n  // This will result in wrong snapshot\n  element.style.animationFillMode = 'forwards';\n\n  for (const timestampRules of Object.values(\n    config.definitions as KeyframeDefinitions\n  )) {\n    if ('originX' in timestampRules || 'originY' in timestampRules) {\n      element.style.position = 'absolute';\n      return;\n    }\n  }\n}\n\nexport function saveSnapshot(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n\n  const snapshot: ReanimatedSnapshot = {\n    top: rect.top,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    scrollOffsets: getElementScrollValue(element),\n  };\n\n  snapshots.set(element, snapshot);\n}\n\nexport function setElementAnimation(\n  element: ReanimatedHTMLElement,\n  animationConfig: AnimationConfig,\n  shouldSavePosition = false,\n  parent: Element | null = null\n) {\n  const { animationName, duration, delay, easing } = animationConfig;\n\n  const configureAnimation = () => {\n    element.style.animationName = animationName;\n    element.style.animationDuration = `${duration}s`;\n    element.style.animationDelay = `${delay}s`;\n    element.style.animationTimingFunction = easing;\n  };\n\n  if (animationConfig.animationType === LayoutAnimationType.ENTERING) {\n    // On chrome sometimes entering animations flicker. This is most likely caused by animation being interrupted\n    // by already started tasks. To avoid flickering, we use `requestAnimationFrame`, which will run callback right before repaint.\n    requestAnimationFrame(configureAnimation);\n  } else {\n    configureAnimation();\n  }\n\n  element.onanimationend = () => {\n    if (shouldSavePosition) {\n      saveSnapshot(element);\n    }\n\n    if (parent?.contains(element)) {\n      element.removedAfterAnimation = true;\n      parent.removeChild(element);\n    }\n\n    animationConfig.callback?.(true);\n    element.removeEventListener('animationcancel', animationCancelHandler);\n  };\n\n  const animationCancelHandler = () => {\n    animationConfig.callback?.(false);\n\n    if (parent?.contains(element)) {\n      element.removedAfterAnimation = true;\n      parent.removeChild(element);\n    }\n\n    element.removeEventListener('animationcancel', animationCancelHandler);\n  };\n\n  // Here we have to use `addEventListener` since element.onanimationcancel doesn't work on chrome\n  element.onanimationstart = () => {\n    if (animationConfig.animationType === LayoutAnimationType.ENTERING) {\n      _updatePropsJS({ visibility: 'initial' }, element);\n    }\n\n    element.addEventListener('animationcancel', animationCancelHandler);\n  };\n\n  if (!(animationName in Animations)) {\n    scheduleAnimationCleanup(animationName, duration + delay, () => {\n      if (shouldSavePosition) {\n        setElementPosition(element, snapshots.get(element)!);\n      }\n    });\n  }\n}\n\nexport function handleLayoutTransition(\n  element: ReanimatedHTMLElement,\n  animationConfig: AnimationConfig,\n  transitionData: TransitionData\n) {\n  const { animationName } = animationConfig;\n\n  let animationType;\n\n  switch (animationName) {\n    case 'LinearTransition':\n      animationType = TransitionType.LINEAR;\n      break;\n    case 'SequencedTransition':\n      animationType = TransitionType.SEQUENCED;\n      break;\n    case 'FadingTransition':\n      animationType = TransitionType.FADING;\n      break;\n    case 'JumpingTransition':\n      animationType = TransitionType.JUMPING;\n      break;\n    case 'CurvedTransition':\n      animationType = TransitionType.CURVED;\n      break;\n    case 'EntryExitTransition':\n      animationType = TransitionType.ENTRY_EXIT;\n      break;\n    default:\n      animationType = TransitionType.LINEAR;\n      break;\n  }\n\n  const { transitionKeyframeName, dummyTransitionKeyframeName } =\n    TransitionGenerator(animationType, transitionData);\n\n  animationConfig.animationName = transitionKeyframeName;\n\n  if (animationType === TransitionType.CURVED) {\n    const { dummy, dummyAnimationConfig } = prepareCurvedTransition(\n      element,\n      animationConfig,\n      transitionData,\n      dummyTransitionKeyframeName! // In `CurvedTransition` it cannot be undefined\n    );\n\n    setElementAnimation(dummy, dummyAnimationConfig);\n  }\n  setElementAnimation(element, animationConfig);\n}\n\nfunction getElementScrollValue(element: HTMLElement): ScrollOffsets {\n  let current: HTMLElement | null = element;\n\n  const scrollOffsets: ScrollOffsets = {\n    scrollTopOffset: 0,\n    scrollLeftOffset: 0,\n  };\n\n  while (current) {\n    if (current.scrollTop !== 0 && scrollOffsets.scrollTopOffset === 0) {\n      scrollOffsets.scrollTopOffset = current.scrollTop;\n    }\n\n    if (current.scrollLeft !== 0 && scrollOffsets.scrollLeftOffset === 0) {\n      scrollOffsets.scrollLeftOffset = current.scrollLeft;\n    }\n\n    current = current.parentElement;\n  }\n\n  return scrollOffsets;\n}\n\nexport function handleExitingAnimation(\n  element: HTMLElement,\n  animationConfig: AnimationConfig\n) {\n  const parent = element.offsetParent;\n  const dummy = element.cloneNode() as ReanimatedHTMLElement;\n  dummy.reanimatedDummy = true;\n\n  element.style.animationName = '';\n  dummy.style.animationName = '';\n\n  // After cloning the element, we want to move all children from original element to its clone. This is because original element\n  // will be unmounted, therefore when this code executes in child component, parent will be either empty or removed soon.\n  // Using element.cloneNode(true) doesn't solve the problem, because it creates copy of children and we won't be able to set their animations\n  //\n  // This loop works because appendChild() moves element into its new parent instead of copying it\n  while (element.firstChild) {\n    dummy.appendChild(element.firstChild);\n  }\n\n  parent?.appendChild(dummy);\n\n  const snapshot = snapshots.get(element)!;\n\n  const scrollOffsets = getElementScrollValue(element);\n\n  // Scroll does not trigger snapshotting, therefore if we start exiting animation after\n  // scrolling through parent component, dummy will end up in wrong place. In order to fix that\n  // we keep last known scroll position in snapshot and then adjust dummy position based on\n  // last known scroll offset and current scroll offset\n\n  const currentScrollTopOffset = scrollOffsets.scrollTopOffset;\n  const lastScrollTopOffset = snapshot.scrollOffsets.scrollTopOffset;\n\n  if (currentScrollTopOffset !== lastScrollTopOffset) {\n    snapshot.top += lastScrollTopOffset - currentScrollTopOffset;\n  }\n\n  const currentScrollLeftOffset = scrollOffsets.scrollLeftOffset;\n  const lastScrollLeftOffset = snapshot.scrollOffsets.scrollLeftOffset;\n\n  if (currentScrollLeftOffset !== lastScrollLeftOffset) {\n    snapshot.left += lastScrollLeftOffset - currentScrollLeftOffset;\n  }\n\n  snapshots.set(dummy, snapshot);\n\n  setElementPosition(dummy, snapshot);\n\n  setElementAnimation(dummy, animationConfig, false, parent);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,UAAU,EAAEC,cAAc,QAAQ,UAAU;AAQrD,SAASC,UAAU,EAAEC,eAAe,QAAQ,cAAc;AAG1D,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,wBAAwB,QAAQ,YAAY;AACrD,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,mBAAmB,QAAQ,iCAAiC;AAErE,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,kBAAkB;AAChE,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,gBAAgB,QAAQ,cAAc;AAE/C,SAASC,mBAAmBA,CAACC,MAAoB,EAAU;EACzD,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;IACnB,OAAOd,eAAe,CAAC,QAAQ,CAAC;EAClC;EAEA,MAAMe,UAAU,GAAGF,MAAM,CAACC,OAAO,CAACH,gBAAgB,CAAC;EAEnD,IAAI,EAAEI,UAAU,IAAIhB,UAAU,CAAC,EAAE;IAC/BiB,OAAO,CAACC,IAAI,CACT,iEACH,CAAC;IAED,OAAOjB,eAAe,CAAC,QAAQ,CAAC;EAClC;EAEA,OAAOA,eAAe,CAACe,UAA6B,CAAC;AACvD;AAEA,SAASG,cAAcA,CAACC,QAAQ,GAAG,IAAI,EAAE;EACvC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAIH,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;AAC1D;AAEA,SAASI,kBAAkBA,CAACV,MAAoB,EAAU;EACxD,MAAMW,oBAAoB,GAAGX,MAAM,CAACY,cAAc;EAElD,MAAMC,KAAK,GAAGF,oBAAoB,GAAGN,cAAc,CAAC,CAAC,GAAG,CAAC;EAEzD,IAAI,CAACL,MAAM,CAACc,MAAM,EAAE;IAClB,OAAOD,KAAK;EACd;EAEA,OAAOF,oBAAoB,GACvBN,cAAc,CAACL,MAAM,CAACc,MAAM,CAAC,GAC7Bd,MAAM,CAACc,MAAM,GAAG,IAAI;AAC1B;AAEA,OAAO,SAASC,0BAA0BA,CAACf,MAAoB,EAAE;EAC/D,IAAI,CAACA,MAAM,CAACgB,aAAa,EAAE;IACzB,OAAOpB,oBAAoB,CAACqB,OAAO;EACrC;EAEA,QAAQjB,MAAM,CAACgB,aAAa;IAC1B,KAAKzB,YAAY,CAAC2B,KAAK;MACrB,OAAO,KAAK;IACd,KAAK3B,YAAY,CAAC4B,MAAM;MACtB,OAAO,IAAI;IACb;MACE,OAAOvB,oBAAoB,CAACqB,OAAO;EACvC;AACF;AAEA,SAASG,qBAAqBA,CAC5BpB,MAAoB,EACpBqB,aAAqB,EACb;EACR;EACA;;EAEA,MAAMC,eAAe,GACnBD,aAAa,IAAIrC,UAAU,GACvBA,UAAU,CAACqC,aAAa,CAAmB,CAACE,QAAQ,GACpD,GAAG;EAET,OAAOvB,MAAM,CAACwB,SAAS,KAAKC,SAAS,GACjCzB,MAAM,CAACwB,SAAS,GAAG,IAAI,GACvBF,eAAe;AACrB;AAEA,SAASI,qBAAqBA,CAAC1B,MAAoB,EAAqB;EACtE,OAAOA,MAAM,CAAC2B,SAAS,KAAKF,SAAS,GAAGzB,MAAM,CAAC2B,SAAS,GAAG,IAAI;AACjE;AAEA,SAASC,qBAAqBA,CAAC5B,MAAoB,EAAE;EACnD,OAAO,CAAC,CAACA,MAAM,CAAC6B,QAAQ;AAC1B;AAEA,OAAO,SAASC,kBAAkBA,CAChCT,aAAqB,EACrBU,aAAkC,EAClC/B,MAAoB,EACH;EACjB,OAAO;IACLqB,aAAa;IACbU,aAAa;IACbR,QAAQ,EAAEH,qBAAqB,CAACpB,MAAM,EAAEqB,aAAa,CAAC;IACtDR,KAAK,EAAEH,kBAAkB,CAACV,MAAM,CAAC;IACjCgC,MAAM,EAAEjC,mBAAmB,CAACC,MAAM,CAAC;IACnCiC,QAAQ,EAAEP,qBAAqB,CAAC1B,MAAM,CAAC;IACvC6B,QAAQ,EAAED,qBAAqB,CAAC5B,MAAM;EACxC,CAAC;AACH;AAEA,OAAO,SAASkC,2BAA2BA,CACzCC,OAAoB,EACpBnC,MAAoB,EACpB;EACA,IAAI,EAAEA,MAAM,YAAYL,QAAQ,CAAC,EAAE;IACjC;EACF;;EAEA;EACA;EACAwC,OAAO,CAACC,KAAK,CAACC,iBAAiB,GAAG,UAAU;EAE5C,KAAK,MAAMC,cAAc,IAAIC,MAAM,CAACC,MAAM,CACxCxC,MAAM,CAACyC,WACT,CAAC,EAAE;IACD,IAAI,SAAS,IAAIH,cAAc,IAAI,SAAS,IAAIA,cAAc,EAAE;MAC9DH,OAAO,CAACC,KAAK,CAACM,QAAQ,GAAG,UAAU;MACnC;IACF;EACF;AACF;AAEA,OAAO,SAASC,YAAYA,CAACR,OAAoB,EAAE;EACjD,MAAMS,IAAI,GAAGT,OAAO,CAACU,qBAAqB,CAAC,CAAC;EAE5C,MAAMC,QAA4B,GAAG;IACnCC,GAAG,EAAEH,IAAI,CAACG,GAAG;IACbC,IAAI,EAAEJ,IAAI,CAACI,IAAI;IACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;IACjBC,MAAM,EAAEN,IAAI,CAACM,MAAM;IACnBC,aAAa,EAAEC,qBAAqB,CAACjB,OAAO;EAC9C,CAAC;EAEDzC,SAAS,CAAC2D,GAAG,CAAClB,OAAO,EAAEW,QAAQ,CAAC;AAClC;AAEA,OAAO,SAASQ,mBAAmBA,CACjCnB,OAA8B,EAC9BoB,eAAgC,EAChCC,kBAAkB,GAAG,KAAK,EAC1BC,MAAsB,GAAG,IAAI,EAC7B;EACA,MAAM;IAAEpC,aAAa;IAAEE,QAAQ;IAAEV,KAAK;IAAEmB;EAAO,CAAC,GAAGuB,eAAe;EAElE,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvB,OAAO,CAACC,KAAK,CAACf,aAAa,GAAGA,aAAa;IAC3Cc,OAAO,CAACC,KAAK,CAACuB,iBAAiB,GAAI,GAAEpC,QAAS,GAAE;IAChDY,OAAO,CAACC,KAAK,CAACwB,cAAc,GAAI,GAAE/C,KAAM,GAAE;IAC1CsB,OAAO,CAACC,KAAK,CAACyB,uBAAuB,GAAG7B,MAAM;EAChD,CAAC;EAED,IAAIuB,eAAe,CAACxB,aAAa,KAAKvC,mBAAmB,CAACsE,QAAQ,EAAE;IAClE;IACA;IACAC,qBAAqB,CAACL,kBAAkB,CAAC;EAC3C,CAAC,MAAM;IACLA,kBAAkB,CAAC,CAAC;EACtB;EAEAvB,OAAO,CAAC6B,cAAc,GAAG,MAAM;IAAA,IAAAC,qBAAA;IAC7B,IAAIT,kBAAkB,EAAE;MACtBb,YAAY,CAACR,OAAO,CAAC;IACvB;IAEA,IAAIsB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAES,QAAQ,CAAC/B,OAAO,CAAC,EAAE;MAC7BA,OAAO,CAACgC,qBAAqB,GAAG,IAAI;MACpCV,MAAM,CAACW,WAAW,CAACjC,OAAO,CAAC;IAC7B;IAEA,CAAA8B,qBAAA,GAAAV,eAAe,CAACtB,QAAQ,cAAAgC,qBAAA,eAAxBA,qBAAA,CAAAI,IAAA,CAAAd,eAAe,EAAY,IAAI,CAAC;IAChCpB,OAAO,CAACmC,mBAAmB,CAAC,iBAAiB,EAAEC,sBAAsB,CAAC;EACxE,CAAC;EAED,MAAMA,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA;IACnC,CAAAA,sBAAA,GAAAjB,eAAe,CAACtB,QAAQ,cAAAuC,sBAAA,eAAxBA,sBAAA,CAAAH,IAAA,CAAAd,eAAe,EAAY,KAAK,CAAC;IAEjC,IAAIE,MAAM,aAANA,MAAM,eAANA,MAAM,CAAES,QAAQ,CAAC/B,OAAO,CAAC,EAAE;MAC7BA,OAAO,CAACgC,qBAAqB,GAAG,IAAI;MACpCV,MAAM,CAACW,WAAW,CAACjC,OAAO,CAAC;IAC7B;IAEAA,OAAO,CAACmC,mBAAmB,CAAC,iBAAiB,EAAEC,sBAAsB,CAAC;EACxE,CAAC;;EAED;EACApC,OAAO,CAACsC,gBAAgB,GAAG,MAAM;IAC/B,IAAIlB,eAAe,CAACxB,aAAa,KAAKvC,mBAAmB,CAACsE,QAAQ,EAAE;MAClExE,cAAc,CAAC;QAAEoF,UAAU,EAAE;MAAU,CAAC,EAAEvC,OAAO,CAAC;IACpD;IAEAA,OAAO,CAACwC,gBAAgB,CAAC,iBAAiB,EAAEJ,sBAAsB,CAAC;EACrE,CAAC;EAED,IAAI,EAAElD,aAAa,IAAIrC,UAAU,CAAC,EAAE;IAClCK,wBAAwB,CAACgC,aAAa,EAAEE,QAAQ,GAAGV,KAAK,EAAE,MAAM;MAC9D,IAAI2C,kBAAkB,EAAE;QACtB/D,kBAAkB,CAAC0C,OAAO,EAAEzC,SAAS,CAACkF,GAAG,CAACzC,OAAO,CAAE,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,SAAS0C,sBAAsBA,CACpC1C,OAA8B,EAC9BoB,eAAgC,EAChCuB,cAA8B,EAC9B;EACA,MAAM;IAAEzD;EAAc,CAAC,GAAGkC,eAAe;EAEzC,IAAIxB,aAAa;EAEjB,QAAQV,aAAa;IACnB,KAAK,kBAAkB;MACrBU,aAAa,GAAG9C,cAAc,CAAC8F,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxBhD,aAAa,GAAG9C,cAAc,CAAC+F,SAAS;MACxC;IACF,KAAK,kBAAkB;MACrBjD,aAAa,GAAG9C,cAAc,CAACgG,MAAM;MACrC;IACF,KAAK,mBAAmB;MACtBlD,aAAa,GAAG9C,cAAc,CAACiG,OAAO;MACtC;IACF,KAAK,kBAAkB;MACrBnD,aAAa,GAAG9C,cAAc,CAACkG,MAAM;MACrC;IACF,KAAK,qBAAqB;MACxBpD,aAAa,GAAG9C,cAAc,CAACmG,UAAU;MACzC;IACF;MACErD,aAAa,GAAG9C,cAAc,CAAC8F,MAAM;MACrC;EACJ;EAEA,MAAM;IAAEM,sBAAsB;IAAEC;EAA4B,CAAC,GAC3DlG,mBAAmB,CAAC2C,aAAa,EAAE+C,cAAc,CAAC;EAEpDvB,eAAe,CAAClC,aAAa,GAAGgE,sBAAsB;EAEtD,IAAItD,aAAa,KAAK9C,cAAc,CAACkG,MAAM,EAAE;IAC3C,MAAM;MAAEI,KAAK;MAAEC;IAAqB,CAAC,GAAG3F,uBAAuB,CAC7DsC,OAAO,EACPoB,eAAe,EACfuB,cAAc,EACdQ,2BAA2B,CAAE;IAC/B,CAAC;IAEDhC,mBAAmB,CAACiC,KAAK,EAAEC,oBAAoB,CAAC;EAClD;EACAlC,mBAAmB,CAACnB,OAAO,EAAEoB,eAAe,CAAC;AAC/C;AAEA,SAASH,qBAAqBA,CAACjB,OAAoB,EAAiB;EAClE,IAAIsD,OAA2B,GAAGtD,OAAO;EAEzC,MAAMgB,aAA4B,GAAG;IACnCuC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAED,OAAOF,OAAO,EAAE;IACd,IAAIA,OAAO,CAACG,SAAS,KAAK,CAAC,IAAIzC,aAAa,CAACuC,eAAe,KAAK,CAAC,EAAE;MAClEvC,aAAa,CAACuC,eAAe,GAAGD,OAAO,CAACG,SAAS;IACnD;IAEA,IAAIH,OAAO,CAACI,UAAU,KAAK,CAAC,IAAI1C,aAAa,CAACwC,gBAAgB,KAAK,CAAC,EAAE;MACpExC,aAAa,CAACwC,gBAAgB,GAAGF,OAAO,CAACI,UAAU;IACrD;IAEAJ,OAAO,GAAGA,OAAO,CAACK,aAAa;EACjC;EAEA,OAAO3C,aAAa;AACtB;AAEA,OAAO,SAAS4C,sBAAsBA,CACpC5D,OAAoB,EACpBoB,eAAgC,EAChC;EACA,MAAME,MAAM,GAAGtB,OAAO,CAAC6D,YAAY;EACnC,MAAMT,KAAK,GAAGpD,OAAO,CAAC8D,SAAS,CAAC,CAA0B;EAC1DV,KAAK,CAACW,eAAe,GAAG,IAAI;EAE5B/D,OAAO,CAACC,KAAK,CAACf,aAAa,GAAG,EAAE;EAChCkE,KAAK,CAACnD,KAAK,CAACf,aAAa,GAAG,EAAE;;EAE9B;EACA;EACA;EACA;EACA;EACA,OAAOc,OAAO,CAACgE,UAAU,EAAE;IACzBZ,KAAK,CAACa,WAAW,CAACjE,OAAO,CAACgE,UAAU,CAAC;EACvC;EAEA1C,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE2C,WAAW,CAACb,KAAK,CAAC;EAE1B,MAAMzC,QAAQ,GAAGpD,SAAS,CAACkF,GAAG,CAACzC,OAAO,CAAE;EAExC,MAAMgB,aAAa,GAAGC,qBAAqB,CAACjB,OAAO,CAAC;;EAEpD;EACA;EACA;EACA;;EAEA,MAAMkE,sBAAsB,GAAGlD,aAAa,CAACuC,eAAe;EAC5D,MAAMY,mBAAmB,GAAGxD,QAAQ,CAACK,aAAa,CAACuC,eAAe;EAElE,IAAIW,sBAAsB,KAAKC,mBAAmB,EAAE;IAClDxD,QAAQ,CAACC,GAAG,IAAIuD,mBAAmB,GAAGD,sBAAsB;EAC9D;EAEA,MAAME,uBAAuB,GAAGpD,aAAa,CAACwC,gBAAgB;EAC9D,MAAMa,oBAAoB,GAAG1D,QAAQ,CAACK,aAAa,CAACwC,gBAAgB;EAEpE,IAAIY,uBAAuB,KAAKC,oBAAoB,EAAE;IACpD1D,QAAQ,CAACE,IAAI,IAAIwD,oBAAoB,GAAGD,uBAAuB;EACjE;EAEA7G,SAAS,CAAC2D,GAAG,CAACkC,KAAK,EAAEzC,QAAQ,CAAC;EAE9BrD,kBAAkB,CAAC8F,KAAK,EAAEzC,QAAQ,CAAC;EAEnCQ,mBAAmB,CAACiC,KAAK,EAAEhC,eAAe,EAAE,KAAK,EAAEE,MAAM,CAAC;AAC5D", "ignoreList": []}