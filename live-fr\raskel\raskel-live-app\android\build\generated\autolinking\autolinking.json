{"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app", "reactNativePath": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-agora": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-agora", "name": "react-native-agora", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-agora\\android", "packageImportPath": "import io.agora.rtc.ng.react.AgoraRtcNgPackage;", "packageInstance": "new AgoraRtcNgPackage()", "buildTypes": [], "libraryName": "AgoraRtcNgSpec", "componentDescriptors": ["AgoraRtcTextureViewComponentDescriptor", "AgoraRtcSurfaceViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-agora/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-worklets": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-worklets", "name": "react-native-worklets", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-worklets\\android", "packageImportPath": "import com.swmansion.worklets.WorkletsPackage;", "packageInstance": "new WorkletsPackage()", "buildTypes": [], "libraryName": "rnworklets", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.msrfi.liveapp", "sourceDir": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\android"}}}