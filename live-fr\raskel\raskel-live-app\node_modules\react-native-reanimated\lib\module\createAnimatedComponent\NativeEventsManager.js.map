{"version": 3, "names": ["_classPrivateFieldInitSpec", "obj", "privateMap", "value", "_checkPrivateRedeclaration", "set", "privateCollection", "has", "TypeError", "_classPrivateFieldGet", "s", "a", "get", "_assert<PERSON>lassBrand", "_classPrivateFieldSet", "r", "e", "t", "n", "arguments", "length", "WorkletEventHandler", "findNodeHandle", "_managedComponent", "WeakMap", "_componentOptions", "_eventViewTag", "NativeEventsManager", "constructor", "component", "options", "getEventViewTag", "attachEvents", "executeForEachEventHandler", "props", "key", "handler", "registerForEvents", "detachEvents", "_key", "unregisterFromEvents", "updateEvents", "prevProps", "computedEventTag", "prev<PERSON><PERSON><PERSON>", "newProp", "isWorkletEventHandler", "workletEventHandler", "componentAnimatedRef", "_component", "newTag", "getScrollableNode", "scrollableNode", "_classPrivateFieldGet2", "setNativeProps", "prop", "callback"], "sources": ["NativeEventsManager.ts"], "sourcesContent": ["'use strict';\nimport type {\n  INativeEventsManager,\n  IAnimatedComponentInternal,\n  AnimatedComponentProps,\n  InitialComponentProps,\n  AnimatedComponentRef,\n} from './commonTypes';\nimport { has } from './utils';\nimport { WorkletEventHandler } from '../WorkletEventHandler';\nimport { findNodeHandle } from 'react-native';\n\nexport class NativeEventsManager implements INativeEventsManager {\n  readonly #managedComponent: ManagedAnimatedComponent;\n  readonly #componentOptions?: ComponentOptions;\n  #eventViewTag = -1;\n\n  constructor(component: ManagedAnimatedComponent, options?: ComponentOptions) {\n    this.#managedComponent = component;\n    this.#componentOptions = options;\n    this.#eventViewTag = this.getEventViewTag();\n  }\n\n  public attachEvents() {\n    executeForEachEventHandler(this.#managedComponent.props, (key, handler) => {\n      handler.registerForEvents(this.#eventViewTag, key);\n    });\n  }\n\n  public detachEvents() {\n    executeForEachEventHandler(\n      this.#managedComponent.props,\n      (_key, handler) => {\n        handler.unregisterFromEvents(this.#eventViewTag);\n      }\n    );\n  }\n\n  public updateEvents(\n    prevProps: AnimatedComponentProps<InitialComponentProps>\n  ) {\n    const computedEventTag = this.getEventViewTag();\n    // If the event view tag changes, we need to completely re-mount all events\n    if (this.#eventViewTag !== computedEventTag) {\n      // Remove all bindings from previous props that ran on the old viewTag\n      executeForEachEventHandler(prevProps, (_key, handler) => {\n        handler.unregisterFromEvents(this.#eventViewTag);\n      });\n      // We don't need to unregister from current (new) props, because their events weren't registered yet\n      // Replace the view tag\n      this.#eventViewTag = computedEventTag;\n      // Attach the events with a new viewTag\n      this.attachEvents();\n      return;\n    }\n\n    executeForEachEventHandler(prevProps, (key, prevHandler) => {\n      const newProp = this.#managedComponent.props[key];\n      if (!newProp) {\n        // Prop got deleted\n        prevHandler.unregisterFromEvents(this.#eventViewTag);\n      } else if (\n        isWorkletEventHandler(newProp) &&\n        newProp.workletEventHandler !== prevHandler\n      ) {\n        // Prop got changed\n        prevHandler.unregisterFromEvents(this.#eventViewTag);\n        newProp.workletEventHandler.registerForEvents(this.#eventViewTag);\n      }\n    });\n\n    executeForEachEventHandler(this.#managedComponent.props, (key, handler) => {\n      if (!prevProps[key]) {\n        // Prop got added\n        handler.registerForEvents(this.#eventViewTag);\n      }\n    });\n  }\n\n  private getEventViewTag() {\n    // Get the tag for registering events - since the event emitting view can be nested inside the main component\n    const componentAnimatedRef = this.#managedComponent\n      ._component as AnimatedComponentRef;\n    let newTag: number;\n    if (componentAnimatedRef.getScrollableNode) {\n      const scrollableNode = componentAnimatedRef.getScrollableNode();\n      newTag = findNodeHandle(scrollableNode) ?? -1;\n    } else {\n      newTag =\n        findNodeHandle(\n          this.#componentOptions?.setNativeProps\n            ? this.#managedComponent\n            : componentAnimatedRef\n        ) ?? -1;\n    }\n    return newTag;\n  }\n}\n\nfunction isWorkletEventHandler(\n  prop: unknown\n): prop is WorkletEventHandlerHolder {\n  return (\n    has('workletEventHandler', prop) &&\n    prop.workletEventHandler instanceof WorkletEventHandler\n  );\n}\n\nfunction executeForEachEventHandler(\n  props: AnimatedComponentProps<InitialComponentProps>,\n  callback: (\n    key: string,\n    handler: InstanceType<typeof WorkletEventHandler>\n  ) => void\n) {\n  for (const key in props) {\n    const prop = props[key];\n    if (isWorkletEventHandler(prop)) {\n      callback(key, prop.workletEventHandler);\n    }\n  }\n}\n\ntype ManagedAnimatedComponent = React.Component<\n  AnimatedComponentProps<InitialComponentProps>\n> &\n  IAnimatedComponentInternal;\n\ntype ComponentOptions = {\n  setNativeProps: (\n    ref: AnimatedComponentRef,\n    props: InitialComponentProps\n  ) => void;\n};\n\ntype WorkletEventHandlerHolder = {\n  workletEventHandler: InstanceType<typeof WorkletEventHandler>;\n};\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,2BAAAC,GAAA,EAAAC,UAAA,EAAAC,KAAA,IAAAC,0BAAA,CAAAH,GAAA,EAAAC,UAAA,GAAAA,UAAA,CAAAG,GAAA,CAAAJ,GAAA,EAAAE,KAAA;AAAA,SAAAC,2BAAAH,GAAA,EAAAK,iBAAA,QAAAA,iBAAA,CAAAC,GAAA,CAAAN,GAAA,eAAAO,SAAA;AAAA,SAAAC,sBAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,CAAAE,GAAA,CAAAC,iBAAA,CAAAH,CAAA,EAAAC,CAAA;AAAA,SAAAG,sBAAAJ,CAAA,EAAAC,CAAA,EAAAI,CAAA,WAAAL,CAAA,CAAAL,GAAA,CAAAQ,iBAAA,CAAAH,CAAA,EAAAC,CAAA,GAAAI,CAAA,GAAAA,CAAA;AAAA,SAAAF,kBAAAG,CAAA,EAAAC,CAAA,EAAAC,CAAA,6BAAAF,CAAA,GAAAA,CAAA,KAAAC,CAAA,GAAAD,CAAA,CAAAT,GAAA,CAAAU,CAAA,UAAAE,SAAA,CAAAC,MAAA,OAAAH,CAAA,GAAAC,CAAA,YAAAV,SAAA;AAQb,SAASD,GAAG,QAAQ,SAAS;AAC7B,SAASc,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,cAAc,QAAQ,cAAc;AAAC,IAAAC,iBAAA,oBAAAC,OAAA;AAAA,IAAAC,iBAAA,oBAAAD,OAAA;AAAA,IAAAE,aAAA,oBAAAF,OAAA;AAE9C,OAAO,MAAMG,mBAAmB,CAAiC;EAK/DC,WAAWA,CAACC,SAAmC,EAAEC,OAA0B,EAAE;IAAA9B,0BAAA,OAAAuB,iBAAA;IAAAvB,0BAAA,OAAAyB,iBAAA;IAAAzB,0BAAA,OAAA0B,aAAA,EAF7D,CAAC,CAAC;IAGhBZ,qBAAA,CAAAS,iBAAA,MAAI,EAAqBM,SAAS;IAClCf,qBAAA,CAAAW,iBAAA,MAAI,EAAqBK,OAAO;IAChChB,qBAAA,CAAAY,aAAA,MAAI,EAAiB,IAAI,CAACK,eAAe,CAAC,CAAC;EAC7C;EAEOC,YAAYA,CAAA,EAAG;IACpBC,0BAA0B,CAACxB,qBAAA,CAAAc,iBAAA,MAAI,EAAmBW,KAAK,EAAE,CAACC,GAAG,EAAEC,OAAO,KAAK;MACzEA,OAAO,CAACC,iBAAiB,CAAA5B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,GAAgBS,GAAG,CAAC;IACpD,CAAC,CAAC;EACJ;EAEOG,YAAYA,CAAA,EAAG;IACpBL,0BAA0B,CACxBxB,qBAAA,CAAAc,iBAAA,MAAI,EAAmBW,KAAK,EAC5B,CAACK,IAAI,EAAEH,OAAO,KAAK;MACjBA,OAAO,CAACI,oBAAoB,CAAA/B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,CAAc,CAAC;IAClD,CACF,CAAC;EACH;EAEOe,YAAYA,CACjBC,SAAwD,EACxD;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACZ,eAAe,CAAC,CAAC;IAC/C;IACA,IAAItB,qBAAA,CAAAiB,aAAA,MAAI,MAAmBiB,gBAAgB,EAAE;MAC3C;MACAV,0BAA0B,CAACS,SAAS,EAAE,CAACH,IAAI,EAAEH,OAAO,KAAK;QACvDA,OAAO,CAACI,oBAAoB,CAAA/B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,CAAc,CAAC;MAClD,CAAC,CAAC;MACF;MACA;MACAZ,qBAAA,CAAAY,aAAA,MAAI,EAAiBiB,gBAAgB;MACrC;MACA,IAAI,CAACX,YAAY,CAAC,CAAC;MACnB;IACF;IAEAC,0BAA0B,CAACS,SAAS,EAAE,CAACP,GAAG,EAAES,WAAW,KAAK;MAC1D,MAAMC,OAAO,GAAGpC,qBAAA,CAAAc,iBAAA,MAAI,EAAmBW,KAAK,CAACC,GAAG,CAAC;MACjD,IAAI,CAACU,OAAO,EAAE;QACZ;QACAD,WAAW,CAACJ,oBAAoB,CAAA/B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,CAAc,CAAC;MACtD,CAAC,MAAM,IACLoB,qBAAqB,CAACD,OAAO,CAAC,IAC9BA,OAAO,CAACE,mBAAmB,KAAKH,WAAW,EAC3C;QACA;QACAA,WAAW,CAACJ,oBAAoB,CAAA/B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,CAAc,CAAC;QACpDmB,OAAO,CAACE,mBAAmB,CAACV,iBAAiB,CAAA5B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,CAAc,CAAC;MACnE;IACF,CAAC,CAAC;IAEFO,0BAA0B,CAACxB,qBAAA,CAAAc,iBAAA,MAAI,EAAmBW,KAAK,EAAE,CAACC,GAAG,EAAEC,OAAO,KAAK;MACzE,IAAI,CAACM,SAAS,CAACP,GAAG,CAAC,EAAE;QACnB;QACAC,OAAO,CAACC,iBAAiB,CAAA5B,qBAAA,CAAAiB,aAAA,EAAC,IAAI,CAAc,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEQK,eAAeA,CAAA,EAAG;IACxB;IACA,MAAMiB,oBAAoB,GAAGvC,qBAAA,CAAAc,iBAAA,MAAI,EAC9B0B,UAAkC;IACrC,IAAIC,MAAc;IAClB,IAAIF,oBAAoB,CAACG,iBAAiB,EAAE;MAC1C,MAAMC,cAAc,GAAGJ,oBAAoB,CAACG,iBAAiB,CAAC,CAAC;MAC/DD,MAAM,GAAG5B,cAAc,CAAC8B,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC,MAAM;MAAA,IAAAC,sBAAA;MACLH,MAAM,GACJ5B,cAAc,CACZ,CAAA+B,sBAAA,GAAA5C,qBAAA,CAAAgB,iBAAA,MAAI,eAAA4B,sBAAA,eAAJA,sBAAA,CAAwBC,cAAc,GAAA7C,qBAAA,CAAAc,iBAAA,EAClC,IAAI,IACJyB,oBACN,CAAC,IAAI,CAAC,CAAC;IACX;IACA,OAAOE,MAAM;EACf;AACF;AAEA,SAASJ,qBAAqBA,CAC5BS,IAAa,EACsB;EACnC,OACEhD,GAAG,CAAC,qBAAqB,EAAEgD,IAAI,CAAC,IAChCA,IAAI,CAACR,mBAAmB,YAAY1B,mBAAmB;AAE3D;AAEA,SAASY,0BAA0BA,CACjCC,KAAoD,EACpDsB,QAGS,EACT;EACA,KAAK,MAAMrB,GAAG,IAAID,KAAK,EAAE;IACvB,MAAMqB,IAAI,GAAGrB,KAAK,CAACC,GAAG,CAAC;IACvB,IAAIW,qBAAqB,CAACS,IAAI,CAAC,EAAE;MAC/BC,QAAQ,CAACrB,GAAG,EAAEoB,IAAI,CAACR,mBAAmB,CAAC;IACzC;EACF;AACF", "ignoreList": []}