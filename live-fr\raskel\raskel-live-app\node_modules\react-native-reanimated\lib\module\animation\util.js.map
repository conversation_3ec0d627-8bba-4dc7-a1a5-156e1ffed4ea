{"version": 3, "names": ["isColor", "convertToRGBA", "rgbaArrayToRGBAColor", "toGammaSpace", "toLinearSpace", "ReduceMotion", "isWorkletFunction", "flatten", "multiplyMatrices", "scaleMatrix", "addMatrices", "decomposeMatrixIntoMatricesAndAngles", "isAffineMatrixFlat", "subtractMatrices", "getRotationMatrix", "shouldBeUseWeb", "ReducedMotionManager", "IN_STYLE_UPDATER", "SHOULD_BE_USE_WEB", "__DEV__", "jsValue", "console", "warn", "assertEasingIsWorklet", "easing", "_WORKLET", "factory", "Error", "initialUpdaterRun", "updater", "result", "recognizePrefixSuffix", "value", "match", "prefix", "suffix", "number", "strippedValue", "parseFloat", "isReduceMotionOnUI", "uiValue", "getReduceMotionFromConfig", "config", "System", "Always", "getReduceMotionForAnimation", "undefined", "applyProgressToMatrix", "progress", "a", "b", "applyProgressToNumber", "decorateAnimation", "animation", "baseOnStart", "onStart", "baseOnFrame", "onFrame", "isHigherOrder", "timestamp", "previousAnimation", "reduceMotion", "animationCopy", "Object", "assign", "callback", "prefNumberSuffOnStart", "__prefix", "__suffix", "strippedCurrent", "strippedToValue", "toValue", "current", "startValue", "paPrefix", "paSuffix", "paStrippedValue", "prefNumberSuffOnFrame", "res", "tab", "colorOnStart", "RGBAValue", "RGBACurrent", "RGBAToValue", "for<PERSON>ach", "i", "index", "push", "colorOnFrame", "finished", "transformationMatrixOnStart", "startMatrices", "stopMatrices", "transformationMatrixOnFrame", "transforms", "mappedTransforms", "key", "_", "currentTranslation", "currentScale", "skewMatrix", "rotations", "mappedRotations", "angle", "rotationMatrixX", "rotationMatrixY", "rotationMatrixZ", "rotationMatrix", "updated", "arrayOnStart", "v", "arrayOnFrame", "objectOnStart", "objectOnFrame", "newObject", "startTime", "Array", "isArray", "defineAnimation", "starting", "create", "cancelAnimation", "sharedValue"], "sources": ["util.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-shadow */\n'use strict';\nimport type { HigherOrderAnimation, StyleLayoutAnimation } from './commonTypes';\nimport type { ParsedColorArray } from '../Colors';\nimport {\n  isColor,\n  convertToRGBA,\n  rgbaArrayToRGBAColor,\n  toGammaSpace,\n  toLinearSpace,\n} from '../Colors';\nimport { ReduceMotion, isWorkletFunction } from '../commonTypes';\nimport type {\n  SharedValue,\n  AnimatableValue,\n  Animation,\n  AnimationObject,\n  Timestamp,\n  AnimatableValueObject,\n  EasingFunction,\n} from '../commonTypes';\nimport type {\n  AffineMatrixFlat,\n  AffineMatrix,\n} from './transformationMatrix/matrixUtils';\nimport {\n  flatten,\n  multiplyMatrices,\n  scaleMatrix,\n  addMatrices,\n  decomposeMatrixIntoMatricesAndAngles,\n  isAffineMatrixFlat,\n  subtractMatrices,\n  getRotationMatrix,\n} from './transformationMatrix/matrixUtils';\nimport { shouldBeUseWeb } from '../PlatformChecker';\nimport type { EasingFunctionFactory } from '../Easing';\nimport { ReducedMotionManager } from '../ReducedMotion';\n\nlet IN_STYLE_UPDATER = false;\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\nif (__DEV__ && ReducedMotionManager.jsValue) {\n  console.warn(\n    `[Reanimated] Reduced motion setting is enabled on this device. This warning is visible only in the development mode. Some animations will be disabled by default. You can override the behavior for individual animations, see https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#reduced-motion-setting-is-enabled-on-this-device.`\n  );\n}\n\nexport function assertEasingIsWorklet(\n  easing: EasingFunction | EasingFunctionFactory\n): void {\n  'worklet';\n  if (_WORKLET) {\n    // If this is called on UI (for example from gesture handler with worklets), we don't get easing,\n    // but its bound copy, which is not a worklet. We don't want to throw any error then.\n    return;\n  }\n  if (SHOULD_BE_USE_WEB) {\n    // It is possible to run reanimated on web without plugin, so let's skip this check on web\n    return;\n  }\n  // @ts-ignore typescript wants us to use `in` instead, which doesn't work with host objects\n  if (easing?.factory) {\n    return;\n  }\n\n  if (!isWorkletFunction(easing)) {\n    throw new Error(\n      '[Reanimated] The easing function is not a worklet. Please make sure you import `Easing` from react-native-reanimated.'\n    );\n  }\n}\n\nexport function initialUpdaterRun<T>(updater: () => T) {\n  IN_STYLE_UPDATER = true;\n  const result = updater();\n  IN_STYLE_UPDATER = false;\n  return result;\n}\n\ninterface RecognizedPrefixSuffix {\n  prefix?: string;\n  suffix?: string;\n  strippedValue: number;\n}\n\nexport function recognizePrefixSuffix(\n  value: string | number\n): RecognizedPrefixSuffix {\n  'worklet';\n  if (typeof value === 'string') {\n    const match = value.match(\n      /([A-Za-z]*)(-?\\d*\\.?\\d*)([eE][-+]?[0-9]+)?([A-Za-z%]*)/\n    );\n    if (!match) {\n      throw new Error(\"[Reanimated] Couldn't parse animation value.\");\n    }\n    const prefix = match[1];\n    const suffix = match[4];\n    // number with scientific notation\n    const number = match[2] + (match[3] ?? '');\n    return { prefix, suffix, strippedValue: parseFloat(number) };\n  } else {\n    return { strippedValue: value };\n  }\n}\n\n/**\n * Returns whether the motion should be reduced for a specified config.\n * By default returns the system setting.\n */\nconst isReduceMotionOnUI = ReducedMotionManager.uiValue;\nexport function getReduceMotionFromConfig(config?: ReduceMotion) {\n  'worklet';\n  return !config || config === ReduceMotion.System\n    ? isReduceMotionOnUI.value\n    : config === ReduceMotion.Always;\n}\n\n/**\n * Returns the value that should be assigned to `animation.reduceMotion`\n * for a given config. If the config is not defined, `undefined` is returned.\n */\nexport function getReduceMotionForAnimation(config?: ReduceMotion) {\n  'worklet';\n  // if the config is not defined, we want `reduceMotion` to be undefined,\n  // so the parent animation knows if it should overwrite it\n  if (!config) {\n    return undefined;\n  }\n\n  return getReduceMotionFromConfig(config);\n}\n\nfunction applyProgressToMatrix(\n  progress: number,\n  a: AffineMatrix,\n  b: AffineMatrix\n) {\n  'worklet';\n  return addMatrices(a, scaleMatrix(subtractMatrices(b, a), progress));\n}\n\nfunction applyProgressToNumber(progress: number, a: number, b: number) {\n  'worklet';\n  return a + progress * (b - a);\n}\n\nfunction decorateAnimation<T extends AnimationObject | StyleLayoutAnimation>(\n  animation: T\n): void {\n  'worklet';\n  const baseOnStart = (animation as Animation<AnimationObject>).onStart;\n  const baseOnFrame = (animation as Animation<AnimationObject>).onFrame;\n\n  if ((animation as HigherOrderAnimation).isHigherOrder) {\n    animation.onStart = (\n      animation: Animation<AnimationObject>,\n      value: number,\n      timestamp: Timestamp,\n      previousAnimation: Animation<AnimationObject>\n    ) => {\n      if (animation.reduceMotion === undefined) {\n        animation.reduceMotion = getReduceMotionFromConfig();\n      }\n      return baseOnStart(animation, value, timestamp, previousAnimation);\n    };\n    return;\n  }\n\n  const animationCopy = Object.assign({}, animation);\n  delete animationCopy.callback;\n\n  const prefNumberSuffOnStart = (\n    animation: Animation<AnimationObject>,\n    value: string | number,\n    timestamp: number,\n    previousAnimation: Animation<AnimationObject>\n  ) => {\n    // recognize prefix, suffix, and updates stripped value on animation start\n    const { prefix, suffix, strippedValue } = recognizePrefixSuffix(value);\n    animation.__prefix = prefix;\n    animation.__suffix = suffix;\n    animation.strippedCurrent = strippedValue;\n    const { strippedValue: strippedToValue } = recognizePrefixSuffix(\n      animation.toValue as string | number\n    );\n    animation.current = strippedValue;\n    animation.startValue = strippedValue;\n    animation.toValue = strippedToValue;\n    if (previousAnimation && previousAnimation !== animation) {\n      const {\n        prefix: paPrefix,\n        suffix: paSuffix,\n        strippedValue: paStrippedValue,\n      } = recognizePrefixSuffix(previousAnimation.current as string | number);\n      previousAnimation.current = paStrippedValue;\n      previousAnimation.__prefix = paPrefix;\n      previousAnimation.__suffix = paSuffix;\n    }\n\n    baseOnStart(animation, strippedValue, timestamp, previousAnimation);\n\n    animation.current =\n      (animation.__prefix ?? '') +\n      animation.current +\n      (animation.__suffix ?? '');\n\n    if (previousAnimation && previousAnimation !== animation) {\n      previousAnimation.current =\n        (previousAnimation.__prefix ?? '') +\n        // FIXME\n        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n        previousAnimation.current +\n        (previousAnimation.__suffix ?? '');\n    }\n  };\n  const prefNumberSuffOnFrame = (\n    animation: Animation<AnimationObject>,\n    timestamp: number\n  ) => {\n    animation.current = animation.strippedCurrent;\n    const res = baseOnFrame(animation, timestamp);\n    animation.strippedCurrent = animation.current;\n    animation.current =\n      (animation.__prefix ?? '') +\n      animation.current +\n      (animation.__suffix ?? '');\n    return res;\n  };\n\n  const tab = ['R', 'G', 'B', 'A'];\n  const colorOnStart = (\n    animation: Animation<AnimationObject>,\n    value: string | number,\n    timestamp: Timestamp,\n    previousAnimation: Animation<AnimationObject>\n  ): void => {\n    let RGBAValue: ParsedColorArray;\n    let RGBACurrent: ParsedColorArray;\n    let RGBAToValue: ParsedColorArray;\n    const res: Array<number> = [];\n    if (isColor(value)) {\n      RGBACurrent = toLinearSpace(convertToRGBA(animation.current));\n      RGBAValue = toLinearSpace(convertToRGBA(value));\n      if (animation.toValue) {\n        RGBAToValue = toLinearSpace(convertToRGBA(animation.toValue));\n      }\n    }\n    tab.forEach((i, index) => {\n      animation[i] = Object.assign({}, animationCopy);\n      animation[i].current = RGBACurrent[index];\n      animation[i].toValue = RGBAToValue ? RGBAToValue[index] : undefined;\n      animation[i].onStart(\n        animation[i],\n        RGBAValue[index],\n        timestamp,\n        previousAnimation ? previousAnimation[i] : undefined\n      );\n      res.push(animation[i].current);\n    });\n\n    animation.current = rgbaArrayToRGBAColor(\n      toGammaSpace(res as ParsedColorArray)\n    );\n  };\n\n  const colorOnFrame = (\n    animation: Animation<AnimationObject>,\n    timestamp: Timestamp\n  ): boolean => {\n    const RGBACurrent = toLinearSpace(convertToRGBA(animation.current));\n    const res: Array<number> = [];\n    let finished = true;\n    tab.forEach((i, index) => {\n      animation[i].current = RGBACurrent[index];\n      const result = animation[i].onFrame(animation[i], timestamp);\n      // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n      finished = finished && result;\n      res.push(animation[i].current);\n    });\n\n    animation.current = rgbaArrayToRGBAColor(\n      toGammaSpace(res as ParsedColorArray)\n    );\n    return finished;\n  };\n\n  const transformationMatrixOnStart = (\n    animation: Animation<AnimationObject>,\n    value: AffineMatrixFlat,\n    timestamp: Timestamp,\n    previousAnimation: Animation<AnimationObject>\n  ): void => {\n    const toValue = animation.toValue as AffineMatrixFlat;\n\n    animation.startMatrices = decomposeMatrixIntoMatricesAndAngles(value);\n    animation.stopMatrices = decomposeMatrixIntoMatricesAndAngles(toValue);\n\n    // We create an animation copy to animate single value between 0 and 100\n    // We set limits from 0 to 100 (instead of 0-1) to make spring look good\n    // with default thresholds.\n\n    animation[0] = Object.assign({}, animationCopy);\n    animation[0].current = 0;\n    animation[0].toValue = 100;\n    animation[0].onStart(\n      animation[0],\n      0,\n      timestamp,\n      previousAnimation ? previousAnimation[0] : undefined\n    );\n\n    animation.current = value;\n  };\n\n  const transformationMatrixOnFrame = (\n    animation: Animation<AnimationObject>,\n    timestamp: Timestamp\n  ): boolean => {\n    let finished = true;\n    const result = animation[0].onFrame(animation[0], timestamp);\n    // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n    finished = finished && result;\n\n    const progress = animation[0].current / 100;\n\n    const transforms = ['translationMatrix', 'scaleMatrix', 'skewMatrix'];\n    const mappedTransforms: Array<AffineMatrix> = [];\n\n    transforms.forEach((key, _) =>\n      mappedTransforms.push(\n        applyProgressToMatrix(\n          progress,\n          animation.startMatrices[key],\n          animation.stopMatrices[key]\n        )\n      )\n    );\n\n    const [currentTranslation, currentScale, skewMatrix] = mappedTransforms;\n\n    const rotations: Array<'x' | 'y' | 'z'> = ['x', 'y', 'z'];\n    const mappedRotations: Array<AffineMatrix> = [];\n\n    rotations.forEach((key, _) => {\n      const angle = applyProgressToNumber(\n        progress,\n        animation.startMatrices['r' + key],\n        animation.stopMatrices['r' + key]\n      );\n      mappedRotations.push(getRotationMatrix(angle, key));\n    });\n\n    const [rotationMatrixX, rotationMatrixY, rotationMatrixZ] = mappedRotations;\n\n    const rotationMatrix = multiplyMatrices(\n      rotationMatrixX,\n      multiplyMatrices(rotationMatrixY, rotationMatrixZ)\n    );\n\n    const updated = flatten(\n      multiplyMatrices(\n        multiplyMatrices(\n          currentScale,\n          multiplyMatrices(skewMatrix, rotationMatrix)\n        ),\n        currentTranslation\n      )\n    );\n\n    animation.current = updated;\n\n    return finished;\n  };\n\n  const arrayOnStart = (\n    animation: Animation<AnimationObject>,\n    value: Array<number>,\n    timestamp: Timestamp,\n    previousAnimation: Animation<AnimationObject>\n  ): void => {\n    value.forEach((v, i) => {\n      animation[i] = Object.assign({}, animationCopy);\n      animation[i].current = v;\n      animation[i].toValue = (animation.toValue as Array<number>)[i];\n      animation[i].onStart(\n        animation[i],\n        v,\n        timestamp,\n        previousAnimation ? previousAnimation[i] : undefined\n      );\n    });\n\n    animation.current = value;\n  };\n\n  const arrayOnFrame = (\n    animation: Animation<AnimationObject>,\n    timestamp: Timestamp\n  ): boolean => {\n    let finished = true;\n    (animation.current as Array<number>).forEach((_, i) => {\n      const result = animation[i].onFrame(animation[i], timestamp);\n      // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n      finished = finished && result;\n      (animation.current as Array<number>)[i] = animation[i].current;\n    });\n\n    return finished;\n  };\n\n  const objectOnStart = (\n    animation: Animation<AnimationObject>,\n    value: AnimatableValueObject,\n    timestamp: Timestamp,\n    previousAnimation: Animation<AnimationObject>\n  ): void => {\n    for (const key in value) {\n      animation[key] = Object.assign({}, animationCopy);\n      animation[key].onStart = animation.onStart;\n\n      animation[key].current = value[key];\n      animation[key].toValue = (animation.toValue as AnimatableValueObject)[\n        key\n      ];\n      animation[key].onStart(\n        animation[key],\n        value[key],\n        timestamp,\n        previousAnimation ? previousAnimation[key] : undefined\n      );\n    }\n    animation.current = value;\n  };\n\n  const objectOnFrame = (\n    animation: Animation<AnimationObject>,\n    timestamp: Timestamp\n  ): boolean => {\n    let finished = true;\n    const newObject: AnimatableValueObject = {};\n    for (const key in animation.current as AnimatableValueObject) {\n      const result = animation[key].onFrame(animation[key], timestamp);\n      // We really need to assign this value to result, instead of passing it directly - otherwise once \"finished\" is false, onFrame won't be called\n      finished = finished && result;\n      newObject[key] = animation[key].current;\n    }\n    animation.current = newObject;\n    return finished;\n  };\n\n  animation.onStart = (\n    animation: Animation<AnimationObject>,\n    value: number,\n    timestamp: Timestamp,\n    previousAnimation: Animation<AnimationObject>\n  ) => {\n    if (animation.reduceMotion === undefined) {\n      animation.reduceMotion = getReduceMotionFromConfig();\n    }\n    if (animation.reduceMotion) {\n      if (animation.toValue !== undefined) {\n        animation.current = animation.toValue;\n      } else {\n        // if there is no `toValue`, then the base function is responsible for setting the current value\n        baseOnStart(animation, value, timestamp, previousAnimation);\n      }\n      animation.startTime = 0;\n      animation.onFrame = () => true;\n      return;\n    }\n    if (isColor(value)) {\n      colorOnStart(animation, value, timestamp, previousAnimation);\n      animation.onFrame = colorOnFrame;\n      return;\n    } else if (isAffineMatrixFlat(value)) {\n      transformationMatrixOnStart(\n        animation,\n        value,\n        timestamp,\n        previousAnimation\n      );\n      animation.onFrame = transformationMatrixOnFrame;\n      return;\n    } else if (Array.isArray(value)) {\n      arrayOnStart(animation, value, timestamp, previousAnimation);\n      animation.onFrame = arrayOnFrame;\n      return;\n    } else if (typeof value === 'string') {\n      prefNumberSuffOnStart(animation, value, timestamp, previousAnimation);\n      animation.onFrame = prefNumberSuffOnFrame;\n      return;\n    } else if (typeof value === 'object' && value !== null) {\n      objectOnStart(animation, value, timestamp, previousAnimation);\n      animation.onFrame = objectOnFrame;\n      return;\n    }\n    baseOnStart(animation, value, timestamp, previousAnimation);\n  };\n}\n\ntype AnimationToDecoration<\n  T extends AnimationObject | StyleLayoutAnimation,\n  U extends AnimationObject | StyleLayoutAnimation\n> = T extends StyleLayoutAnimation\n  ? Record<string, unknown>\n  : U | (() => U) | AnimatableValue;\n\nexport function defineAnimation<\n  T extends AnimationObject | StyleLayoutAnimation, // type that's supposed to be returned\n  U extends AnimationObject | StyleLayoutAnimation = T // type that's received\n>(starting: AnimationToDecoration<T, U>, factory: () => T): T {\n  'worklet';\n  if (IN_STYLE_UPDATER) {\n    return starting as unknown as T;\n  }\n  const create = () => {\n    'worklet';\n    const animation = factory();\n    decorateAnimation<U>(animation as unknown as U);\n    return animation;\n  };\n\n  if (_WORKLET || SHOULD_BE_USE_WEB) {\n    return create();\n  }\n  // @ts-ignore: eslint-disable-line\n  return create;\n}\n\n/**\n * Lets you cancel a running animation paired to a shared value.\n *\n * @param sharedValue - The shared value of a running animation that you want to cancel.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/cancelAnimation\n */\nexport function cancelAnimation<T>(sharedValue: SharedValue<T>): void {\n  'worklet';\n  // setting the current value cancels the animation if one is currently running\n  sharedValue.value = sharedValue.value; // eslint-disable-line no-self-assign\n}\n"], "mappings": "AAAA;AACA,YAAY;;AAGZ,SACEA,OAAO,EACPC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,aAAa,QACR,WAAW;AAClB,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,gBAAgB;AAchE,SACEC,OAAO,EACPC,gBAAgB,EAChBC,WAAW,EACXC,WAAW,EACXC,oCAAoC,EACpCC,kBAAkB,EAClBC,gBAAgB,EAChBC,iBAAiB,QACZ,oCAAoC;AAC3C,SAASC,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,oBAAoB,QAAQ,kBAAkB;AAEvD,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,MAAMC,iBAAiB,GAAGH,cAAc,CAAC,CAAC;AAE1C,IAAII,OAAO,IAAIH,oBAAoB,CAACI,OAAO,EAAE;EAC3CC,OAAO,CAACC,IAAI,CACT,iWACH,CAAC;AACH;AAEA,OAAO,SAASC,qBAAqBA,CACnCC,MAA8C,EACxC;EACN,SAAS;;EACT,IAAIC,QAAQ,EAAE;IACZ;IACA;IACA;EACF;EACA,IAAIP,iBAAiB,EAAE;IACrB;IACA;EACF;EACA;EACA,IAAIM,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEE,OAAO,EAAE;IACnB;EACF;EAEA,IAAI,CAACpB,iBAAiB,CAACkB,MAAM,CAAC,EAAE;IAC9B,MAAM,IAAIG,KAAK,CACb,uHACF,CAAC;EACH;AACF;AAEA,OAAO,SAASC,iBAAiBA,CAAIC,OAAgB,EAAE;EACrDZ,gBAAgB,GAAG,IAAI;EACvB,MAAMa,MAAM,GAAGD,OAAO,CAAC,CAAC;EACxBZ,gBAAgB,GAAG,KAAK;EACxB,OAAOa,MAAM;AACf;AAQA,OAAO,SAASC,qBAAqBA,CACnCC,KAAsB,EACE;EACxB,SAAS;;EACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK,CACvB,wDACF,CAAC;IACD,IAAI,CAACA,KAAK,EAAE;MACV,MAAM,IAAIN,KAAK,CAAC,8CAA8C,CAAC;IACjE;IACA,MAAMO,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;IACvB,MAAME,MAAM,GAAGF,KAAK,CAAC,CAAC,CAAC;IACvB;IACA,MAAMG,MAAM,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC1C,OAAO;MAAEC,MAAM;MAAEC,MAAM;MAAEE,aAAa,EAAEC,UAAU,CAACF,MAAM;IAAE,CAAC;EAC9D,CAAC,MAAM;IACL,OAAO;MAAEC,aAAa,EAAEL;IAAM,CAAC;EACjC;AACF;;AAEA;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,GAAGvB,oBAAoB,CAACwB,OAAO;AACvD,OAAO,SAASC,yBAAyBA,CAACC,MAAqB,EAAE;EAC/D,SAAS;;EACT,OAAO,CAACA,MAAM,IAAIA,MAAM,KAAKrC,YAAY,CAACsC,MAAM,GAC5CJ,kBAAkB,CAACP,KAAK,GACxBU,MAAM,KAAKrC,YAAY,CAACuC,MAAM;AACpC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,2BAA2BA,CAACH,MAAqB,EAAE;EACjE,SAAS;;EACT;EACA;EACA,IAAI,CAACA,MAAM,EAAE;IACX,OAAOI,SAAS;EAClB;EAEA,OAAOL,yBAAyB,CAACC,MAAM,CAAC;AAC1C;AAEA,SAASK,qBAAqBA,CAC5BC,QAAgB,EAChBC,CAAe,EACfC,CAAe,EACf;EACA,SAAS;;EACT,OAAOxC,WAAW,CAACuC,CAAC,EAAExC,WAAW,CAACI,gBAAgB,CAACqC,CAAC,EAAED,CAAC,CAAC,EAAED,QAAQ,CAAC,CAAC;AACtE;AAEA,SAASG,qBAAqBA,CAACH,QAAgB,EAAEC,CAAS,EAAEC,CAAS,EAAE;EACrE,SAAS;;EACT,OAAOD,CAAC,GAAGD,QAAQ,IAAIE,CAAC,GAAGD,CAAC,CAAC;AAC/B;AAEA,SAASG,iBAAiBA,CACxBC,SAAY,EACN;EACN,SAAS;;EACT,MAAMC,WAAW,GAAID,SAAS,CAAgCE,OAAO;EACrE,MAAMC,WAAW,GAAIH,SAAS,CAAgCI,OAAO;EAErE,IAAKJ,SAAS,CAA0BK,aAAa,EAAE;IACrDL,SAAS,CAACE,OAAO,GAAG,CAClBF,SAAqC,EACrCrB,KAAa,EACb2B,SAAoB,EACpBC,iBAA6C,KAC1C;MACH,IAAIP,SAAS,CAACQ,YAAY,KAAKf,SAAS,EAAE;QACxCO,SAAS,CAACQ,YAAY,GAAGpB,yBAAyB,CAAC,CAAC;MACtD;MACA,OAAOa,WAAW,CAACD,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;IACpE,CAAC;IACD;EACF;EAEA,MAAME,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,SAAS,CAAC;EAClD,OAAOS,aAAa,CAACG,QAAQ;EAE7B,MAAMC,qBAAqB,GAAGA,CAC5Bb,SAAqC,EACrCrB,KAAsB,EACtB2B,SAAiB,EACjBC,iBAA6C,KAC1C;IACH;IACA,MAAM;MAAE1B,MAAM;MAAEC,MAAM;MAAEE;IAAc,CAAC,GAAGN,qBAAqB,CAACC,KAAK,CAAC;IACtEqB,SAAS,CAACc,QAAQ,GAAGjC,MAAM;IAC3BmB,SAAS,CAACe,QAAQ,GAAGjC,MAAM;IAC3BkB,SAAS,CAACgB,eAAe,GAAGhC,aAAa;IACzC,MAAM;MAAEA,aAAa,EAAEiC;IAAgB,CAAC,GAAGvC,qBAAqB,CAC9DsB,SAAS,CAACkB,OACZ,CAAC;IACDlB,SAAS,CAACmB,OAAO,GAAGnC,aAAa;IACjCgB,SAAS,CAACoB,UAAU,GAAGpC,aAAa;IACpCgB,SAAS,CAACkB,OAAO,GAAGD,eAAe;IACnC,IAAIV,iBAAiB,IAAIA,iBAAiB,KAAKP,SAAS,EAAE;MACxD,MAAM;QACJnB,MAAM,EAAEwC,QAAQ;QAChBvC,MAAM,EAAEwC,QAAQ;QAChBtC,aAAa,EAAEuC;MACjB,CAAC,GAAG7C,qBAAqB,CAAC6B,iBAAiB,CAACY,OAA0B,CAAC;MACvEZ,iBAAiB,CAACY,OAAO,GAAGI,eAAe;MAC3ChB,iBAAiB,CAACO,QAAQ,GAAGO,QAAQ;MACrCd,iBAAiB,CAACQ,QAAQ,GAAGO,QAAQ;IACvC;IAEArB,WAAW,CAACD,SAAS,EAAEhB,aAAa,EAAEsB,SAAS,EAAEC,iBAAiB,CAAC;IAEnEP,SAAS,CAACmB,OAAO,GACf,CAACnB,SAAS,CAACc,QAAQ,IAAI,EAAE,IACzBd,SAAS,CAACmB,OAAO,IAChBnB,SAAS,CAACe,QAAQ,IAAI,EAAE,CAAC;IAE5B,IAAIR,iBAAiB,IAAIA,iBAAiB,KAAKP,SAAS,EAAE;MACxDO,iBAAiB,CAACY,OAAO,GACvB,CAACZ,iBAAiB,CAACO,QAAQ,IAAI,EAAE;MACjC;MACA;MACAP,iBAAiB,CAACY,OAAO,IACxBZ,iBAAiB,CAACQ,QAAQ,IAAI,EAAE,CAAC;IACtC;EACF,CAAC;EACD,MAAMS,qBAAqB,GAAGA,CAC5BxB,SAAqC,EACrCM,SAAiB,KACd;IACHN,SAAS,CAACmB,OAAO,GAAGnB,SAAS,CAACgB,eAAe;IAC7C,MAAMS,GAAG,GAAGtB,WAAW,CAACH,SAAS,EAAEM,SAAS,CAAC;IAC7CN,SAAS,CAACgB,eAAe,GAAGhB,SAAS,CAACmB,OAAO;IAC7CnB,SAAS,CAACmB,OAAO,GACf,CAACnB,SAAS,CAACc,QAAQ,IAAI,EAAE,IACzBd,SAAS,CAACmB,OAAO,IAChBnB,SAAS,CAACe,QAAQ,IAAI,EAAE,CAAC;IAC5B,OAAOU,GAAG;EACZ,CAAC;EAED,MAAMC,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChC,MAAMC,YAAY,GAAGA,CACnB3B,SAAqC,EACrCrB,KAAsB,EACtB2B,SAAoB,EACpBC,iBAA6C,KACpC;IACT,IAAIqB,SAA2B;IAC/B,IAAIC,WAA6B;IACjC,IAAIC,WAA6B;IACjC,MAAML,GAAkB,GAAG,EAAE;IAC7B,IAAI9E,OAAO,CAACgC,KAAK,CAAC,EAAE;MAClBkD,WAAW,GAAG9E,aAAa,CAACH,aAAa,CAACoD,SAAS,CAACmB,OAAO,CAAC,CAAC;MAC7DS,SAAS,GAAG7E,aAAa,CAACH,aAAa,CAAC+B,KAAK,CAAC,CAAC;MAC/C,IAAIqB,SAAS,CAACkB,OAAO,EAAE;QACrBY,WAAW,GAAG/E,aAAa,CAACH,aAAa,CAACoD,SAAS,CAACkB,OAAO,CAAC,CAAC;MAC/D;IACF;IACAQ,GAAG,CAACK,OAAO,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;MACxBjC,SAAS,CAACgC,CAAC,CAAC,GAAGtB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC;MAC/CT,SAAS,CAACgC,CAAC,CAAC,CAACb,OAAO,GAAGU,WAAW,CAACI,KAAK,CAAC;MACzCjC,SAAS,CAACgC,CAAC,CAAC,CAACd,OAAO,GAAGY,WAAW,GAAGA,WAAW,CAACG,KAAK,CAAC,GAAGxC,SAAS;MACnEO,SAAS,CAACgC,CAAC,CAAC,CAAC9B,OAAO,CAClBF,SAAS,CAACgC,CAAC,CAAC,EACZJ,SAAS,CAACK,KAAK,CAAC,EAChB3B,SAAS,EACTC,iBAAiB,GAAGA,iBAAiB,CAACyB,CAAC,CAAC,GAAGvC,SAC7C,CAAC;MACDgC,GAAG,CAACS,IAAI,CAAClC,SAAS,CAACgC,CAAC,CAAC,CAACb,OAAO,CAAC;IAChC,CAAC,CAAC;IAEFnB,SAAS,CAACmB,OAAO,GAAGtE,oBAAoB,CACtCC,YAAY,CAAC2E,GAAuB,CACtC,CAAC;EACH,CAAC;EAED,MAAMU,YAAY,GAAGA,CACnBnC,SAAqC,EACrCM,SAAoB,KACR;IACZ,MAAMuB,WAAW,GAAG9E,aAAa,CAACH,aAAa,CAACoD,SAAS,CAACmB,OAAO,CAAC,CAAC;IACnE,MAAMM,GAAkB,GAAG,EAAE;IAC7B,IAAIW,QAAQ,GAAG,IAAI;IACnBV,GAAG,CAACK,OAAO,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;MACxBjC,SAAS,CAACgC,CAAC,CAAC,CAACb,OAAO,GAAGU,WAAW,CAACI,KAAK,CAAC;MACzC,MAAMxD,MAAM,GAAGuB,SAAS,CAACgC,CAAC,CAAC,CAAC5B,OAAO,CAACJ,SAAS,CAACgC,CAAC,CAAC,EAAE1B,SAAS,CAAC;MAC5D;MACA8B,QAAQ,GAAGA,QAAQ,IAAI3D,MAAM;MAC7BgD,GAAG,CAACS,IAAI,CAAClC,SAAS,CAACgC,CAAC,CAAC,CAACb,OAAO,CAAC;IAChC,CAAC,CAAC;IAEFnB,SAAS,CAACmB,OAAO,GAAGtE,oBAAoB,CACtCC,YAAY,CAAC2E,GAAuB,CACtC,CAAC;IACD,OAAOW,QAAQ;EACjB,CAAC;EAED,MAAMC,2BAA2B,GAAGA,CAClCrC,SAAqC,EACrCrB,KAAuB,EACvB2B,SAAoB,EACpBC,iBAA6C,KACpC;IACT,MAAMW,OAAO,GAAGlB,SAAS,CAACkB,OAA2B;IAErDlB,SAAS,CAACsC,aAAa,GAAGhF,oCAAoC,CAACqB,KAAK,CAAC;IACrEqB,SAAS,CAACuC,YAAY,GAAGjF,oCAAoC,CAAC4D,OAAO,CAAC;;IAEtE;IACA;IACA;;IAEAlB,SAAS,CAAC,CAAC,CAAC,GAAGU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC;IAC/CT,SAAS,CAAC,CAAC,CAAC,CAACmB,OAAO,GAAG,CAAC;IACxBnB,SAAS,CAAC,CAAC,CAAC,CAACkB,OAAO,GAAG,GAAG;IAC1BlB,SAAS,CAAC,CAAC,CAAC,CAACE,OAAO,CAClBF,SAAS,CAAC,CAAC,CAAC,EACZ,CAAC,EACDM,SAAS,EACTC,iBAAiB,GAAGA,iBAAiB,CAAC,CAAC,CAAC,GAAGd,SAC7C,CAAC;IAEDO,SAAS,CAACmB,OAAO,GAAGxC,KAAK;EAC3B,CAAC;EAED,MAAM6D,2BAA2B,GAAGA,CAClCxC,SAAqC,EACrCM,SAAoB,KACR;IACZ,IAAI8B,QAAQ,GAAG,IAAI;IACnB,MAAM3D,MAAM,GAAGuB,SAAS,CAAC,CAAC,CAAC,CAACI,OAAO,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAEM,SAAS,CAAC;IAC5D;IACA8B,QAAQ,GAAGA,QAAQ,IAAI3D,MAAM;IAE7B,MAAMkB,QAAQ,GAAGK,SAAS,CAAC,CAAC,CAAC,CAACmB,OAAO,GAAG,GAAG;IAE3C,MAAMsB,UAAU,GAAG,CAAC,mBAAmB,EAAE,aAAa,EAAE,YAAY,CAAC;IACrE,MAAMC,gBAAqC,GAAG,EAAE;IAEhDD,UAAU,CAACV,OAAO,CAAC,CAACY,GAAG,EAAEC,CAAC,KACxBF,gBAAgB,CAACR,IAAI,CACnBxC,qBAAqB,CACnBC,QAAQ,EACRK,SAAS,CAACsC,aAAa,CAACK,GAAG,CAAC,EAC5B3C,SAAS,CAACuC,YAAY,CAACI,GAAG,CAC5B,CACF,CACF,CAAC;IAED,MAAM,CAACE,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,CAAC,GAAGL,gBAAgB;IAEvE,MAAMM,SAAiC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACzD,MAAMC,eAAoC,GAAG,EAAE;IAE/CD,SAAS,CAACjB,OAAO,CAAC,CAACY,GAAG,EAAEC,CAAC,KAAK;MAC5B,MAAMM,KAAK,GAAGpD,qBAAqB,CACjCH,QAAQ,EACRK,SAAS,CAACsC,aAAa,CAAC,GAAG,GAAGK,GAAG,CAAC,EAClC3C,SAAS,CAACuC,YAAY,CAAC,GAAG,GAAGI,GAAG,CAClC,CAAC;MACDM,eAAe,CAACf,IAAI,CAACzE,iBAAiB,CAACyF,KAAK,EAAEP,GAAG,CAAC,CAAC;IACrD,CAAC,CAAC;IAEF,MAAM,CAACQ,eAAe,EAAEC,eAAe,EAAEC,eAAe,CAAC,GAAGJ,eAAe;IAE3E,MAAMK,cAAc,GAAGnG,gBAAgB,CACrCgG,eAAe,EACfhG,gBAAgB,CAACiG,eAAe,EAAEC,eAAe,CACnD,CAAC;IAED,MAAME,OAAO,GAAGrG,OAAO,CACrBC,gBAAgB,CACdA,gBAAgB,CACd2F,YAAY,EACZ3F,gBAAgB,CAAC4F,UAAU,EAAEO,cAAc,CAC7C,CAAC,EACDT,kBACF,CACF,CAAC;IAED7C,SAAS,CAACmB,OAAO,GAAGoC,OAAO;IAE3B,OAAOnB,QAAQ;EACjB,CAAC;EAED,MAAMoB,YAAY,GAAGA,CACnBxD,SAAqC,EACrCrB,KAAoB,EACpB2B,SAAoB,EACpBC,iBAA6C,KACpC;IACT5B,KAAK,CAACoD,OAAO,CAAC,CAAC0B,CAAC,EAAEzB,CAAC,KAAK;MACtBhC,SAAS,CAACgC,CAAC,CAAC,GAAGtB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC;MAC/CT,SAAS,CAACgC,CAAC,CAAC,CAACb,OAAO,GAAGsC,CAAC;MACxBzD,SAAS,CAACgC,CAAC,CAAC,CAACd,OAAO,GAAIlB,SAAS,CAACkB,OAAO,CAAmBc,CAAC,CAAC;MAC9DhC,SAAS,CAACgC,CAAC,CAAC,CAAC9B,OAAO,CAClBF,SAAS,CAACgC,CAAC,CAAC,EACZyB,CAAC,EACDnD,SAAS,EACTC,iBAAiB,GAAGA,iBAAiB,CAACyB,CAAC,CAAC,GAAGvC,SAC7C,CAAC;IACH,CAAC,CAAC;IAEFO,SAAS,CAACmB,OAAO,GAAGxC,KAAK;EAC3B,CAAC;EAED,MAAM+E,YAAY,GAAGA,CACnB1D,SAAqC,EACrCM,SAAoB,KACR;IACZ,IAAI8B,QAAQ,GAAG,IAAI;IAClBpC,SAAS,CAACmB,OAAO,CAAmBY,OAAO,CAAC,CAACa,CAAC,EAAEZ,CAAC,KAAK;MACrD,MAAMvD,MAAM,GAAGuB,SAAS,CAACgC,CAAC,CAAC,CAAC5B,OAAO,CAACJ,SAAS,CAACgC,CAAC,CAAC,EAAE1B,SAAS,CAAC;MAC5D;MACA8B,QAAQ,GAAGA,QAAQ,IAAI3D,MAAM;MAC5BuB,SAAS,CAACmB,OAAO,CAAmBa,CAAC,CAAC,GAAGhC,SAAS,CAACgC,CAAC,CAAC,CAACb,OAAO;IAChE,CAAC,CAAC;IAEF,OAAOiB,QAAQ;EACjB,CAAC;EAED,MAAMuB,aAAa,GAAGA,CACpB3D,SAAqC,EACrCrB,KAA4B,EAC5B2B,SAAoB,EACpBC,iBAA6C,KACpC;IACT,KAAK,MAAMoC,GAAG,IAAIhE,KAAK,EAAE;MACvBqB,SAAS,CAAC2C,GAAG,CAAC,GAAGjC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC;MACjDT,SAAS,CAAC2C,GAAG,CAAC,CAACzC,OAAO,GAAGF,SAAS,CAACE,OAAO;MAE1CF,SAAS,CAAC2C,GAAG,CAAC,CAACxB,OAAO,GAAGxC,KAAK,CAACgE,GAAG,CAAC;MACnC3C,SAAS,CAAC2C,GAAG,CAAC,CAACzB,OAAO,GAAIlB,SAAS,CAACkB,OAAO,CACzCyB,GAAG,CACJ;MACD3C,SAAS,CAAC2C,GAAG,CAAC,CAACzC,OAAO,CACpBF,SAAS,CAAC2C,GAAG,CAAC,EACdhE,KAAK,CAACgE,GAAG,CAAC,EACVrC,SAAS,EACTC,iBAAiB,GAAGA,iBAAiB,CAACoC,GAAG,CAAC,GAAGlD,SAC/C,CAAC;IACH;IACAO,SAAS,CAACmB,OAAO,GAAGxC,KAAK;EAC3B,CAAC;EAED,MAAMiF,aAAa,GAAGA,CACpB5D,SAAqC,EACrCM,SAAoB,KACR;IACZ,IAAI8B,QAAQ,GAAG,IAAI;IACnB,MAAMyB,SAAgC,GAAG,CAAC,CAAC;IAC3C,KAAK,MAAMlB,GAAG,IAAI3C,SAAS,CAACmB,OAAO,EAA2B;MAC5D,MAAM1C,MAAM,GAAGuB,SAAS,CAAC2C,GAAG,CAAC,CAACvC,OAAO,CAACJ,SAAS,CAAC2C,GAAG,CAAC,EAAErC,SAAS,CAAC;MAChE;MACA8B,QAAQ,GAAGA,QAAQ,IAAI3D,MAAM;MAC7BoF,SAAS,CAAClB,GAAG,CAAC,GAAG3C,SAAS,CAAC2C,GAAG,CAAC,CAACxB,OAAO;IACzC;IACAnB,SAAS,CAACmB,OAAO,GAAG0C,SAAS;IAC7B,OAAOzB,QAAQ;EACjB,CAAC;EAEDpC,SAAS,CAACE,OAAO,GAAG,CAClBF,SAAqC,EACrCrB,KAAa,EACb2B,SAAoB,EACpBC,iBAA6C,KAC1C;IACH,IAAIP,SAAS,CAACQ,YAAY,KAAKf,SAAS,EAAE;MACxCO,SAAS,CAACQ,YAAY,GAAGpB,yBAAyB,CAAC,CAAC;IACtD;IACA,IAAIY,SAAS,CAACQ,YAAY,EAAE;MAC1B,IAAIR,SAAS,CAACkB,OAAO,KAAKzB,SAAS,EAAE;QACnCO,SAAS,CAACmB,OAAO,GAAGnB,SAAS,CAACkB,OAAO;MACvC,CAAC,MAAM;QACL;QACAjB,WAAW,CAACD,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;MAC7D;MACAP,SAAS,CAAC8D,SAAS,GAAG,CAAC;MACvB9D,SAAS,CAACI,OAAO,GAAG,MAAM,IAAI;MAC9B;IACF;IACA,IAAIzD,OAAO,CAACgC,KAAK,CAAC,EAAE;MAClBgD,YAAY,CAAC3B,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;MAC5DP,SAAS,CAACI,OAAO,GAAG+B,YAAY;MAChC;IACF,CAAC,MAAM,IAAI5E,kBAAkB,CAACoB,KAAK,CAAC,EAAE;MACpC0D,2BAA2B,CACzBrC,SAAS,EACTrB,KAAK,EACL2B,SAAS,EACTC,iBACF,CAAC;MACDP,SAAS,CAACI,OAAO,GAAGoC,2BAA2B;MAC/C;IACF,CAAC,MAAM,IAAIuB,KAAK,CAACC,OAAO,CAACrF,KAAK,CAAC,EAAE;MAC/B6E,YAAY,CAACxD,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;MAC5DP,SAAS,CAACI,OAAO,GAAGsD,YAAY;MAChC;IACF,CAAC,MAAM,IAAI,OAAO/E,KAAK,KAAK,QAAQ,EAAE;MACpCkC,qBAAqB,CAACb,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;MACrEP,SAAS,CAACI,OAAO,GAAGoB,qBAAqB;MACzC;IACF,CAAC,MAAM,IAAI,OAAO7C,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MACtDgF,aAAa,CAAC3D,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;MAC7DP,SAAS,CAACI,OAAO,GAAGwD,aAAa;MACjC;IACF;IACA3D,WAAW,CAACD,SAAS,EAAErB,KAAK,EAAE2B,SAAS,EAAEC,iBAAiB,CAAC;EAC7D,CAAC;AACH;AASA,OAAO,SAAS0D,eAAeA,CAG7BC,QAAqC,EAAE7F,OAAgB,EAAK;EAC5D,SAAS;;EACT,IAAIT,gBAAgB,EAAE;IACpB,OAAOsG,QAAQ;EACjB;EACA,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnB,SAAS;;IACT,MAAMnE,SAAS,GAAG3B,OAAO,CAAC,CAAC;IAC3B0B,iBAAiB,CAAIC,SAAyB,CAAC;IAC/C,OAAOA,SAAS;EAClB,CAAC;EAED,IAAI5B,QAAQ,IAAIP,iBAAiB,EAAE;IACjC,OAAOsG,MAAM,CAAC,CAAC;EACjB;EACA;EACA,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAIC,WAA2B,EAAQ;EACpE,SAAS;;EACT;EACAA,WAAW,CAAC1F,KAAK,GAAG0F,WAAW,CAAC1F,KAAK,CAAC,CAAC;AACzC", "ignoreList": []}