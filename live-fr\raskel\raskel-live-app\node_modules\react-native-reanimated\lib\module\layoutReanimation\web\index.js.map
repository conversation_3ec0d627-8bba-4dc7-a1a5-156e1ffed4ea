{"version": 3, "names": ["startWebLayoutAnimation", "tryActivateLayoutTransition", "getReducedMotionFromConfig", "saveSnapshot", "configureWebLayoutAnimations"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\n\nexport {\n  startWebLayoutAnimation,\n  tryActivateLayoutTransition,\n} from './animationsManager';\n\nexport { getReducedMotionFromConfig, saveSnapshot } from './componentUtils';\n\nexport { configureWebLayoutAnimations } from './domUtils';\n"], "mappings": "AAAA,YAAY;;AAEZ,SACEA,uBAAuB,EACvBC,2BAA2B,QACtB,qBAAqB;AAE5B,SAASC,0BAA0B,EAAEC,YAAY,QAAQ,kBAAkB;AAE3E,SAASC,4BAA4B,QAAQ,YAAY", "ignoreList": []}