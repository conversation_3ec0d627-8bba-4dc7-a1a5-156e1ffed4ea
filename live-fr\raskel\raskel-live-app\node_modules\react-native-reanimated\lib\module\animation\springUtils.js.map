{"version": 3, "names": ["checkIfConfigIsValid", "config", "_config$clamp", "_config$clamp2", "errorMessage", "for<PERSON>ach", "prop", "value", "duration", "clamp", "min", "max", "console", "warn", "bisectRoot", "func", "maxIterations", "ACCURACY", "idx", "current", "Math", "abs", "initialCalculations", "mass", "skipAnimation", "zeta", "omega0", "omega1", "useDuration", "stiffness", "k", "dampingRatio", "sqrt", "damping", "c", "m", "scaleZetaToMatchClamps", "animation", "toValue", "startValue", "toValueNum", "Number", "firstBound", "secondBound", "relativeExtremum1", "undefined", "relativeExtremum2", "newZeta1", "log", "PI", "newZeta2", "zetaSatisfyingClamp", "filter", "x", "calculateNewMassToMatchDuration", "x0", "v0", "restSpeedThreshold", "threshold", "durationForMass", "amplitude", "exp", "criticallyDampedSpringCalculations", "precalculatedValues", "t", "criticallyDampedEnvelope", "criticallyDampedPosition", "criticallyDampedVelocity", "position", "velocity", "underDampedSpringCalculations", "sin1", "sin", "cos1", "cos", "underDampedEnvelope", "underDampedFrag1", "underDampedPosition", "underDampedVelocity", "isAnimationTerminatingCalculation", "isOvershooting", "overshootClamping", "isVelocity", "isDisplacement", "restDisplacementThreshold"], "sources": ["springUtils.ts"], "sourcesContent": ["'use strict';\nimport type {\n  Animation,\n  AnimatableValue,\n  Timestamp,\n  ReduceMotion,\n} from '../commonTypes';\n\n/**\n * Spring animation configuration.\n *\n * @param mass - The weight of the spring. Reducing this value makes the animation faster. Defaults to 1.\n * @param damping - How quickly a spring slows down. Higher damping means the spring will come to rest faster. Defaults to 10.\n * @param duration - Length of the animation (in milliseconds). Defaults to 2000.\n * @param dampingRatio - How damped the spring is. Value 1 means the spring is critically damped, and value \\>1 means the spring is overdamped. Defaults to 0.5.\n * @param stiffness - How bouncy the spring is. Defaults to 100.\n * @param velocity - Initial velocity applied to the spring equation. Defaults to 0.\n * @param overshootClamping - Whether a spring can bounce over the `toValue`. Defaults to false.\n * @param restDisplacementThreshold - The displacement below which the spring will snap to toValue without further oscillations. Defaults to 0.01.\n * @param restSpeedThreshold - The speed in pixels per second from which the spring will snap to toValue without further oscillations. Defaults to 2.\n * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSpring/#config-\n */\nexport type SpringConfig = {\n  stiffness?: number;\n  overshootClamping?: boolean;\n  restDisplacementThreshold?: number;\n  restSpeedThreshold?: number;\n  velocity?: number;\n  reduceMotion?: ReduceMotion;\n} & (\n  | {\n      mass?: number;\n      damping?: number;\n      duration?: never;\n      dampingRatio?: never;\n      clamp?: never;\n    }\n  | {\n      mass?: never;\n      damping?: never;\n      duration?: number;\n      dampingRatio?: number;\n      clamp?: { min?: number; max?: number };\n    }\n);\n\n// This type contains all the properties from SpringConfig, which are changed to be required,\n// except for optional 'reduceMotion' and 'clamp'\nexport type DefaultSpringConfig = {\n  [K in keyof Required<SpringConfig>]: K extends 'reduceMotion' | 'clamp'\n    ? Required<SpringConfig>[K] | undefined\n    : Required<SpringConfig>[K];\n};\nexport type WithSpringConfig = SpringConfig;\n\nexport interface SpringConfigInner {\n  useDuration: boolean;\n  skipAnimation: boolean;\n}\n\nexport interface SpringAnimation extends Animation<SpringAnimation> {\n  current: AnimatableValue;\n  toValue: AnimatableValue;\n  velocity: number;\n  lastTimestamp: Timestamp;\n  startTimestamp: Timestamp;\n  startValue: number;\n  zeta: number;\n  omega0: number;\n  omega1: number;\n}\n\nexport interface InnerSpringAnimation\n  extends Omit<SpringAnimation, 'toValue' | 'current'> {\n  toValue: number;\n  current: number;\n}\nexport function checkIfConfigIsValid(config: DefaultSpringConfig): boolean {\n  'worklet';\n  let errorMessage = '';\n  (\n    [\n      'stiffness',\n      'damping',\n      'dampingRatio',\n      'restDisplacementThreshold',\n      'restSpeedThreshold',\n      'mass',\n    ] as const\n  ).forEach((prop) => {\n    const value = config[prop];\n    if (value <= 0) {\n      errorMessage += `, ${prop} must be grater than zero but got ${value}`;\n    }\n  });\n\n  if (config.duration < 0) {\n    errorMessage += `, duration can't be negative, got ${config.duration}`;\n  }\n\n  if (\n    config.clamp?.min &&\n    config.clamp?.max &&\n    config.clamp.min > config.clamp.max\n  ) {\n    errorMessage += `, clamp.min should be lower than clamp.max, got clamp: {min: ${config.clamp.min}, max: ${config.clamp.max}} `;\n  }\n\n  if (errorMessage !== '') {\n    console.warn('[Reanimated] Invalid spring config' + errorMessage);\n  }\n\n  return errorMessage === '';\n}\n\n// ts-prune-ignore-next This function is exported to be tested\nexport function bisectRoot({\n  min,\n  max,\n  func,\n  maxIterations = 20,\n}: {\n  min: number;\n  max: number;\n  func: (x: number) => number;\n  maxIterations?: number;\n}) {\n  'worklet';\n  const ACCURACY = 0.00005;\n  let idx = maxIterations;\n  let current = (max + min) / 2;\n  while (Math.abs(func(current)) > ACCURACY && idx > 0) {\n    idx -= 1;\n\n    if (func(current) < 0) {\n      min = current;\n    } else {\n      max = current;\n    }\n    current = (min + max) / 2;\n  }\n  return current;\n}\n\nexport function initialCalculations(\n  mass = 0,\n  config: DefaultSpringConfig & SpringConfigInner\n): {\n  zeta: number;\n  omega0: number;\n  omega1: number;\n} {\n  'worklet';\n\n  if (config.skipAnimation) {\n    return { zeta: 0, omega0: 0, omega1: 0 };\n  }\n\n  if (config.useDuration) {\n    const { stiffness: k, dampingRatio: zeta } = config;\n\n    /** omega0 and omega1 denote angular frequency and natural angular frequency, see this link for formulas:\n     *  https://courses.lumenlearning.com/suny-osuniversityphysics/chapter/15-5-damped-oscillations/\n     */\n    const omega0 = Math.sqrt(k / mass);\n    const omega1 = omega0 * Math.sqrt(1 - zeta ** 2);\n\n    return { zeta, omega0, omega1 };\n  } else {\n    const { damping: c, mass: m, stiffness: k } = config;\n\n    const zeta = c / (2 * Math.sqrt(k * m)); // damping ratio\n    const omega0 = Math.sqrt(k / m); // undamped angular frequency of the oscillator (rad/ms)\n    const omega1 = omega0 * Math.sqrt(1 - zeta ** 2); // exponential decay\n\n    return { zeta, omega0, omega1 };\n  }\n}\n\n/** We make an assumption that we can manipulate zeta without changing duration of movement.\n *  According to theory this change is small and tests shows that we can indeed ignore it.\n */\nexport function scaleZetaToMatchClamps(\n  animation: SpringAnimation,\n  clamp: { min?: number; max?: number }\n): number {\n  'worklet';\n  const { zeta, toValue, startValue } = animation;\n  const toValueNum = Number(toValue);\n\n  if (toValueNum === startValue) {\n    return zeta;\n  }\n\n  const [firstBound, secondBound] =\n    toValueNum - startValue > 0\n      ? [clamp.min, clamp.max]\n      : [clamp.max, clamp.min];\n\n  /** The extrema we get from equation below are relative (we obtain a ratio),\n   *  To get absolute extrema we convert it as follows:\n   *\n   *  AbsoluteExtremum = startValue ± RelativeExtremum * (toValue - startValue)\n   *  Where ± denotes:\n   *    + if extremum is over the target\n   *    - otherwise\n   */\n\n  const relativeExtremum1 =\n    secondBound !== undefined\n      ? Math.abs((secondBound - toValueNum) / (toValueNum - startValue))\n      : undefined;\n\n  const relativeExtremum2 =\n    firstBound !== undefined\n      ? Math.abs((firstBound - toValueNum) / (toValueNum - startValue))\n      : undefined;\n\n  /** Use this formula http://hyperphysics.phy-astr.gsu.edu/hbase/oscda.html to calculate\n   *  first two extrema. These extrema are located where cos = +- 1\n   *\n   *  Therefore the first two extrema are:\n   *\n   *     Math.exp(-zeta * Math.PI);      (over the target)\n   *     Math.exp(-zeta * 2 * Math.PI);  (before the target)\n   */\n\n  const newZeta1 =\n    relativeExtremum1 !== undefined\n      ? Math.abs(Math.log(relativeExtremum1) / Math.PI)\n      : undefined;\n\n  const newZeta2 =\n    relativeExtremum2 !== undefined\n      ? Math.abs(Math.log(relativeExtremum2) / (2 * Math.PI))\n      : undefined;\n\n  const zetaSatisfyingClamp = [newZeta1, newZeta2].filter(\n    (x: number | undefined): x is number => x !== undefined\n  );\n  // The bigger is zeta the smaller are bounces, we return the biggest one\n  // because it should satisfy all conditions\n  return Math.max(...zetaSatisfyingClamp, zeta);\n}\n\n/** Runs before initial */\nexport function calculateNewMassToMatchDuration(\n  x0: number,\n  config: DefaultSpringConfig & SpringConfigInner,\n  v0: number\n) {\n  'worklet';\n  if (config.skipAnimation) {\n    return 0;\n  }\n\n  /** Use this formula: https://phys.libretexts.org/Bookshelves/University_Physics/Book%3A_University_Physics_(OpenStax)/Book%3A_University_Physics_I_-_Mechanics_Sound_Oscillations_and_Waves_(OpenStax)/15%3A_Oscillations/15.06%3A_Damped_Oscillations\n       * to find the asymptote and estimate the damping that gives us the expected duration \n\n            ⎛ ⎛ c⎞           ⎞           \n            ⎜-⎜──⎟ ⋅ duration⎟           \n            ⎝ ⎝2m⎠           ⎠           \n       A ⋅ e                   = threshold\n\n \n      Amplitude calculated using \"Conservation of energy\"\n                       _________________\n                      ╱      2         2\n                     ╱ m ⋅ v0  + k ⋅ x0 \n      amplitude =   ╱  ─────────────────\n                  ╲╱           k        \n\n      And replace mass with damping ratio which is provided: m = (c^2)/(4 * k * zeta^2)   \n      */\n  const {\n    stiffness: k,\n    dampingRatio: zeta,\n    restSpeedThreshold: threshold,\n    duration,\n  } = config;\n\n  const durationForMass = (mass: number) => {\n    'worklet';\n    const amplitude =\n      (mass * v0 * v0 + k * x0 * x0) / (Math.exp(1 - 0.5 * zeta) * k);\n    const c = zeta * 2 * Math.sqrt(k * mass);\n    return (\n      1000 * ((-2 * mass) / c) * Math.log((threshold * 0.01) / amplitude) -\n      duration\n    );\n  };\n\n  // Bisection turns out to be much faster than Newton's method in our case\n  return bisectRoot({ min: 0, max: 100, func: durationForMass });\n}\n\nexport function criticallyDampedSpringCalculations(\n  animation: InnerSpringAnimation,\n  precalculatedValues: {\n    v0: number;\n    x0: number;\n    omega0: number;\n    t: number;\n  }\n): { position: number; velocity: number } {\n  'worklet';\n  const { toValue } = animation;\n\n  const { v0, x0, omega0, t } = precalculatedValues;\n\n  const criticallyDampedEnvelope = Math.exp(-omega0 * t);\n  const criticallyDampedPosition =\n    toValue - criticallyDampedEnvelope * (x0 + (v0 + omega0 * x0) * t);\n\n  const criticallyDampedVelocity =\n    criticallyDampedEnvelope *\n    (v0 * (t * omega0 - 1) + t * x0 * omega0 * omega0);\n\n  return {\n    position: criticallyDampedPosition,\n    velocity: criticallyDampedVelocity,\n  };\n}\n\nexport function underDampedSpringCalculations(\n  animation: InnerSpringAnimation,\n  precalculatedValues: {\n    zeta: number;\n    v0: number;\n    x0: number;\n    omega0: number;\n    omega1: number;\n    t: number;\n  }\n): { position: number; velocity: number } {\n  'worklet';\n  const { toValue, current, velocity } = animation;\n\n  const { zeta, t, omega0, omega1 } = precalculatedValues;\n\n  const v0 = -velocity;\n  const x0 = toValue - current;\n\n  const sin1 = Math.sin(omega1 * t);\n  const cos1 = Math.cos(omega1 * t);\n\n  // under damped\n  const underDampedEnvelope = Math.exp(-zeta * omega0 * t);\n  const underDampedFrag1 =\n    underDampedEnvelope *\n    (sin1 * ((v0 + zeta * omega0 * x0) / omega1) + x0 * cos1);\n\n  const underDampedPosition = toValue - underDampedFrag1;\n  // This looks crazy -- it's actually just the derivative of the oscillation function\n  const underDampedVelocity =\n    zeta * omega0 * underDampedFrag1 -\n    underDampedEnvelope *\n      (cos1 * (v0 + zeta * omega0 * x0) - omega1 * x0 * sin1);\n\n  return { position: underDampedPosition, velocity: underDampedVelocity };\n}\n\nexport function isAnimationTerminatingCalculation(\n  animation: InnerSpringAnimation,\n  config: DefaultSpringConfig\n): {\n  isOvershooting: boolean;\n  isVelocity: boolean;\n  isDisplacement: boolean;\n} {\n  'worklet';\n  const { toValue, velocity, startValue, current } = animation;\n\n  const isOvershooting = config.overshootClamping\n    ? (current > toValue && startValue < toValue) ||\n      (current < toValue && startValue > toValue)\n    : false;\n\n  const isVelocity = Math.abs(velocity) < config.restSpeedThreshold;\n  const isDisplacement =\n    Math.abs(toValue - current) < config.restDisplacementThreshold;\n\n  return { isOvershooting, isVelocity, isDisplacement };\n}\n"], "mappings": "AAAA,YAAY;;AAQZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAyBA;AACA;;AA8BA,OAAO,SAASA,oBAAoBA,CAACC,MAA2B,EAAW;EACzE,SAAS;;EAAC,IAAAC,aAAA,EAAAC,cAAA;EACV,IAAIC,YAAY,GAAG,EAAE;EAEnB,CACE,WAAW,EACX,SAAS,EACT,cAAc,EACd,2BAA2B,EAC3B,oBAAoB,EACpB,MAAM,CACP,CACDC,OAAO,CAAEC,IAAI,IAAK;IAClB,MAAMC,KAAK,GAAGN,MAAM,CAACK,IAAI,CAAC;IAC1B,IAAIC,KAAK,IAAI,CAAC,EAAE;MACdH,YAAY,IAAK,KAAIE,IAAK,qCAAoCC,KAAM,EAAC;IACvE;EACF,CAAC,CAAC;EAEF,IAAIN,MAAM,CAACO,QAAQ,GAAG,CAAC,EAAE;IACvBJ,YAAY,IAAK,qCAAoCH,MAAM,CAACO,QAAS,EAAC;EACxE;EAEA,IACE,CAAAN,aAAA,GAAAD,MAAM,CAACQ,KAAK,cAAAP,aAAA,eAAZA,aAAA,CAAcQ,GAAG,KAAAP,cAAA,GACjBF,MAAM,CAACQ,KAAK,cAAAN,cAAA,eAAZA,cAAA,CAAcQ,GAAG,IACjBV,MAAM,CAACQ,KAAK,CAACC,GAAG,GAAGT,MAAM,CAACQ,KAAK,CAACE,GAAG,EACnC;IACAP,YAAY,IAAK,gEAA+DH,MAAM,CAACQ,KAAK,CAACC,GAAI,UAAST,MAAM,CAACQ,KAAK,CAACE,GAAI,IAAG;EAChI;EAEA,IAAIP,YAAY,KAAK,EAAE,EAAE;IACvBQ,OAAO,CAACC,IAAI,CAAC,oCAAoC,GAAGT,YAAY,CAAC;EACnE;EAEA,OAAOA,YAAY,KAAK,EAAE;AAC5B;;AAEA;AACA,OAAO,SAASU,UAAUA,CAAC;EACzBJ,GAAG;EACHC,GAAG;EACHI,IAAI;EACJC,aAAa,GAAG;AAMlB,CAAC,EAAE;EACD,SAAS;;EACT,MAAMC,QAAQ,GAAG,OAAO;EACxB,IAAIC,GAAG,GAAGF,aAAa;EACvB,IAAIG,OAAO,GAAG,CAACR,GAAG,GAAGD,GAAG,IAAI,CAAC;EAC7B,OAAOU,IAAI,CAACC,GAAG,CAACN,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGF,QAAQ,IAAIC,GAAG,GAAG,CAAC,EAAE;IACpDA,GAAG,IAAI,CAAC;IAER,IAAIH,IAAI,CAACI,OAAO,CAAC,GAAG,CAAC,EAAE;MACrBT,GAAG,GAAGS,OAAO;IACf,CAAC,MAAM;MACLR,GAAG,GAAGQ,OAAO;IACf;IACAA,OAAO,GAAG,CAACT,GAAG,GAAGC,GAAG,IAAI,CAAC;EAC3B;EACA,OAAOQ,OAAO;AAChB;AAEA,OAAO,SAASG,mBAAmBA,CACjCC,IAAI,GAAG,CAAC,EACRtB,MAA+C,EAK/C;EACA,SAAS;;EAET,IAAIA,MAAM,CAACuB,aAAa,EAAE;IACxB,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EAC1C;EAEA,IAAI1B,MAAM,CAAC2B,WAAW,EAAE;IACtB,MAAM;MAAEC,SAAS,EAAEC,CAAC;MAAEC,YAAY,EAAEN;IAAK,CAAC,GAAGxB,MAAM;;IAEnD;AACJ;AACA;IACI,MAAMyB,MAAM,GAAGN,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGP,IAAI,CAAC;IAClC,MAAMI,MAAM,GAAGD,MAAM,GAAGN,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGP,IAAI,IAAI,CAAC,CAAC;IAEhD,OAAO;MAAEA,IAAI;MAAEC,MAAM;MAAEC;IAAO,CAAC;EACjC,CAAC,MAAM;IACL,MAAM;MAAEM,OAAO,EAAEC,CAAC;MAAEX,IAAI,EAAEY,CAAC;MAAEN,SAAS,EAAEC;IAAE,CAAC,GAAG7B,MAAM;IAEpD,MAAMwB,IAAI,GAAGS,CAAC,IAAI,CAAC,GAAGd,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,MAAMT,MAAM,GAAGN,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGK,CAAC,CAAC,CAAC,CAAC;IACjC,MAAMR,MAAM,GAAGD,MAAM,GAAGN,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGP,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;;IAElD,OAAO;MAAEA,IAAI;MAAEC,MAAM;MAAEC;IAAO,CAAC;EACjC;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASS,sBAAsBA,CACpCC,SAA0B,EAC1B5B,KAAqC,EAC7B;EACR,SAAS;;EACT,MAAM;IAAEgB,IAAI;IAAEa,OAAO;IAAEC;EAAW,CAAC,GAAGF,SAAS;EAC/C,MAAMG,UAAU,GAAGC,MAAM,CAACH,OAAO,CAAC;EAElC,IAAIE,UAAU,KAAKD,UAAU,EAAE;IAC7B,OAAOd,IAAI;EACb;EAEA,MAAM,CAACiB,UAAU,EAAEC,WAAW,CAAC,GAC7BH,UAAU,GAAGD,UAAU,GAAG,CAAC,GACvB,CAAC9B,KAAK,CAACC,GAAG,EAAED,KAAK,CAACE,GAAG,CAAC,GACtB,CAACF,KAAK,CAACE,GAAG,EAAEF,KAAK,CAACC,GAAG,CAAC;;EAE5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAMkC,iBAAiB,GACrBD,WAAW,KAAKE,SAAS,GACrBzB,IAAI,CAACC,GAAG,CAAC,CAACsB,WAAW,GAAGH,UAAU,KAAKA,UAAU,GAAGD,UAAU,CAAC,CAAC,GAChEM,SAAS;EAEf,MAAMC,iBAAiB,GACrBJ,UAAU,KAAKG,SAAS,GACpBzB,IAAI,CAACC,GAAG,CAAC,CAACqB,UAAU,GAAGF,UAAU,KAAKA,UAAU,GAAGD,UAAU,CAAC,CAAC,GAC/DM,SAAS;;EAEf;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,MAAME,QAAQ,GACZH,iBAAiB,KAAKC,SAAS,GAC3BzB,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC4B,GAAG,CAACJ,iBAAiB,CAAC,GAAGxB,IAAI,CAAC6B,EAAE,CAAC,GAC/CJ,SAAS;EAEf,MAAMK,QAAQ,GACZJ,iBAAiB,KAAKD,SAAS,GAC3BzB,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC4B,GAAG,CAACF,iBAAiB,CAAC,IAAI,CAAC,GAAG1B,IAAI,CAAC6B,EAAE,CAAC,CAAC,GACrDJ,SAAS;EAEf,MAAMM,mBAAmB,GAAG,CAACJ,QAAQ,EAAEG,QAAQ,CAAC,CAACE,MAAM,CACpDC,CAAqB,IAAkBA,CAAC,KAAKR,SAChD,CAAC;EACD;EACA;EACA,OAAOzB,IAAI,CAACT,GAAG,CAAC,GAAGwC,mBAAmB,EAAE1B,IAAI,CAAC;AAC/C;;AAEA;AACA,OAAO,SAAS6B,+BAA+BA,CAC7CC,EAAU,EACVtD,MAA+C,EAC/CuD,EAAU,EACV;EACA,SAAS;;EACT,IAAIvD,MAAM,CAACuB,aAAa,EAAE;IACxB,OAAO,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAIE,MAAM;IACJK,SAAS,EAAEC,CAAC;IACZC,YAAY,EAAEN,IAAI;IAClBgC,kBAAkB,EAAEC,SAAS;IAC7BlD;EACF,CAAC,GAAGP,MAAM;EAEV,MAAM0D,eAAe,GAAIpC,IAAY,IAAK;IACxC,SAAS;;IACT,MAAMqC,SAAS,GACb,CAACrC,IAAI,GAAGiC,EAAE,GAAGA,EAAE,GAAG1B,CAAC,GAAGyB,EAAE,GAAGA,EAAE,KAAKnC,IAAI,CAACyC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGpC,IAAI,CAAC,GAAGK,CAAC,CAAC;IACjE,MAAMI,CAAC,GAAGT,IAAI,GAAG,CAAC,GAAGL,IAAI,CAACY,IAAI,CAACF,CAAC,GAAGP,IAAI,CAAC;IACxC,OACE,IAAI,IAAK,CAAC,CAAC,GAAGA,IAAI,GAAIW,CAAC,CAAC,GAAGd,IAAI,CAAC4B,GAAG,CAAEU,SAAS,GAAG,IAAI,GAAIE,SAAS,CAAC,GACnEpD,QAAQ;EAEZ,CAAC;;EAED;EACA,OAAOM,UAAU,CAAC;IAAEJ,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,GAAG;IAAEI,IAAI,EAAE4C;EAAgB,CAAC,CAAC;AAChE;AAEA,OAAO,SAASG,kCAAkCA,CAChDzB,SAA+B,EAC/B0B,mBAKC,EACuC;EACxC,SAAS;;EACT,MAAM;IAAEzB;EAAQ,CAAC,GAAGD,SAAS;EAE7B,MAAM;IAAEmB,EAAE;IAAED,EAAE;IAAE7B,MAAM;IAAEsC;EAAE,CAAC,GAAGD,mBAAmB;EAEjD,MAAME,wBAAwB,GAAG7C,IAAI,CAACyC,GAAG,CAAC,CAACnC,MAAM,GAAGsC,CAAC,CAAC;EACtD,MAAME,wBAAwB,GAC5B5B,OAAO,GAAG2B,wBAAwB,IAAIV,EAAE,GAAG,CAACC,EAAE,GAAG9B,MAAM,GAAG6B,EAAE,IAAIS,CAAC,CAAC;EAEpE,MAAMG,wBAAwB,GAC5BF,wBAAwB,IACvBT,EAAE,IAAIQ,CAAC,GAAGtC,MAAM,GAAG,CAAC,CAAC,GAAGsC,CAAC,GAAGT,EAAE,GAAG7B,MAAM,GAAGA,MAAM,CAAC;EAEpD,OAAO;IACL0C,QAAQ,EAAEF,wBAAwB;IAClCG,QAAQ,EAAEF;EACZ,CAAC;AACH;AAEA,OAAO,SAASG,6BAA6BA,CAC3CjC,SAA+B,EAC/B0B,mBAOC,EACuC;EACxC,SAAS;;EACT,MAAM;IAAEzB,OAAO;IAAEnB,OAAO;IAAEkD;EAAS,CAAC,GAAGhC,SAAS;EAEhD,MAAM;IAAEZ,IAAI;IAAEuC,CAAC;IAAEtC,MAAM;IAAEC;EAAO,CAAC,GAAGoC,mBAAmB;EAEvD,MAAMP,EAAE,GAAG,CAACa,QAAQ;EACpB,MAAMd,EAAE,GAAGjB,OAAO,GAAGnB,OAAO;EAE5B,MAAMoD,IAAI,GAAGnD,IAAI,CAACoD,GAAG,CAAC7C,MAAM,GAAGqC,CAAC,CAAC;EACjC,MAAMS,IAAI,GAAGrD,IAAI,CAACsD,GAAG,CAAC/C,MAAM,GAAGqC,CAAC,CAAC;;EAEjC;EACA,MAAMW,mBAAmB,GAAGvD,IAAI,CAACyC,GAAG,CAAC,CAACpC,IAAI,GAAGC,MAAM,GAAGsC,CAAC,CAAC;EACxD,MAAMY,gBAAgB,GACpBD,mBAAmB,IAClBJ,IAAI,IAAI,CAACf,EAAE,GAAG/B,IAAI,GAAGC,MAAM,GAAG6B,EAAE,IAAI5B,MAAM,CAAC,GAAG4B,EAAE,GAAGkB,IAAI,CAAC;EAE3D,MAAMI,mBAAmB,GAAGvC,OAAO,GAAGsC,gBAAgB;EACtD;EACA,MAAME,mBAAmB,GACvBrD,IAAI,GAAGC,MAAM,GAAGkD,gBAAgB,GAChCD,mBAAmB,IAChBF,IAAI,IAAIjB,EAAE,GAAG/B,IAAI,GAAGC,MAAM,GAAG6B,EAAE,CAAC,GAAG5B,MAAM,GAAG4B,EAAE,GAAGgB,IAAI,CAAC;EAE3D,OAAO;IAAEH,QAAQ,EAAES,mBAAmB;IAAER,QAAQ,EAAES;EAAoB,CAAC;AACzE;AAEA,OAAO,SAASC,iCAAiCA,CAC/C1C,SAA+B,EAC/BpC,MAA2B,EAK3B;EACA,SAAS;;EACT,MAAM;IAAEqC,OAAO;IAAE+B,QAAQ;IAAE9B,UAAU;IAAEpB;EAAQ,CAAC,GAAGkB,SAAS;EAE5D,MAAM2C,cAAc,GAAG/E,MAAM,CAACgF,iBAAiB,GAC1C9D,OAAO,GAAGmB,OAAO,IAAIC,UAAU,GAAGD,OAAO,IACzCnB,OAAO,GAAGmB,OAAO,IAAIC,UAAU,GAAGD,OAAQ,GAC3C,KAAK;EAET,MAAM4C,UAAU,GAAG9D,IAAI,CAACC,GAAG,CAACgD,QAAQ,CAAC,GAAGpE,MAAM,CAACwD,kBAAkB;EACjE,MAAM0B,cAAc,GAClB/D,IAAI,CAACC,GAAG,CAACiB,OAAO,GAAGnB,OAAO,CAAC,GAAGlB,MAAM,CAACmF,yBAAyB;EAEhE,OAAO;IAAEJ,cAAc;IAAEE,UAAU;IAAEC;EAAe,CAAC;AACvD", "ignoreList": []}