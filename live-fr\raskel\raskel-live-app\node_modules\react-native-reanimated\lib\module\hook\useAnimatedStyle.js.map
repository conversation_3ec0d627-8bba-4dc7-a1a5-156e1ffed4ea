{"version": 3, "names": ["useEffect", "useRef", "makeShareable", "startMapper", "stopMapper", "updateProps", "updatePropsJestWrapper", "initialUpdaterRun", "useSharedValue", "buildWorkletsHash", "isAnimated", "shallowEqual", "validateAnimatedStyles", "makeViewDescriptorsSet", "isJest", "shouldBeUseWeb", "isWorkletFunction", "SHOULD_BE_USE_WEB", "prepareAnimation", "frameTimestamp", "animatedProp", "lastAnimation", "lastValue", "Array", "isArray", "for<PERSON>ach", "prop", "index", "onFrame", "animation", "value", "current", "undefined", "callStart", "timestamp", "onStart", "Object", "keys", "key", "runAnimations", "result", "animationsActive", "allFinished", "entry", "finished", "callback", "k", "styleUpdater", "viewDescriptors", "updater", "state", "isAnimatedProps", "animations", "newValues", "oldValues", "last", "nonAnimatedNewValues", "hasAnimations", "hasNonAnimatedValues", "global", "__frameTimestamp", "_getAnimationTimestamp", "frame", "isAnimationCancelled", "isAnimationRunning", "updates", "propName", "requestAnimationFrame", "jestStyleUpdater", "animatedStyle", "adapters", "length", "checkSharedValueUsage", "current<PERSON><PERSON>", "element", "Error", "useAnimatedStyle", "dependencies", "animatedUpdaterData", "inputs", "values", "__closure", "_dependencies", "__DEV__", "adaptersArray", "adaptersHash", "areAnimationsActive", "jestAnimatedStyle", "__workletHash", "push", "initialStyle", "initial", "remoteState", "shareableViewDescriptors", "fun", "updaterFn", "adapter", "mapperId", "animatedStyleHandle"], "sources": ["useAnimatedStyle.ts"], "sourcesContent": ["'use strict';\nimport type { MutableRefObject } from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport { makeShareable, startMapper, stopMapper } from '../core';\nimport updateProps, { updatePropsJestWrapper } from '../UpdateProps';\nimport { initialUpdaterRun } from '../animation';\nimport { useSharedValue } from './useSharedValue';\nimport {\n  buildWorkletsHash,\n  isAnimated,\n  shallowEqual,\n  validateAnimatedStyles,\n} from './utils';\nimport type {\n  AnimatedStyleHandle,\n  DefaultStyle,\n  DependencyList,\n  Descriptor,\n  JestAnimatedStyleHandle,\n} from './commonTypes';\nimport type { ViewDescriptorsSet } from '../ViewDescriptorsSet';\nimport { makeViewDescriptorsSet } from '../ViewDescriptorsSet';\nimport { isJest, shouldBeUseWeb } from '../PlatformChecker';\nimport type {\n  AnimationObject,\n  Timestamp,\n  NestedObjectValues,\n  SharedValue,\n  StyleProps,\n  WorkletFunction,\n  AnimatedPropsAdapterFunction,\n  AnimatedPropsAdapterWorklet,\n  AnimatedStyle,\n} from '../commonTypes';\nimport { isWorkletFunction } from '../commonTypes';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\ninterface AnimatedState {\n  last: AnimatedStyle<any>;\n  animations: AnimatedStyle<any>;\n  isAnimationRunning: boolean;\n  isAnimationCancelled: boolean;\n}\n\ninterface AnimatedUpdaterData {\n  initial: {\n    value: AnimatedStyle<any>;\n    updater: () => AnimatedStyle<any>;\n  };\n  remoteState: AnimatedState;\n  viewDescriptors: ViewDescriptorsSet;\n}\n\nfunction prepareAnimation(\n  frameTimestamp: number,\n  animatedProp: AnimatedStyle<any>,\n  lastAnimation: AnimatedStyle<any>,\n  lastValue: AnimatedStyle<any>\n): void {\n  'worklet';\n  if (Array.isArray(animatedProp)) {\n    animatedProp.forEach((prop, index) => {\n      prepareAnimation(\n        frameTimestamp,\n        prop,\n        lastAnimation && lastAnimation[index],\n        lastValue && lastValue[index]\n      );\n    });\n    // return animatedProp;\n  }\n  if (typeof animatedProp === 'object' && animatedProp.onFrame) {\n    const animation = animatedProp;\n\n    let value = animation.current;\n    if (lastValue !== undefined && lastValue !== null) {\n      if (typeof lastValue === 'object') {\n        if (lastValue.value !== undefined) {\n          // previously it was a shared value\n          value = lastValue.value;\n        } else if (lastValue.onFrame !== undefined) {\n          if (lastAnimation?.current !== undefined) {\n            // it was an animation before, copy its state\n            value = lastAnimation.current;\n          } else if (lastValue?.current !== undefined) {\n            // it was initialized\n            value = lastValue.current;\n          }\n        }\n      } else {\n        // previously it was a plain value, just set it as starting point\n        value = lastValue;\n      }\n    }\n\n    animation.callStart = (timestamp: Timestamp) => {\n      animation.onStart(animation, value, timestamp, lastAnimation);\n    };\n    animation.callStart(frameTimestamp);\n    animation.callStart = null;\n  } else if (typeof animatedProp === 'object') {\n    // it is an object\n    Object.keys(animatedProp).forEach((key) =>\n      prepareAnimation(\n        frameTimestamp,\n        animatedProp[key],\n        lastAnimation && lastAnimation[key],\n        lastValue && lastValue[key]\n      )\n    );\n  }\n}\n\nfunction runAnimations(\n  animation: AnimatedStyle<any>,\n  timestamp: Timestamp,\n  key: number | string,\n  result: AnimatedStyle<any>,\n  animationsActive: SharedValue<boolean>\n): boolean {\n  'worklet';\n  if (!animationsActive.value) {\n    return true;\n  }\n  if (Array.isArray(animation)) {\n    result[key] = [];\n    let allFinished = true;\n    animation.forEach((entry, index) => {\n      if (\n        !runAnimations(entry, timestamp, index, result[key], animationsActive)\n      ) {\n        allFinished = false;\n      }\n    });\n    return allFinished;\n  } else if (typeof animation === 'object' && animation.onFrame) {\n    let finished = true;\n    if (!animation.finished) {\n      if (animation.callStart) {\n        animation.callStart(timestamp);\n        animation.callStart = null;\n      }\n      finished = animation.onFrame(animation, timestamp);\n      animation.timestamp = timestamp;\n      if (finished) {\n        animation.finished = true;\n        animation.callback && animation.callback(true /* finished */);\n      }\n    }\n    result[key] = animation.current;\n    return finished;\n  } else if (typeof animation === 'object') {\n    result[key] = {};\n    let allFinished = true;\n    Object.keys(animation).forEach((k) => {\n      if (\n        !runAnimations(\n          animation[k],\n          timestamp,\n          k,\n          result[key],\n          animationsActive\n        )\n      ) {\n        allFinished = false;\n      }\n    });\n    return allFinished;\n  } else {\n    result[key] = animation;\n    return true;\n  }\n}\n\nfunction styleUpdater(\n  viewDescriptors: SharedValue<Descriptor[]>,\n  updater: WorkletFunction<[], AnimatedStyle<any>> | (() => AnimatedStyle<any>),\n  state: AnimatedState,\n  animationsActive: SharedValue<boolean>,\n  isAnimatedProps = false\n): void {\n  'worklet';\n  const animations = state.animations ?? {};\n  const newValues = updater() ?? {};\n  const oldValues = state.last;\n  const nonAnimatedNewValues: StyleProps = {};\n\n  let hasAnimations = false;\n  let frameTimestamp: number | undefined;\n  let hasNonAnimatedValues = false;\n  for (const key in newValues) {\n    const value = newValues[key];\n    if (isAnimated(value)) {\n      frameTimestamp =\n        global.__frameTimestamp || global._getAnimationTimestamp();\n      prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n      animations[key] = value;\n      hasAnimations = true;\n    } else {\n      hasNonAnimatedValues = true;\n      nonAnimatedNewValues[key] = value;\n      delete animations[key];\n    }\n  }\n\n  if (hasAnimations) {\n    const frame = (timestamp: Timestamp) => {\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      const { animations, last, isAnimationCancelled } = state;\n      if (isAnimationCancelled) {\n        state.isAnimationRunning = false;\n        return;\n      }\n\n      const updates: AnimatedStyle<any> = {};\n      let allFinished = true;\n      for (const propName in animations) {\n        const finished = runAnimations(\n          animations[propName],\n          timestamp,\n          propName,\n          updates,\n          animationsActive\n        );\n        if (finished) {\n          last[propName] = updates[propName];\n          delete animations[propName];\n        } else {\n          allFinished = false;\n        }\n      }\n\n      if (updates) {\n        updateProps(viewDescriptors, updates);\n      }\n\n      if (!allFinished) {\n        requestAnimationFrame(frame);\n      } else {\n        state.isAnimationRunning = false;\n      }\n    };\n\n    state.animations = animations;\n    if (!state.isAnimationRunning) {\n      state.isAnimationCancelled = false;\n      state.isAnimationRunning = true;\n      frame(frameTimestamp!);\n    }\n\n    if (hasNonAnimatedValues) {\n      updateProps(viewDescriptors, nonAnimatedNewValues);\n    }\n  } else {\n    state.isAnimationCancelled = true;\n    state.animations = [];\n\n    if (!shallowEqual(oldValues, newValues)) {\n      updateProps(viewDescriptors, newValues, isAnimatedProps);\n    }\n  }\n  state.last = newValues;\n}\n\nfunction jestStyleUpdater(\n  viewDescriptors: SharedValue<Descriptor[]>,\n  updater: WorkletFunction<[], AnimatedStyle<any>> | (() => AnimatedStyle<any>),\n  state: AnimatedState,\n  animationsActive: SharedValue<boolean>,\n  animatedStyle: MutableRefObject<AnimatedStyle<any>>,\n  adapters: AnimatedPropsAdapterFunction[]\n): void {\n  'worklet';\n  const animations: AnimatedStyle<any> = state.animations ?? {};\n  const newValues = updater() ?? {};\n  const oldValues = state.last;\n\n  // extract animated props\n  let hasAnimations = false;\n  let frameTimestamp: number | undefined;\n  Object.keys(animations).forEach((key) => {\n    const value = newValues[key];\n    if (!isAnimated(value)) {\n      delete animations[key];\n    }\n  });\n  Object.keys(newValues).forEach((key) => {\n    const value = newValues[key];\n    if (isAnimated(value)) {\n      frameTimestamp =\n        global.__frameTimestamp || global._getAnimationTimestamp();\n      prepareAnimation(frameTimestamp, value, animations[key], oldValues[key]);\n      animations[key] = value;\n      hasAnimations = true;\n    }\n  });\n\n  function frame(timestamp: Timestamp) {\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const { animations, last, isAnimationCancelled } = state;\n    if (isAnimationCancelled) {\n      state.isAnimationRunning = false;\n      return;\n    }\n\n    const updates: AnimatedStyle<any> = {};\n    let allFinished = true;\n    Object.keys(animations).forEach((propName) => {\n      const finished = runAnimations(\n        animations[propName],\n        timestamp,\n        propName,\n        updates,\n        animationsActive\n      );\n      if (finished) {\n        last[propName] = updates[propName];\n        delete animations[propName];\n      } else {\n        allFinished = false;\n      }\n    });\n\n    if (Object.keys(updates).length) {\n      updatePropsJestWrapper(viewDescriptors, updates, animatedStyle, adapters);\n    }\n\n    if (!allFinished) {\n      requestAnimationFrame(frame);\n    } else {\n      state.isAnimationRunning = false;\n    }\n  }\n\n  if (hasAnimations) {\n    state.animations = animations;\n    if (!state.isAnimationRunning) {\n      state.isAnimationCancelled = false;\n      state.isAnimationRunning = true;\n      frame(frameTimestamp!);\n    }\n  } else {\n    state.isAnimationCancelled = true;\n    state.animations = [];\n  }\n\n  // calculate diff\n  state.last = newValues;\n\n  if (!shallowEqual(oldValues, newValues)) {\n    updatePropsJestWrapper(viewDescriptors, newValues, animatedStyle, adapters);\n  }\n}\n\n// check for invalid usage of shared values in returned object\nfunction checkSharedValueUsage(\n  prop: NestedObjectValues<AnimationObject>,\n  currentKey?: string\n): void {\n  if (Array.isArray(prop)) {\n    // if it's an array (i.ex. transform) validate all its elements\n    for (const element of prop) {\n      checkSharedValueUsage(element, currentKey);\n    }\n  } else if (\n    typeof prop === 'object' &&\n    prop !== null &&\n    prop.value === undefined\n  ) {\n    // if it's a nested object, run validation for all its props\n    for (const key of Object.keys(prop)) {\n      checkSharedValueUsage(prop[key], key);\n    }\n  } else if (\n    currentKey !== undefined &&\n    typeof prop === 'object' &&\n    prop !== null &&\n    prop.value !== undefined\n  ) {\n    // if shared value is passed insted of its value, throw an error\n    throw new Error(\n      `[Reanimated] Invalid value passed to \\`${currentKey}\\`, maybe you forgot to use \\`.value\\`?`\n    );\n  }\n}\n\n/**\n * Lets you create a styles object, similar to StyleSheet styles, which can be animated using shared values.\n *\n * @param updater - A function returning an object with style properties you want to animate.\n * @param dependencies - An optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.\n * @returns An animated style object which has to be passed to the `style` property of an Animated component you want to animate.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle\n */\n// You cannot pass Shared Values to `useAnimatedStyle` directly.\n// @ts-expect-error This overload is required by our API.\nexport function useAnimatedStyle<Style extends DefaultStyle>(\n  updater: () => Style,\n  dependencies?: DependencyList | null\n): Style;\n\nexport function useAnimatedStyle<Style extends DefaultStyle>(\n  updater:\n    | WorkletFunction<[], Style>\n    | ((() => Style) & Record<string, unknown>),\n  dependencies?: DependencyList | null,\n  adapters?: AnimatedPropsAdapterWorklet | AnimatedPropsAdapterWorklet[] | null,\n  isAnimatedProps = false\n): AnimatedStyleHandle<Style> | JestAnimatedStyleHandle<Style> {\n  const animatedUpdaterData = useRef<AnimatedUpdaterData>();\n  let inputs = Object.values(updater.__closure ?? {});\n  if (SHOULD_BE_USE_WEB) {\n    if (!inputs.length && dependencies?.length) {\n      // let web work without a Babel plugin\n      inputs = dependencies;\n    }\n    if (\n      __DEV__ &&\n      !inputs.length &&\n      !dependencies &&\n      !isWorkletFunction(updater)\n    ) {\n      throw new Error(\n        `[Reanimated] \\`useAnimatedStyle\\` was used without a dependency array or Babel plugin. Please explicitly pass a dependency array, or enable the Babel plugin.\nFor more, see the docs: \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/web-support#web-without-the-babel-plugin\\`.`\n      );\n    }\n  }\n  const adaptersArray = adapters\n    ? Array.isArray(adapters)\n      ? adapters\n      : [adapters]\n    : [];\n  const adaptersHash = adapters ? buildWorkletsHash(adaptersArray) : null;\n  const areAnimationsActive = useSharedValue<boolean>(true);\n  const jestAnimatedStyle = useRef<Style>({} as Style);\n\n  // build dependencies\n  if (!dependencies) {\n    dependencies = [...inputs, updater.__workletHash];\n  } else {\n    dependencies.push(updater.__workletHash);\n  }\n  adaptersHash && dependencies.push(adaptersHash);\n\n  if (!animatedUpdaterData.current) {\n    const initialStyle = initialUpdaterRun(updater);\n    if (__DEV__) {\n      validateAnimatedStyles(initialStyle);\n    }\n    animatedUpdaterData.current = {\n      initial: {\n        value: initialStyle,\n        updater,\n      },\n      remoteState: makeShareable({\n        last: initialStyle,\n        animations: {},\n        isAnimationCancelled: false,\n        isAnimationRunning: false,\n      }),\n      viewDescriptors: makeViewDescriptorsSet(),\n    };\n  }\n\n  const { initial, remoteState, viewDescriptors } = animatedUpdaterData.current;\n  const shareableViewDescriptors = viewDescriptors.shareableViewDescriptors;\n\n  dependencies.push(shareableViewDescriptors);\n\n  useEffect(() => {\n    let fun;\n    let updaterFn = updater;\n    if (adapters) {\n      updaterFn = (() => {\n        'worklet';\n        const newValues = updater();\n        adaptersArray.forEach((adapter) => {\n          adapter(newValues as Record<string, unknown>);\n        });\n        return newValues;\n      }) as WorkletFunction<[], Style>;\n    }\n\n    if (isJest()) {\n      fun = () => {\n        'worklet';\n        jestStyleUpdater(\n          shareableViewDescriptors,\n          updater,\n          remoteState,\n          areAnimationsActive,\n          jestAnimatedStyle,\n          adaptersArray\n        );\n      };\n    } else {\n      fun = () => {\n        'worklet';\n        styleUpdater(\n          shareableViewDescriptors,\n          updaterFn,\n          remoteState,\n          areAnimationsActive,\n          isAnimatedProps\n        );\n      };\n    }\n    const mapperId = startMapper(fun, inputs);\n    return () => {\n      stopMapper(mapperId);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n\n  useEffect(() => {\n    areAnimationsActive.value = true;\n    return () => {\n      areAnimationsActive.value = false;\n    };\n  }, [areAnimationsActive]);\n\n  checkSharedValueUsage(initial.value);\n\n  const animatedStyleHandle = useRef<\n    AnimatedStyleHandle<Style> | JestAnimatedStyleHandle<Style> | null\n  >(null);\n\n  if (!animatedStyleHandle.current) {\n    animatedStyleHandle.current = isJest()\n      ? { viewDescriptors, initial, jestAnimatedStyle }\n      : { viewDescriptors, initial };\n  }\n\n  return animatedStyleHandle.current;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,SAASC,aAAa,EAAEC,WAAW,EAAEC,UAAU,QAAQ,SAAS;AAChE,OAAOC,WAAW,IAAIC,sBAAsB,QAAQ,gBAAgB;AACpE,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SACEC,iBAAiB,EACjBC,UAAU,EACVC,YAAY,EACZC,sBAAsB,QACjB,SAAS;AAShB,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,SAASC,MAAM,EAAEC,cAAc,QAAQ,oBAAoB;AAY3D,SAASC,iBAAiB,QAAQ,gBAAgB;AAElD,MAAMC,iBAAiB,GAAGF,cAAc,CAAC,CAAC;AAkB1C,SAASG,gBAAgBA,CACvBC,cAAsB,EACtBC,YAAgC,EAChCC,aAAiC,EACjCC,SAA6B,EACvB;EACN,SAAS;;EACT,IAAIC,KAAK,CAACC,OAAO,CAACJ,YAAY,CAAC,EAAE;IAC/BA,YAAY,CAACK,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACpCT,gBAAgB,CACdC,cAAc,EACdO,IAAI,EACJL,aAAa,IAAIA,aAAa,CAACM,KAAK,CAAC,EACrCL,SAAS,IAAIA,SAAS,CAACK,KAAK,CAC9B,CAAC;IACH,CAAC,CAAC;IACF;EACF;EACA,IAAI,OAAOP,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACQ,OAAO,EAAE;IAC5D,MAAMC,SAAS,GAAGT,YAAY;IAE9B,IAAIU,KAAK,GAAGD,SAAS,CAACE,OAAO;IAC7B,IAAIT,SAAS,KAAKU,SAAS,IAAIV,SAAS,KAAK,IAAI,EAAE;MACjD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAIA,SAAS,CAACQ,KAAK,KAAKE,SAAS,EAAE;UACjC;UACAF,KAAK,GAAGR,SAAS,CAACQ,KAAK;QACzB,CAAC,MAAM,IAAIR,SAAS,CAACM,OAAO,KAAKI,SAAS,EAAE;UAC1C,IAAI,CAAAX,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEU,OAAO,MAAKC,SAAS,EAAE;YACxC;YACAF,KAAK,GAAGT,aAAa,CAACU,OAAO;UAC/B,CAAC,MAAM,IAAI,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,OAAO,MAAKC,SAAS,EAAE;YAC3C;YACAF,KAAK,GAAGR,SAAS,CAACS,OAAO;UAC3B;QACF;MACF,CAAC,MAAM;QACL;QACAD,KAAK,GAAGR,SAAS;MACnB;IACF;IAEAO,SAAS,CAACI,SAAS,GAAIC,SAAoB,IAAK;MAC9CL,SAAS,CAACM,OAAO,CAACN,SAAS,EAAEC,KAAK,EAAEI,SAAS,EAAEb,aAAa,CAAC;IAC/D,CAAC;IACDQ,SAAS,CAACI,SAAS,CAACd,cAAc,CAAC;IACnCU,SAAS,CAACI,SAAS,GAAG,IAAI;EAC5B,CAAC,MAAM,IAAI,OAAOb,YAAY,KAAK,QAAQ,EAAE;IAC3C;IACAgB,MAAM,CAACC,IAAI,CAACjB,YAAY,CAAC,CAACK,OAAO,CAAEa,GAAG,IACpCpB,gBAAgB,CACdC,cAAc,EACdC,YAAY,CAACkB,GAAG,CAAC,EACjBjB,aAAa,IAAIA,aAAa,CAACiB,GAAG,CAAC,EACnChB,SAAS,IAAIA,SAAS,CAACgB,GAAG,CAC5B,CACF,CAAC;EACH;AACF;AAEA,SAASC,aAAaA,CACpBV,SAA6B,EAC7BK,SAAoB,EACpBI,GAAoB,EACpBE,MAA0B,EAC1BC,gBAAsC,EAC7B;EACT,SAAS;;EACT,IAAI,CAACA,gBAAgB,CAACX,KAAK,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAIP,KAAK,CAACC,OAAO,CAACK,SAAS,CAAC,EAAE;IAC5BW,MAAM,CAACF,GAAG,CAAC,GAAG,EAAE;IAChB,IAAII,WAAW,GAAG,IAAI;IACtBb,SAAS,CAACJ,OAAO,CAAC,CAACkB,KAAK,EAAEhB,KAAK,KAAK;MAClC,IACE,CAACY,aAAa,CAACI,KAAK,EAAET,SAAS,EAAEP,KAAK,EAAEa,MAAM,CAACF,GAAG,CAAC,EAAEG,gBAAgB,CAAC,EACtE;QACAC,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,CAAC;IACF,OAAOA,WAAW;EACpB,CAAC,MAAM,IAAI,OAAOb,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACD,OAAO,EAAE;IAC7D,IAAIgB,QAAQ,GAAG,IAAI;IACnB,IAAI,CAACf,SAAS,CAACe,QAAQ,EAAE;MACvB,IAAIf,SAAS,CAACI,SAAS,EAAE;QACvBJ,SAAS,CAACI,SAAS,CAACC,SAAS,CAAC;QAC9BL,SAAS,CAACI,SAAS,GAAG,IAAI;MAC5B;MACAW,QAAQ,GAAGf,SAAS,CAACD,OAAO,CAACC,SAAS,EAAEK,SAAS,CAAC;MAClDL,SAAS,CAACK,SAAS,GAAGA,SAAS;MAC/B,IAAIU,QAAQ,EAAE;QACZf,SAAS,CAACe,QAAQ,GAAG,IAAI;QACzBf,SAAS,CAACgB,QAAQ,IAAIhB,SAAS,CAACgB,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;MAC/D;IACF;IACAL,MAAM,CAACF,GAAG,CAAC,GAAGT,SAAS,CAACE,OAAO;IAC/B,OAAOa,QAAQ;EACjB,CAAC,MAAM,IAAI,OAAOf,SAAS,KAAK,QAAQ,EAAE;IACxCW,MAAM,CAACF,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,IAAII,WAAW,GAAG,IAAI;IACtBN,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACJ,OAAO,CAAEqB,CAAC,IAAK;MACpC,IACE,CAACP,aAAa,CACZV,SAAS,CAACiB,CAAC,CAAC,EACZZ,SAAS,EACTY,CAAC,EACDN,MAAM,CAACF,GAAG,CAAC,EACXG,gBACF,CAAC,EACD;QACAC,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,CAAC;IACF,OAAOA,WAAW;EACpB,CAAC,MAAM;IACLF,MAAM,CAACF,GAAG,CAAC,GAAGT,SAAS;IACvB,OAAO,IAAI;EACb;AACF;AAEA,SAASkB,YAAYA,CACnBC,eAA0C,EAC1CC,OAA6E,EAC7EC,KAAoB,EACpBT,gBAAsC,EACtCU,eAAe,GAAG,KAAK,EACjB;EACN,SAAS;;EACT,MAAMC,UAAU,GAAGF,KAAK,CAACE,UAAU,IAAI,CAAC,CAAC;EACzC,MAAMC,SAAS,GAAGJ,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMK,SAAS,GAAGJ,KAAK,CAACK,IAAI;EAC5B,MAAMC,oBAAgC,GAAG,CAAC,CAAC;EAE3C,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAItC,cAAkC;EACtC,IAAIuC,oBAAoB,GAAG,KAAK;EAChC,KAAK,MAAMpB,GAAG,IAAIe,SAAS,EAAE;IAC3B,MAAMvB,KAAK,GAAGuB,SAAS,CAACf,GAAG,CAAC;IAC5B,IAAI5B,UAAU,CAACoB,KAAK,CAAC,EAAE;MACrBX,cAAc,GACZwC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;MAC5D3C,gBAAgB,CAACC,cAAc,EAAEW,KAAK,EAAEsB,UAAU,CAACd,GAAG,CAAC,EAAEgB,SAAS,CAAChB,GAAG,CAAC,CAAC;MACxEc,UAAU,CAACd,GAAG,CAAC,GAAGR,KAAK;MACvB2B,aAAa,GAAG,IAAI;IACtB,CAAC,MAAM;MACLC,oBAAoB,GAAG,IAAI;MAC3BF,oBAAoB,CAAClB,GAAG,CAAC,GAAGR,KAAK;MACjC,OAAOsB,UAAU,CAACd,GAAG,CAAC;IACxB;EACF;EAEA,IAAImB,aAAa,EAAE;IACjB,MAAMK,KAAK,GAAI5B,SAAoB,IAAK;MACtC;MACA,MAAM;QAAEkB,UAAU;QAAEG,IAAI;QAAEQ;MAAqB,CAAC,GAAGb,KAAK;MACxD,IAAIa,oBAAoB,EAAE;QACxBb,KAAK,CAACc,kBAAkB,GAAG,KAAK;QAChC;MACF;MAEA,MAAMC,OAA2B,GAAG,CAAC,CAAC;MACtC,IAAIvB,WAAW,GAAG,IAAI;MACtB,KAAK,MAAMwB,QAAQ,IAAId,UAAU,EAAE;QACjC,MAAMR,QAAQ,GAAGL,aAAa,CAC5Ba,UAAU,CAACc,QAAQ,CAAC,EACpBhC,SAAS,EACTgC,QAAQ,EACRD,OAAO,EACPxB,gBACF,CAAC;QACD,IAAIG,QAAQ,EAAE;UACZW,IAAI,CAACW,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC;UAClC,OAAOd,UAAU,CAACc,QAAQ,CAAC;QAC7B,CAAC,MAAM;UACLxB,WAAW,GAAG,KAAK;QACrB;MACF;MAEA,IAAIuB,OAAO,EAAE;QACX5D,WAAW,CAAC2C,eAAe,EAAEiB,OAAO,CAAC;MACvC;MAEA,IAAI,CAACvB,WAAW,EAAE;QAChByB,qBAAqB,CAACL,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLZ,KAAK,CAACc,kBAAkB,GAAG,KAAK;MAClC;IACF,CAAC;IAEDd,KAAK,CAACE,UAAU,GAAGA,UAAU;IAC7B,IAAI,CAACF,KAAK,CAACc,kBAAkB,EAAE;MAC7Bd,KAAK,CAACa,oBAAoB,GAAG,KAAK;MAClCb,KAAK,CAACc,kBAAkB,GAAG,IAAI;MAC/BF,KAAK,CAAC3C,cAAe,CAAC;IACxB;IAEA,IAAIuC,oBAAoB,EAAE;MACxBrD,WAAW,CAAC2C,eAAe,EAAEQ,oBAAoB,CAAC;IACpD;EACF,CAAC,MAAM;IACLN,KAAK,CAACa,oBAAoB,GAAG,IAAI;IACjCb,KAAK,CAACE,UAAU,GAAG,EAAE;IAErB,IAAI,CAACzC,YAAY,CAAC2C,SAAS,EAAED,SAAS,CAAC,EAAE;MACvChD,WAAW,CAAC2C,eAAe,EAAEK,SAAS,EAAEF,eAAe,CAAC;IAC1D;EACF;EACAD,KAAK,CAACK,IAAI,GAAGF,SAAS;AACxB;AAEA,SAASe,gBAAgBA,CACvBpB,eAA0C,EAC1CC,OAA6E,EAC7EC,KAAoB,EACpBT,gBAAsC,EACtC4B,aAAmD,EACnDC,QAAwC,EAClC;EACN,SAAS;;EACT,MAAMlB,UAA8B,GAAGF,KAAK,CAACE,UAAU,IAAI,CAAC,CAAC;EAC7D,MAAMC,SAAS,GAAGJ,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMK,SAAS,GAAGJ,KAAK,CAACK,IAAI;;EAE5B;EACA,IAAIE,aAAa,GAAG,KAAK;EACzB,IAAItC,cAAkC;EACtCiB,MAAM,CAACC,IAAI,CAACe,UAAU,CAAC,CAAC3B,OAAO,CAAEa,GAAG,IAAK;IACvC,MAAMR,KAAK,GAAGuB,SAAS,CAACf,GAAG,CAAC;IAC5B,IAAI,CAAC5B,UAAU,CAACoB,KAAK,CAAC,EAAE;MACtB,OAAOsB,UAAU,CAACd,GAAG,CAAC;IACxB;EACF,CAAC,CAAC;EACFF,MAAM,CAACC,IAAI,CAACgB,SAAS,CAAC,CAAC5B,OAAO,CAAEa,GAAG,IAAK;IACtC,MAAMR,KAAK,GAAGuB,SAAS,CAACf,GAAG,CAAC;IAC5B,IAAI5B,UAAU,CAACoB,KAAK,CAAC,EAAE;MACrBX,cAAc,GACZwC,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC;MAC5D3C,gBAAgB,CAACC,cAAc,EAAEW,KAAK,EAAEsB,UAAU,CAACd,GAAG,CAAC,EAAEgB,SAAS,CAAChB,GAAG,CAAC,CAAC;MACxEc,UAAU,CAACd,GAAG,CAAC,GAAGR,KAAK;MACvB2B,aAAa,GAAG,IAAI;IACtB;EACF,CAAC,CAAC;EAEF,SAASK,KAAKA,CAAC5B,SAAoB,EAAE;IACnC;IACA,MAAM;MAAEkB,UAAU;MAAEG,IAAI;MAAEQ;IAAqB,CAAC,GAAGb,KAAK;IACxD,IAAIa,oBAAoB,EAAE;MACxBb,KAAK,CAACc,kBAAkB,GAAG,KAAK;MAChC;IACF;IAEA,MAAMC,OAA2B,GAAG,CAAC,CAAC;IACtC,IAAIvB,WAAW,GAAG,IAAI;IACtBN,MAAM,CAACC,IAAI,CAACe,UAAU,CAAC,CAAC3B,OAAO,CAAEyC,QAAQ,IAAK;MAC5C,MAAMtB,QAAQ,GAAGL,aAAa,CAC5Ba,UAAU,CAACc,QAAQ,CAAC,EACpBhC,SAAS,EACTgC,QAAQ,EACRD,OAAO,EACPxB,gBACF,CAAC;MACD,IAAIG,QAAQ,EAAE;QACZW,IAAI,CAACW,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC;QAClC,OAAOd,UAAU,CAACc,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACLxB,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,CAAC;IAEF,IAAIN,MAAM,CAACC,IAAI,CAAC4B,OAAO,CAAC,CAACM,MAAM,EAAE;MAC/BjE,sBAAsB,CAAC0C,eAAe,EAAEiB,OAAO,EAAEI,aAAa,EAAEC,QAAQ,CAAC;IAC3E;IAEA,IAAI,CAAC5B,WAAW,EAAE;MAChByB,qBAAqB,CAACL,KAAK,CAAC;IAC9B,CAAC,MAAM;MACLZ,KAAK,CAACc,kBAAkB,GAAG,KAAK;IAClC;EACF;EAEA,IAAIP,aAAa,EAAE;IACjBP,KAAK,CAACE,UAAU,GAAGA,UAAU;IAC7B,IAAI,CAACF,KAAK,CAACc,kBAAkB,EAAE;MAC7Bd,KAAK,CAACa,oBAAoB,GAAG,KAAK;MAClCb,KAAK,CAACc,kBAAkB,GAAG,IAAI;MAC/BF,KAAK,CAAC3C,cAAe,CAAC;IACxB;EACF,CAAC,MAAM;IACL+B,KAAK,CAACa,oBAAoB,GAAG,IAAI;IACjCb,KAAK,CAACE,UAAU,GAAG,EAAE;EACvB;;EAEA;EACAF,KAAK,CAACK,IAAI,GAAGF,SAAS;EAEtB,IAAI,CAAC1C,YAAY,CAAC2C,SAAS,EAAED,SAAS,CAAC,EAAE;IACvC/C,sBAAsB,CAAC0C,eAAe,EAAEK,SAAS,EAAEgB,aAAa,EAAEC,QAAQ,CAAC;EAC7E;AACF;;AAEA;AACA,SAASE,qBAAqBA,CAC5B9C,IAAyC,EACzC+C,UAAmB,EACb;EACN,IAAIlD,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,EAAE;IACvB;IACA,KAAK,MAAMgD,OAAO,IAAIhD,IAAI,EAAE;MAC1B8C,qBAAqB,CAACE,OAAO,EAAED,UAAU,CAAC;IAC5C;EACF,CAAC,MAAM,IACL,OAAO/C,IAAI,KAAK,QAAQ,IACxBA,IAAI,KAAK,IAAI,IACbA,IAAI,CAACI,KAAK,KAAKE,SAAS,EACxB;IACA;IACA,KAAK,MAAMM,GAAG,IAAIF,MAAM,CAACC,IAAI,CAACX,IAAI,CAAC,EAAE;MACnC8C,qBAAqB,CAAC9C,IAAI,CAACY,GAAG,CAAC,EAAEA,GAAG,CAAC;IACvC;EACF,CAAC,MAAM,IACLmC,UAAU,KAAKzC,SAAS,IACxB,OAAON,IAAI,KAAK,QAAQ,IACxBA,IAAI,KAAK,IAAI,IACbA,IAAI,CAACI,KAAK,KAAKE,SAAS,EACxB;IACA;IACA,MAAM,IAAI2C,KAAK,CACZ,0CAAyCF,UAAW,yCACvD,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASG,gBAAgBA,CAC9B3B,OAE6C,EAC7C4B,YAAoC,EACpCP,QAA6E,EAC7EnB,eAAe,GAAG,KAAK,EACsC;EAC7D,MAAM2B,mBAAmB,GAAG7E,MAAM,CAAsB,CAAC;EACzD,IAAI8E,MAAM,GAAG3C,MAAM,CAAC4C,MAAM,CAAC/B,OAAO,CAACgC,SAAS,IAAI,CAAC,CAAC,CAAC;EACnD,IAAIhE,iBAAiB,EAAE;IAAA,IAAAiE,aAAA;IACrB,IAAI,CAACH,MAAM,CAACR,MAAM,KAAAW,aAAA,GAAIL,YAAY,cAAAK,aAAA,eAAZA,aAAA,CAAcX,MAAM,EAAE;MAC1C;MACAQ,MAAM,GAAGF,YAAY;IACvB;IACA,IACEM,OAAO,IACP,CAACJ,MAAM,CAACR,MAAM,IACd,CAACM,YAAY,IACb,CAAC7D,iBAAiB,CAACiC,OAAO,CAAC,EAC3B;MACA,MAAM,IAAI0B,KAAK,CACZ;AACT,qIACM,CAAC;IACH;EACF;EACA,MAAMS,aAAa,GAAGd,QAAQ,GAC1B/C,KAAK,CAACC,OAAO,CAAC8C,QAAQ,CAAC,GACrBA,QAAQ,GACR,CAACA,QAAQ,CAAC,GACZ,EAAE;EACN,MAAMe,YAAY,GAAGf,QAAQ,GAAG7D,iBAAiB,CAAC2E,aAAa,CAAC,GAAG,IAAI;EACvE,MAAME,mBAAmB,GAAG9E,cAAc,CAAU,IAAI,CAAC;EACzD,MAAM+E,iBAAiB,GAAGtF,MAAM,CAAQ,CAAC,CAAU,CAAC;;EAEpD;EACA,IAAI,CAAC4E,YAAY,EAAE;IACjBA,YAAY,GAAG,CAAC,GAAGE,MAAM,EAAE9B,OAAO,CAACuC,aAAa,CAAC;EACnD,CAAC,MAAM;IACLX,YAAY,CAACY,IAAI,CAACxC,OAAO,CAACuC,aAAa,CAAC;EAC1C;EACAH,YAAY,IAAIR,YAAY,CAACY,IAAI,CAACJ,YAAY,CAAC;EAE/C,IAAI,CAACP,mBAAmB,CAAC/C,OAAO,EAAE;IAChC,MAAM2D,YAAY,GAAGnF,iBAAiB,CAAC0C,OAAO,CAAC;IAC/C,IAAIkC,OAAO,EAAE;MACXvE,sBAAsB,CAAC8E,YAAY,CAAC;IACtC;IACAZ,mBAAmB,CAAC/C,OAAO,GAAG;MAC5B4D,OAAO,EAAE;QACP7D,KAAK,EAAE4D,YAAY;QACnBzC;MACF,CAAC;MACD2C,WAAW,EAAE1F,aAAa,CAAC;QACzBqD,IAAI,EAAEmC,YAAY;QAClBtC,UAAU,EAAE,CAAC,CAAC;QACdW,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAE;MACtB,CAAC,CAAC;MACFhB,eAAe,EAAEnC,sBAAsB,CAAC;IAC1C,CAAC;EACH;EAEA,MAAM;IAAE8E,OAAO;IAAEC,WAAW;IAAE5C;EAAgB,CAAC,GAAG8B,mBAAmB,CAAC/C,OAAO;EAC7E,MAAM8D,wBAAwB,GAAG7C,eAAe,CAAC6C,wBAAwB;EAEzEhB,YAAY,CAACY,IAAI,CAACI,wBAAwB,CAAC;EAE3C7F,SAAS,CAAC,MAAM;IACd,IAAI8F,GAAG;IACP,IAAIC,SAAS,GAAG9C,OAAO;IACvB,IAAIqB,QAAQ,EAAE;MACZyB,SAAS,GAAIA,CAAA,KAAM;QACjB,SAAS;;QACT,MAAM1C,SAAS,GAAGJ,OAAO,CAAC,CAAC;QAC3BmC,aAAa,CAAC3D,OAAO,CAAEuE,OAAO,IAAK;UACjCA,OAAO,CAAC3C,SAAoC,CAAC;QAC/C,CAAC,CAAC;QACF,OAAOA,SAAS;MAClB,CAAgC;IAClC;IAEA,IAAIvC,MAAM,CAAC,CAAC,EAAE;MACZgF,GAAG,GAAGA,CAAA,KAAM;QACV,SAAS;;QACT1B,gBAAgB,CACdyB,wBAAwB,EACxB5C,OAAO,EACP2C,WAAW,EACXN,mBAAmB,EACnBC,iBAAiB,EACjBH,aACF,CAAC;MACH,CAAC;IACH,CAAC,MAAM;MACLU,GAAG,GAAGA,CAAA,KAAM;QACV,SAAS;;QACT/C,YAAY,CACV8C,wBAAwB,EACxBE,SAAS,EACTH,WAAW,EACXN,mBAAmB,EACnBnC,eACF,CAAC;MACH,CAAC;IACH;IACA,MAAM8C,QAAQ,GAAG9F,WAAW,CAAC2F,GAAG,EAAEf,MAAM,CAAC;IACzC,OAAO,MAAM;MACX3E,UAAU,CAAC6F,QAAQ,CAAC;IACtB,CAAC;IACD;EACF,CAAC,EAAEpB,YAAY,CAAC;EAEhB7E,SAAS,CAAC,MAAM;IACdsF,mBAAmB,CAACxD,KAAK,GAAG,IAAI;IAChC,OAAO,MAAM;MACXwD,mBAAmB,CAACxD,KAAK,GAAG,KAAK;IACnC,CAAC;EACH,CAAC,EAAE,CAACwD,mBAAmB,CAAC,CAAC;EAEzBd,qBAAqB,CAACmB,OAAO,CAAC7D,KAAK,CAAC;EAEpC,MAAMoE,mBAAmB,GAAGjG,MAAM,CAEhC,IAAI,CAAC;EAEP,IAAI,CAACiG,mBAAmB,CAACnE,OAAO,EAAE;IAChCmE,mBAAmB,CAACnE,OAAO,GAAGjB,MAAM,CAAC,CAAC,GAClC;MAAEkC,eAAe;MAAE2C,OAAO;MAAEJ;IAAkB,CAAC,GAC/C;MAAEvC,eAAe;MAAE2C;IAAQ,CAAC;EAClC;EAEA,OAAOO,mBAAmB,CAACnE,OAAO;AACpC", "ignoreList": []}