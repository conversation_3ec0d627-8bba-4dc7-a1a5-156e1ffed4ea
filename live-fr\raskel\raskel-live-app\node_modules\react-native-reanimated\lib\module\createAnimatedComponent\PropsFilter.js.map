{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "isSharedValue", "isChromeDebugger", "WorkletEventHandler", "initialUpdaterRun", "hasInlineStyles", "getInlineStyle", "flattenArray", "has", "StyleSheet", "dummyListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "filterNonAnimatedProps", "component", "inputProps", "props", "styleProp", "style", "styles", "processedStyle", "map", "viewDescriptors", "_isFirstRender", "_initialStyle", "initial", "updater", "flatten", "animatedProp", "animatedProps", "undefined", "keys", "for<PERSON>ach", "initialValueKey", "_animatedProp$initial", "workletEventHandler", "eventNames", "length", "eventName", "listeners"], "sources": ["PropsFilter.tsx"], "sourcesContent": ["'use strict';\n\nimport type { StyleProps } from '../commonTypes';\nimport { isSharedValue } from '../isSharedValue';\nimport { isChromeDebugger } from '../PlatformChecker';\nimport { WorkletEventHandler } from '../WorkletEventHandler';\nimport { initialUpdaterRun } from '../animation';\nimport { hasInlineStyles, getInlineStyle } from './InlinePropManager';\nimport type {\n  AnimatedComponentProps,\n  AnimatedProps,\n  InitialComponentProps,\n  IAnimatedComponentInternal,\n  IPropsFilter,\n} from './commonTypes';\nimport { flattenArray, has } from './utils';\nimport { StyleSheet } from 'react-native';\n\nfunction dummyListener() {\n  // empty listener we use to assign to listener properties for which animated\n  // event is used.\n}\n\nexport class PropsFilter implements IPropsFilter {\n  private _initialStyle = {};\n\n  public filterNonAnimatedProps(\n    component: React.Component<unknown, unknown> & IAnimatedComponentInternal\n  ): Record<string, unknown> {\n    const inputProps =\n      component.props as AnimatedComponentProps<InitialComponentProps>;\n    const props: Record<string, unknown> = {};\n    for (const key in inputProps) {\n      const value = inputProps[key];\n      if (key === 'style') {\n        const styleProp = inputProps.style;\n        const styles = flattenArray<StyleProps>(styleProp ?? []);\n        const processedStyle: StyleProps = styles.map((style) => {\n          if (style && style.viewDescriptors) {\n            // this is how we recognize styles returned by useAnimatedStyle\n            if (component._isFirstRender) {\n              this._initialStyle = {\n                ...style.initial.value,\n                ...this._initialStyle,\n                ...initialUpdaterRun<StyleProps>(style.initial.updater),\n              };\n            }\n            return this._initialStyle;\n          } else if (hasInlineStyles(style)) {\n            return getInlineStyle(style, component._isFirstRender);\n          } else {\n            return style;\n          }\n        });\n        props[key] = StyleSheet.flatten(processedStyle);\n      } else if (key === 'animatedProps') {\n        const animatedProp = inputProps.animatedProps as Partial<\n          AnimatedComponentProps<AnimatedProps>\n        >;\n        if (animatedProp.initial !== undefined) {\n          Object.keys(animatedProp.initial.value).forEach((initialValueKey) => {\n            props[initialValueKey] =\n              animatedProp.initial?.value[initialValueKey];\n          });\n        }\n      } else if (\n        has('workletEventHandler', value) &&\n        value.workletEventHandler instanceof WorkletEventHandler\n      ) {\n        if (value.workletEventHandler.eventNames.length > 0) {\n          value.workletEventHandler.eventNames.forEach((eventName) => {\n            props[eventName] = has('listeners', value.workletEventHandler)\n              ? (\n                  value.workletEventHandler.listeners as Record<string, unknown>\n                )[eventName]\n              : dummyListener;\n          });\n        } else {\n          props[key] = dummyListener;\n        }\n      } else if (isSharedValue(value)) {\n        if (component._isFirstRender) {\n          props[key] = value.value;\n        }\n      } else if (key !== 'onGestureHandlerStateChange' || !isChromeDebugger()) {\n        props[key] = value;\n      }\n    }\n    return props;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAGb,SAASW,aAAa,QAAQ,kBAAkB;AAChD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AAQrE,SAASC,YAAY,EAAEC,GAAG,QAAQ,SAAS;AAC3C,SAASC,UAAU,QAAQ,cAAc;AAEzC,SAASC,aAAaA,CAAA,EAAG;EACvB;EACA;AAAA;AAGF,OAAO,MAAMC,WAAW,CAAyB;EAAAC,YAAA;IAAAhC,eAAA,wBACvB,CAAC,CAAC;EAAA;EAEnBiC,sBAAsBA,CAC3BC,SAAyE,EAChD;IACzB,MAAMC,UAAU,GACdD,SAAS,CAACE,KAAsD;IAClE,MAAMA,KAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMlC,GAAG,IAAIiC,UAAU,EAAE;MAC5B,MAAMhC,KAAK,GAAGgC,UAAU,CAACjC,GAAG,CAAC;MAC7B,IAAIA,GAAG,KAAK,OAAO,EAAE;QACnB,MAAMmC,SAAS,GAAGF,UAAU,CAACG,KAAK;QAClC,MAAMC,MAAM,GAAGZ,YAAY,CAAaU,SAAS,IAAI,EAAE,CAAC;QACxD,MAAMG,cAA0B,GAAGD,MAAM,CAACE,GAAG,CAAEH,KAAK,IAAK;UACvD,IAAIA,KAAK,IAAIA,KAAK,CAACI,eAAe,EAAE;YAClC;YACA,IAAIR,SAAS,CAACS,cAAc,EAAE;cAC5B,IAAI,CAACC,aAAa,GAAG;gBACnB,GAAGN,KAAK,CAACO,OAAO,CAAC1C,KAAK;gBACtB,GAAG,IAAI,CAACyC,aAAa;gBACrB,GAAGpB,iBAAiB,CAAac,KAAK,CAACO,OAAO,CAACC,OAAO;cACxD,CAAC;YACH;YACA,OAAO,IAAI,CAACF,aAAa;UAC3B,CAAC,MAAM,IAAInB,eAAe,CAACa,KAAK,CAAC,EAAE;YACjC,OAAOZ,cAAc,CAACY,KAAK,EAAEJ,SAAS,CAACS,cAAc,CAAC;UACxD,CAAC,MAAM;YACL,OAAOL,KAAK;UACd;QACF,CAAC,CAAC;QACFF,KAAK,CAAClC,GAAG,CAAC,GAAG2B,UAAU,CAACkB,OAAO,CAACP,cAAc,CAAC;MACjD,CAAC,MAAM,IAAItC,GAAG,KAAK,eAAe,EAAE;QAClC,MAAM8C,YAAY,GAAGb,UAAU,CAACc,aAE/B;QACD,IAAID,YAAY,CAACH,OAAO,KAAKK,SAAS,EAAE;UACtC7C,MAAM,CAAC8C,IAAI,CAACH,YAAY,CAACH,OAAO,CAAC1C,KAAK,CAAC,CAACiD,OAAO,CAAEC,eAAe,IAAK;YAAA,IAAAC,qBAAA;YACnElB,KAAK,CAACiB,eAAe,CAAC,IAAAC,qBAAA,GACpBN,YAAY,CAACH,OAAO,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBnD,KAAK,CAACkD,eAAe,CAAC;UAChD,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IACLzB,GAAG,CAAC,qBAAqB,EAAEzB,KAAK,CAAC,IACjCA,KAAK,CAACoD,mBAAmB,YAAYhC,mBAAmB,EACxD;QACA,IAAIpB,KAAK,CAACoD,mBAAmB,CAACC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;UACnDtD,KAAK,CAACoD,mBAAmB,CAACC,UAAU,CAACJ,OAAO,CAAEM,SAAS,IAAK;YAC1DtB,KAAK,CAACsB,SAAS,CAAC,GAAG9B,GAAG,CAAC,WAAW,EAAEzB,KAAK,CAACoD,mBAAmB,CAAC,GAExDpD,KAAK,CAACoD,mBAAmB,CAACI,SAAS,CACnCD,SAAS,CAAC,GACZ5B,aAAa;UACnB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLM,KAAK,CAAClC,GAAG,CAAC,GAAG4B,aAAa;QAC5B;MACF,CAAC,MAAM,IAAIT,aAAa,CAAClB,KAAK,CAAC,EAAE;QAC/B,IAAI+B,SAAS,CAACS,cAAc,EAAE;UAC5BP,KAAK,CAAClC,GAAG,CAAC,GAAGC,KAAK,CAACA,KAAK;QAC1B;MACF,CAAC,MAAM,IAAID,GAAG,KAAK,6BAA6B,IAAI,CAACoB,gBAAgB,CAAC,CAAC,EAAE;QACvEc,KAAK,CAAClC,GAAG,CAAC,GAAGC,KAAK;MACpB;IACF;IACA,OAAOiC,KAAK;EACd;AACF", "ignoreList": []}