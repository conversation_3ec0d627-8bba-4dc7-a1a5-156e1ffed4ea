{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "withSequence", "withTiming", "ComplexAnimationBuilder", "LightSpeedInRight", "constructor", "args", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "duration", "getDuration", "callback", "callbackV", "initialValues", "values", "animations", "opacity", "transform", "translateX", "skewX", "windowWidth", "createInstance", "LightSpeedInLeft", "LightSpeedOutRight", "LightSpeedOutLeft"], "sources": ["Lightspeed.ts"], "sourcesContent": ["'use strict';\nimport { withSequence, withTiming } from '../../animation';\nimport type { BaseAnimationBuilder } from '../animationBuilder';\nimport { ComplexAnimationBuilder } from '../animationBuilder';\nimport type {\n  EntryExitAnimationsValues,\n  EntryExitAnimationFunction,\n  IEntryExitAnimationBuilder,\n} from '../animationBuilder/commonTypes';\n\n/**\n * Entry from right animation with change in skew and opacity. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#lightspeed\n */\nexport class LightSpeedInRight\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'LightSpeedInRight';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new LightSpeedInRight() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const duration = this.getDuration();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          opacity: delayFunction(delay, withTiming(1, { duration })),\n          transform: [\n            {\n              translateX: delayFunction(\n                delay,\n                animation(0, { ...config, duration: duration * 0.7 })\n              ),\n            },\n            {\n              skewX: delayFunction(\n                delay,\n                withSequence(\n                  withTiming('10deg', { duration: duration * 0.7 }),\n                  withTiming('-5deg', { duration: duration * 0.15 }),\n                  withTiming('0deg', { duration: duration * 0.15 })\n                )\n              ),\n            },\n          ],\n        },\n        initialValues: {\n          opacity: 0,\n          transform: [{ translateX: values.windowWidth }, { skewX: '-45deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Entry from left animation with change in skew and opacity. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#lightspeed\n */\nexport class LightSpeedInLeft\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'LightSpeedInLeft';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new LightSpeedInLeft() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const duration = this.getDuration();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          opacity: delayFunction(delay, withTiming(1, { duration })),\n          transform: [\n            {\n              translateX: delayFunction(\n                delay,\n                animation(0, { ...config, duration: duration * 0.7 })\n              ),\n            },\n            {\n              skewX: delayFunction(\n                delay,\n                withSequence(\n                  withTiming('-10deg', { duration: duration * 0.7 }),\n                  withTiming('5deg', { duration: duration * 0.15 }),\n                  withTiming('0deg', { duration: duration * 0.15 })\n                )\n              ),\n            },\n          ],\n        },\n        initialValues: {\n          opacity: 0,\n          transform: [{ translateX: -values.windowWidth }, { skewX: '45deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Exit to right animation with change in skew and opacity. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#lightspeed\n */\nexport class LightSpeedOutRight\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'LightSpeedOutRight';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new LightSpeedOutRight() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          opacity: delayFunction(delay, animation(0, config)),\n          transform: [\n            {\n              translateX: delayFunction(\n                delay,\n                animation(values.windowWidth, config)\n              ),\n            },\n            {\n              skewX: delayFunction(delay, animation('-45deg', config)),\n            },\n          ],\n        },\n        initialValues: {\n          opacity: 1,\n          transform: [{ translateX: 0 }, { skewX: '0deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Exit to left animation with change in skew and opacity. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations/#lightspeed\n */\nexport class LightSpeedOutLeft\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'LightSpeedOutLeft';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new LightSpeedOutLeft() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          opacity: delayFunction(delay, animation(0, config)),\n          transform: [\n            {\n              translateX: delayFunction(\n                delay,\n                animation(-values.windowWidth, config)\n              ),\n            },\n            {\n              skewX: delayFunction(delay, animation('45deg', config)),\n            },\n          ],\n        },\n        initialValues: {\n          opacity: 1,\n          transform: [{ translateX: 0 }, { skewX: '0deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAE1D,SAASC,uBAAuB,QAAQ,qBAAqB;AAO7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,SACpBD,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA1B,eAAA,gBASU,MAAkC;MACxC,MAAM2B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEV,UAAU,CAAC,CAAC,EAAE;cAAEY;YAAS,CAAC,CAAC,CAAC;YAC1DQ,SAAS,EAAE,CACT;cACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAAC,EAAE;gBAAE,GAAGC,MAAM;gBAAEI,QAAQ,EAAEA,QAAQ,GAAG;cAAI,CAAC,CACtD;YACF,CAAC,EACD;cACEU,KAAK,EAAEjB,aAAa,CAClBK,KAAK,EACLX,YAAY,CACVC,UAAU,CAAC,OAAO,EAAE;gBAAEY,QAAQ,EAAEA,QAAQ,GAAG;cAAI,CAAC,CAAC,EACjDZ,UAAU,CAAC,OAAO,EAAE;gBAAEY,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAAC,EAClDZ,UAAU,CAAC,MAAM,EAAE;gBAAEY,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAClD,CACF;YACF,CAAC;UAEL,CAAC;UACDI,aAAa,EAAE;YACbG,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAEJ,MAAM,CAACM;YAAY,CAAC,EAAE;cAAED,KAAK,EAAE;YAAS,CAAC,CAAC;YACpE,GAAGN;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA9CD,OAAOU,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAItB,iBAAiB,CAAC,CAAC;EAChC;AA2CF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAxB,eAAA,CAvDawB,iBAAiB,gBAIR,mBAAmB;AA0DzC,OAAO,MAAMuB,gBAAgB,SACnBxB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA1B,eAAA,gBASU,MAAkC;MACxC,MAAM2B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEV,UAAU,CAAC,CAAC,EAAE;cAAEY;YAAS,CAAC,CAAC,CAAC;YAC1DQ,SAAS,EAAE,CACT;cACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAAC,EAAE;gBAAE,GAAGC,MAAM;gBAAEI,QAAQ,EAAEA,QAAQ,GAAG;cAAI,CAAC,CACtD;YACF,CAAC,EACD;cACEU,KAAK,EAAEjB,aAAa,CAClBK,KAAK,EACLX,YAAY,CACVC,UAAU,CAAC,QAAQ,EAAE;gBAAEY,QAAQ,EAAEA,QAAQ,GAAG;cAAI,CAAC,CAAC,EAClDZ,UAAU,CAAC,MAAM,EAAE;gBAAEY,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAAC,EACjDZ,UAAU,CAAC,MAAM,EAAE;gBAAEY,QAAQ,EAAEA,QAAQ,GAAG;cAAK,CAAC,CAClD,CACF;YACF,CAAC;UAEL,CAAC;UACDI,aAAa,EAAE;YACbG,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE,CAACJ,MAAM,CAACM;YAAY,CAAC,EAAE;cAAED,KAAK,EAAE;YAAQ,CAAC,CAAC;YACpE,GAAGN;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA9CD,OAAOU,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIC,gBAAgB,CAAC,CAAC;EAC/B;AA2CF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA/C,eAAA,CAvDa+C,gBAAgB,gBAIP,kBAAkB;AA0DxC,OAAO,MAAMC,kBAAkB,SACrBzB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA1B,eAAA,gBASU,MAAkC;MACxC,MAAM2B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMG,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDY,SAAS,EAAE,CACT;cACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAACU,MAAM,CAACM,WAAW,EAAEf,MAAM,CACtC;YACF,CAAC,EACD;cACEc,KAAK,EAAEjB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;YACzD,CAAC;UAEL,CAAC;UACDQ,aAAa,EAAE;YACbG,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAC,CAAC;YACjD,GAAGN;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAtCD,OAAOU,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIE,kBAAkB,CAAC,CAAC;EACjC;AAmCF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAhD,eAAA,CA/CagD,kBAAkB,gBAIT,oBAAoB;AAkD1C,OAAO,MAAMC,iBAAiB,SACpB1B,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA1B,eAAA,gBASU,MAAkC;MACxC,MAAM2B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMG,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDY,SAAS,EAAE,CACT;cACEC,UAAU,EAAEhB,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAACU,MAAM,CAACM,WAAW,EAAEf,MAAM,CACvC;YACF,CAAC,EACD;cACEc,KAAK,EAAEjB,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,OAAO,EAAEC,MAAM,CAAC;YACxD,CAAC;UAEL,CAAC;UACDQ,aAAa,EAAE;YACbG,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAC,CAAC;YACjD,GAAGN;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAtCD,OAAOU,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIG,iBAAiB,CAAC,CAAC;EAChC;AAmCF;AAACjD,eAAA,CA7CYiD,iBAAiB,gBAIR,mBAAmB", "ignoreList": []}