{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "rubberBandDecay", "isValidRubberBandConfig", "rigidDecay", "validateConfig", "config", "clamp", "Array", "isArray", "Error", "length", "velocityFactor", "rubberBandEffect", "<PERSON><PERSON><PERSON><PERSON>", "userConfig", "callback", "deceleration", "velocity", "rubberBandFactor", "Object", "keys", "for<PERSON>ach", "key", "decay", "animation", "now", "onStart", "value", "current", "lastTimestamp", "startTimestamp", "initialVelocity", "reduceMotion", "onFrame"], "sources": ["decay.ts"], "sourcesContent": ["'use strict';\nimport { defineAnimation, getReduceMotionForAnimation } from '../util';\nimport type {\n  AnimationCallback,\n  Timestamp,\n  Animation,\n} from '../../commonTypes';\nimport { rubberBandDecay } from './rubberBandDecay';\nimport { isValidRubberBandConfig } from './utils';\nimport type {\n  DecayAnimation,\n  DecayConfig,\n  DefaultDecayConfig,\n  InnerDecayAnimation,\n} from './utils';\nimport { rigidDecay } from './rigidDecay';\n\nexport type WithDecayConfig = DecayConfig;\n\n// TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\ntype withDecayType = (\n  userConfig: DecayConfig,\n  callback?: AnimationCallback\n) => number;\n\nfunction validateConfig(config: DefaultDecayConfig): void {\n  'worklet';\n  if (config.clamp) {\n    if (!Array.isArray(config.clamp)) {\n      throw new Error(\n        `[Reanimated] \\`config.clamp\\` must be an array but is ${typeof config.clamp}.`\n      );\n    }\n    if (config.clamp.length !== 2) {\n      throw new Error(\n        `[Reanimated] \\`clamp array\\` must contain 2 items but is given ${\n          config.clamp.length as number\n        }.`\n      );\n    }\n  }\n  if (config.velocityFactor <= 0) {\n    throw new Error(\n      `[Reanimated] \\`config.velocityFactor\\` must be greather then 0 but is ${config.velocityFactor}.`\n    );\n  }\n  if (config.rubberBandEffect && !config.clamp) {\n    throw new Error(\n      '[Reanimated] You need to set `clamp` property when using `rubberBandEffect`.'\n    );\n  }\n}\n\n/**\n * Lets you create animations that mimic objects in motion with friction.\n *\n * @param config - The decay animation configuration - {@link DecayConfig}.\n * @param callback - A function called upon animation completion - {@link AnimationCallback}.\n * @returns An [animation object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object) which holds the current state of the animation.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDecay\n */\nexport const withDecay = function (\n  userConfig: DecayConfig,\n  callback?: AnimationCallback\n): Animation<DecayAnimation> {\n  'worklet';\n\n  return defineAnimation<DecayAnimation>(0, () => {\n    'worklet';\n    const config: DefaultDecayConfig = {\n      deceleration: 0.998,\n      velocityFactor: 1,\n      velocity: 0,\n      rubberBandFactor: 0.6,\n    };\n    if (userConfig) {\n      Object.keys(userConfig).forEach(\n        (key) =>\n          ((config as any)[key] = userConfig[key as keyof typeof userConfig])\n      );\n    }\n\n    const decay: (animation: InnerDecayAnimation, now: number) => boolean =\n      isValidRubberBandConfig(config)\n        ? (animation, now) => rubberBandDecay(animation, now, config)\n        : (animation, now) => rigidDecay(animation, now, config);\n\n    function onStart(\n      animation: DecayAnimation,\n      value: number,\n      now: Timestamp\n    ): void {\n      animation.current = value;\n      animation.lastTimestamp = now;\n      animation.startTimestamp = now;\n      animation.initialVelocity = config.velocity;\n      validateConfig(config);\n\n      if (animation.reduceMotion && config.clamp) {\n        if (value < config.clamp[0]) {\n          animation.current = config.clamp[0];\n        } else if (value > config.clamp[1]) {\n          animation.current = config.clamp[1];\n        }\n      }\n    }\n\n    return {\n      onFrame: decay,\n      onStart,\n      callback,\n      velocity: config.velocity ?? 0,\n      initialVelocity: 0,\n      current: 0,\n      lastTimestamp: 0,\n      startTimestamp: 0,\n      reduceMotion: getReduceMotionForAnimation(config.reduceMotion),\n    } as DecayAnimation;\n  });\n} as unknown as withDecayType;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,SAAS;AAMtE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,uBAAuB,QAAQ,SAAS;AAOjD,SAASC,UAAU,QAAQ,cAAc;;AAIzC;;AAMA,SAASC,cAAcA,CAACC,MAA0B,EAAQ;EACxD,SAAS;;EACT,IAAIA,MAAM,CAACC,KAAK,EAAE;IAChB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACC,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIG,KAAK,CACZ,yDAAwD,OAAOJ,MAAM,CAACC,KAAM,GAC/E,CAAC;IACH;IACA,IAAID,MAAM,CAACC,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAID,KAAK,CACZ,kEACCJ,MAAM,CAACC,KAAK,CAACI,MACd,GACH,CAAC;IACH;EACF;EACA,IAAIL,MAAM,CAACM,cAAc,IAAI,CAAC,EAAE;IAC9B,MAAM,IAAIF,KAAK,CACZ,yEAAwEJ,MAAM,CAACM,cAAe,GACjG,CAAC;EACH;EACA,IAAIN,MAAM,CAACO,gBAAgB,IAAI,CAACP,MAAM,CAACC,KAAK,EAAE;IAC5C,MAAM,IAAIG,KAAK,CACb,8EACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,SAAS,GAAG,SAAAA,CACvBC,UAAuB,EACvBC,QAA4B,EACD;EAC3B,SAAS;;EAET,OAAOhB,eAAe,CAAiB,CAAC,EAAE,MAAM;IAC9C,SAAS;;IACT,MAAMM,MAA0B,GAAG;MACjCW,YAAY,EAAE,KAAK;MACnBL,cAAc,EAAE,CAAC;MACjBM,QAAQ,EAAE,CAAC;MACXC,gBAAgB,EAAE;IACpB,CAAC;IACD,IAAIJ,UAAU,EAAE;MACdK,MAAM,CAACC,IAAI,CAACN,UAAU,CAAC,CAACO,OAAO,CAC5BC,GAAG,IACAjB,MAAM,CAASiB,GAAG,CAAC,GAAGR,UAAU,CAACQ,GAAG,CAC1C,CAAC;IACH;IAEA,MAAMC,KAA+D,GACnErB,uBAAuB,CAACG,MAAM,CAAC,GAC3B,CAACmB,SAAS,EAAEC,GAAG,KAAKxB,eAAe,CAACuB,SAAS,EAAEC,GAAG,EAAEpB,MAAM,CAAC,GAC3D,CAACmB,SAAS,EAAEC,GAAG,KAAKtB,UAAU,CAACqB,SAAS,EAAEC,GAAG,EAAEpB,MAAM,CAAC;IAE5D,SAASqB,OAAOA,CACdF,SAAyB,EACzBG,KAAa,EACbF,GAAc,EACR;MACND,SAAS,CAACI,OAAO,GAAGD,KAAK;MACzBH,SAAS,CAACK,aAAa,GAAGJ,GAAG;MAC7BD,SAAS,CAACM,cAAc,GAAGL,GAAG;MAC9BD,SAAS,CAACO,eAAe,GAAG1B,MAAM,CAACY,QAAQ;MAC3Cb,cAAc,CAACC,MAAM,CAAC;MAEtB,IAAImB,SAAS,CAACQ,YAAY,IAAI3B,MAAM,CAACC,KAAK,EAAE;QAC1C,IAAIqB,KAAK,GAAGtB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAC3BkB,SAAS,CAACI,OAAO,GAAGvB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM,IAAIqB,KAAK,GAAGtB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAClCkB,SAAS,CAACI,OAAO,GAAGvB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC;MACF;IACF;IAEA,OAAO;MACL2B,OAAO,EAAEV,KAAK;MACdG,OAAO;MACPX,QAAQ;MACRE,QAAQ,EAAEZ,MAAM,CAACY,QAAQ,IAAI,CAAC;MAC9Bc,eAAe,EAAE,CAAC;MAClBH,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBE,YAAY,EAAEhC,2BAA2B,CAACK,MAAM,CAAC2B,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAA6B", "ignoreList": []}