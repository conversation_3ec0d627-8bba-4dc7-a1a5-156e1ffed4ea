{"version": 3, "names": ["getUseOfValueInStyleWarning"], "sources": ["pluginUtils.ts"], "sourcesContent": ["'use strict';\nexport function getUseOfValueInStyleWarning() {\n  return (\n    \"It looks like you might be using shared value's .value inside reanimated inline style. \" +\n    'If you want a component to update when shared value changes you should use the shared value' +\n    ' directly instead of its current state represented by `.value`. See documentation here: ' +\n    'https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary/#animations-in-inline-styling'\n  );\n}\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,SAASA,2BAA2BA,CAAA,EAAG;EAC5C,OACE,yFAAyF,GACzF,6FAA6F,GAC7F,0FAA0F,GAC1F,6GAA6G;AAEjH", "ignoreList": []}