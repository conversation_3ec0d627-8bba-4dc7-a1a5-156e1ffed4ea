{"version": 3, "names": ["FadingTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "fadingTransition", "style", "opacity", "transform", "scale", "duration"], "sources": ["Fading.web.ts"], "sourcesContent": ["'use strict';\nimport type { TransitionData } from '../animationParser';\n\nexport function FadingTransition(name: string, transitionData: TransitionData) {\n  const { translateX, translateY, scaleX, scaleY } = transitionData;\n\n  const fadingTransition = {\n    name,\n    style: {\n      0: {\n        opacity: 1,\n        transform: [\n          {\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`,\n          },\n        ],\n      },\n      20: {\n        opacity: 0,\n        transform: [\n          {\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`,\n          },\n        ],\n      },\n      60: {\n        opacity: 0,\n        transform: [\n          {\n            translateX: '0px',\n            translateY: '0px',\n            scale: `1,1`,\n          },\n        ],\n      },\n      100: {\n        opacity: 1,\n        transform: [\n          {\n            translateX: '0px',\n            translateY: '0px',\n            scale: `1,1`,\n          },\n        ],\n      },\n    },\n    duration: 300,\n  };\n\n  return fadingTransition;\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,gBAAgBA,CAACC,IAAY,EAAEC,cAA8B,EAAE;EAC7E,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGJ,cAAc;EAEjE,MAAMK,gBAAgB,GAAG;IACvBN,IAAI;IACJO,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CACT;UACEP,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BC,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BO,KAAK,EAAG,GAAEN,MAAO,IAAGC,MAAO;QAC7B,CAAC;MAEL,CAAC;MACD,EAAE,EAAE;QACFG,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CACT;UACEP,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BC,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BO,KAAK,EAAG,GAAEN,MAAO,IAAGC,MAAO;QAC7B,CAAC;MAEL,CAAC;MACD,EAAE,EAAE;QACFG,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CACT;UACEP,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,KAAK;UACjBO,KAAK,EAAG;QACV,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHF,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CACT;UACEP,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,KAAK;UACjBO,KAAK,EAAG;QACV,CAAC;MAEL;IACF,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC;EAED,OAAOL,gBAAgB;AACzB", "ignoreList": []}