{"version": 3, "names": ["defineAnimation", "withTiming", "ColorProperties", "processColor", "<PERSON><PERSON><PERSON>", "obj", "path", "keys", "Array", "isArray", "reduce", "acc", "current", "undefined", "set<PERSON>ath", "value", "currObj", "i", "length", "withStyleAnimation", "styleAnimations", "onFrame", "animation", "now", "stillGoing", "entriesToCheck", "currentEntry", "pop", "index", "push", "concat", "key", "Object", "currentStyleAnimation", "finished", "callback", "isAnimatingColorProp", "includes", "onStart", "previousAnimation", "prevAnimation", "prevVal", "console", "warn", "join", "currentAnimation", "duration", "animationsToCheck", "element", "values", "isHigherOrder"], "sources": ["styleAnimation.ts"], "sourcesContent": ["'use strict';\nimport { defineAnimation } from './util';\nimport type {\n  Timestamp,\n  AnimatableValue,\n  AnimationObject,\n  Animation,\n  NestedObject,\n  NestedObjectValues,\n  AnimatedStyle,\n} from '../commonTypes';\nimport type { StyleLayoutAnimation } from './commonTypes';\nimport { withTiming } from './timing';\nimport { ColorProperties, processColor } from '../Colors';\n\n// resolves path to value for nested objects\n// if path cannot be resolved returns undefined\nfunction resolvePath<T>(\n  obj: NestedObject<T>,\n  path: AnimatableValue[] | AnimatableValue\n): NestedObjectValues<T> | undefined {\n  'worklet';\n  const keys: AnimatableValue[] = Array.isArray(path) ? path : [path];\n  return keys.reduce<NestedObjectValues<T> | undefined>((acc, current) => {\n    if (Array.isArray(acc) && typeof current === 'number') {\n      return acc[current];\n    } else if (\n      acc !== null &&\n      typeof acc === 'object' &&\n      (current as number | string) in acc\n    ) {\n      return (acc as { [key: string]: NestedObjectValues<T> })[\n        current as number | string\n      ];\n    }\n    return undefined;\n  }, obj);\n}\n\n// set value at given path\ntype Path = Array<string | number> | string | number;\nfunction setPath<T>(\n  obj: NestedObject<T>,\n  path: Path,\n  value: NestedObjectValues<T>\n): void {\n  'worklet';\n  const keys: Path = Array.isArray(path) ? path : [path];\n  let currObj: NestedObjectValues<T> = obj;\n  for (let i = 0; i < keys.length - 1; i++) {\n    // creates entry if there isn't one\n    currObj = currObj as { [key: string]: NestedObjectValues<T> };\n    if (!(keys[i] in currObj)) {\n      // if next key is a number create an array\n      if (typeof keys[i + 1] === 'number') {\n        currObj[keys[i]] = [];\n      } else {\n        currObj[keys[i]] = {};\n      }\n    }\n    currObj = currObj[keys[i]];\n  }\n\n  (currObj as { [key: string]: NestedObjectValues<T> })[keys[keys.length - 1]] =\n    value;\n}\n\ninterface NestedObjectEntry<T> {\n  value: NestedObjectValues<T>;\n  path: (string | number)[];\n}\n\nexport function withStyleAnimation(\n  styleAnimations: AnimatedStyle<any>\n): StyleLayoutAnimation {\n  'worklet';\n  return defineAnimation<StyleLayoutAnimation>({}, () => {\n    'worklet';\n\n    const onFrame = (\n      animation: StyleLayoutAnimation,\n      now: Timestamp\n    ): boolean => {\n      let stillGoing = false;\n      const entriesToCheck: NestedObjectEntry<AnimationObject>[] = [\n        { value: animation.styleAnimations, path: [] },\n      ];\n      while (entriesToCheck.length > 0) {\n        const currentEntry: NestedObjectEntry<AnimationObject> =\n          entriesToCheck.pop() as NestedObjectEntry<AnimationObject>;\n        if (Array.isArray(currentEntry.value)) {\n          for (let index = 0; index < currentEntry.value.length; index++) {\n            entriesToCheck.push({\n              value: currentEntry.value[index],\n              path: currentEntry.path.concat(index),\n            });\n          }\n        } else if (\n          typeof currentEntry.value === 'object' &&\n          currentEntry.value.onFrame === undefined\n        ) {\n          // nested object\n          for (const key of Object.keys(currentEntry.value)) {\n            entriesToCheck.push({\n              value: currentEntry.value[key],\n              path: currentEntry.path.concat(key),\n            });\n          }\n        } else {\n          const currentStyleAnimation: AnimationObject =\n            currentEntry.value as AnimationObject;\n          if (currentStyleAnimation.finished) {\n            continue;\n          }\n          const finished = currentStyleAnimation.onFrame(\n            currentStyleAnimation,\n            now\n          );\n          if (finished) {\n            currentStyleAnimation.finished = true;\n            if (currentStyleAnimation.callback) {\n              currentStyleAnimation.callback(true);\n            }\n          } else {\n            stillGoing = true;\n          }\n\n          // When working with animations changing colors, we need to make sure that each one of them begins with a rgba, not a processed number.\n          // Thus, we only set the path to a processed color, but currentStyleAnimation.current stays as rgba.\n          const isAnimatingColorProp = ColorProperties.includes(\n            currentEntry.path[0] as string\n          );\n\n          setPath(\n            animation.current,\n            currentEntry.path,\n            isAnimatingColorProp\n              ? processColor(currentStyleAnimation.current)\n              : currentStyleAnimation.current\n          );\n        }\n      }\n      return !stillGoing;\n    };\n\n    const onStart = (\n      animation: StyleLayoutAnimation,\n      value: AnimatedStyle<any>,\n      now: Timestamp,\n      previousAnimation: StyleLayoutAnimation\n    ): void => {\n      const entriesToCheck: NestedObjectEntry<\n        AnimationObject | AnimatableValue\n      >[] = [{ value: styleAnimations, path: [] }];\n      while (entriesToCheck.length > 0) {\n        const currentEntry: NestedObjectEntry<\n          AnimationObject | AnimatableValue\n        > = entriesToCheck.pop() as NestedObjectEntry<\n          AnimationObject | AnimatableValue\n        >;\n        if (Array.isArray(currentEntry.value)) {\n          for (let index = 0; index < currentEntry.value.length; index++) {\n            entriesToCheck.push({\n              value: currentEntry.value[index],\n              path: currentEntry.path.concat(index),\n            });\n          }\n        } else if (\n          typeof currentEntry.value === 'object' &&\n          currentEntry.value.onStart === undefined\n        ) {\n          for (const key of Object.keys(currentEntry.value)) {\n            entriesToCheck.push({\n              value: currentEntry.value[key],\n              path: currentEntry.path.concat(key),\n            });\n          }\n        } else {\n          const prevAnimation = resolvePath(\n            previousAnimation?.styleAnimations,\n            currentEntry.path\n          );\n          let prevVal = resolvePath(value, currentEntry.path);\n          if (prevAnimation && !prevVal) {\n            prevVal = (prevAnimation as any).current;\n          }\n          if (prevVal === undefined) {\n            console.warn(\n              `Initial values for animation are missing for property ${currentEntry.path.join(\n                '.'\n              )}`\n            );\n          }\n          setPath(animation.current, currentEntry.path, prevVal);\n          let currentAnimation: AnimationObject;\n          if (\n            typeof currentEntry.value !== 'object' ||\n            !currentEntry.value.onStart\n          ) {\n            currentAnimation = withTiming(\n              currentEntry.value as AnimatableValue,\n              { duration: 0 }\n            ) as AnimationObject; // TODO TYPESCRIPT this temporary cast is to get rid of .d.ts file.\n            setPath(\n              animation.styleAnimations,\n              currentEntry.path,\n              currentAnimation\n            );\n          } else {\n            currentAnimation = currentEntry.value as Animation<AnimationObject>;\n          }\n          currentAnimation.onStart(\n            currentAnimation,\n            prevVal,\n            now,\n            prevAnimation\n          );\n        }\n      }\n    };\n\n    const callback = (finished: boolean): void => {\n      if (!finished) {\n        const animationsToCheck: NestedObjectValues<AnimationObject>[] = [\n          styleAnimations,\n        ];\n        while (animationsToCheck.length > 0) {\n          const currentAnimation: NestedObjectValues<AnimationObject> =\n            animationsToCheck.pop() as NestedObjectValues<AnimationObject>;\n          if (Array.isArray(currentAnimation)) {\n            for (const element of currentAnimation) {\n              animationsToCheck.push(element);\n            }\n          } else if (\n            typeof currentAnimation === 'object' &&\n            currentAnimation.onStart === undefined\n          ) {\n            for (const value of Object.values(currentAnimation)) {\n              animationsToCheck.push(value);\n            }\n          } else {\n            const currentStyleAnimation: AnimationObject =\n              currentAnimation as AnimationObject;\n            if (\n              !currentStyleAnimation.finished &&\n              currentStyleAnimation.callback\n            ) {\n              currentStyleAnimation.callback(false);\n            }\n          }\n        }\n      }\n    };\n\n    return {\n      isHigherOrder: true,\n      onFrame,\n      onStart,\n      current: {},\n      styleAnimations,\n      callback,\n    } as StyleLayoutAnimation;\n  });\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,QAAQ;AAWxC,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,eAAe,EAAEC,YAAY,QAAQ,WAAW;;AAEzD;AACA;AACA,SAASC,WAAWA,CAClBC,GAAoB,EACpBC,IAAyC,EACN;EACnC,SAAS;;EACT,MAAMC,IAAuB,GAAGC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACnE,OAAOC,IAAI,CAACG,MAAM,CAAoC,CAACC,GAAG,EAAEC,OAAO,KAAK;IACtE,IAAIJ,KAAK,CAACC,OAAO,CAACE,GAAG,CAAC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MACrD,OAAOD,GAAG,CAACC,OAAO,CAAC;IACrB,CAAC,MAAM,IACLD,GAAG,KAAK,IAAI,IACZ,OAAOA,GAAG,KAAK,QAAQ,IACtBC,OAAO,IAAwBD,GAAG,EACnC;MACA,OAAQA,GAAG,CACTC,OAAO,CACR;IACH;IACA,OAAOC,SAAS;EAClB,CAAC,EAAER,GAAG,CAAC;AACT;;AAEA;;AAEA,SAASS,OAAOA,CACdT,GAAoB,EACpBC,IAAU,EACVS,KAA4B,EACtB;EACN,SAAS;;EACT,MAAMR,IAAU,GAAGC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACtD,IAAIU,OAA8B,GAAGX,GAAG;EACxC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACW,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IACxC;IACAD,OAAO,GAAGA,OAAmD;IAC7D,IAAI,EAAET,IAAI,CAACU,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;MACzB;MACA,IAAI,OAAOT,IAAI,CAACU,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;QACnCD,OAAO,CAACT,IAAI,CAACU,CAAC,CAAC,CAAC,GAAG,EAAE;MACvB,CAAC,MAAM;QACLD,OAAO,CAACT,IAAI,CAACU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvB;IACF;IACAD,OAAO,GAAGA,OAAO,CAACT,IAAI,CAACU,CAAC,CAAC,CAAC;EAC5B;EAECD,OAAO,CAA8CT,IAAI,CAACA,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC,GAC1EH,KAAK;AACT;AAOA,OAAO,SAASI,kBAAkBA,CAChCC,eAAmC,EACb;EACtB,SAAS;;EACT,OAAOpB,eAAe,CAAuB,CAAC,CAAC,EAAE,MAAM;IACrD,SAAS;;IAET,MAAMqB,OAAO,GAAGA,CACdC,SAA+B,EAC/BC,GAAc,KACF;MACZ,IAAIC,UAAU,GAAG,KAAK;MACtB,MAAMC,cAAoD,GAAG,CAC3D;QAAEV,KAAK,EAAEO,SAAS,CAACF,eAAe;QAAEd,IAAI,EAAE;MAAG,CAAC,CAC/C;MACD,OAAOmB,cAAc,CAACP,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMQ,YAAgD,GACpDD,cAAc,CAACE,GAAG,CAAC,CAAuC;QAC5D,IAAInB,KAAK,CAACC,OAAO,CAACiB,YAAY,CAACX,KAAK,CAAC,EAAE;UACrC,KAAK,IAAIa,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,YAAY,CAACX,KAAK,CAACG,MAAM,EAAEU,KAAK,EAAE,EAAE;YAC9DH,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACa,KAAK,CAAC;cAChCtB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACF,KAAK;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IACL,OAAOF,YAAY,CAACX,KAAK,KAAK,QAAQ,IACtCW,YAAY,CAACX,KAAK,CAACM,OAAO,KAAKR,SAAS,EACxC;UACA;UACA,KAAK,MAAMkB,GAAG,IAAIC,MAAM,CAACzB,IAAI,CAACmB,YAAY,CAACX,KAAK,CAAC,EAAE;YACjDU,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACgB,GAAG,CAAC;cAC9BzB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACC,GAAG;YACpC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL,MAAME,qBAAsC,GAC1CP,YAAY,CAACX,KAAwB;UACvC,IAAIkB,qBAAqB,CAACC,QAAQ,EAAE;YAClC;UACF;UACA,MAAMA,QAAQ,GAAGD,qBAAqB,CAACZ,OAAO,CAC5CY,qBAAqB,EACrBV,GACF,CAAC;UACD,IAAIW,QAAQ,EAAE;YACZD,qBAAqB,CAACC,QAAQ,GAAG,IAAI;YACrC,IAAID,qBAAqB,CAACE,QAAQ,EAAE;cAClCF,qBAAqB,CAACE,QAAQ,CAAC,IAAI,CAAC;YACtC;UACF,CAAC,MAAM;YACLX,UAAU,GAAG,IAAI;UACnB;;UAEA;UACA;UACA,MAAMY,oBAAoB,GAAGlC,eAAe,CAACmC,QAAQ,CACnDX,YAAY,CAACpB,IAAI,CAAC,CAAC,CACrB,CAAC;UAEDQ,OAAO,CACLQ,SAAS,CAACV,OAAO,EACjBc,YAAY,CAACpB,IAAI,EACjB8B,oBAAoB,GAChBjC,YAAY,CAAC8B,qBAAqB,CAACrB,OAAO,CAAC,GAC3CqB,qBAAqB,CAACrB,OAC5B,CAAC;QACH;MACF;MACA,OAAO,CAACY,UAAU;IACpB,CAAC;IAED,MAAMc,OAAO,GAAGA,CACdhB,SAA+B,EAC/BP,KAAyB,EACzBQ,GAAc,EACdgB,iBAAuC,KAC9B;MACT,MAAMd,cAEH,GAAG,CAAC;QAAEV,KAAK,EAAEK,eAAe;QAAEd,IAAI,EAAE;MAAG,CAAC,CAAC;MAC5C,OAAOmB,cAAc,CAACP,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMQ,YAEL,GAAGD,cAAc,CAACE,GAAG,CAAC,CAEtB;QACD,IAAInB,KAAK,CAACC,OAAO,CAACiB,YAAY,CAACX,KAAK,CAAC,EAAE;UACrC,KAAK,IAAIa,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,YAAY,CAACX,KAAK,CAACG,MAAM,EAAEU,KAAK,EAAE,EAAE;YAC9DH,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACa,KAAK,CAAC;cAChCtB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACF,KAAK;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IACL,OAAOF,YAAY,CAACX,KAAK,KAAK,QAAQ,IACtCW,YAAY,CAACX,KAAK,CAACuB,OAAO,KAAKzB,SAAS,EACxC;UACA,KAAK,MAAMkB,GAAG,IAAIC,MAAM,CAACzB,IAAI,CAACmB,YAAY,CAACX,KAAK,CAAC,EAAE;YACjDU,cAAc,CAACI,IAAI,CAAC;cAClBd,KAAK,EAAEW,YAAY,CAACX,KAAK,CAACgB,GAAG,CAAC;cAC9BzB,IAAI,EAAEoB,YAAY,CAACpB,IAAI,CAACwB,MAAM,CAACC,GAAG;YACpC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL,MAAMS,aAAa,GAAGpC,WAAW,CAC/BmC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEnB,eAAe,EAClCM,YAAY,CAACpB,IACf,CAAC;UACD,IAAImC,OAAO,GAAGrC,WAAW,CAACW,KAAK,EAAEW,YAAY,CAACpB,IAAI,CAAC;UACnD,IAAIkC,aAAa,IAAI,CAACC,OAAO,EAAE;YAC7BA,OAAO,GAAID,aAAa,CAAS5B,OAAO;UAC1C;UACA,IAAI6B,OAAO,KAAK5B,SAAS,EAAE;YACzB6B,OAAO,CAACC,IAAI,CACT,yDAAwDjB,YAAY,CAACpB,IAAI,CAACsC,IAAI,CAC7E,GACF,CAAE,EACJ,CAAC;UACH;UACA9B,OAAO,CAACQ,SAAS,CAACV,OAAO,EAAEc,YAAY,CAACpB,IAAI,EAAEmC,OAAO,CAAC;UACtD,IAAII,gBAAiC;UACrC,IACE,OAAOnB,YAAY,CAACX,KAAK,KAAK,QAAQ,IACtC,CAACW,YAAY,CAACX,KAAK,CAACuB,OAAO,EAC3B;YACAO,gBAAgB,GAAG5C,UAAU,CAC3ByB,YAAY,CAACX,KAAK,EAClB;cAAE+B,QAAQ,EAAE;YAAE,CAChB,CAAoB,CAAC,CAAC;YACtBhC,OAAO,CACLQ,SAAS,CAACF,eAAe,EACzBM,YAAY,CAACpB,IAAI,EACjBuC,gBACF,CAAC;UACH,CAAC,MAAM;YACLA,gBAAgB,GAAGnB,YAAY,CAACX,KAAmC;UACrE;UACA8B,gBAAgB,CAACP,OAAO,CACtBO,gBAAgB,EAChBJ,OAAO,EACPlB,GAAG,EACHiB,aACF,CAAC;QACH;MACF;IACF,CAAC;IAED,MAAML,QAAQ,GAAID,QAAiB,IAAW;MAC5C,IAAI,CAACA,QAAQ,EAAE;QACb,MAAMa,iBAAwD,GAAG,CAC/D3B,eAAe,CAChB;QACD,OAAO2B,iBAAiB,CAAC7B,MAAM,GAAG,CAAC,EAAE;UACnC,MAAM2B,gBAAqD,GACzDE,iBAAiB,CAACpB,GAAG,CAAC,CAAwC;UAChE,IAAInB,KAAK,CAACC,OAAO,CAACoC,gBAAgB,CAAC,EAAE;YACnC,KAAK,MAAMG,OAAO,IAAIH,gBAAgB,EAAE;cACtCE,iBAAiB,CAAClB,IAAI,CAACmB,OAAO,CAAC;YACjC;UACF,CAAC,MAAM,IACL,OAAOH,gBAAgB,KAAK,QAAQ,IACpCA,gBAAgB,CAACP,OAAO,KAAKzB,SAAS,EACtC;YACA,KAAK,MAAME,KAAK,IAAIiB,MAAM,CAACiB,MAAM,CAACJ,gBAAgB,CAAC,EAAE;cACnDE,iBAAiB,CAAClB,IAAI,CAACd,KAAK,CAAC;YAC/B;UACF,CAAC,MAAM;YACL,MAAMkB,qBAAsC,GAC1CY,gBAAmC;YACrC,IACE,CAACZ,qBAAqB,CAACC,QAAQ,IAC/BD,qBAAqB,CAACE,QAAQ,EAC9B;cACAF,qBAAqB,CAACE,QAAQ,CAAC,KAAK,CAAC;YACvC;UACF;QACF;MACF;IACF,CAAC;IAED,OAAO;MACLe,aAAa,EAAE,IAAI;MACnB7B,OAAO;MACPiB,OAAO;MACP1B,OAAO,EAAE,CAAC,CAAC;MACXQ,eAAe;MACfe;IACF,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}