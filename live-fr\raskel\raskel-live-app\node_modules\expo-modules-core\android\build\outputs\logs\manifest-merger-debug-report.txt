-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:1:1-14:12
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:1:1-14:12
	package
		INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:2:11-57
	xmlns:android
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:1:11-69
application
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:4:5-12:19
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:5:9-7:89
	android:value
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:7:13-86
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:6:13-79
meta-data#com.facebook.soloader.enabled
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:8:9-11:45
	tools:replace
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:11:13-42
	android:value
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:10:13-33
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:9:13-57
uses-sdk
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml
