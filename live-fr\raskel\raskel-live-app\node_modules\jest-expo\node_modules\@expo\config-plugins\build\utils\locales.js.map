{"version": 3, "file": "locales.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_path", "_warnings", "e", "__esModule", "default", "getResolvedLocalesAsync", "projectRoot", "input", "forPlatform", "locales", "lang", "localeJsonPath", "Object", "entries", "locale", "JsonFile", "readAsync", "path", "join", "addWarningForPlatform", "android", "ios", "rest"], "sources": ["../../src/utils/locales.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport JsonFile, { JSONObject } from '@expo/json-file';\nimport path from 'path';\n\nimport { addWarningForPlatform } from './warnings';\n\nexport type LocaleJson = Record<string, string> & {\n  ios?: Record<string, string>;\n  android?: Record<string, string>;\n};\nexport type ResolvedLocalesJson = Record<string, LocaleJson>;\nexport type ExpoConfigLocales = NonNullable<ExpoConfig['locales']>;\n\nexport async function getResolvedLocalesAsync(\n  projectRoot: string,\n  input: ExpoConfigLocales,\n  forPlatform: 'ios' | 'android'\n): Promise<ResolvedLocalesJson> {\n  const locales: ResolvedLocalesJson = {};\n  for (const [lang, localeJsonPath] of Object.entries(input)) {\n    let locale: JSONObject | null = null;\n    if (typeof localeJsonPath === 'string') {\n      try {\n        locale = await JsonFile.readAsync(path.join(projectRoot, localeJsonPath));\n      } catch {\n        // Add a warning when a json file cannot be parsed.\n        addWarningForPlatform(\n          forPlatform,\n          `locales.${lang}`,\n          `Failed to parse JSON of locale file for language: ${lang}`,\n          'https://docs.expo.dev/guides/localization/#translating-app-metadata'\n        );\n      }\n    } else {\n      // In the off chance that someone defined the locales json in the config, pass it directly to the object.\n      // We do this to make the types more elegant.\n      locale = localeJsonPath;\n    }\n    if (locale) {\n      const { android, ios, ...rest } = { android: {}, ios: {}, ...locale };\n      if (forPlatform === 'ios') {\n        locales[lang] = { ...rest, ...ios };\n      } else {\n        locales[lang] = { ...rest, ...android };\n      }\n    }\n  }\n\n  return locales;\n}\n"], "mappings": ";;;;;;AACA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,UAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,SAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmD,SAAAC,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAS5C,eAAeG,uBAAuBA,CAC3CC,WAAmB,EACnBC,KAAwB,EACxBC,WAA8B,EACA;EAC9B,MAAMC,OAA4B,GAAG,CAAC,CAAC;EACvC,KAAK,MAAM,CAACC,IAAI,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;IAC1D,IAAIO,MAAyB,GAAG,IAAI;IACpC,IAAI,OAAOH,cAAc,KAAK,QAAQ,EAAE;MACtC,IAAI;QACFG,MAAM,GAAG,MAAMC,mBAAQ,CAACC,SAAS,CAACC,eAAI,CAACC,IAAI,CAACZ,WAAW,EAAEK,cAAc,CAAC,CAAC;MAC3E,CAAC,CAAC,MAAM;QACN;QACA,IAAAQ,iCAAqB,EACnBX,WAAW,EACX,WAAWE,IAAI,EAAE,EACjB,qDAAqDA,IAAI,EAAE,EAC3D,qEACF,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA;MACAI,MAAM,GAAGH,cAAc;IACzB;IACA,IAAIG,MAAM,EAAE;MACV,MAAM;QAAEM,OAAO;QAAEC,GAAG;QAAE,GAAGC;MAAK,CAAC,GAAG;QAAEF,OAAO,EAAE,CAAC,CAAC;QAAEC,GAAG,EAAE,CAAC,CAAC;QAAE,GAAGP;MAAO,CAAC;MACrE,IAAIN,WAAW,KAAK,KAAK,EAAE;QACzBC,OAAO,CAACC,IAAI,CAAC,GAAG;UAAE,GAAGY,IAAI;UAAE,GAAGD;QAAI,CAAC;MACrC,CAAC,MAAM;QACLZ,OAAO,CAACC,IAAI,CAAC,GAAG;UAAE,GAAGY,IAAI;UAAE,GAAGF;QAAQ,CAAC;MACzC;IACF;EACF;EAEA,OAAOX,OAAO;AAChB", "ignoreList": []}