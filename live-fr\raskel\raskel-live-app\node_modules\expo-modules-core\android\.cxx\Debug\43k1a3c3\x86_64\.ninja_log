# ninja log v5
1	1561	7802382651878967	CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch	6f3b16db976de2e1
1565	1905	7802382655282886	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o	64b05c5a53fd7a82
1617	1961	7802382655523642	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	c39f1a67786c92e1
1597	1969	7802382655669143	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	63cfa77d1bda7a05
1568	2028	7802382655844604	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	e73458865d9fb812
1574	2052	7802382656050057	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o	69d2e76dcd56fab6
1591	2060	7802382656385777	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	319079968c21cba3
1657	2280	7802382658886890	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	c79a94c265bdf54f
1614	2360	7802382659694017	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	cec5a24ebbda31fb
1576	2374	7802382659754131	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	3a4771f0426a8bcc
1571	2430	7802382660265206	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o	3f6bd8d3afb74fa8
1969	2447	7802382660586006	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	5fa0f56830ba96e2
1663	2503	7802382661217347	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	ff80ad63ff861747
1668	2568	7802382661808763	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	52d2f3050158810e
1673	2631	7802382662545342	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	452ee33506aeab4e
1600	2666	7802382662926313	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	e8338fab1d80663c
1621	2746	7802382663690012	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	24ec8477a89a3d38
1607	2767	7802382663910390	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	76ae1d39b52b477b
1594	2809	7802382664347625	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	4ae662075886b464
1638	2839	7802382664624346	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	1dcce6c0bc597486
1699	2841	7802382664654323	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	29fa5d1c59e95c8a
1648	2890	7802382665155102	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	165f702951166e31
1584	3022	7802382666457614	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	db68edcef9434a31
1630	3032	7802382666558750	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	7962a82ee502d849
2061	3046	7802382666674172	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	d236d5372c15afbe
1961	3073	7802382666949818	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	93b22776c6ae0c17
2053	3142	7802382667697832	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	4714151651b594aa
1634	3159	7802382667878233	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	4c10079743c18d82
1624	3161	7802382667903293	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	912b04697609cd3d
1693	3184	7802382668122079	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	ae828a3db7f1e95f
1678	3342	7802382669699912	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	d473ce52699777da
1687	3407	7802382670296317	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	18b844134a7b540
1627	3467	7802382670957624	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	9f146571d320116
1642	3494	7802382671234177	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	517275dd1e902553
1683	3570	7802382671996696	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	4438cbd962540497
1652	3633	7802382672579438	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	9c0273d5d4c451b0
1588	3642	7802382672659629	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	8091261958c28571
2044	3657	7802382672854946	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o	7cc73d28d11b47dc
1906	3724	7802382673521290	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	f5c10964051973d2
1603	3747	7802382673756687	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	dc10245e80d7b5a7
1611	5800	7802382694046550	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	cf53c752b83d9902
5800	5891	7802382695093828	../../../../build/intermediates/cxx/Debug/43k1a3c3/obj/x86_64/libexpo-modules-core.so	3f12eb8021732ac
0	18	0	clean	17a56fa2c58de3da
6	1489	7802385148979735	CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch	6f3b16db976de2e1
1492	1855	7802385152148931	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o	64b05c5a53fd7a82
1538	1869	7802385152592941	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	c39f1a67786c92e1
1495	1883	7802385152582870	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	e73458865d9fb812
1517	1895	7802385152672987	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	63cfa77d1bda7a05
1501	1903	7802385152978998	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o	69d2e76dcd56fab6
1504	1962	7802385153309270	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	319079968c21cba3
1498	2210	7802385155827854	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	3a4771f0426a8bcc
1596	2273	7802385156662808	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	c79a94c265bdf54f
1489	2336	7802385157000414	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o	3f6bd8d3afb74fa8
1534	2338	7802385157330766	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	cec5a24ebbda31fb
1884	2368	7802385157515867	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	5fa0f56830ba96e2
1581	2472	7802385158647016	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	52d2f3050158810e
1586	2493	7802385158963555	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	ff80ad63ff861747
1521	2592	7802385159974708	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	e8338fab1d80663c
1591	2618	7802385160247434	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	452ee33506aeab4e
1527	2670	7802385160732934	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	76ae1d39b52b477b
1542	2696	7802385161003316	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	24ec8477a89a3d38
1514	2733	7802385161383720	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	4ae662075886b464
1576	2840	7802385162476430	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	165f702951166e31
1570	2866	7802385162721783	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	1dcce6c0bc597486
1855	2945	7802385163514788	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	29fa5d1c59e95c8a
1565	2977	7802385163835393	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	7962a82ee502d849
1963	2996	7802385164000557	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	d236d5372c15afbe
1869	3043	7802385164486053	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	93b22776c6ae0c17
1510	3061	7802385164691282	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	db68edcef9434a31
1903	3062	7802385164691282	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	4714151651b594aa
1550	3119	7802385165272172	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	912b04697609cd3d
1561	3143	7802385165517620	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	4c10079743c18d82
1623	3155	7802385165642786	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	ae828a3db7f1e95f
1606	3312	7802385167211460	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	d473ce52699777da
1555	3391	7802385167977507	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	517275dd1e902553
1546	3424	7802385168338112	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	9f146571d320116
1617	3428	7802385168338112	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	18b844134a7b540
1629	3498	7802385169068899	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	4438cbd962540497
1601	3505	7802385169144069	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	9c0273d5d4c451b0
1612	3628	7802385170340876	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	f5c10964051973d2
1895	3640	7802385170497386	CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o	7cc73d28d11b47dc
1524	3678	7802385170877826	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	dc10245e80d7b5a7
1507	3697	7802385171063100	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	8091261958c28571
1531	6288	7802385196626242	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	cf53c752b83d9902
6289	6394	7802385197917767	../../../../build/intermediates/cxx/Debug/43k1a3c3/obj/x86_64/libexpo-modules-core.so	3f12eb8021732ac
