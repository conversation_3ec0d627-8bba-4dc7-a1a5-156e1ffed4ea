{"version": 3, "names": ["isF<PERSON><PERSON>", "updateProps", "IS_FABRIC", "createViewDescriptorPaper", "screenId", "tag", "name", "createViewDescriptorFabric", "shadowNodeWrapper", "createViewDescriptor", "applyStyleForTopScreen", "screenTransitionConfig", "event", "screenDimensions", "topScreenId", "screenTransition", "topScreenStyle", "computeTopScreenStyle", "topScreenDescriptor", "value", "undefined", "applyStyleForBelowTopScreen", "belowTopScreenId", "belowTopScreenStyle", "computeBelowTopScreenStyle", "belowTopScreenDescriptor", "applyStyle"], "sources": ["styleUpdater.ts"], "sourcesContent": ["'use strict';\nimport { isFabric } from '../PlatformChecker';\nimport updateProps from '../UpdateProps';\nimport type { ShadowNodeWrapper, SharedValue } from '../commonTypes';\nimport type { Descriptor } from '../hook/commonTypes';\nimport type {\n  PanGestureHandlerEventPayload,\n  ScreenTransitionConfig,\n} from './commonTypes';\n\nconst IS_FABRIC = isFabric();\n\nfunction createViewDescriptorPaper(screenId: number | ShadowNodeWrapper) {\n  'worklet';\n  return { tag: screenId, name: 'RCTView' };\n}\nfunction createViewDescriptorFabric(screenId: number | ShadowNodeWrapper) {\n  'worklet';\n  return { shadowNodeWrapper: screenId };\n}\nconst createViewDescriptor = IS_FABRIC\n  ? createViewDescriptorFabric\n  : createViewDescriptorPaper;\n\nfunction applyStyleForTopScreen(\n  screenTransitionConfig: ScreenTransitionConfig,\n  event: PanGestureHandlerEventPayload\n) {\n  'worklet';\n  const { screenDimensions, topScreenId, screenTransition } =\n    screenTransitionConfig;\n  const { topScreenStyle: computeTopScreenStyle } = screenTransition;\n  const topScreenStyle = computeTopScreenStyle(event, screenDimensions);\n  const topScreenDescriptor = {\n    value: [createViewDescriptor(topScreenId)],\n  };\n  updateProps(\n    topScreenDescriptor as SharedValue<Descriptor[]>,\n    topScreenStyle,\n    undefined\n  );\n}\n\nexport function applyStyleForBelowTopScreen(\n  screenTransitionConfig: ScreenTransitionConfig,\n  event: PanGestureHandlerEventPayload\n) {\n  'worklet';\n  const { screenDimensions, belowTopScreenId, screenTransition } =\n    screenTransitionConfig;\n  const { belowTopScreenStyle: computeBelowTopScreenStyle } = screenTransition;\n  const belowTopScreenStyle = computeBelowTopScreenStyle(\n    event,\n    screenDimensions\n  );\n  const belowTopScreenDescriptor = {\n    value: [createViewDescriptor(belowTopScreenId)],\n  };\n  updateProps(\n    belowTopScreenDescriptor as SharedValue<Descriptor[]>,\n    belowTopScreenStyle,\n    undefined\n  );\n}\n\nexport function applyStyle(\n  screenTransitionConfig: ScreenTransitionConfig,\n  event: PanGestureHandlerEventPayload\n) {\n  'worklet';\n  applyStyleForTopScreen(screenTransitionConfig, event);\n  applyStyleForBelowTopScreen(screenTransitionConfig, event);\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,gBAAgB;AAQxC,MAAMC,SAAS,GAAGF,QAAQ,CAAC,CAAC;AAE5B,SAASG,yBAAyBA,CAACC,QAAoC,EAAE;EACvE,SAAS;;EACT,OAAO;IAAEC,GAAG,EAAED,QAAQ;IAAEE,IAAI,EAAE;EAAU,CAAC;AAC3C;AACA,SAASC,0BAA0BA,CAACH,QAAoC,EAAE;EACxE,SAAS;;EACT,OAAO;IAAEI,iBAAiB,EAAEJ;EAAS,CAAC;AACxC;AACA,MAAMK,oBAAoB,GAAGP,SAAS,GAClCK,0BAA0B,GAC1BJ,yBAAyB;AAE7B,SAASO,sBAAsBA,CAC7BC,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACT,MAAM;IAAEC,gBAAgB;IAAEC,WAAW;IAAEC;EAAiB,CAAC,GACvDJ,sBAAsB;EACxB,MAAM;IAAEK,cAAc,EAAEC;EAAsB,CAAC,GAAGF,gBAAgB;EAClE,MAAMC,cAAc,GAAGC,qBAAqB,CAACL,KAAK,EAAEC,gBAAgB,CAAC;EACrE,MAAMK,mBAAmB,GAAG;IAC1BC,KAAK,EAAE,CAACV,oBAAoB,CAACK,WAAW,CAAC;EAC3C,CAAC;EACDb,WAAW,CACTiB,mBAAmB,EACnBF,cAAc,EACdI,SACF,CAAC;AACH;AAEA,OAAO,SAASC,2BAA2BA,CACzCV,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACT,MAAM;IAAEC,gBAAgB;IAAES,gBAAgB;IAAEP;EAAiB,CAAC,GAC5DJ,sBAAsB;EACxB,MAAM;IAAEY,mBAAmB,EAAEC;EAA2B,CAAC,GAAGT,gBAAgB;EAC5E,MAAMQ,mBAAmB,GAAGC,0BAA0B,CACpDZ,KAAK,EACLC,gBACF,CAAC;EACD,MAAMY,wBAAwB,GAAG;IAC/BN,KAAK,EAAE,CAACV,oBAAoB,CAACa,gBAAgB,CAAC;EAChD,CAAC;EACDrB,WAAW,CACTwB,wBAAwB,EACxBF,mBAAmB,EACnBH,SACF,CAAC;AACH;AAEA,OAAO,SAASM,UAAUA,CACxBf,sBAA8C,EAC9CC,KAAoC,EACpC;EACA,SAAS;;EACTF,sBAAsB,CAACC,sBAAsB,EAAEC,KAAK,CAAC;EACrDS,2BAA2B,CAACV,sBAAsB,EAAEC,KAAK,CAAC;AAC5D", "ignoreList": []}