1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.msrfi.liveapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:3-75
11-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:15:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:3-76
14-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.BLUETOOTH" />
15-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:3-65
15-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:5:20-63
16    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
16-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:3-71
16-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:6:20-69
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:3-62
17-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:7:20-60
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:3-74
18-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:8:20-72
19    <uses-permission android:name="android.permission.INTERNET" />
19-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:3-64
19-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:9:20-62
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:3-77
20-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:10:20-75
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:3-77
21-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:11:20-75
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:3-73
22-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:12:20-71
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:3-72
23-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:13:20-70
24    <uses-permission android:name="android.permission.RECORD_AUDIO" />
24-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:3-68
24-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:14:20-66
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:3-63
25-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:16:20-61
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:3-78
26-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:17:20-76
27
28    <queries>
28-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:18:3-24:13
29        <intent>
29-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:19:5-23:14
30            <action android:name="android.intent.action.VIEW" />
30-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
30-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
31
32            <category android:name="android.intent.category.BROWSABLE" />
32-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
32-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
33
34            <data android:scheme="https" />
34-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
34-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
35        </intent>
36
37        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
37-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
37-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
38        <intent>
38-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:10:9-17:18
39            <action android:name="android.intent.action.OPEN_DOCUMENT" />
39-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:11:13-74
39-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:11:21-71
40
41            <category android:name="android.intent.category.DEFAULT" />
41-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
41-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
42            <category android:name="android.intent.category.OPENABLE" />
42-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:13-73
42-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:23-70
43
44            <data android:mimeType="*/*" />
44-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
45        </intent> <!-- Query open documents -->
46        <intent>
46-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
47            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
47-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
47-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
48        </intent>
49        <intent>
49-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
50
51            <!-- Required for picking images from the camera roll if targeting API 30 -->
52            <action android:name="android.media.action.IMAGE_CAPTURE" />
52-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
52-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
53        </intent>
54        <intent>
54-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
55
56            <!-- Required for picking images from the camera if targeting API 30 -->
57            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
57-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
57-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
58        </intent>
59        <intent>
59-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
60            <action android:name="android.intent.action.GET_CONTENT" />
60-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
60-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
61
62            <category android:name="android.intent.category.OPENABLE" />
62-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:13-73
62-->[host.exp.exponent:expo.modules.documentpicker:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\288dc8593dd4586da98a0d2f352fda09\transformed\expo.modules.documentpicker-13.1.6\AndroidManifest.xml:14:23-70
63
64            <data android:mimeType="*/*" />
64-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
65        </intent>
66    </queries>
67
68    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
68-->[:react-native-agora] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-76
68-->[:react-native-agora] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-73
69
70    <permission
70-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
71        android:name="com.msrfi.liveapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
72        android:protectionLevel="signature" />
72-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
73
74    <uses-permission android:name="com.msrfi.liveapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
74-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
74-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
75    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
75-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
75-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
76
77    <application
77-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:3-41:17
78        android:name="com.msrfi.liveapp.MainApplication"
78-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:16-47
79        android:allowBackup="true"
79-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:162-188
80        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
81        android:debuggable="true"
82        android:extractNativeLibs="false"
83        android:icon="@mipmap/ic_launcher"
83-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:81-115
84        android:label="@string/app_name"
84-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:48-80
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:116-161
86        android:supportsRtl="true"
86-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:221-247
87        android:theme="@style/AppTheme"
87-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:25:189-220
88        android:usesCleartextTraffic="true" >
88-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\debug\AndroidManifest.xml:6:18-53
89        <meta-data
89-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:5-83
90            android:name="expo.modules.updates.ENABLED"
90-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:16-59
91            android:value="false" />
91-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:26:60-81
92        <meta-data
92-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:5-105
93            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
93-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:16-80
94            android:value="ALWAYS" />
94-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:27:81-103
95        <meta-data
95-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:5-99
96            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
96-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:16-79
97            android:value="0" />
97-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:28:80-97
98
99        <activity
99-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:5-40:16
100            android:name="com.msrfi.liveapp.MainActivity"
100-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:15-43
101            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
101-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:44-134
102            android:exported="true"
102-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:256-279
103            android:launchMode="singleTask"
103-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:135-166
104            android:screenOrientation="portrait"
104-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:280-316
105            android:theme="@style/Theme.App.SplashScreen"
105-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:210-255
106            android:windowSoftInputMode="adjustResize" >
106-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:29:167-209
107            <intent-filter>
107-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:30:7-33:23
108                <action android:name="android.intent.action.MAIN" />
108-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:9-60
108-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:31:17-58
109
110                <category android:name="android.intent.category.LAUNCHER" />
110-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:9-68
110-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:32:19-66
111            </intent-filter>
112            <intent-filter>
112-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:34:7-39:23
113                <action android:name="android.intent.action.VIEW" />
113-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
113-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
114
115                <category android:name="android.intent.category.DEFAULT" />
115-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
115-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
116                <category android:name="android.intent.category.BROWSABLE" />
116-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
116-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
117
118                <data android:scheme="exp+msrfi-live-app" />
118-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
118-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
119            </intent-filter>
120        </activity>
121        <activity
121-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
122            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
122-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
123            android:exported="true"
123-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
124            android:launchMode="singleTask"
124-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
125            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
125-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
126            <intent-filter>
126-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
127                <action android:name="android.intent.action.VIEW" />
127-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
127-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
129-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
130                <category android:name="android.intent.category.BROWSABLE" />
130-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
130-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
131
132                <data android:scheme="expo-dev-launcher" />
132-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
132-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
133            </intent-filter>
134        </activity>
135        <activity
135-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
136            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
136-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
137            android:screenOrientation="portrait"
137-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
138            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
138-->[:expo-dev-launcher] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
139        <activity
139-->[:expo-dev-menu] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
140            android:name="expo.modules.devmenu.DevMenuActivity"
140-->[:expo-dev-menu] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
141            android:exported="true"
141-->[:expo-dev-menu] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
142            android:launchMode="singleTask"
142-->[:expo-dev-menu] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
143            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
143-->[:expo-dev-menu] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
144            <intent-filter>
144-->[:expo-dev-menu] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
145                <action android:name="android.intent.action.VIEW" />
145-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:7-58
145-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:20:15-56
146
147                <category android:name="android.intent.category.DEFAULT" />
147-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:9-67
147-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:36:19-65
148                <category android:name="android.intent.category.BROWSABLE" />
148-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:7-67
148-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:21:17-65
149
150                <data android:scheme="expo-dev-menu" />
150-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:7-37
150-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\android\app\src\main\AndroidManifest.xml:22:13-35
151            </intent-filter>
152        </activity>
153
154        <meta-data
154-->[:expo-modules-core] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
155            android:name="org.unimodules.core.AppLoader#react-native-headless"
155-->[:expo-modules-core] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
156            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
156-->[:expo-modules-core] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
157        <meta-data
157-->[:expo-modules-core] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
158            android:name="com.facebook.soloader.enabled"
158-->[:expo-modules-core] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
159            android:value="true" />
159-->[:expo-modules-core] C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
160
161        <activity
161-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
162            android:name="com.facebook.react.devsupport.DevSettingsActivity"
162-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
163            android:exported="false" />
163-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
164
165        <provider
165-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
166            android:name="expo.modules.filesystem.FileSystemFileProvider"
166-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
167            android:authorities="com.msrfi.liveapp.FileSystemFileProvider"
167-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
168            android:exported="false"
168-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
169            android:grantUriPermissions="true" >
169-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
170            <meta-data
170-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
171                android:name="android.support.FILE_PROVIDER_PATHS"
171-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
172                android:resource="@xml/file_system_provider_paths" />
172-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
173        </provider>
174
175        <service
175-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
176            android:name="com.google.android.gms.metadata.ModuleDependencies"
176-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
177            android:enabled="false"
177-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
178            android:exported="false" >
178-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
179            <intent-filter>
179-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
180                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
180-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
180-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
181            </intent-filter>
182
183            <meta-data
183-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
184                android:name="photopicker_activity:0:required"
184-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
185                android:value="" />
185-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
186        </service>
187
188        <activity
188-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
189            android:name="com.canhub.cropper.CropImageActivity"
189-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
190            android:exported="true"
190-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
191            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
191-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
192        <provider
192-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
193            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
193-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
194            android:authorities="com.msrfi.liveapp.ImagePickerFileProvider"
194-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
195            android:exported="false"
195-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
196            android:grantUriPermissions="true" >
196-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
197            <meta-data
197-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
198                android:name="android.support.FILE_PROVIDER_PATHS"
198-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
199                android:resource="@xml/image_picker_provider_paths" />
199-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
200        </provider>
201
202        <service
202-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:11:9-14:56
203            android:name="expo.modules.location.services.LocationTaskService"
203-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:12:13-78
204            android:exported="false"
204-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:13:13-37
205            android:foregroundServiceType="location" />
205-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c5ca8463323586cb345100497b452b6\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:14:13-53
206
207        <provider
207-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
208            android:name="com.canhub.cropper.CropFileProvider"
208-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
209            android:authorities="com.msrfi.liveapp.cropper.fileprovider"
209-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
210            android:exported="false"
210-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
211            android:grantUriPermissions="true" >
211-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
212            <meta-data
212-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
213                android:name="android.support.FILE_PROVIDER_PATHS"
213-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
214                android:resource="@xml/library_file_paths" />
214-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
215        </provider>
216
217        <activity
217-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\962ce58a92e31b97c141ac06310bb23c\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
218            android:name="com.google.android.gms.common.api.GoogleApiActivity"
218-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\962ce58a92e31b97c141ac06310bb23c\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
219            android:exported="false"
219-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\962ce58a92e31b97c141ac06310bb23c\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
220            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
220-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\962ce58a92e31b97c141ac06310bb23c\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
221
222        <meta-data
222-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\107af08ab037182b56f33201f0856ef3\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
223            android:name="com.google.android.gms.version"
223-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\107af08ab037182b56f33201f0856ef3\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
224            android:value="@integer/google_play_services_version" />
224-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\107af08ab037182b56f33201f0856ef3\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
225
226        <provider
226-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
227            android:name="androidx.startup.InitializationProvider"
227-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
228            android:authorities="com.msrfi.liveapp.androidx-startup"
228-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
229            android:exported="false" >
229-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
230            <meta-data
230-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
231                android:name="androidx.emoji2.text.EmojiCompatInitializer"
231-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
232                android:value="androidx.startup" />
232-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
233            <meta-data
233-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
234                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
234-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
235                android:value="androidx.startup" />
235-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
236            <meta-data
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
237                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
238                android:value="androidx.startup" />
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
239        </provider>
240
241        <receiver
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
242            android:name="androidx.profileinstaller.ProfileInstallReceiver"
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
243            android:directBootAware="false"
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
244            android:enabled="true"
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
245            android:exported="true"
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
246            android:permission="android.permission.DUMP" >
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
248                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
251                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
254                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
257                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
258            </intent-filter>
259        </receiver>
260
261        <activity
261-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
262            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity"
262-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
263            android:configChanges="screenSize|orientation"
263-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
264            android:screenOrientation="unspecified"
264-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
265            android:theme="@android:style/Theme.Translucent" />
265-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
266
267        <service
267-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
268            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService"
268-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
269            android:foregroundServiceType="mediaProjection" >
269-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0035c38ae63178506de6bc0e3a55e6d\transformed\full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
270        </service>
271    </application>
272
273</manifest>
