{"version": 3, "names": ["useEffect", "ReduceMotion", "ReducedMotionManager", "isReducedMotionEnabledInSystem", "ReducedMotionConfig", "mode", "__DEV__", "console", "warn", "wasEnabled", "jsValue", "System", "setEnabled", "Always", "Never"], "sources": ["ReducedMotionConfig.tsx"], "sourcesContent": ["'use strict';\nimport { useEffect } from 'react';\nimport { ReduceMotion } from '../commonTypes';\nimport {\n  ReducedMotionManager,\n  isReducedMotionEnabledInSystem,\n} from '../ReducedMotion';\n\n/**\n * A component that lets you overwrite default reduce motion behavior globally in your application.\n *\n * @param mode - Determines default reduce motion behavior globally in your application. Configured with {@link ReduceMotion} enum.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/components/ReducedMotionConfig\n */\nexport function ReducedMotionConfig({ mode }: { mode: ReduceMotion }) {\n  useEffect(() => {\n    if (!__DEV__) {\n      return;\n    }\n    console.warn(\n      `[Reanimated] Reduced motion setting is overwritten with mode '${mode}'.`\n    );\n  }, []);\n\n  useEffect(() => {\n    const wasEnabled = ReducedMotionManager.jsValue;\n    switch (mode) {\n      case ReduceMotion.System:\n        ReducedMotionManager.setEnabled(isReducedMotionEnabledInSystem());\n        break;\n      case ReduceMotion.Always:\n        ReducedMotionManager.setEnabled(true);\n        break;\n      case ReduceMotion.Never:\n        ReducedMotionManager.setEnabled(false);\n        break;\n    }\n    return () => {\n      ReducedMotionManager.setEnabled(wasEnabled);\n    };\n  }, [mode]);\n\n  return null;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SACEC,oBAAoB,EACpBC,8BAA8B,QACzB,kBAAkB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAAC;EAAEC;AAA6B,CAAC,EAAE;EACpEL,SAAS,CAAC,MAAM;IACd,IAAI,CAACM,OAAO,EAAE;MACZ;IACF;IACAC,OAAO,CAACC,IAAI,CACT,iEAAgEH,IAAK,IACxE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENL,SAAS,CAAC,MAAM;IACd,MAAMS,UAAU,GAAGP,oBAAoB,CAACQ,OAAO;IAC/C,QAAQL,IAAI;MACV,KAAKJ,YAAY,CAACU,MAAM;QACtBT,oBAAoB,CAACU,UAAU,CAACT,8BAA8B,CAAC,CAAC,CAAC;QACjE;MACF,KAAKF,YAAY,CAACY,MAAM;QACtBX,oBAAoB,CAACU,UAAU,CAAC,IAAI,CAAC;QACrC;MACF,KAAKX,YAAY,CAACa,KAAK;QACrBZ,oBAAoB,CAACU,UAAU,CAAC,KAAK,CAAC;QACtC;IACJ;IACA,OAAO,MAAM;MACXV,oBAAoB,CAACU,UAAU,CAACH,UAAU,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;EAEV,OAAO,IAAI;AACb", "ignoreList": []}