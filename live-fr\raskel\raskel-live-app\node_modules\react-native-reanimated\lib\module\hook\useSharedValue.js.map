{"version": 3, "names": ["useEffect", "useState", "cancelAnimation", "makeMutable", "useSharedValue", "initialValue", "mutable"], "sources": ["useSharedValue.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useState } from 'react';\nimport { cancelAnimation } from '../animation';\nimport type { SharedValue } from '../commonTypes';\nimport { makeMutable } from '../core';\n\n/**\n * Lets you define [shared values](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value) in your components.\n *\n * @param initialValue - The value you want to be initially stored to a `.value` property.\n * @returns A shared value with a single `.value` property initially set to the `initialValue` - {@link SharedValue}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useSharedValue\n */\nexport function useSharedValue<Value>(initialValue: Value): SharedValue<Value> {\n  const [mutable] = useState(() => makeMutable(initialValue));\n  useEffect(() => {\n    return () => {\n      cancelAnimation(mutable);\n    };\n  }, [mutable]);\n  return mutable;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,eAAe,QAAQ,cAAc;AAE9C,SAASC,WAAW,QAAQ,SAAS;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAQC,YAAmB,EAAsB;EAC7E,MAAM,CAACC,OAAO,CAAC,GAAGL,QAAQ,CAAC,MAAME,WAAW,CAACE,YAAY,CAAC,CAAC;EAC3DL,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXE,eAAe,CAACI,OAAO,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAOA,OAAO;AAChB", "ignoreList": []}