{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "withSequence", "_reduceMotionOrFirstAnimation", "_animations", "reduceMotion", "unshift", "length", "console", "warn", "onStart", "animation", "value", "current", "onFrame", "animationIndex", "animations", "map", "a", "result", "finished", "findNextNonReducedMotionAnimationIndex", "index", "callback", "for<PERSON>ach", "sequence", "now", "currentAnim", "nextAnim", "previousAnimation", "anim", "undefined", "currentAnimation", "isHigherOrder"], "sources": ["sequence.ts"], "sourcesContent": ["'use strict';\nimport { defineAnimation, getReduceMotionForAnimation } from './util';\nimport type { NextAnimation, SequenceAnimation } from './commonTypes';\nimport type {\n  Animation,\n  AnimatableValue,\n  AnimationObject,\n  ReduceMotion,\n  Timestamp,\n} from '../commonTypes';\n\n/**\n * Lets you run animations in a sequence.\n *\n * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n * @param animations - Any number of animation objects to be run in a sequence.\n * @returns An [animation object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object) which holds the current state of the animation/\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSequence\n */\nexport function withSequence<T extends AnimatableValue>(\n  _reduceMotion: ReduceMotion,\n  ...animations: T[]\n): T;\n\nexport function withSequence<T extends AnimatableValue>(...animations: T[]): T;\n\nexport function withSequence(\n  _reduceMotionOrFirstAnimation?: ReduceMotion | NextAnimation<AnimationObject>,\n  ..._animations: NextAnimation<AnimationObject>[]\n): Animation<SequenceAnimation> {\n  'worklet';\n  let reduceMotion: ReduceMotion | undefined;\n\n  // the first argument is either a config or an animation\n  // this is done to allow the reduce motion config prop to be optional\n  if (_reduceMotionOrFirstAnimation) {\n    if (typeof _reduceMotionOrFirstAnimation === 'string') {\n      reduceMotion = _reduceMotionOrFirstAnimation as ReduceMotion;\n    } else {\n      _animations.unshift(\n        _reduceMotionOrFirstAnimation as NextAnimation<AnimationObject>\n      );\n    }\n  }\n\n  if (_animations.length === 0) {\n    console.warn('[Reanimated] No animation was provided for the sequence');\n\n    return defineAnimation<SequenceAnimation>(0, () => {\n      'worklet';\n      return {\n        onStart: (animation, value) => (animation.current = value),\n        onFrame: () => true,\n        current: 0,\n        animationIndex: 0,\n        reduceMotion: getReduceMotionForAnimation(reduceMotion),\n      } as SequenceAnimation;\n    });\n  }\n\n  return defineAnimation<SequenceAnimation>(\n    _animations[0] as SequenceAnimation,\n    () => {\n      'worklet';\n\n      const animations = _animations.map((a) => {\n        const result = typeof a === 'function' ? a() : a;\n        result.finished = false;\n        return result;\n      });\n\n      function findNextNonReducedMotionAnimationIndex(index: number) {\n        // the last animation is returned even if reduced motion is enabled,\n        // because we want the sequence to finish at the right spot\n        while (\n          index < animations.length - 1 &&\n          animations[index].reduceMotion\n        ) {\n          index++;\n        }\n\n        return index;\n      }\n\n      const callback = (finished: boolean): void => {\n        if (finished) {\n          // we want to call the callback after every single animation\n          // not after all of them\n          return;\n        }\n        // this is going to be called only if sequence has been cancelled\n        animations.forEach((animation) => {\n          if (typeof animation.callback === 'function' && !animation.finished) {\n            animation.callback(finished);\n          }\n        });\n      };\n\n      function sequence(animation: SequenceAnimation, now: Timestamp): boolean {\n        const currentAnim = animations[animation.animationIndex];\n        const finished = currentAnim.onFrame(currentAnim, now);\n        animation.current = currentAnim.current;\n        if (finished) {\n          // we want to call the callback after every single animation\n          if (currentAnim.callback) {\n            currentAnim.callback(true /* finished */);\n          }\n          currentAnim.finished = true;\n          animation.animationIndex = findNextNonReducedMotionAnimationIndex(\n            animation.animationIndex + 1\n          );\n          if (animation.animationIndex < animations.length) {\n            const nextAnim = animations[animation.animationIndex];\n            nextAnim.onStart(nextAnim, currentAnim.current, now, currentAnim);\n            return false;\n          }\n          return true;\n        }\n        return false;\n      }\n\n      function onStart(\n        animation: SequenceAnimation,\n        value: AnimatableValue,\n        now: Timestamp,\n        previousAnimation: SequenceAnimation\n      ): void {\n        // child animations inherit the setting, unless they already have it defined\n        // they will have it defined only if the user used the `reduceMotion` prop\n        animations.forEach((anim) => {\n          if (anim.reduceMotion === undefined) {\n            anim.reduceMotion = animation.reduceMotion;\n          }\n        });\n        animation.animationIndex = findNextNonReducedMotionAnimationIndex(0);\n\n        if (previousAnimation === undefined) {\n          previousAnimation = animations[\n            animations.length - 1\n          ] as SequenceAnimation;\n        }\n\n        const currentAnimation = animations[animation.animationIndex];\n        currentAnimation.onStart(\n          currentAnimation,\n          value,\n          now,\n          previousAnimation\n        );\n      }\n\n      return {\n        isHigherOrder: true,\n        onFrame: sequence,\n        onStart,\n        animationIndex: 0,\n        current: animations[0].current,\n        callback,\n        reduceMotion: getReduceMotionForAnimation(reduceMotion),\n      } as SequenceAnimation;\n    }\n  );\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,QAAQ;;AAUrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA,OAAO,SAASC,YAAYA,CAC1BC,6BAA6E,EAC7E,GAAGC,WAA6C,EAClB;EAC9B,SAAS;;EACT,IAAIC,YAAsC;;EAE1C;EACA;EACA,IAAIF,6BAA6B,EAAE;IACjC,IAAI,OAAOA,6BAA6B,KAAK,QAAQ,EAAE;MACrDE,YAAY,GAAGF,6BAA6C;IAC9D,CAAC,MAAM;MACLC,WAAW,CAACE,OAAO,CACjBH,6BACF,CAAC;IACH;EACF;EAEA,IAAIC,WAAW,CAACG,MAAM,KAAK,CAAC,EAAE;IAC5BC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;IAEvE,OAAOT,eAAe,CAAoB,CAAC,EAAE,MAAM;MACjD,SAAS;;MACT,OAAO;QACLU,OAAO,EAAEA,CAACC,SAAS,EAAEC,KAAK,KAAMD,SAAS,CAACE,OAAO,GAAGD,KAAM;QAC1DE,OAAO,EAAEA,CAAA,KAAM,IAAI;QACnBD,OAAO,EAAE,CAAC;QACVE,cAAc,EAAE,CAAC;QACjBV,YAAY,EAAEJ,2BAA2B,CAACI,YAAY;MACxD,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,OAAOL,eAAe,CACpBI,WAAW,CAAC,CAAC,CAAC,EACd,MAAM;IACJ,SAAS;;IAET,MAAMY,UAAU,GAAGZ,WAAW,CAACa,GAAG,CAAEC,CAAC,IAAK;MACxC,MAAMC,MAAM,GAAG,OAAOD,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,CAAC,GAAGA,CAAC;MAChDC,MAAM,CAACC,QAAQ,GAAG,KAAK;MACvB,OAAOD,MAAM;IACf,CAAC,CAAC;IAEF,SAASE,sCAAsCA,CAACC,KAAa,EAAE;MAC7D;MACA;MACA,OACEA,KAAK,GAAGN,UAAU,CAACT,MAAM,GAAG,CAAC,IAC7BS,UAAU,CAACM,KAAK,CAAC,CAACjB,YAAY,EAC9B;QACAiB,KAAK,EAAE;MACT;MAEA,OAAOA,KAAK;IACd;IAEA,MAAMC,QAAQ,GAAIH,QAAiB,IAAW;MAC5C,IAAIA,QAAQ,EAAE;QACZ;QACA;QACA;MACF;MACA;MACAJ,UAAU,CAACQ,OAAO,CAAEb,SAAS,IAAK;QAChC,IAAI,OAAOA,SAAS,CAACY,QAAQ,KAAK,UAAU,IAAI,CAACZ,SAAS,CAACS,QAAQ,EAAE;UACnET,SAAS,CAACY,QAAQ,CAACH,QAAQ,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC;IAED,SAASK,QAAQA,CAACd,SAA4B,EAAEe,GAAc,EAAW;MACvE,MAAMC,WAAW,GAAGX,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC;MACxD,MAAMK,QAAQ,GAAGO,WAAW,CAACb,OAAO,CAACa,WAAW,EAAED,GAAG,CAAC;MACtDf,SAAS,CAACE,OAAO,GAAGc,WAAW,CAACd,OAAO;MACvC,IAAIO,QAAQ,EAAE;QACZ;QACA,IAAIO,WAAW,CAACJ,QAAQ,EAAE;UACxBI,WAAW,CAACJ,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;QAC3C;QACAI,WAAW,CAACP,QAAQ,GAAG,IAAI;QAC3BT,SAAS,CAACI,cAAc,GAAGM,sCAAsC,CAC/DV,SAAS,CAACI,cAAc,GAAG,CAC7B,CAAC;QACD,IAAIJ,SAAS,CAACI,cAAc,GAAGC,UAAU,CAACT,MAAM,EAAE;UAChD,MAAMqB,QAAQ,GAAGZ,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC;UACrDa,QAAQ,CAAClB,OAAO,CAACkB,QAAQ,EAAED,WAAW,CAACd,OAAO,EAAEa,GAAG,EAAEC,WAAW,CAAC;UACjE,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd;IAEA,SAASjB,OAAOA,CACdC,SAA4B,EAC5BC,KAAsB,EACtBc,GAAc,EACdG,iBAAoC,EAC9B;MACN;MACA;MACAb,UAAU,CAACQ,OAAO,CAAEM,IAAI,IAAK;QAC3B,IAAIA,IAAI,CAACzB,YAAY,KAAK0B,SAAS,EAAE;UACnCD,IAAI,CAACzB,YAAY,GAAGM,SAAS,CAACN,YAAY;QAC5C;MACF,CAAC,CAAC;MACFM,SAAS,CAACI,cAAc,GAAGM,sCAAsC,CAAC,CAAC,CAAC;MAEpE,IAAIQ,iBAAiB,KAAKE,SAAS,EAAE;QACnCF,iBAAiB,GAAGb,UAAU,CAC5BA,UAAU,CAACT,MAAM,GAAG,CAAC,CACD;MACxB;MAEA,MAAMyB,gBAAgB,GAAGhB,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC;MAC7DiB,gBAAgB,CAACtB,OAAO,CACtBsB,gBAAgB,EAChBpB,KAAK,EACLc,GAAG,EACHG,iBACF,CAAC;IACH;IAEA,OAAO;MACLI,aAAa,EAAE,IAAI;MACnBnB,OAAO,EAAEW,QAAQ;MACjBf,OAAO;MACPK,cAAc,EAAE,CAAC;MACjBF,OAAO,EAAEG,UAAU,CAAC,CAAC,CAAC,CAACH,OAAO;MAC9BU,QAAQ;MACRlB,YAAY,EAAEJ,2BAA2B,CAACI,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH", "ignoreList": []}