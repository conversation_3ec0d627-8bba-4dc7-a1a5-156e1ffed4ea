{"version": 3, "names": ["Animations", "LayoutAnimationType", "createAnimationWithInitialValues", "createCustomKeyFrameAnimation", "getProcessedConfig", "handleExitingAnimation", "handleLayoutTransition", "maybeModifyStyleForKeyframe", "setElementAnimation", "areDOMRectsEqual", "Keyframe", "makeElementVisible", "EasingNameSymbol", "chooseConfig", "animationType", "props", "config", "ENTERING", "entering", "EXITING", "exiting", "LAYOUT", "layout", "checkUndefinedAnimationFail", "initialAnimationName", "needsCustomization", "console", "warn", "maybeReportOverwrittenProperties", "keyframe", "styles", "propertyRegex", "animationProperties", "Set", "match", "matchAll", "add", "commonProperties", "Array", "from", "filter", "style", "has", "length", "join", "chooseAction", "animationConfig", "element", "transitionData", "reversed", "tryGetAnimationConfig", "isLayoutTransition", "isCustomKeyframe", "hasInitialValues", "initialValues", "undefined", "animationName", "definitions", "presetName", "constructor", "shouldFail", "keyframeTimestamps", "Object", "keys", "includes", "startWebLayoutAnimation", "tryActivateLayoutTransition", "snapshot", "_enteringV", "_exitingV", "_easingXV", "_easingYV", "rect", "getBoundingClientRect", "enteringAnimation", "enteringV", "exitingAnimation", "exitingV", "translateX", "x", "translateY", "y", "scaleX", "width", "scaleY", "height", "easingX", "easingXV", "easingY", "easingYV"], "sources": ["animationsManager.ts"], "sourcesContent": ["'use strict';\n\nimport type {\n  AnimationConfig,\n  AnimationNames,\n  CustomConfig,\n  InitialValuesStyleProps,\n  KeyframeDefinitions,\n} from './config';\nimport { Animations } from './config';\nimport type {\n  AnimatedComponentProps,\n  LayoutAnimationStaticContext,\n} from '../../createAnimatedComponent/commonTypes';\nimport { LayoutAnimationType } from '../animationBuilder/commonTypes';\nimport {\n  createAnimationWithInitialValues,\n  createCustomKeyFrameAnimation,\n} from './createAnimation';\nimport {\n  getProcessedConfig,\n  handleExitingAnimation,\n  handleLayoutTransition,\n  maybeModifyStyleForKeyframe,\n  setElementAnimation,\n} from './componentUtils';\nimport { areDOMRectsEqual } from './domUtils';\nimport type { TransitionData } from './animationParser';\nimport { Keyframe } from '../animationBuilder';\nimport { makeElementVisible } from './componentStyle';\nimport { EasingNameSymbol } from '../../Easing';\nimport type { ReanimatedHTMLElement } from '../../js-reanimated';\n\nfunction chooseConfig<ComponentProps extends Record<string, unknown>>(\n  animationType: LayoutAnimationType,\n  props: Readonly<AnimatedComponentProps<ComponentProps>>\n) {\n  const config =\n    animationType === LayoutAnimationType.ENTERING\n      ? props.entering\n      : animationType === LayoutAnimationType.EXITING\n      ? props.exiting\n      : animationType === LayoutAnimationType.LAYOUT\n      ? props.layout\n      : null;\n\n  return config;\n}\n\nfunction checkUndefinedAnimationFail(\n  initialAnimationName: string,\n  needsCustomization: boolean\n) {\n  // This prevents crashes if we try to set animations that are not defined.\n  // We don't care about layout transitions or custom keyframes since they're created dynamically\n  if (initialAnimationName in Animations || needsCustomization) {\n    return false;\n  }\n\n  console.warn(\n    \"[Reanimated] Couldn't load entering/exiting animation. Current version supports only predefined animations with modifiers: duration, delay, easing, randomizeDelay, withCallback, reducedMotion.\"\n  );\n\n  return true;\n}\n\nfunction maybeReportOverwrittenProperties(\n  keyframe: string,\n  styles: CSSStyleDeclaration\n) {\n  const propertyRegex = /([a-zA-Z-]+)(?=:)/g;\n  const animationProperties = new Set();\n\n  for (const match of keyframe.matchAll(propertyRegex)) {\n    animationProperties.add(match[1]);\n  }\n\n  const commonProperties = Array.from(styles).filter((style) =>\n    animationProperties.has(style)\n  );\n\n  if (commonProperties.length === 0) {\n    return;\n  }\n\n  console.warn(\n    `[Reanimated] ${\n      commonProperties.length === 1 ? 'Property' : 'Properties'\n    } [${commonProperties.join(\n      ', '\n    )}] may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`\n  );\n}\n\nfunction chooseAction(\n  animationType: LayoutAnimationType,\n  animationConfig: AnimationConfig,\n  element: ReanimatedHTMLElement,\n  transitionData: TransitionData\n) {\n  switch (animationType) {\n    case LayoutAnimationType.ENTERING:\n      setElementAnimation(element, animationConfig, true);\n      break;\n    case LayoutAnimationType.LAYOUT:\n      transitionData.reversed = animationConfig.reversed;\n      handleLayoutTransition(element, animationConfig, transitionData);\n      break;\n    case LayoutAnimationType.EXITING:\n      handleExitingAnimation(element, animationConfig);\n      break;\n  }\n}\n\nfunction tryGetAnimationConfig<ComponentProps extends Record<string, unknown>>(\n  props: Readonly<AnimatedComponentProps<ComponentProps>>,\n  animationType: LayoutAnimationType\n) {\n  const config = chooseConfig(animationType, props);\n  if (!config) {\n    return null;\n  }\n\n  type ConstructorWithStaticContext = LayoutAnimationStaticContext &\n    typeof config.constructor;\n\n  const isLayoutTransition = animationType === LayoutAnimationType.LAYOUT;\n  const isCustomKeyframe = config instanceof Keyframe;\n  const hasInitialValues = (config as CustomConfig).initialValues !== undefined;\n\n  let animationName;\n\n  if (isCustomKeyframe) {\n    animationName = createCustomKeyFrameAnimation(\n      (config as CustomConfig).definitions as KeyframeDefinitions\n    );\n  } else if (typeof config === 'function') {\n    animationName = config.presetName;\n  } else {\n    animationName = (config.constructor as ConstructorWithStaticContext)\n      .presetName;\n  }\n\n  if (hasInitialValues) {\n    animationName = createAnimationWithInitialValues(\n      animationName,\n      (config as CustomConfig).initialValues as InitialValuesStyleProps\n    );\n  }\n\n  const shouldFail = checkUndefinedAnimationFail(\n    animationName,\n    isLayoutTransition || isCustomKeyframe || hasInitialValues\n  );\n\n  if (shouldFail) {\n    return null;\n  }\n\n  if (isCustomKeyframe) {\n    const keyframeTimestamps = Object.keys(\n      (config as CustomConfig).definitions as KeyframeDefinitions\n    );\n\n    if (\n      !(keyframeTimestamps.includes('100') || keyframeTimestamps.includes('to'))\n    ) {\n      console.warn(\n        `[Reanimated] Neither '100' nor 'to' was specified in Keyframe definition. This may result in wrong final position of your component. One possible solution is to duplicate last timestamp in definition as '100' (or 'to')`\n      );\n    }\n  }\n\n  const animationConfig = getProcessedConfig(\n    animationName,\n    animationType,\n    config as CustomConfig\n  );\n\n  return animationConfig;\n}\n\nexport function startWebLayoutAnimation<\n  ComponentProps extends Record<string, unknown>\n>(\n  props: Readonly<AnimatedComponentProps<ComponentProps>>,\n  element: ReanimatedHTMLElement,\n  animationType: LayoutAnimationType,\n  transitionData?: TransitionData\n) {\n  const animationConfig = tryGetAnimationConfig(props, animationType);\n\n  maybeModifyStyleForKeyframe(element, props.entering as CustomConfig);\n\n  if ((animationConfig?.animationName as AnimationNames) in Animations) {\n    maybeReportOverwrittenProperties(\n      Animations[animationConfig?.animationName as AnimationNames].style,\n      element.style\n    );\n  }\n\n  if (animationConfig) {\n    chooseAction(\n      animationType,\n      animationConfig,\n      element,\n      transitionData as TransitionData\n    );\n  } else {\n    makeElementVisible(element, 0);\n  }\n}\n\nexport function tryActivateLayoutTransition<\n  ComponentProps extends Record<string, unknown>\n>(\n  props: Readonly<AnimatedComponentProps<ComponentProps>>,\n  element: ReanimatedHTMLElement,\n  snapshot: DOMRect\n) {\n  if (!props.layout) {\n    return;\n  }\n\n  const rect = element.getBoundingClientRect();\n\n  if (areDOMRectsEqual(rect, snapshot)) {\n    return;\n  }\n\n  const enteringAnimation = (props.layout as CustomConfig).enteringV\n    ?.presetName;\n  const exitingAnimation = (props.layout as CustomConfig).exitingV?.presetName;\n\n  const transitionData: TransitionData = {\n    translateX: snapshot.x - rect.x,\n    translateY: snapshot.y - rect.y,\n    scaleX: snapshot.width / rect.width,\n    scaleY: snapshot.height / rect.height,\n    reversed: false, // This field is used only in `SequencedTransition`, so by default it will be false\n    easingX:\n      (props.layout as CustomConfig).easingXV?.[EasingNameSymbol] ?? 'ease',\n    easingY:\n      (props.layout as CustomConfig).easingYV?.[EasingNameSymbol] ?? 'ease',\n    entering: enteringAnimation,\n    exiting: exitingAnimation,\n  };\n\n  startWebLayoutAnimation(\n    props,\n    element,\n    LayoutAnimationType.LAYOUT,\n    transitionData\n  );\n}\n"], "mappings": "AAAA,YAAY;;AASZ,SAASA,UAAU,QAAQ,UAAU;AAKrC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SACEC,gCAAgC,EAChCC,6BAA6B,QACxB,mBAAmB;AAC1B,SACEC,kBAAkB,EAClBC,sBAAsB,EACtBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,mBAAmB,QACd,kBAAkB;AACzB,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,SAASC,gBAAgB,QAAQ,cAAc;AAG/C,SAASC,YAAYA,CACnBC,aAAkC,EAClCC,KAAuD,EACvD;EACA,MAAMC,MAAM,GACVF,aAAa,KAAKb,mBAAmB,CAACgB,QAAQ,GAC1CF,KAAK,CAACG,QAAQ,GACdJ,aAAa,KAAKb,mBAAmB,CAACkB,OAAO,GAC7CJ,KAAK,CAACK,OAAO,GACbN,aAAa,KAAKb,mBAAmB,CAACoB,MAAM,GAC5CN,KAAK,CAACO,MAAM,GACZ,IAAI;EAEV,OAAON,MAAM;AACf;AAEA,SAASO,2BAA2BA,CAClCC,oBAA4B,EAC5BC,kBAA2B,EAC3B;EACA;EACA;EACA,IAAID,oBAAoB,IAAIxB,UAAU,IAAIyB,kBAAkB,EAAE;IAC5D,OAAO,KAAK;EACd;EAEAC,OAAO,CAACC,IAAI,CACV,kMACF,CAAC;EAED,OAAO,IAAI;AACb;AAEA,SAASC,gCAAgCA,CACvCC,QAAgB,EAChBC,MAA2B,EAC3B;EACA,MAAMC,aAAa,GAAG,oBAAoB;EAC1C,MAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAErC,KAAK,MAAMC,KAAK,IAAIL,QAAQ,CAACM,QAAQ,CAACJ,aAAa,CAAC,EAAE;IACpDC,mBAAmB,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,MAAMG,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,CAAEC,KAAK,IACvDT,mBAAmB,CAACU,GAAG,CAACD,KAAK,CAC/B,CAAC;EAED,IAAIJ,gBAAgB,CAACM,MAAM,KAAK,CAAC,EAAE;IACjC;EACF;EAEAjB,OAAO,CAACC,IAAI,CACT,gBACCU,gBAAgB,CAACM,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAC9C,KAAIN,gBAAgB,CAACO,IAAI,CACxB,IACF,CAAE,6IACJ,CAAC;AACH;AAEA,SAASC,YAAYA,CACnB/B,aAAkC,EAClCgC,eAAgC,EAChCC,OAA8B,EAC9BC,cAA8B,EAC9B;EACA,QAAQlC,aAAa;IACnB,KAAKb,mBAAmB,CAACgB,QAAQ;MAC/BT,mBAAmB,CAACuC,OAAO,EAAED,eAAe,EAAE,IAAI,CAAC;MACnD;IACF,KAAK7C,mBAAmB,CAACoB,MAAM;MAC7B2B,cAAc,CAACC,QAAQ,GAAGH,eAAe,CAACG,QAAQ;MAClD3C,sBAAsB,CAACyC,OAAO,EAAED,eAAe,EAAEE,cAAc,CAAC;MAChE;IACF,KAAK/C,mBAAmB,CAACkB,OAAO;MAC9Bd,sBAAsB,CAAC0C,OAAO,EAAED,eAAe,CAAC;MAChD;EACJ;AACF;AAEA,SAASI,qBAAqBA,CAC5BnC,KAAuD,EACvDD,aAAkC,EAClC;EACA,MAAME,MAAM,GAAGH,YAAY,CAACC,aAAa,EAAEC,KAAK,CAAC;EACjD,IAAI,CAACC,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAKA,MAAMmC,kBAAkB,GAAGrC,aAAa,KAAKb,mBAAmB,CAACoB,MAAM;EACvE,MAAM+B,gBAAgB,GAAGpC,MAAM,YAAYN,QAAQ;EACnD,MAAM2C,gBAAgB,GAAIrC,MAAM,CAAkBsC,aAAa,KAAKC,SAAS;EAE7E,IAAIC,aAAa;EAEjB,IAAIJ,gBAAgB,EAAE;IACpBI,aAAa,GAAGrD,6BAA6B,CAC1Ca,MAAM,CAAkByC,WAC3B,CAAC;EACH,CAAC,MAAM,IAAI,OAAOzC,MAAM,KAAK,UAAU,EAAE;IACvCwC,aAAa,GAAGxC,MAAM,CAAC0C,UAAU;EACnC,CAAC,MAAM;IACLF,aAAa,GAAIxC,MAAM,CAAC2C,WAAW,CAChCD,UAAU;EACf;EAEA,IAAIL,gBAAgB,EAAE;IACpBG,aAAa,GAAGtD,gCAAgC,CAC9CsD,aAAa,EACZxC,MAAM,CAAkBsC,aAC3B,CAAC;EACH;EAEA,MAAMM,UAAU,GAAGrC,2BAA2B,CAC5CiC,aAAa,EACbL,kBAAkB,IAAIC,gBAAgB,IAAIC,gBAC5C,CAAC;EAED,IAAIO,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIR,gBAAgB,EAAE;IACpB,MAAMS,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CACnC/C,MAAM,CAAkByC,WAC3B,CAAC;IAED,IACE,EAAEI,kBAAkB,CAACG,QAAQ,CAAC,KAAK,CAAC,IAAIH,kBAAkB,CAACG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAC1E;MACAtC,OAAO,CAACC,IAAI,CACT,4NACH,CAAC;IACH;EACF;EAEA,MAAMmB,eAAe,GAAG1C,kBAAkB,CACxCoD,aAAa,EACb1C,aAAa,EACbE,MACF,CAAC;EAED,OAAO8B,eAAe;AACxB;AAEA,OAAO,SAASmB,uBAAuBA,CAGrClD,KAAuD,EACvDgC,OAA8B,EAC9BjC,aAAkC,EAClCkC,cAA+B,EAC/B;EACA,MAAMF,eAAe,GAAGI,qBAAqB,CAACnC,KAAK,EAAED,aAAa,CAAC;EAEnEP,2BAA2B,CAACwC,OAAO,EAAEhC,KAAK,CAACG,QAAwB,CAAC;EAEpE,IAAI,CAAC4B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEU,aAAa,KAAuBxD,UAAU,EAAE;IACpE4B,gCAAgC,CAC9B5B,UAAU,CAAC8C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEU,aAAa,CAAmB,CAACf,KAAK,EAClEM,OAAO,CAACN,KACV,CAAC;EACH;EAEA,IAAIK,eAAe,EAAE;IACnBD,YAAY,CACV/B,aAAa,EACbgC,eAAe,EACfC,OAAO,EACPC,cACF,CAAC;EACH,CAAC,MAAM;IACLrC,kBAAkB,CAACoC,OAAO,EAAE,CAAC,CAAC;EAChC;AACF;AAEA,OAAO,SAASmB,2BAA2BA,CAGzCnD,KAAuD,EACvDgC,OAA8B,EAC9BoB,QAAiB,EACjB;EAAA,IAAAC,UAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA;EACA,IAAI,CAACxD,KAAK,CAACO,MAAM,EAAE;IACjB;EACF;EAEA,MAAMkD,IAAI,GAAGzB,OAAO,CAAC0B,qBAAqB,CAAC,CAAC;EAE5C,IAAIhE,gBAAgB,CAAC+D,IAAI,EAAEL,QAAQ,CAAC,EAAE;IACpC;EACF;EAEA,MAAMO,iBAAiB,IAAAN,UAAA,GAAIrD,KAAK,CAACO,MAAM,CAAkBqD,SAAS,cAAAP,UAAA,uBAAxCA,UAAA,CACtBV,UAAU;EACd,MAAMkB,gBAAgB,IAAAP,SAAA,GAAItD,KAAK,CAACO,MAAM,CAAkBuD,QAAQ,cAAAR,SAAA,uBAAvCA,SAAA,CAAyCX,UAAU;EAE5E,MAAMV,cAA8B,GAAG;IACrC8B,UAAU,EAAEX,QAAQ,CAACY,CAAC,GAAGP,IAAI,CAACO,CAAC;IAC/BC,UAAU,EAAEb,QAAQ,CAACc,CAAC,GAAGT,IAAI,CAACS,CAAC;IAC/BC,MAAM,EAAEf,QAAQ,CAACgB,KAAK,GAAGX,IAAI,CAACW,KAAK;IACnCC,MAAM,EAAEjB,QAAQ,CAACkB,MAAM,GAAGb,IAAI,CAACa,MAAM;IACrCpC,QAAQ,EAAE,KAAK;IAAE;IACjBqC,OAAO,EACL,EAAAhB,SAAA,GAACvD,KAAK,CAACO,MAAM,CAAkBiE,QAAQ,cAAAjB,SAAA,uBAAvCA,SAAA,CAA0C1D,gBAAgB,CAAC,KAAI,MAAM;IACvE4E,OAAO,EACL,EAAAjB,SAAA,GAACxD,KAAK,CAACO,MAAM,CAAkBmE,QAAQ,cAAAlB,SAAA,uBAAvCA,SAAA,CAA0C3D,gBAAgB,CAAC,KAAI,MAAM;IACvEM,QAAQ,EAAEwD,iBAAiB;IAC3BtD,OAAO,EAAEwD;EACX,CAAC;EAEDX,uBAAuB,CACrBlD,KAAK,EACLgC,OAAO,EACP9C,mBAAmB,CAACoB,MAAM,EAC1B2B,cACF,CAAC;AACH", "ignoreList": []}