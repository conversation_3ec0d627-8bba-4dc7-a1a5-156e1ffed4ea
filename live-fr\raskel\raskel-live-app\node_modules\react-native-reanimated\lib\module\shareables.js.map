{"version": 3, "names": ["NativeReanimatedModule", "isWorkletFunction", "shouldBeUseWeb", "registerWorkletStackDetails", "jsVersion", "shareableMappingCache", "shareableMappingFlag", "SHOULD_BE_USE_WEB", "MAGIC_KEY", "isHostObject", "value", "isPlainJSObject", "object", "Object", "getPrototypeOf", "prototype", "INACCESSIBLE_OBJECT", "__init", "Proxy", "get", "_", "prop", "Error", "String", "set", "VALID_ARRAY_VIEWS_NAMES", "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD", "processedObjectAtThresholdDepth", "makeShareableCloneRecursive", "shouldPersistRemote", "depth", "undefined", "type", "isTypeObject", "isTypeFunction", "cached", "toAdapt", "Array", "isArray", "map", "element", "freezeObjectIfDev", "__workletContextObjectFactory", "workletContextObjectFactory", "handle", "__DEV__", "babelVersion", "__initData", "version", "getWorkletCode", "__workletHash", "__stackDetails", "key", "entries", "RegExp", "pattern", "source", "flags", "name", "message", "stack", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "typeName", "constructor", "includes", "global", "inaccessibleObject", "adapted", "makeShareableClone", "WORKLET_CODE_THRESHOLD", "_value$__initData", "code", "length", "substring", "isRemoteFunction", "__remoteFunction", "for<PERSON>ach", "descriptor", "getOwnPropertyDescriptor", "configurable", "defineProperty", "console", "warn", "preventExtensions", "makeShareableCloneOnUIRecursive", "cloneRecursive", "_makeShareableClone", "makeShareableJS", "makeShareableNative", "makeShareable"], "sources": ["shareables.ts"], "sourcesContent": ["'use strict';\nimport NativeReanimatedModule from './NativeReanimated';\nimport { isWorkletFunction } from './commonTypes';\nimport type {\n  ShareableRef,\n  FlatShareableRef,\n  WorkletFunction,\n} from './commonTypes';\nimport { shouldBeUseWeb } from './PlatformChecker';\nimport { registerWorkletStackDetails } from './errors';\nimport { jsVersion } from './platform-specific/jsVersion';\nimport {\n  shareableMappingCache,\n  shareableMappingFlag,\n} from './shareableMappingCache';\n\n// for web/chrome debugger/jest environments this file provides a stub implementation\n// where no shareable references are used. Instead, the objects themselves are used\n// instead of shareable references, because of the fact that we don't have to deal with\n// running the code on separate VMs.\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\nconst MAGIC_KEY = 'REANIMATED_MAGIC_KEY';\n\nfunction isHostObject(value: NonNullable<object>) {\n  'worklet';\n  // We could use JSI to determine whether an object is a host object, however\n  // the below workaround works well and is way faster than an additional JSI call.\n  // We use the fact that host objects have broken implementation of `hasOwnProperty`\n  // and hence return true for all `in` checks regardless of the key we ask for.\n  return MAGIC_KEY in value;\n}\n\nfunction isPlainJSObject(object: object) {\n  return Object.getPrototypeOf(object) === Object.prototype;\n}\n\n// The below object is used as a replacement for objects that cannot be transferred\n// as shareable values. In makeShareableCloneRecursive we detect if an object is of\n// a plain Object.prototype and only allow such objects to be transferred. This lets\n// us avoid all sorts of react internals from leaking into the UI runtime. To make it\n// possible to catch errors when someone actually tries to access such object on the UI\n// runtime, we use the below Proxy object which is instantiated on the UI runtime and\n// throws whenever someone tries to access its fields.\nconst INACCESSIBLE_OBJECT = {\n  __init: () => {\n    'worklet';\n    return new Proxy(\n      {},\n      {\n        get: (_: unknown, prop: string | symbol) => {\n          if (\n            prop === '_isReanimatedSharedValue' ||\n            prop === '__remoteFunction'\n          ) {\n            // not very happy about this check here, but we need to allow for\n            // \"inaccessible\" objects to be tested with isSharedValue check\n            // as it is being used in the mappers when extracting inputs recursively\n            // as well as with isRemoteFunction when cloning objects recursively.\n            // Apparently we can't check if a key exists there as HostObjects always\n            // return true for such tests, so the only possibility for us is to\n            // actually access that key and see if it is set to true. We therefore\n            // need to allow for this key to be accessed here.\n            return false;\n          }\n          throw new Error(\n            `[Reanimated] Trying to access property \\`${String(\n              prop\n            )}\\` of an object which cannot be sent to the UI runtime.`\n          );\n        },\n        set: () => {\n          throw new Error(\n            '[Reanimated] Trying to write to an object which cannot be sent to the UI runtime.'\n          );\n        },\n      }\n    );\n  },\n};\n\nconst VALID_ARRAY_VIEWS_NAMES = [\n  'Int8Array',\n  'Uint8Array',\n  'Uint8ClampedArray',\n  'Int16Array',\n  'Uint16Array',\n  'Int32Array',\n  'Uint32Array',\n  'Float32Array',\n  'Float64Array',\n  'BigInt64Array',\n  'BigUint64Array',\n  'DataView',\n];\n\nconst DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD = 30;\n// Below variable stores object that we process in makeShareableCloneRecursive at the specified depth.\n// We use it to check if later on the function reenters with the same object\nlet processedObjectAtThresholdDepth: unknown;\n\nexport function makeShareableCloneRecursive<T>(\n  value: any,\n  shouldPersistRemote = false,\n  depth = 0\n): ShareableRef<T> {\n  if (SHOULD_BE_USE_WEB) {\n    return value;\n  }\n  if (depth >= DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD) {\n    // if we reach certain recursion depth we suspect that we are dealing with a cyclic object.\n    // this type of objects are not supported and cannot be transferred as shareable, so we\n    // implement a simple detection mechanism that remembers the value at a given depth and\n    // tests whether we try reenter this method later on with the same value. If that happens\n    // we throw an appropriate error.\n    if (depth === DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD) {\n      processedObjectAtThresholdDepth = value;\n    } else if (value === processedObjectAtThresholdDepth) {\n      throw new Error(\n        '[Reanimated] Trying to convert a cyclic object to a shareable. This is not supported.'\n      );\n    }\n  } else {\n    processedObjectAtThresholdDepth = undefined;\n  }\n  // This one actually may be worth to be moved to c++, we also need similar logic to run on the UI thread\n  const type = typeof value;\n  const isTypeObject = type === 'object';\n  const isTypeFunction = type === 'function';\n  if ((isTypeObject || isTypeFunction) && value !== null) {\n    const cached = shareableMappingCache.get(value);\n    if (cached === shareableMappingFlag) {\n      return value;\n    } else if (cached !== undefined) {\n      return cached as ShareableRef<T>;\n    } else {\n      let toAdapt: any;\n      if (Array.isArray(value)) {\n        toAdapt = value.map((element) =>\n          makeShareableCloneRecursive(element, shouldPersistRemote, depth + 1)\n        );\n        freezeObjectIfDev(value);\n      } else if (isTypeFunction && !isWorkletFunction(value)) {\n        // this is a remote function\n        toAdapt = value;\n        freezeObjectIfDev(value);\n      } else if (isHostObject(value)) {\n        // for host objects we pass the reference to the object as shareable and\n        // then recreate new host object wrapping the same instance on the UI thread.\n        // there is no point of iterating over keys as we do for regular objects.\n        toAdapt = value;\n      } else if (\n        isPlainJSObject(value) &&\n        value.__workletContextObjectFactory\n      ) {\n        const workletContextObjectFactory = value.__workletContextObjectFactory;\n        const handle = makeShareableCloneRecursive({\n          __init: () => {\n            'worklet';\n            return workletContextObjectFactory();\n          },\n        });\n        shareableMappingCache.set(value, handle);\n        return handle as ShareableRef<T>;\n      } else if (isPlainJSObject(value) || isTypeFunction) {\n        toAdapt = {};\n        if (isWorkletFunction(value)) {\n          if (__DEV__) {\n            const babelVersion = value.__initData.version;\n            if (babelVersion !== undefined && babelVersion !== jsVersion) {\n              throw new Error(`[Reanimated] Mismatch between JavaScript code version and Reanimated Babel plugin version (${jsVersion} vs. ${babelVersion}).        \nSee \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#mismatch-between-javascript-code-version-and-reanimated-babel-plugin-version\\` for more details.\nOffending code was: \\`${getWorkletCode(value)}\\``);\n            }\n            registerWorkletStackDetails(\n              value.__workletHash,\n              value.__stackDetails!\n            );\n          }\n          if (value.__stackDetails) {\n            // `Error` type of value cannot be copied to the UI thread, so we\n            // remove it after we handled it in dev mode or delete it to ignore it in production mode.\n            // Not removing this would cause an infinite loop in production mode and it just\n            // seems more elegant to handle it this way.\n            delete value.__stackDetails;\n          }\n          // to save on transferring static __initData field of worklet structure\n          // we request shareable value to persist its UI counterpart. This means\n          // that the __initData field that contains long strings representing the\n          // worklet code, source map, and location, will always be\n          // serialized/deserialized once.\n          toAdapt.__initData = makeShareableCloneRecursive(\n            value.__initData,\n            true,\n            depth + 1\n          );\n        }\n\n        for (const [key, element] of Object.entries(value)) {\n          if (key === '__initData' && toAdapt.__initData !== undefined) {\n            continue;\n          }\n          toAdapt[key] = makeShareableCloneRecursive(\n            element,\n            shouldPersistRemote,\n            depth + 1\n          );\n        }\n        freezeObjectIfDev(value);\n      } else if (value instanceof RegExp) {\n        const pattern = value.source;\n        const flags = value.flags;\n        const handle = makeShareableCloneRecursive({\n          __init: () => {\n            'worklet';\n            return new RegExp(pattern, flags);\n          },\n        });\n        shareableMappingCache.set(value, handle);\n        return handle as ShareableRef<T>;\n      } else if (value instanceof Error) {\n        const { name, message, stack } = value;\n        const handle = makeShareableCloneRecursive({\n          __init: () => {\n            'worklet';\n            const error = new Error();\n            error.name = name;\n            error.message = message;\n            error.stack = stack;\n            return error;\n          },\n        });\n        shareableMappingCache.set(value, handle);\n        return handle as ShareableRef<T>;\n      } else if (value instanceof ArrayBuffer) {\n        toAdapt = value;\n      } else if (ArrayBuffer.isView(value)) {\n        // typed array (e.g. Int32Array, Uint8ClampedArray) or DataView\n        const buffer = value.buffer;\n        const typeName = value.constructor.name;\n        const handle = makeShareableCloneRecursive({\n          __init: () => {\n            'worklet';\n            if (!VALID_ARRAY_VIEWS_NAMES.includes(typeName)) {\n              throw new Error(\n                `[Reanimated] Invalid array view name \\`${typeName}\\`.`\n              );\n            }\n            const constructor = global[typeName as keyof typeof global];\n            if (constructor === undefined) {\n              throw new Error(\n                `[Reanimated] Constructor for \\`${typeName}\\` not found.`\n              );\n            }\n            return new constructor(buffer);\n          },\n        });\n        shareableMappingCache.set(value, handle);\n        return handle as ShareableRef<T>;\n      } else {\n        // This is reached for object types that are not of plain Object.prototype.\n        // We don't support such objects from being transferred as shareables to\n        // the UI runtime and hence we replace them with \"inaccessible object\"\n        // which is implemented as a Proxy object that throws on any attempt\n        // of accessing its fields. We argue that such objects can sometimes leak\n        // as attributes of objects being captured by worklets but should never\n        // be used on the UI runtime regardless. If they are being accessed, the user\n        // will get an appropriate error message.\n        const inaccessibleObject =\n          makeShareableCloneRecursive<T>(INACCESSIBLE_OBJECT);\n        shareableMappingCache.set(value, inaccessibleObject);\n        return inaccessibleObject;\n      }\n      const adapted = NativeReanimatedModule.makeShareableClone(\n        toAdapt,\n        shouldPersistRemote,\n        value\n      );\n      shareableMappingCache.set(value, adapted);\n      shareableMappingCache.set(adapted);\n      return adapted;\n    }\n  }\n  return NativeReanimatedModule.makeShareableClone(\n    value,\n    shouldPersistRemote,\n    undefined\n  );\n}\n\nconst WORKLET_CODE_THRESHOLD = 255;\n\nfunction getWorkletCode(value: WorkletFunction) {\n  // @ts-ignore this is fine\n  const code = value?.__initData?.code;\n  if (!code) {\n    return 'unknown';\n  }\n  if (code.length > WORKLET_CODE_THRESHOLD) {\n    return `${code.substring(0, WORKLET_CODE_THRESHOLD)}...`;\n  }\n  return code;\n}\n\ntype RemoteFunction<T> = {\n  __remoteFunction: FlatShareableRef<T>;\n};\n\nfunction isRemoteFunction<T>(value: {\n  __remoteFunction?: unknown;\n}): value is RemoteFunction<T> {\n  'worklet';\n  return !!value.__remoteFunction;\n}\n\n/**\n * We freeze\n * - arrays,\n * - remote functions,\n * - plain JS objects,\n *\n * that are transformed to a shareable with a meaningful warning.\n * This should help detect issues when someone modifies data after it's been converted.\n * Meaning that they may be doing a faulty assumption in their\n * code expecting that the updates are going to automatically propagate to\n * the object sent to the UI thread. If the user really wants some objects\n * to be mutable they should use shared values instead.\n */\nfunction freezeObjectIfDev<T extends object>(value: T) {\n  if (!__DEV__) {\n    return;\n  }\n  Object.entries(value).forEach(([key, element]) => {\n    const descriptor = Object.getOwnPropertyDescriptor(value, key)!;\n    if (!descriptor.configurable) {\n      return;\n    }\n    Object.defineProperty(value, key, {\n      get() {\n        return element;\n      },\n      set() {\n        console.warn(\n          `[Reanimated] Tried to modify key \\`${key}\\` of an object which has been already passed to a worklet. See \nhttps://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#tried-to-modify-key-of-an-object-which-has-been-converted-to-a-shareable \nfor more details.`\n        );\n      },\n    });\n  });\n  Object.preventExtensions(value);\n}\n\nexport function makeShareableCloneOnUIRecursive<T>(\n  value: T\n): FlatShareableRef<T> {\n  'worklet';\n  if (SHOULD_BE_USE_WEB) {\n    // @ts-ignore web is an interesting place where we don't run a secondary VM on the UI thread\n    // see more details in the comment where USE_STUB_IMPLEMENTATION is defined.\n    return value;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  function cloneRecursive(value: T): FlatShareableRef<T> {\n    if (\n      (typeof value === 'object' && value !== null) ||\n      typeof value === 'function'\n    ) {\n      if (isHostObject(value)) {\n        // We call `_makeShareableClone` to wrap the provided HostObject\n        // inside ShareableJSRef.\n        return global._makeShareableClone(\n          value,\n          undefined\n        ) as FlatShareableRef<T>;\n      }\n      if (isRemoteFunction<T>(value)) {\n        // RemoteFunctions are created by us therefore they are\n        // a Shareable out of the box and there is no need to\n        // call `_makeShareableClone`.\n        return value.__remoteFunction;\n      }\n      if (Array.isArray(value)) {\n        return global._makeShareableClone(\n          value.map(cloneRecursive),\n          undefined\n        ) as FlatShareableRef<T>;\n      }\n      const toAdapt: Record<string, FlatShareableRef<T>> = {};\n      for (const [key, element] of Object.entries(value)) {\n        toAdapt[key] = cloneRecursive(element);\n      }\n      return global._makeShareableClone(toAdapt, value) as FlatShareableRef<T>;\n    }\n    return global._makeShareableClone(value, undefined);\n  }\n  return cloneRecursive(value);\n}\n\nfunction makeShareableJS<T extends object>(value: T): T {\n  return value;\n}\n\nfunction makeShareableNative<T extends object>(value: T): T {\n  if (shareableMappingCache.get(value)) {\n    return value;\n  }\n  const handle = makeShareableCloneRecursive({\n    __init: () => {\n      'worklet';\n      return value;\n    },\n  });\n  shareableMappingCache.set(value, handle);\n  return value;\n}\n\n/**\n * This function creates a value on UI with persistent state - changes to it on the UI\n * thread will be seen by all worklets. Use it when you want to create a value\n * that is read and written only on the UI thread.\n */\nexport const makeShareable = SHOULD_BE_USE_WEB\n  ? makeShareableJS\n  : makeShareableNative;\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,iBAAiB,QAAQ,eAAe;AAMjD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,2BAA2B,QAAQ,UAAU;AACtD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SACEC,qBAAqB,EACrBC,oBAAoB,QACf,yBAAyB;;AAEhC;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGL,cAAc,CAAC,CAAC;AAE1C,MAAMM,SAAS,GAAG,sBAAsB;AAExC,SAASC,YAAYA,CAACC,KAA0B,EAAE;EAChD,SAAS;;EACT;EACA;EACA;EACA;EACA,OAAOF,SAAS,IAAIE,KAAK;AAC3B;AAEA,SAASC,eAAeA,CAACC,MAAc,EAAE;EACvC,OAAOC,MAAM,CAACC,cAAc,CAACF,MAAM,CAAC,KAAKC,MAAM,CAACE,SAAS;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,MAAM,EAAEA,CAAA,KAAM;IACZ,SAAS;;IACT,OAAO,IAAIC,KAAK,CACd,CAAC,CAAC,EACF;MACEC,GAAG,EAAEA,CAACC,CAAU,EAAEC,IAAqB,KAAK;QAC1C,IACEA,IAAI,KAAK,0BAA0B,IACnCA,IAAI,KAAK,kBAAkB,EAC3B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,OAAO,KAAK;QACd;QACA,MAAM,IAAIC,KAAK,CACZ,4CAA2CC,MAAM,CAChDF,IACF,CAAE,yDACJ,CAAC;MACH,CAAC;MACDG,GAAG,EAAEA,CAAA,KAAM;QACT,MAAM,IAAIF,KAAK,CACb,mFACF,CAAC;MACH;IACF,CACF,CAAC;EACH;AACF,CAAC;AAED,MAAMG,uBAAuB,GAAG,CAC9B,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,UAAU,CACX;AAED,MAAMC,oCAAoC,GAAG,EAAE;AAC/C;AACA;AACA,IAAIC,+BAAwC;AAE5C,OAAO,SAASC,2BAA2BA,CACzClB,KAAU,EACVmB,mBAAmB,GAAG,KAAK,EAC3BC,KAAK,GAAG,CAAC,EACQ;EACjB,IAAIvB,iBAAiB,EAAE;IACrB,OAAOG,KAAK;EACd;EACA,IAAIoB,KAAK,IAAIJ,oCAAoC,EAAE;IACjD;IACA;IACA;IACA;IACA;IACA,IAAII,KAAK,KAAKJ,oCAAoC,EAAE;MAClDC,+BAA+B,GAAGjB,KAAK;IACzC,CAAC,MAAM,IAAIA,KAAK,KAAKiB,+BAA+B,EAAE;MACpD,MAAM,IAAIL,KAAK,CACb,uFACF,CAAC;IACH;EACF,CAAC,MAAM;IACLK,+BAA+B,GAAGI,SAAS;EAC7C;EACA;EACA,MAAMC,IAAI,GAAG,OAAOtB,KAAK;EACzB,MAAMuB,YAAY,GAAGD,IAAI,KAAK,QAAQ;EACtC,MAAME,cAAc,GAAGF,IAAI,KAAK,UAAU;EAC1C,IAAI,CAACC,YAAY,IAAIC,cAAc,KAAKxB,KAAK,KAAK,IAAI,EAAE;IACtD,MAAMyB,MAAM,GAAG9B,qBAAqB,CAACc,GAAG,CAACT,KAAK,CAAC;IAC/C,IAAIyB,MAAM,KAAK7B,oBAAoB,EAAE;MACnC,OAAOI,KAAK;IACd,CAAC,MAAM,IAAIyB,MAAM,KAAKJ,SAAS,EAAE;MAC/B,OAAOI,MAAM;IACf,CAAC,MAAM;MACL,IAAIC,OAAY;MAChB,IAAIC,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,EAAE;QACxB0B,OAAO,GAAG1B,KAAK,CAAC6B,GAAG,CAAEC,OAAO,IAC1BZ,2BAA2B,CAACY,OAAO,EAAEX,mBAAmB,EAAEC,KAAK,GAAG,CAAC,CACrE,CAAC;QACDW,iBAAiB,CAAC/B,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIwB,cAAc,IAAI,CAACjC,iBAAiB,CAACS,KAAK,CAAC,EAAE;QACtD;QACA0B,OAAO,GAAG1B,KAAK;QACf+B,iBAAiB,CAAC/B,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAID,YAAY,CAACC,KAAK,CAAC,EAAE;QAC9B;QACA;QACA;QACA0B,OAAO,GAAG1B,KAAK;MACjB,CAAC,MAAM,IACLC,eAAe,CAACD,KAAK,CAAC,IACtBA,KAAK,CAACgC,6BAA6B,EACnC;QACA,MAAMC,2BAA2B,GAAGjC,KAAK,CAACgC,6BAA6B;QACvE,MAAME,MAAM,GAAGhB,2BAA2B,CAAC;UACzCX,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,OAAO0B,2BAA2B,CAAC,CAAC;UACtC;QACF,CAAC,CAAC;QACFtC,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAEkC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIjC,eAAe,CAACD,KAAK,CAAC,IAAIwB,cAAc,EAAE;QACnDE,OAAO,GAAG,CAAC,CAAC;QACZ,IAAInC,iBAAiB,CAACS,KAAK,CAAC,EAAE;UAC5B,IAAImC,OAAO,EAAE;YACX,MAAMC,YAAY,GAAGpC,KAAK,CAACqC,UAAU,CAACC,OAAO;YAC7C,IAAIF,YAAY,KAAKf,SAAS,IAAIe,YAAY,KAAK1C,SAAS,EAAE;cAC5D,MAAM,IAAIkB,KAAK,CAAE,8FAA6FlB,SAAU,QAAO0C,YAAa;AAC1J;AACA,wBAAwBG,cAAc,CAACvC,KAAK,CAAE,IAAG,CAAC;YACtC;YACAP,2BAA2B,CACzBO,KAAK,CAACwC,aAAa,EACnBxC,KAAK,CAACyC,cACR,CAAC;UACH;UACA,IAAIzC,KAAK,CAACyC,cAAc,EAAE;YACxB;YACA;YACA;YACA;YACA,OAAOzC,KAAK,CAACyC,cAAc;UAC7B;UACA;UACA;UACA;UACA;UACA;UACAf,OAAO,CAACW,UAAU,GAAGnB,2BAA2B,CAC9ClB,KAAK,CAACqC,UAAU,EAChB,IAAI,EACJjB,KAAK,GAAG,CACV,CAAC;QACH;QAEA,KAAK,MAAM,CAACsB,GAAG,EAAEZ,OAAO,CAAC,IAAI3B,MAAM,CAACwC,OAAO,CAAC3C,KAAK,CAAC,EAAE;UAClD,IAAI0C,GAAG,KAAK,YAAY,IAAIhB,OAAO,CAACW,UAAU,KAAKhB,SAAS,EAAE;YAC5D;UACF;UACAK,OAAO,CAACgB,GAAG,CAAC,GAAGxB,2BAA2B,CACxCY,OAAO,EACPX,mBAAmB,EACnBC,KAAK,GAAG,CACV,CAAC;QACH;QACAW,iBAAiB,CAAC/B,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIA,KAAK,YAAY4C,MAAM,EAAE;QAClC,MAAMC,OAAO,GAAG7C,KAAK,CAAC8C,MAAM;QAC5B,MAAMC,KAAK,GAAG/C,KAAK,CAAC+C,KAAK;QACzB,MAAMb,MAAM,GAAGhB,2BAA2B,CAAC;UACzCX,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,OAAO,IAAIqC,MAAM,CAACC,OAAO,EAAEE,KAAK,CAAC;UACnC;QACF,CAAC,CAAC;QACFpD,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAEkC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIlC,KAAK,YAAYY,KAAK,EAAE;QACjC,MAAM;UAAEoC,IAAI;UAAEC,OAAO;UAAEC;QAAM,CAAC,GAAGlD,KAAK;QACtC,MAAMkC,MAAM,GAAGhB,2BAA2B,CAAC;UACzCX,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,MAAM4C,KAAK,GAAG,IAAIvC,KAAK,CAAC,CAAC;YACzBuC,KAAK,CAACH,IAAI,GAAGA,IAAI;YACjBG,KAAK,CAACF,OAAO,GAAGA,OAAO;YACvBE,KAAK,CAACD,KAAK,GAAGA,KAAK;YACnB,OAAOC,KAAK;UACd;QACF,CAAC,CAAC;QACFxD,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAEkC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIlC,KAAK,YAAYoD,WAAW,EAAE;QACvC1B,OAAO,GAAG1B,KAAK;MACjB,CAAC,MAAM,IAAIoD,WAAW,CAACC,MAAM,CAACrD,KAAK,CAAC,EAAE;QACpC;QACA,MAAMsD,MAAM,GAAGtD,KAAK,CAACsD,MAAM;QAC3B,MAAMC,QAAQ,GAAGvD,KAAK,CAACwD,WAAW,CAACR,IAAI;QACvC,MAAMd,MAAM,GAAGhB,2BAA2B,CAAC;UACzCX,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,IAAI,CAACQ,uBAAuB,CAAC0C,QAAQ,CAACF,QAAQ,CAAC,EAAE;cAC/C,MAAM,IAAI3C,KAAK,CACZ,0CAAyC2C,QAAS,KACrD,CAAC;YACH;YACA,MAAMC,WAAW,GAAGE,MAAM,CAACH,QAAQ,CAAwB;YAC3D,IAAIC,WAAW,KAAKnC,SAAS,EAAE;cAC7B,MAAM,IAAIT,KAAK,CACZ,kCAAiC2C,QAAS,eAC7C,CAAC;YACH;YACA,OAAO,IAAIC,WAAW,CAACF,MAAM,CAAC;UAChC;QACF,CAAC,CAAC;QACF3D,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAEkC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMyB,kBAAkB,GACtBzC,2BAA2B,CAAIZ,mBAAmB,CAAC;QACrDX,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAE2D,kBAAkB,CAAC;QACpD,OAAOA,kBAAkB;MAC3B;MACA,MAAMC,OAAO,GAAGtE,sBAAsB,CAACuE,kBAAkB,CACvDnC,OAAO,EACPP,mBAAmB,EACnBnB,KACF,CAAC;MACDL,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAE4D,OAAO,CAAC;MACzCjE,qBAAqB,CAACmB,GAAG,CAAC8C,OAAO,CAAC;MAClC,OAAOA,OAAO;IAChB;EACF;EACA,OAAOtE,sBAAsB,CAACuE,kBAAkB,CAC9C7D,KAAK,EACLmB,mBAAmB,EACnBE,SACF,CAAC;AACH;AAEA,MAAMyC,sBAAsB,GAAG,GAAG;AAElC,SAASvB,cAAcA,CAACvC,KAAsB,EAAE;EAAA,IAAA+D,iBAAA;EAC9C;EACA,MAAMC,IAAI,GAAGhE,KAAK,aAALA,KAAK,gBAAA+D,iBAAA,GAAL/D,KAAK,CAAEqC,UAAU,cAAA0B,iBAAA,uBAAjBA,iBAAA,CAAmBC,IAAI;EACpC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,SAAS;EAClB;EACA,IAAIA,IAAI,CAACC,MAAM,GAAGH,sBAAsB,EAAE;IACxC,OAAQ,GAAEE,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEJ,sBAAsB,CAAE,KAAI;EAC1D;EACA,OAAOE,IAAI;AACb;AAMA,SAASG,gBAAgBA,CAAInE,KAE5B,EAA8B;EAC7B,SAAS;;EACT,OAAO,CAAC,CAACA,KAAK,CAACoE,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrC,iBAAiBA,CAAmB/B,KAAQ,EAAE;EACrD,IAAI,CAACmC,OAAO,EAAE;IACZ;EACF;EACAhC,MAAM,CAACwC,OAAO,CAAC3C,KAAK,CAAC,CAACqE,OAAO,CAAC,CAAC,CAAC3B,GAAG,EAAEZ,OAAO,CAAC,KAAK;IAChD,MAAMwC,UAAU,GAAGnE,MAAM,CAACoE,wBAAwB,CAACvE,KAAK,EAAE0C,GAAG,CAAE;IAC/D,IAAI,CAAC4B,UAAU,CAACE,YAAY,EAAE;MAC5B;IACF;IACArE,MAAM,CAACsE,cAAc,CAACzE,KAAK,EAAE0C,GAAG,EAAE;MAChCjC,GAAGA,CAAA,EAAG;QACJ,OAAOqB,OAAO;MAChB,CAAC;MACDhB,GAAGA,CAAA,EAAG;QACJ4D,OAAO,CAACC,IAAI,CACT,sCAAqCjC,GAAI;AACpD;AACA,kBACQ,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFvC,MAAM,CAACyE,iBAAiB,CAAC5E,KAAK,CAAC;AACjC;AAEA,OAAO,SAAS6E,+BAA+BA,CAC7C7E,KAAQ,EACa;EACrB,SAAS;;EACT,IAAIH,iBAAiB,EAAE;IACrB;IACA;IACA,OAAOG,KAAK;EACd;EACA;EACA,SAAS8E,cAAcA,CAAC9E,KAAQ,EAAuB;IACrD,IACG,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAC5C,OAAOA,KAAK,KAAK,UAAU,EAC3B;MACA,IAAID,YAAY,CAACC,KAAK,CAAC,EAAE;QACvB;QACA;QACA,OAAO0D,MAAM,CAACqB,mBAAmB,CAC/B/E,KAAK,EACLqB,SACF,CAAC;MACH;MACA,IAAI8C,gBAAgB,CAAInE,KAAK,CAAC,EAAE;QAC9B;QACA;QACA;QACA,OAAOA,KAAK,CAACoE,gBAAgB;MAC/B;MACA,IAAIzC,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,EAAE;QACxB,OAAO0D,MAAM,CAACqB,mBAAmB,CAC/B/E,KAAK,CAAC6B,GAAG,CAACiD,cAAc,CAAC,EACzBzD,SACF,CAAC;MACH;MACA,MAAMK,OAA4C,GAAG,CAAC,CAAC;MACvD,KAAK,MAAM,CAACgB,GAAG,EAAEZ,OAAO,CAAC,IAAI3B,MAAM,CAACwC,OAAO,CAAC3C,KAAK,CAAC,EAAE;QAClD0B,OAAO,CAACgB,GAAG,CAAC,GAAGoC,cAAc,CAAChD,OAAO,CAAC;MACxC;MACA,OAAO4B,MAAM,CAACqB,mBAAmB,CAACrD,OAAO,EAAE1B,KAAK,CAAC;IACnD;IACA,OAAO0D,MAAM,CAACqB,mBAAmB,CAAC/E,KAAK,EAAEqB,SAAS,CAAC;EACrD;EACA,OAAOyD,cAAc,CAAC9E,KAAK,CAAC;AAC9B;AAEA,SAASgF,eAAeA,CAAmBhF,KAAQ,EAAK;EACtD,OAAOA,KAAK;AACd;AAEA,SAASiF,mBAAmBA,CAAmBjF,KAAQ,EAAK;EAC1D,IAAIL,qBAAqB,CAACc,GAAG,CAACT,KAAK,CAAC,EAAE;IACpC,OAAOA,KAAK;EACd;EACA,MAAMkC,MAAM,GAAGhB,2BAA2B,CAAC;IACzCX,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOP,KAAK;IACd;EACF,CAAC,CAAC;EACFL,qBAAqB,CAACmB,GAAG,CAACd,KAAK,EAAEkC,MAAM,CAAC;EACxC,OAAOlC,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkF,aAAa,GAAGrF,iBAAiB,GAC1CmF,eAAe,GACfC,mBAAmB", "ignoreList": []}