{"version": 3, "file": "DevelopmentTeam.js", "names": ["_nodeFs", "data", "_interopRequireDefault", "require", "_xcode", "_Paths", "_Target", "_Xcodeproj", "_string", "_iosPlugins", "e", "__esModule", "default", "withDevelopmentTeam", "config", "appleTeamId", "withXcodeProject", "teamId", "getDevelopmentTeam", "modResults", "updateDevelopmentTeamForPbxproj", "exports", "ios", "setDevelopmentTeamForBuildConfiguration", "xcBuildConfiguration", "developmentTeam", "buildSettings", "DEVELOPMENT_TEAM", "trimQuotes", "project", "nativeTargets", "getNativeTargets", "for<PERSON>ach", "nativeTarget", "getBuildConfigurationsForListId", "buildConfigurationList", "buildConfig", "setDevelopmentTeamForPbxproj", "projectRoot", "pbxproj<PERSON><PERSON><PERSON>", "getAllPBXProjectPaths", "pbxproj<PERSON><PERSON>", "xcode", "parseSync", "fs", "writeFileSync", "writeSync"], "sources": ["../../src/ios/DevelopmentTeam.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\nimport fs from 'node:fs';\nimport xcode, { type XCBuildConfiguration } from 'xcode';\n\nimport { getAllPBXProjectPaths } from './Paths';\nimport { getNativeTargets } from './Target';\nimport type { ConfigPlugin, XcodeProject } from '../Plugin.types';\nimport { getBuildConfigurationsForListId } from './utils/Xcodeproj';\nimport { trimQuotes } from './utils/string';\nimport { withXcodeProject } from '../plugins/ios-plugins';\n\n/**\n * Set the Apple development team ID for all build configurations using the first native target.\n */\nexport const withDevelopmentTeam: ConfigPlugin<{ appleTeamId?: string } | void> = (\n  config,\n  { appleTeamId } = {}\n) => {\n  return withXcodeProject(config, (config) => {\n    const teamId = appleTeamId ?? getDevelopmentTeam(config);\n    if (teamId) {\n      config.modResults = updateDevelopmentTeamForPbxproj(config.modResults, teamId);\n    }\n\n    return config;\n  });\n};\n\n/** Get the Apple development team ID from Expo config, if defined */\nexport function getDevelopmentTeam(config: Pick<ExpoConfig, 'ios'>): string | null {\n  return config.ios?.appleTeamId ?? null;\n}\n\n/** Set the Apple development team ID for an XCBuildConfiguration object */\nexport function setDevelopmentTeamForBuildConfiguration(\n  xcBuildConfiguration: XCBuildConfiguration,\n  developmentTeam?: string\n): void {\n  if (developmentTeam) {\n    xcBuildConfiguration.buildSettings.DEVELOPMENT_TEAM = trimQuotes(developmentTeam);\n  } else {\n    delete xcBuildConfiguration.buildSettings.DEVELOPMENT_TEAM;\n  }\n}\n\n/**\n * Update the Apple development team ID for all XCBuildConfiguration entries, in all native targets.\n *\n * A development team is stored as a value in XCBuildConfiguration entry.\n * Those entries exist for every pair (build target, build configuration).\n * Unless target name is passed, the first target defined in the pbxproj is used\n * (to keep compatibility with the inaccurate legacy implementation of this function).\n */\nexport function updateDevelopmentTeamForPbxproj(\n  project: XcodeProject,\n  appleTeamId?: string\n): XcodeProject {\n  const nativeTargets = getNativeTargets(project);\n\n  nativeTargets.forEach(([, nativeTarget]) => {\n    getBuildConfigurationsForListId(project, nativeTarget.buildConfigurationList).forEach(\n      ([, buildConfig]) => setDevelopmentTeamForBuildConfiguration(buildConfig, appleTeamId)\n    );\n  });\n\n  return project;\n}\n\n/**\n * Updates the Apple development team ID for pbx projects inside the ios directory of the given project root\n *\n * @param {string} projectRoot Path to project root containing the ios directory\n * @param {[string]} appleTeamId Desired Apple development team ID\n */\nexport function setDevelopmentTeamForPbxproj(projectRoot: string, appleTeamId?: string): void {\n  // Get all pbx projects in the ${projectRoot}/ios directory\n  const pbxprojPaths = getAllPBXProjectPaths(projectRoot);\n\n  for (const pbxprojPath of pbxprojPaths) {\n    let project = xcode.project(pbxprojPath);\n    project.parseSync();\n    project = updateDevelopmentTeamForPbxproj(project, appleTeamId);\n    fs.writeFileSync(pbxprojPath, project.writeSync());\n  }\n}\n"], "mappings": ";;;;;;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,OAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,WAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,UAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,YAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,WAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0D,SAAAC,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1D;AACA;AACA;AACO,MAAMG,mBAAkE,GAAGA,CAChFC,MAAM,EACN;EAAEC;AAAY,CAAC,GAAG,CAAC,CAAC,KACjB;EACH,OAAO,IAAAC,8BAAgB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC1C,MAAMG,MAAM,GAAGF,WAAW,IAAIG,kBAAkB,CAACJ,MAAM,CAAC;IACxD,IAAIG,MAAM,EAAE;MACVH,MAAM,CAACK,UAAU,GAAGC,+BAA+B,CAACN,MAAM,CAACK,UAAU,EAAEF,MAAM,CAAC;IAChF;IAEA,OAAOH,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAO,OAAA,CAAAR,mBAAA,GAAAA,mBAAA;AACO,SAASK,kBAAkBA,CAACJ,MAA+B,EAAiB;EACjF,OAAOA,MAAM,CAACQ,GAAG,EAAEP,WAAW,IAAI,IAAI;AACxC;;AAEA;AACO,SAASQ,uCAAuCA,CACrDC,oBAA0C,EAC1CC,eAAwB,EAClB;EACN,IAAIA,eAAe,EAAE;IACnBD,oBAAoB,CAACE,aAAa,CAACC,gBAAgB,GAAG,IAAAC,oBAAU,EAACH,eAAe,CAAC;EACnF,CAAC,MAAM;IACL,OAAOD,oBAAoB,CAACE,aAAa,CAACC,gBAAgB;EAC5D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASP,+BAA+BA,CAC7CS,OAAqB,EACrBd,WAAoB,EACN;EACd,MAAMe,aAAa,GAAG,IAAAC,0BAAgB,EAACF,OAAO,CAAC;EAE/CC,aAAa,CAACE,OAAO,CAAC,CAAC,GAAGC,YAAY,CAAC,KAAK;IAC1C,IAAAC,4CAA+B,EAACL,OAAO,EAAEI,YAAY,CAACE,sBAAsB,CAAC,CAACH,OAAO,CACnF,CAAC,GAAGI,WAAW,CAAC,KAAKb,uCAAuC,CAACa,WAAW,EAAErB,WAAW,CACvF,CAAC;EACH,CAAC,CAAC;EAEF,OAAOc,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASQ,4BAA4BA,CAACC,WAAmB,EAAEvB,WAAoB,EAAQ;EAC5F;EACA,MAAMwB,YAAY,GAAG,IAAAC,8BAAqB,EAACF,WAAW,CAAC;EAEvD,KAAK,MAAMG,WAAW,IAAIF,YAAY,EAAE;IACtC,IAAIV,OAAO,GAAGa,gBAAK,CAACb,OAAO,CAACY,WAAW,CAAC;IACxCZ,OAAO,CAACc,SAAS,CAAC,CAAC;IACnBd,OAAO,GAAGT,+BAA+B,CAACS,OAAO,EAAEd,WAAW,CAAC;IAC/D6B,iBAAE,CAACC,aAAa,CAACJ,WAAW,EAAEZ,OAAO,CAACiB,SAAS,CAAC,CAAC,CAAC;EACpD;AACF", "ignoreList": []}