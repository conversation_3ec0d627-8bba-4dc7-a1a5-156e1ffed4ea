{"version": 3, "names": ["hsvToColor", "RGBtoHSV", "rgbaColor", "processColor", "red", "green", "blue", "opacity", "makeMutable", "Extrapolation", "interpolate", "useSharedValue", "Extrapolate", "interpolateColorsHSV", "value", "inputRange", "colors", "options", "h", "useCorrectedHSVInterpolation", "correctedInputRange", "originalH", "correctedH", "i", "length", "d", "push", "CLAMP", "s", "v", "a", "toLinearSpace", "x", "gamma", "map", "Math", "pow", "toGammaSpace", "round", "interpolateColorsRGB", "r", "outputR", "g", "outputG", "b", "outputB", "getInterpolateRGB", "color", "processedColor", "undefined", "getInterpolateHSV", "processedHSVColor", "interpolateColor", "outputRange", "colorSpace", "Error", "ColorSpace", "useInterpolateConfig", "RGB", "cache"], "sources": ["interpolateColor.ts"], "sourcesContent": ["'use strict';\nimport {\n  hsvToColor,\n  RGBtoHSV,\n  rgbaColor,\n  processColor,\n  red,\n  green,\n  blue,\n  opacity,\n} from './Colors';\nimport { makeMutable } from './core';\nimport { Extrapolation, interpolate } from './interpolation';\nimport type { SharedValue } from './commonTypes';\nimport { useSharedValue } from './hook/useSharedValue';\n\n/**\n * @deprecated Please use Extrapolation instead\n */\nexport const Extrapolate = Extrapolation;\n\n/**\n * Options for color interpolation.\n *\n * @param gamma - Gamma value used in gamma correction. Defaults to `2.2`.\n * @param useCorrectedHSVInterpolation - Whether to reduce the number of colors the interpolation has to go through. Defaults to `true`.\n */\nexport type InterpolationOptions = {\n  gamma?: number;\n  useCorrectedHSVInterpolation?: boolean;\n};\n\nconst interpolateColorsHSV = (\n  value: number,\n  inputRange: readonly number[],\n  colors: InterpolateHSV,\n  options: InterpolationOptions\n) => {\n  'worklet';\n  let h = 0;\n  const { useCorrectedHSVInterpolation = true } = options;\n  if (useCorrectedHSVInterpolation) {\n    // if the difference between hues in a range is > 180 deg\n    // then move the hue at the right end of the range +/- 360 deg\n    // and add the next point in the original place + 0.00001 with original hue\n    // to not break the next range\n    const correctedInputRange = [inputRange[0]];\n    const originalH = colors.h;\n    const correctedH = [originalH[0]];\n\n    for (let i = 1; i < originalH.length; ++i) {\n      const d = originalH[i] - originalH[i - 1];\n      if (originalH[i] > originalH[i - 1] && d > 0.5) {\n        correctedInputRange.push(inputRange[i]);\n        correctedInputRange.push(inputRange[i] + 0.00001);\n        correctedH.push(originalH[i] - 1);\n        correctedH.push(originalH[i]);\n      } else if (originalH[i] < originalH[i - 1] && d < -0.5) {\n        correctedInputRange.push(inputRange[i]);\n        correctedInputRange.push(inputRange[i] + 0.00001);\n        correctedH.push(originalH[i] + 1);\n        correctedH.push(originalH[i]);\n      } else {\n        correctedInputRange.push(inputRange[i]);\n        correctedH.push(originalH[i]);\n      }\n    }\n    h =\n      (interpolate(\n        value,\n        correctedInputRange,\n        correctedH,\n        Extrapolation.CLAMP\n      ) +\n        1) %\n      1;\n  } else {\n    h = interpolate(value, inputRange, colors.h, Extrapolation.CLAMP);\n  }\n  const s = interpolate(value, inputRange, colors.s, Extrapolation.CLAMP);\n  const v = interpolate(value, inputRange, colors.v, Extrapolation.CLAMP);\n  const a = interpolate(value, inputRange, colors.a, Extrapolation.CLAMP);\n  return hsvToColor(h, s, v, a);\n};\n\nconst toLinearSpace = (x: number[], gamma: number): number[] => {\n  'worklet';\n  return x.map((v) => Math.pow(v / 255, gamma));\n};\n\nconst toGammaSpace = (x: number, gamma: number): number => {\n  'worklet';\n  return Math.round(Math.pow(x, 1 / gamma) * 255);\n};\n\nconst interpolateColorsRGB = (\n  value: number,\n  inputRange: readonly number[],\n  colors: InterpolateRGB,\n  options: InterpolationOptions\n) => {\n  'worklet';\n  const { gamma = 2.2 } = options;\n  let { r: outputR, g: outputG, b: outputB } = colors;\n  if (gamma !== 1) {\n    outputR = toLinearSpace(outputR, gamma);\n    outputG = toLinearSpace(outputG, gamma);\n    outputB = toLinearSpace(outputB, gamma);\n  }\n  const r = interpolate(value, inputRange, outputR, Extrapolation.CLAMP);\n  const g = interpolate(value, inputRange, outputG, Extrapolation.CLAMP);\n  const b = interpolate(value, inputRange, outputB, Extrapolation.CLAMP);\n  const a = interpolate(value, inputRange, colors.a, Extrapolation.CLAMP);\n  if (gamma === 1) {\n    return rgbaColor(r, g, b, a);\n  }\n  return rgbaColor(\n    toGammaSpace(r, gamma),\n    toGammaSpace(g, gamma),\n    toGammaSpace(b, gamma),\n    a\n  );\n};\n\nexport interface InterpolateRGB {\n  r: number[];\n  g: number[];\n  b: number[];\n  a: number[];\n}\n\nconst getInterpolateRGB = (\n  colors: readonly (string | number)[]\n): InterpolateRGB => {\n  'worklet';\n\n  const r = [];\n  const g = [];\n  const b = [];\n  const a = [];\n  for (let i = 0; i < colors.length; ++i) {\n    const color = colors[i];\n    const processedColor = processColor(color);\n    // explicit check in case if processedColor is 0\n    if (processedColor !== null && processedColor !== undefined) {\n      r.push(red(processedColor));\n      g.push(green(processedColor));\n      b.push(blue(processedColor));\n      a.push(opacity(processedColor));\n    }\n  }\n  return { r, g, b, a };\n};\n\nexport interface InterpolateHSV {\n  h: number[];\n  s: number[];\n  v: number[];\n  a: number[];\n}\n\nconst getInterpolateHSV = (\n  colors: readonly (string | number)[]\n): InterpolateHSV => {\n  'worklet';\n  const h = [];\n  const s = [];\n  const v = [];\n  const a = [];\n  for (let i = 0; i < colors.length; ++i) {\n    const color = colors[i];\n    const processedColor = processColor(color) as any;\n    if (typeof processedColor === 'number') {\n      const processedHSVColor = RGBtoHSV(\n        red(processedColor),\n        green(processedColor),\n        blue(processedColor)\n      );\n\n      h.push(processedHSVColor.h);\n      s.push(processedHSVColor.s);\n      v.push(processedHSVColor.v);\n      a.push(opacity(processedColor));\n    }\n  }\n  return { h, s, v, a };\n};\n\n/**\n * Lets you map a value from a range of numbers to a range of colors using linear interpolation.\n *\n * @param value - A number from the `input` range that is going to be mapped to the color in the `output` range.\n * @param inputRange - An array of numbers specifying the input range of the interpolation.\n * @param outputRange - An array of output colors values (eg. \"red\", \"#00FFCC\", \"rgba(255, 0, 0, 0.5)\").\n * @param colorSpace - The color space to use for interpolation. Defaults to 'RGB'.\n * @param options - Additional options for interpolation - {@link InterpolationOptions}.\n * @returns The color after interpolation from within the output range in rgba(r, g, b, a) format.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/interpolateColor\n */\nexport function interpolateColor(\n  value: number,\n  inputRange: readonly number[],\n  outputRange: readonly string[],\n  colorSpace?: 'RGB' | 'HSV',\n  options?: InterpolationOptions\n): string;\n\nexport function interpolateColor(\n  value: number,\n  inputRange: readonly number[],\n  outputRange: readonly number[],\n  colorSpace?: 'RGB' | 'HSV',\n  options?: InterpolationOptions\n): number;\n\nexport function interpolateColor(\n  value: number,\n  inputRange: readonly number[],\n  outputRange: readonly (string | number)[],\n  colorSpace: 'RGB' | 'HSV' = 'RGB',\n  options: InterpolationOptions = {}\n): string | number {\n  'worklet';\n  if (colorSpace === 'HSV') {\n    return interpolateColorsHSV(\n      value,\n      inputRange,\n      getInterpolateHSV(outputRange),\n      options\n    );\n  } else if (colorSpace === 'RGB') {\n    return interpolateColorsRGB(\n      value,\n      inputRange,\n      getInterpolateRGB(outputRange),\n      options\n    );\n  }\n  throw new Error(\n    `[Reanimated] Invalid color space provided: ${\n      colorSpace as string\n    }. Supported values are: ['RGB', 'HSV'].`\n  );\n}\n\nexport enum ColorSpace {\n  RGB = 0,\n  HSV = 1,\n}\n\nexport interface InterpolateConfig {\n  inputRange: readonly number[];\n  outputRange: readonly (string | number)[];\n  colorSpace: ColorSpace;\n  cache: SharedValue<InterpolateRGB | InterpolateHSV | null>;\n  options: InterpolationOptions;\n}\n\nexport function useInterpolateConfig(\n  inputRange: readonly number[],\n  outputRange: readonly (string | number)[],\n  colorSpace = ColorSpace.RGB,\n  options: InterpolationOptions = {}\n): SharedValue<InterpolateConfig> {\n  return useSharedValue<InterpolateConfig>({\n    inputRange,\n    outputRange,\n    colorSpace,\n    cache: makeMutable<InterpolateRGB | InterpolateHSV | null>(null),\n    options,\n  });\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SACEA,UAAU,EACVC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,QACF,UAAU;AACjB,SAASC,WAAW,QAAQ,QAAQ;AACpC,SAASC,aAAa,EAAEC,WAAW,QAAQ,iBAAiB;AAE5D,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAGH,aAAa;;AAExC;AACA;AACA;AACA;AACA;AACA;;AAMA,MAAMI,oBAAoB,GAAGA,CAC3BC,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,KAC1B;EACH,SAAS;;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,MAAM;IAAEC,4BAA4B,GAAG;EAAK,CAAC,GAAGF,OAAO;EACvD,IAAIE,4BAA4B,EAAE;IAChC;IACA;IACA;IACA;IACA,MAAMC,mBAAmB,GAAG,CAACL,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMM,SAAS,GAAGL,MAAM,CAACE,CAAC;IAC1B,MAAMI,UAAU,GAAG,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC;IAEjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;MACzC,MAAME,CAAC,GAAGJ,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC;MACzC,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,GAAG,EAAE;QAC9CL,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,CAAC,GAAG,EAAE;QACtDL,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM;QACLH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B;IACF;IACAL,CAAC,GACC,CAACR,WAAW,CACVI,KAAK,EACLM,mBAAmB,EACnBE,UAAU,EACVb,aAAa,CAACkB,KAChB,CAAC,GACC,CAAC,IACH,CAAC;EACL,CAAC,MAAM;IACLT,CAAC,GAAGR,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACE,CAAC,EAAET,aAAa,CAACkB,KAAK,CAAC;EACnE;EACA,MAAMC,CAAC,GAAGlB,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACY,CAAC,EAAEnB,aAAa,CAACkB,KAAK,CAAC;EACvE,MAAME,CAAC,GAAGnB,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACa,CAAC,EAAEpB,aAAa,CAACkB,KAAK,CAAC;EACvE,MAAMG,CAAC,GAAGpB,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAErB,aAAa,CAACkB,KAAK,CAAC;EACvE,OAAO3B,UAAU,CAACkB,CAAC,EAAEU,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AAC/B,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,CAAW,EAAEC,KAAa,KAAe;EAC9D,SAAS;;EACT,OAAOD,CAAC,CAACE,GAAG,CAAEL,CAAC,IAAKM,IAAI,CAACC,GAAG,CAACP,CAAC,GAAG,GAAG,EAAEI,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED,MAAMI,YAAY,GAAGA,CAACL,CAAS,EAAEC,KAAa,KAAa;EACzD,SAAS;;EACT,OAAOE,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAGC,KAAK,CAAC,GAAG,GAAG,CAAC;AACjD,CAAC;AAED,MAAMM,oBAAoB,GAAGA,CAC3BzB,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,KAC1B;EACH,SAAS;;EACT,MAAM;IAAEgB,KAAK,GAAG;EAAI,CAAC,GAAGhB,OAAO;EAC/B,IAAI;IAAEuB,CAAC,EAAEC,OAAO;IAAEC,CAAC,EAAEC,OAAO;IAAEC,CAAC,EAAEC;EAAQ,CAAC,GAAG7B,MAAM;EACnD,IAAIiB,KAAK,KAAK,CAAC,EAAE;IACfQ,OAAO,GAAGV,aAAa,CAACU,OAAO,EAAER,KAAK,CAAC;IACvCU,OAAO,GAAGZ,aAAa,CAACY,OAAO,EAAEV,KAAK,CAAC;IACvCY,OAAO,GAAGd,aAAa,CAACc,OAAO,EAAEZ,KAAK,CAAC;EACzC;EACA,MAAMO,CAAC,GAAG9B,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAE0B,OAAO,EAAEhC,aAAa,CAACkB,KAAK,CAAC;EACtE,MAAMe,CAAC,GAAGhC,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAE4B,OAAO,EAAElC,aAAa,CAACkB,KAAK,CAAC;EACtE,MAAMiB,CAAC,GAAGlC,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAE8B,OAAO,EAAEpC,aAAa,CAACkB,KAAK,CAAC;EACtE,MAAMG,CAAC,GAAGpB,WAAW,CAACI,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAErB,aAAa,CAACkB,KAAK,CAAC;EACvE,IAAIM,KAAK,KAAK,CAAC,EAAE;IACf,OAAO/B,SAAS,CAACsC,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEd,CAAC,CAAC;EAC9B;EACA,OAAO5B,SAAS,CACdmC,YAAY,CAACG,CAAC,EAAEP,KAAK,CAAC,EACtBI,YAAY,CAACK,CAAC,EAAET,KAAK,CAAC,EACtBI,YAAY,CAACO,CAAC,EAAEX,KAAK,CAAC,EACtBH,CACF,CAAC;AACH,CAAC;AASD,MAAMgB,iBAAiB,GACrB9B,MAAoC,IACjB;EACnB,SAAS;;EAET,MAAMwB,CAAC,GAAG,EAAE;EACZ,MAAME,CAAC,GAAG,EAAE;EACZ,MAAME,CAAC,GAAG,EAAE;EACZ,MAAMd,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,MAAMwB,KAAK,GAAG/B,MAAM,CAACO,CAAC,CAAC;IACvB,MAAMyB,cAAc,GAAG7C,YAAY,CAAC4C,KAAK,CAAC;IAC1C;IACA,IAAIC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKC,SAAS,EAAE;MAC3DT,CAAC,CAACd,IAAI,CAACtB,GAAG,CAAC4C,cAAc,CAAC,CAAC;MAC3BN,CAAC,CAAChB,IAAI,CAACrB,KAAK,CAAC2C,cAAc,CAAC,CAAC;MAC7BJ,CAAC,CAAClB,IAAI,CAACpB,IAAI,CAAC0C,cAAc,CAAC,CAAC;MAC5BlB,CAAC,CAACJ,IAAI,CAACnB,OAAO,CAACyC,cAAc,CAAC,CAAC;IACjC;EACF;EACA,OAAO;IAAER,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEd;EAAE,CAAC;AACvB,CAAC;AASD,MAAMoB,iBAAiB,GACrBlC,MAAoC,IACjB;EACnB,SAAS;;EACT,MAAME,CAAC,GAAG,EAAE;EACZ,MAAMU,CAAC,GAAG,EAAE;EACZ,MAAMC,CAAC,GAAG,EAAE;EACZ,MAAMC,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,MAAMwB,KAAK,GAAG/B,MAAM,CAACO,CAAC,CAAC;IACvB,MAAMyB,cAAc,GAAG7C,YAAY,CAAC4C,KAAK,CAAQ;IACjD,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;MACtC,MAAMG,iBAAiB,GAAGlD,QAAQ,CAChCG,GAAG,CAAC4C,cAAc,CAAC,EACnB3C,KAAK,CAAC2C,cAAc,CAAC,EACrB1C,IAAI,CAAC0C,cAAc,CACrB,CAAC;MAED9B,CAAC,CAACQ,IAAI,CAACyB,iBAAiB,CAACjC,CAAC,CAAC;MAC3BU,CAAC,CAACF,IAAI,CAACyB,iBAAiB,CAACvB,CAAC,CAAC;MAC3BC,CAAC,CAACH,IAAI,CAACyB,iBAAiB,CAACtB,CAAC,CAAC;MAC3BC,CAAC,CAACJ,IAAI,CAACnB,OAAO,CAACyC,cAAc,CAAC,CAAC;IACjC;EACF;EACA,OAAO;IAAE9B,CAAC;IAAEU,CAAC;IAAEC,CAAC;IAAEC;EAAE,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAiBA,OAAO,SAASsB,gBAAgBA,CAC9BtC,KAAa,EACbC,UAA6B,EAC7BsC,WAAyC,EACzCC,UAAyB,GAAG,KAAK,EACjCrC,OAA6B,GAAG,CAAC,CAAC,EACjB;EACjB,SAAS;;EACT,IAAIqC,UAAU,KAAK,KAAK,EAAE;IACxB,OAAOzC,oBAAoB,CACzBC,KAAK,EACLC,UAAU,EACVmC,iBAAiB,CAACG,WAAW,CAAC,EAC9BpC,OACF,CAAC;EACH,CAAC,MAAM,IAAIqC,UAAU,KAAK,KAAK,EAAE;IAC/B,OAAOf,oBAAoB,CACzBzB,KAAK,EACLC,UAAU,EACV+B,iBAAiB,CAACO,WAAW,CAAC,EAC9BpC,OACF,CAAC;EACH;EACA,MAAM,IAAIsC,KAAK,CACZ,8CACCD,UACD,yCACH,CAAC;AACH;AAEA,WAAYE,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAatB,OAAO,SAASC,oBAAoBA,CAClC1C,UAA6B,EAC7BsC,WAAyC,EACzCC,UAAU,GAAGE,UAAU,CAACE,GAAG,EAC3BzC,OAA6B,GAAG,CAAC,CAAC,EACF;EAChC,OAAON,cAAc,CAAoB;IACvCI,UAAU;IACVsC,WAAW;IACXC,UAAU;IACVK,KAAK,EAAEnD,WAAW,CAAyC,IAAI,CAAC;IAChES;EACF,CAAC,CAAC;AACJ", "ignoreList": []}