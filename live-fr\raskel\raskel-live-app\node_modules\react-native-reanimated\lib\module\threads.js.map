{"version": 3, "names": ["NativeReanimatedModule", "isJest", "shouldBeUseWeb", "makeShareableCloneOnUIRecursive", "makeShareableCloneRecursive", "isWorkletFunction", "IS_JEST", "SHOULD_BE_USE_WEB", "_runOnUIQueue", "setupMicrotasks", "microtasksQueue", "isExecutingMicrotasksQueue", "global", "queueMicrotask", "callback", "push", "__callMicrotasks", "index", "length", "_maybeFlushUIUpdatesQueue", "callMicrotasksOnUIThread", "callMicrotasks", "runOnUI", "worklet", "__DEV__", "_WORKLET", "Error", "args", "scheduleOnUI", "queue", "for<PERSON>ach", "executeOnUIRuntimeSync", "result", "runOnUIImmediately", "runWorkletOnJS", "runOnJS", "fun", "__remoteFunction", "_scheduleOnJS", "undefined"], "sources": ["threads.ts"], "sourcesContent": ["'use strict';\nimport NativeReanimatedModule from './NativeReanimated';\nimport { isJest, shouldBeUseWeb } from './PlatformChecker';\nimport type { WorkletFunction } from './commonTypes';\nimport {\n  makeShareableCloneOnUIRecursive,\n  makeShareableCloneRecursive,\n} from './shareables';\nimport { isWorkletFunction } from './commonTypes';\n\nconst IS_JEST = isJest();\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\n/**\n * An array of [worklet, args] pairs.\n * */\nlet _runOnUIQueue: Array<[WorkletFunction<unknown[], unknown>, unknown[]]> = [];\n\nexport function setupMicrotasks() {\n  'worklet';\n\n  let microtasksQueue: Array<() => void> = [];\n  let isExecutingMicrotasksQueue = false;\n  global.queueMicrotask = (callback: () => void) => {\n    microtasksQueue.push(callback);\n  };\n\n  global.__callMicrotasks = () => {\n    if (isExecutingMicrotasksQueue) {\n      return;\n    }\n    try {\n      isExecutingMicrotasksQueue = true;\n      for (let index = 0; index < microtasksQueue.length; index += 1) {\n        // we use classic 'for' loop because the size of the currentTasks array may change while executing some of the callbacks due to queueMicrotask calls\n        microtasksQueue[index]();\n      }\n      microtasksQueue = [];\n      global._maybeFlushUIUpdatesQueue();\n    } finally {\n      isExecutingMicrotasksQueue = false;\n    }\n  };\n}\n\nfunction callMicrotasksOnUIThread() {\n  'worklet';\n  global.__callMicrotasks();\n}\n\nexport const callMicrotasks = SHOULD_BE_USE_WEB\n  ? () => {\n      // on web flushing is a noop as immediates are handled by the browser\n    }\n  : callMicrotasksOnUIThread;\n\n/**\n * Lets you asynchronously run [workletized](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#to-workletize) functions on the [UI thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI).\n *\n * This method does not schedule the work immediately but instead waits for other worklets\n * to be scheduled within the same JS loop. It uses queueMicrotask to schedule all the worklets\n * at once making sure they will run within the same frame boundaries on the UI thread.\n *\n * @param fun - A reference to a function you want to execute on the [UI thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI) from the [JavaScript thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI).\n * @returns A function that accepts arguments for the function passed as the first argument.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI\n */\n// @ts-expect-error This overload is correct since it's what user sees in his code\n// before it's transformed by Reanimated Babel plugin.\nexport function runOnUI<Args extends unknown[], ReturnValue>(\n  worklet: (...args: Args) => ReturnValue\n): (...args: Args) => void;\n\nexport function runOnUI<Args extends unknown[], ReturnValue>(\n  worklet: WorkletFunction<Args, ReturnValue>\n): (...args: Args) => void {\n  'worklet';\n  if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {\n    throw new Error(\n      '[Reanimated] `runOnUI` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.'\n    );\n  }\n  if (__DEV__ && !SHOULD_BE_USE_WEB && !isWorkletFunction(worklet)) {\n    throw new Error('[Reanimated] `runOnUI` can only be used on worklets.');\n  }\n  return (...args) => {\n    if (IS_JEST) {\n      // Mocking time in Jest is tricky as both requestAnimationFrame and queueMicrotask\n      // callbacks run on the same queue and can be interleaved. There is no way\n      // to flush particular queue in Jest and the only control over mocked timers\n      // is by using jest.advanceTimersByTime() method which advances all types\n      // of timers including immediate and animation callbacks. Ideally we'd like\n      // to have some way here to schedule work along with React updates, but\n      // that's not possible, and hence in Jest environment instead of using scheduling\n      // mechanism we just schedule the work ommiting the queue. This is ok for the\n      // uses that we currently have but may not be ok for future tests that we write.\n      NativeReanimatedModule.scheduleOnUI(\n        makeShareableCloneRecursive(() => {\n          'worklet';\n          worklet(...args);\n        })\n      );\n      return;\n    }\n    if (__DEV__) {\n      // in DEV mode we call shareable conversion here because in case the object\n      // can't be converted, we will get a meaningful stack-trace as opposed to the\n      // situation when conversion is only done via microtask queue. This does not\n      // make the app particularily less efficient as converted objects are cached\n      // and for a given worklet the conversion only happens once.\n      makeShareableCloneRecursive(worklet);\n      makeShareableCloneRecursive(args);\n    }\n    _runOnUIQueue.push([worklet as WorkletFunction, args]);\n    if (_runOnUIQueue.length === 1) {\n      queueMicrotask(() => {\n        const queue = _runOnUIQueue;\n        _runOnUIQueue = [];\n        NativeReanimatedModule.scheduleOnUI(\n          makeShareableCloneRecursive(() => {\n            'worklet';\n            // eslint-disable-next-line @typescript-eslint/no-shadow\n            queue.forEach(([worklet, args]) => {\n              worklet(...args);\n            });\n            callMicrotasks();\n          })\n        );\n      });\n    }\n  };\n}\n\n// @ts-expect-error Check `executeOnUIRuntimeSync` overload above.\nexport function executeOnUIRuntimeSync<Args extends unknown[], ReturnValue>(\n  worklet: (...args: Args) => ReturnValue\n): (...args: Args) => ReturnValue;\n\nexport function executeOnUIRuntimeSync<Args extends unknown[], ReturnValue>(\n  worklet: WorkletFunction<Args, ReturnValue>\n): (...args: Args) => ReturnValue {\n  return (...args) => {\n    return NativeReanimatedModule.executeOnUIRuntimeSync(\n      makeShareableCloneRecursive(() => {\n        'worklet';\n        const result = worklet(...args);\n        return makeShareableCloneOnUIRecursive(result);\n      })\n    );\n  };\n}\n\n// @ts-expect-error Check `runOnUI` overload above.\nexport function runOnUIImmediately<Args extends unknown[], ReturnValue>(\n  worklet: (...args: Args) => ReturnValue\n): WorkletFunction<Args, ReturnValue>;\n/**\n * Schedule a worklet to execute on the UI runtime skipping batching mechanism.\n */\nexport function runOnUIImmediately<Args extends unknown[], ReturnValue>(\n  worklet: WorkletFunction<Args, ReturnValue>\n): (...args: Args) => void {\n  'worklet';\n  if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {\n    throw new Error(\n      '[Reanimated] `runOnUIImmediately` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.'\n    );\n  }\n  if (__DEV__ && !SHOULD_BE_USE_WEB && !isWorkletFunction(worklet)) {\n    throw new Error(\n      '[Reanimated] `runOnUIImmediately` can only be used on worklets.'\n    );\n  }\n  return (...args) => {\n    NativeReanimatedModule.scheduleOnUI(\n      makeShareableCloneRecursive(() => {\n        'worklet';\n        worklet(...args);\n      })\n    );\n  };\n}\n\ntype ReleaseRemoteFunction<Args extends unknown[], ReturnValue> = {\n  (...args: Args): ReturnValue;\n};\n\ntype DevRemoteFunction<Args extends unknown[], ReturnValue> = {\n  __remoteFunction: (...args: Args) => ReturnValue;\n};\n\ntype RemoteFunction<Args extends unknown[], ReturnValue> =\n  | ReleaseRemoteFunction<Args, ReturnValue>\n  | DevRemoteFunction<Args, ReturnValue>;\n\nfunction runWorkletOnJS<Args extends unknown[], ReturnValue>(\n  worklet: WorkletFunction<Args, ReturnValue>,\n  ...args: Args\n): void {\n  // remote function that calls a worklet synchronously on the JS runtime\n  worklet(...args);\n}\n\n/**\n * Lets you asynchronously run non-[workletized](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#to-workletize) functions that couldn't otherwise run on the [UI thread](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#ui-thread).\n * This applies to most external libraries as they don't have their functions marked with \"worklet\"; directive.\n *\n * @param fun - A reference to a function you want to execute on the JavaScript thread from the UI thread.\n * @returns A function that accepts arguments for the function passed as the first argument.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnJS\n */\nexport function runOnJS<Args extends unknown[], ReturnValue>(\n  fun:\n    | ((...args: Args) => ReturnValue)\n    | RemoteFunction<Args, ReturnValue>\n    | WorkletFunction<Args, ReturnValue>\n): (...args: Args) => void {\n  'worklet';\n  type FunDevRemote = Extract<typeof fun, DevRemoteFunction<Args, ReturnValue>>;\n  if (SHOULD_BE_USE_WEB || !_WORKLET) {\n    // if we are already on the JS thread, we just schedule the worklet on the JS queue\n    return (...args) =>\n      queueMicrotask(\n        args.length\n          ? () => (fun as (...args: Args) => ReturnValue)(...args)\n          : (fun as () => ReturnValue)\n      );\n  }\n  if (isWorkletFunction<Args, ReturnValue>(fun)) {\n    // If `fun` is a worklet, we schedule a call of a remote function `runWorkletOnJS`\n    // and pass the worklet as a first argument followed by original arguments.\n\n    return (...args) =>\n      runOnJS(runWorkletOnJS<Args, ReturnValue>)(\n        fun as WorkletFunction<Args, ReturnValue>,\n        ...args\n      );\n  }\n  if ((fun as FunDevRemote).__remoteFunction) {\n    // In development mode the function provided as `fun` throws an error message\n    // such that when someone accidentally calls it directly on the UI runtime, they\n    // see that they should use `runOnJS` instead. To facilitate that we put the\n    // reference to the original remote function in the `__remoteFunction` property.\n    fun = (fun as FunDevRemote).__remoteFunction;\n  }\n  return (...args) => {\n    global._scheduleOnJS(\n      fun as\n        | ((...args: Args) => ReturnValue)\n        | WorkletFunction<Args, ReturnValue>,\n      args.length > 0\n        ? // TODO TYPESCRIPT this cast is terrible but will be fixed\n          (makeShareableCloneOnUIRecursive(args) as unknown as unknown[])\n        : undefined\n    );\n  };\n}\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,MAAM,EAAEC,cAAc,QAAQ,mBAAmB;AAE1D,SACEC,+BAA+B,EAC/BC,2BAA2B,QACtB,cAAc;AACrB,SAASC,iBAAiB,QAAQ,eAAe;AAEjD,MAAMC,OAAO,GAAGL,MAAM,CAAC,CAAC;AACxB,MAAMM,iBAAiB,GAAGL,cAAc,CAAC,CAAC;;AAE1C;AACA;AACA;AACA,IAAIM,aAAsE,GAAG,EAAE;AAE/E,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,SAAS;;EAET,IAAIC,eAAkC,GAAG,EAAE;EAC3C,IAAIC,0BAA0B,GAAG,KAAK;EACtCC,MAAM,CAACC,cAAc,GAAIC,QAAoB,IAAK;IAChDJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC;EAChC,CAAC;EAEDF,MAAM,CAACI,gBAAgB,GAAG,MAAM;IAC9B,IAAIL,0BAA0B,EAAE;MAC9B;IACF;IACA,IAAI;MACFA,0BAA0B,GAAG,IAAI;MACjC,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,eAAe,CAACQ,MAAM,EAAED,KAAK,IAAI,CAAC,EAAE;QAC9D;QACAP,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC;MAC1B;MACAP,eAAe,GAAG,EAAE;MACpBE,MAAM,CAACO,yBAAyB,CAAC,CAAC;IACpC,CAAC,SAAS;MACRR,0BAA0B,GAAG,KAAK;IACpC;EACF,CAAC;AACH;AAEA,SAASS,wBAAwBA,CAAA,EAAG;EAClC,SAAS;;EACTR,MAAM,CAACI,gBAAgB,CAAC,CAAC;AAC3B;AAEA,OAAO,MAAMK,cAAc,GAAGd,iBAAiB,GAC3C,MAAM;EACJ;AAAA,CACD,GACDa,wBAAwB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,OAAO,SAASE,OAAOA,CACrBC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACjB,iBAAiB,IAAIkB,QAAQ,EAAE;IAC7C,MAAM,IAAIC,KAAK,CACb,+JACF,CAAC;EACH;EACA,IAAIF,OAAO,IAAI,CAACjB,iBAAiB,IAAI,CAACF,iBAAiB,CAACkB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIG,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAO,CAAC,GAAGC,IAAI,KAAK;IAClB,IAAIrB,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAN,sBAAsB,CAAC4B,YAAY,CACjCxB,2BAA2B,CAAC,MAAM;QAChC,SAAS;;QACTmB,OAAO,CAAC,GAAGI,IAAI,CAAC;MAClB,CAAC,CACH,CAAC;MACD;IACF;IACA,IAAIH,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACApB,2BAA2B,CAACmB,OAAO,CAAC;MACpCnB,2BAA2B,CAACuB,IAAI,CAAC;IACnC;IACAnB,aAAa,CAACO,IAAI,CAAC,CAACQ,OAAO,EAAqBI,IAAI,CAAC,CAAC;IACtD,IAAInB,aAAa,CAACU,MAAM,KAAK,CAAC,EAAE;MAC9BL,cAAc,CAAC,MAAM;QACnB,MAAMgB,KAAK,GAAGrB,aAAa;QAC3BA,aAAa,GAAG,EAAE;QAClBR,sBAAsB,CAAC4B,YAAY,CACjCxB,2BAA2B,CAAC,MAAM;UAChC,SAAS;;UACT;UACAyB,KAAK,CAACC,OAAO,CAAC,CAAC,CAACP,OAAO,EAAEI,IAAI,CAAC,KAAK;YACjCJ,OAAO,CAAC,GAAGI,IAAI,CAAC;UAClB,CAAC,CAAC;UACFN,cAAc,CAAC,CAAC;QAClB,CAAC,CACH,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;;AAKA,OAAO,SAASU,sBAAsBA,CACpCR,OAA2C,EACX;EAChC,OAAO,CAAC,GAAGI,IAAI,KAAK;IAClB,OAAO3B,sBAAsB,CAAC+B,sBAAsB,CAClD3B,2BAA2B,CAAC,MAAM;MAChC,SAAS;;MACT,MAAM4B,MAAM,GAAGT,OAAO,CAAC,GAAGI,IAAI,CAAC;MAC/B,OAAOxB,+BAA+B,CAAC6B,MAAM,CAAC;IAChD,CAAC,CACH,CAAC;EACH,CAAC;AACH;;AAEA;;AAIA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAChCV,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACjB,iBAAiB,IAAIkB,QAAQ,EAAE;IAC7C,MAAM,IAAIC,KAAK,CACb,0KACF,CAAC;EACH;EACA,IAAIF,OAAO,IAAI,CAACjB,iBAAiB,IAAI,CAACF,iBAAiB,CAACkB,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIG,KAAK,CACb,iEACF,CAAC;EACH;EACA,OAAO,CAAC,GAAGC,IAAI,KAAK;IAClB3B,sBAAsB,CAAC4B,YAAY,CACjCxB,2BAA2B,CAAC,MAAM;MAChC,SAAS;;MACTmB,OAAO,CAAC,GAAGI,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACH,CAAC;AACH;AAcA,SAASO,cAAcA,CACrBX,OAA2C,EAC3C,GAAGI,IAAU,EACP;EACN;EACAJ,OAAO,CAAC,GAAGI,IAAI,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,OAAOA,CACrBC,GAGsC,EACb;EACzB,SAAS;;EAET,IAAI7B,iBAAiB,IAAI,CAACkB,QAAQ,EAAE;IAClC;IACA,OAAO,CAAC,GAAGE,IAAI,KACbd,cAAc,CACZc,IAAI,CAACT,MAAM,GACP,MAAOkB,GAAG,CAAoC,GAAGT,IAAI,CAAC,GACrDS,GACP,CAAC;EACL;EACA,IAAI/B,iBAAiB,CAAoB+B,GAAG,CAAC,EAAE;IAC7C;IACA;;IAEA,OAAO,CAAC,GAAGT,IAAI,KACbQ,OAAO,CAACD,cAAiC,CAAC,CACxCE,GAAG,EACH,GAAGT,IACL,CAAC;EACL;EACA,IAAKS,GAAG,CAAkBC,gBAAgB,EAAE;IAC1C;IACA;IACA;IACA;IACAD,GAAG,GAAIA,GAAG,CAAkBC,gBAAgB;EAC9C;EACA,OAAO,CAAC,GAAGV,IAAI,KAAK;IAClBf,MAAM,CAAC0B,aAAa,CAClBF,GAAG,EAGHT,IAAI,CAACT,MAAM,GAAG,CAAC;IACX;IACCf,+BAA+B,CAACwB,IAAI,CAAC,GACtCY,SACN,CAAC;EACH,CAAC;AACH", "ignoreList": []}