# ninja log v5
0	6	0	clean	17a56fa2c58de3da
3	756	7802361310431508	CMakeFiles/rnscreens.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	79cc17d06d1528fc
0	829	7802361311177688	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	4f373a013bbc2e95
829	884	7802361311753555	../../../../build/intermediates/cxx/Debug/2t6y4vk1/obj/arm64-v8a/librnscreens.so	f053e56f18ef4338
7	730	7802382900147150	CMakeFiles/rnscreens.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	20d03e1d6d30e0ed
4	805	7802382900858726	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	ab1ef35cb2a07698
805	861	7802382901459962	../../../../build/intermediates/cxx/Debug/2t6y4vk1/obj/arm64-v8a/librnscreens.so	2cdfc7b9953f8cbb
0	6	0	clean	17a56fa2c58de3da
3	740	7802385420569710	CMakeFiles/rnscreens.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	20d03e1d6d30e0ed
6	814	7802385421330853	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	ab1ef35cb2a07698
814	871	7802385421937220	../../../../build/intermediates/cxx/Debug/2t6y4vk1/obj/arm64-v8a/librnscreens.so	2cdfc7b9953f8cbb
