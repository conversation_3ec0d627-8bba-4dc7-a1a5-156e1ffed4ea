{"version": 3, "names": ["PropsAllowlists", "jsiConfigureProps", "assertNoOverlapInLists", "key", "NATIVE_THREAD_PROPS_WHITELIST", "UI_THREAD_PROPS_WHITELIST", "Error", "configureProps", "Object", "keys", "addWhitelistedNativeProps", "props", "oldSize", "length", "addWhitelistedUIProps", "PROCESSED_VIEW_NAMES", "Set", "adaptViewConfig", "viewConfig", "viewName", "uiViewClassName", "validAttributes", "has", "propsToAdd", "for<PERSON>ach", "add"], "sources": ["ConfigHelper.ts"], "sourcesContent": ["'use strict';\nimport { PropsAllowlists } from './propsAllowlists';\nimport { jsiConfigureProps } from './core';\nfunction assertNoOverlapInLists() {\n  for (const key in PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST) {\n    if (key in PropsAllowlists.UI_THREAD_PROPS_WHITELIST) {\n      throw new Error(\n        `[Reanimated] Property \\`${key}\\` was whitelisted both as UI and native prop. Please remove it from one of the lists.`\n      );\n    }\n  }\n}\n\nexport function configureProps(): void {\n  assertNoOverlapInLists();\n  jsiConfigureProps(\n    Object.keys(PropsAllowlists.UI_THREAD_PROPS_WHITELIST),\n    Object.keys(PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST)\n  );\n}\n\nexport function addWhitelistedNativeProps(\n  props: Record<string, boolean>\n): void {\n  const oldSize = Object.keys(\n    PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST\n  ).length;\n  PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST = {\n    ...PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST,\n    ...props,\n  };\n  if (\n    oldSize !==\n    Object.keys(PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST).length\n  ) {\n    configureProps();\n  }\n}\n\nexport function addWhitelistedUIProps(props: Record<string, boolean>): void {\n  const oldSize = Object.keys(PropsAllowlists.UI_THREAD_PROPS_WHITELIST).length;\n  PropsAllowlists.UI_THREAD_PROPS_WHITELIST = {\n    ...PropsAllowlists.UI_THREAD_PROPS_WHITELIST,\n    ...props,\n  };\n  if (\n    oldSize !== Object.keys(PropsAllowlists.UI_THREAD_PROPS_WHITELIST).length\n  ) {\n    configureProps();\n  }\n}\n\nconst PROCESSED_VIEW_NAMES = new Set();\n\nexport interface ViewConfig {\n  uiViewClassName: string;\n  validAttributes: Record<string, unknown>;\n}\n/**\n * updates UI props whitelist for given view host instance\n * this will work just once for every view name\n */\n\nexport function adaptViewConfig(viewConfig: ViewConfig): void {\n  const viewName = viewConfig.uiViewClassName;\n  const props = viewConfig.validAttributes;\n\n  // update whitelist of UI props for this view name only once\n  if (!PROCESSED_VIEW_NAMES.has(viewName)) {\n    const propsToAdd: Record<string, boolean> = {};\n    Object.keys(props).forEach((key) => {\n      // we don't want to add native props as they affect layout\n      // we also skip props which repeat here\n      if (\n        !(key in PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST) &&\n        !(key in PropsAllowlists.UI_THREAD_PROPS_WHITELIST)\n      ) {\n        propsToAdd[key] = true;\n      }\n    });\n    addWhitelistedUIProps(propsToAdd);\n\n    PROCESSED_VIEW_NAMES.add(viewName);\n  }\n}\n\nconfigureProps();\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAASC,iBAAiB,QAAQ,QAAQ;AAC1C,SAASC,sBAAsBA,CAAA,EAAG;EAChC,KAAK,MAAMC,GAAG,IAAIH,eAAe,CAACI,6BAA6B,EAAE;IAC/D,IAAID,GAAG,IAAIH,eAAe,CAACK,yBAAyB,EAAE;MACpD,MAAM,IAAIC,KAAK,CACZ,2BAA0BH,GAAI,wFACjC,CAAC;IACH;EACF;AACF;AAEA,OAAO,SAASI,cAAcA,CAAA,EAAS;EACrCL,sBAAsB,CAAC,CAAC;EACxBD,iBAAiB,CACfO,MAAM,CAACC,IAAI,CAACT,eAAe,CAACK,yBAAyB,CAAC,EACtDG,MAAM,CAACC,IAAI,CAACT,eAAe,CAACI,6BAA6B,CAC3D,CAAC;AACH;AAEA,OAAO,SAASM,yBAAyBA,CACvCC,KAA8B,EACxB;EACN,MAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CACzBT,eAAe,CAACI,6BAClB,CAAC,CAACS,MAAM;EACRb,eAAe,CAACI,6BAA6B,GAAG;IAC9C,GAAGJ,eAAe,CAACI,6BAA6B;IAChD,GAAGO;EACL,CAAC;EACD,IACEC,OAAO,KACPJ,MAAM,CAACC,IAAI,CAACT,eAAe,CAACI,6BAA6B,CAAC,CAACS,MAAM,EACjE;IACAN,cAAc,CAAC,CAAC;EAClB;AACF;AAEA,OAAO,SAASO,qBAAqBA,CAACH,KAA8B,EAAQ;EAC1E,MAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CAACT,eAAe,CAACK,yBAAyB,CAAC,CAACQ,MAAM;EAC7Eb,eAAe,CAACK,yBAAyB,GAAG;IAC1C,GAAGL,eAAe,CAACK,yBAAyB;IAC5C,GAAGM;EACL,CAAC;EACD,IACEC,OAAO,KAAKJ,MAAM,CAACC,IAAI,CAACT,eAAe,CAACK,yBAAyB,CAAC,CAACQ,MAAM,EACzE;IACAN,cAAc,CAAC,CAAC;EAClB;AACF;AAEA,MAAMQ,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAMtC;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,UAAsB,EAAQ;EAC5D,MAAMC,QAAQ,GAAGD,UAAU,CAACE,eAAe;EAC3C,MAAMT,KAAK,GAAGO,UAAU,CAACG,eAAe;;EAExC;EACA,IAAI,CAACN,oBAAoB,CAACO,GAAG,CAACH,QAAQ,CAAC,EAAE;IACvC,MAAMI,UAAmC,GAAG,CAAC,CAAC;IAC9Cf,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAACa,OAAO,CAAErB,GAAG,IAAK;MAClC;MACA;MACA,IACE,EAAEA,GAAG,IAAIH,eAAe,CAACI,6BAA6B,CAAC,IACvD,EAAED,GAAG,IAAIH,eAAe,CAACK,yBAAyB,CAAC,EACnD;QACAkB,UAAU,CAACpB,GAAG,CAAC,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IACFW,qBAAqB,CAACS,UAAU,CAAC;IAEjCR,oBAAoB,CAACU,GAAG,CAACN,QAAQ,CAAC;EACpC;AACF;AAEAZ,cAAc,CAAC,CAAC", "ignoreList": []}