{"version": 3, "names": ["shouldBeUseWeb", "isWorkletFunction", "valueUnpacker", "objectToUnpack", "category", "remoteFunctionName", "workletsCache", "global", "__workletsCache", "handleCache", "__handleCache", "undefined", "Map", "WeakMap", "workletHash", "__workletHash", "workletFun", "get", "initData", "__initData", "evalWithSourceMap", "code", "location", "sourceMap", "evalWithSourceUrl", "eval", "set", "functionInstance", "bind", "_recur", "__init", "value", "fun", "label", "Error", "__remoteFunction", "_toString", "__DEV__", "testWorklet", "closure", "__closure", "Object", "keys", "length", "getValueUnpackerCode"], "sources": ["valueUnpacker.ts"], "sourcesContent": ["'use strict';\nimport { shouldBeUseWeb } from './PlatformChecker';\nimport { isWorkletFunction } from './commonTypes';\nimport type { WorkletFunction } from './commonTypes';\n\nfunction valueUnpacker(\n  objectToUnpack: any,\n  category?: string,\n  remoteFunctionName?: string\n): any {\n  'worklet';\n  let workletsCache = global.__workletsCache;\n  let handleCache = global.__handleCache;\n  if (workletsCache === undefined) {\n    // init\n    workletsCache = global.__workletsCache = new Map();\n    handleCache = global.__handleCache = new WeakMap();\n  }\n  const workletHash = objectToUnpack.__workletHash;\n  if (workletHash !== undefined) {\n    let workletFun = workletsCache.get(workletHash);\n    if (workletFun === undefined) {\n      const initData = objectToUnpack.__initData;\n      if (global.evalWithSourceMap) {\n        // if the runtime (hermes only for now) supports loading source maps\n        // we want to use the proper filename for the location as it guarantees\n        // that debugger understands and loads the source code of the file where\n        // the worklet is defined.\n        workletFun = global.evalWithSourceMap(\n          '(' + initData.code + '\\n)',\n          initData.location,\n          initData.sourceMap\n        ) as (...args: any[]) => any;\n      } else if (global.evalWithSourceUrl) {\n        // if the runtime doesn't support loading source maps, in dev mode we\n        // can pass source url when evaluating the worklet. Now, instead of using\n        // the actual file location we use worklet hash, as it the allows us to\n        // properly symbolicate traces (see errors.ts for details)\n        workletFun = global.evalWithSourceUrl(\n          '(' + initData.code + '\\n)',\n          `worklet_${workletHash}`\n        ) as (...args: any[]) => any;\n      } else {\n        // in release we use the regular eval to save on JSI calls\n        // eslint-disable-next-line no-eval\n        workletFun = eval('(' + initData.code + '\\n)') as (\n          ...args: any[]\n        ) => any;\n      }\n      workletsCache.set(workletHash, workletFun);\n    }\n    const functionInstance = workletFun.bind(objectToUnpack);\n    objectToUnpack._recur = functionInstance;\n    return functionInstance;\n  } else if (objectToUnpack.__init !== undefined) {\n    let value = handleCache.get(objectToUnpack);\n    if (value === undefined) {\n      value = objectToUnpack.__init();\n      handleCache.set(objectToUnpack, value);\n    }\n    return value;\n  } else if (category === 'RemoteFunction') {\n    const fun = () => {\n      const label = remoteFunctionName\n        ? `function \\`${remoteFunctionName}\\``\n        : 'anonymous function';\n      throw new Error(`[Reanimated] Tried to synchronously call a non-worklet ${label} on the UI thread.\nSee https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#tried-to-synchronously-call-a-non-worklet-function-on-the-ui-thread for more details.`);\n    };\n    fun.__remoteFunction = objectToUnpack;\n    return fun;\n  } else {\n    throw new Error(\n      `[Reanimated] Data type in category \"${category}\" not recognized by value unpacker: \"${_toString(\n        objectToUnpack\n      )}\".`\n    );\n  }\n}\n\ntype ValueUnpacker = WorkletFunction<\n  [objectToUnpack: any, category?: string],\n  any\n>;\n\nif (__DEV__ && !shouldBeUseWeb()) {\n  const testWorklet = (() => {\n    'worklet';\n  }) as WorkletFunction<[], void>;\n  if (!isWorkletFunction(testWorklet)) {\n    throw new Error(\n      `[Reanimated] Failed to create a worklet. See https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#failed-to-create-a-worklet for more details.`\n    );\n  }\n  if (!isWorkletFunction(valueUnpacker)) {\n    throw new Error('[Reanimated] `valueUnpacker` is not a worklet');\n  }\n  const closure = (valueUnpacker as ValueUnpacker).__closure;\n  if (closure === undefined) {\n    throw new Error('[Reanimated] `valueUnpacker` closure is undefined');\n  }\n  if (Object.keys(closure).length !== 0) {\n    throw new Error('[Reanimated] `valueUnpacker` must have empty closure');\n  }\n}\n\nexport function getValueUnpackerCode() {\n  return (valueUnpacker as ValueUnpacker).__initData.code;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,cAAc,QAAQ,mBAAmB;AAClD,SAASC,iBAAiB,QAAQ,eAAe;AAGjD,SAASC,aAAaA,CACpBC,cAAmB,EACnBC,QAAiB,EACjBC,kBAA2B,EACtB;EACL,SAAS;;EACT,IAAIC,aAAa,GAAGC,MAAM,CAACC,eAAe;EAC1C,IAAIC,WAAW,GAAGF,MAAM,CAACG,aAAa;EACtC,IAAIJ,aAAa,KAAKK,SAAS,EAAE;IAC/B;IACAL,aAAa,GAAGC,MAAM,CAACC,eAAe,GAAG,IAAII,GAAG,CAAC,CAAC;IAClDH,WAAW,GAAGF,MAAM,CAACG,aAAa,GAAG,IAAIG,OAAO,CAAC,CAAC;EACpD;EACA,MAAMC,WAAW,GAAGX,cAAc,CAACY,aAAa;EAChD,IAAID,WAAW,KAAKH,SAAS,EAAE;IAC7B,IAAIK,UAAU,GAAGV,aAAa,CAACW,GAAG,CAACH,WAAW,CAAC;IAC/C,IAAIE,UAAU,KAAKL,SAAS,EAAE;MAC5B,MAAMO,QAAQ,GAAGf,cAAc,CAACgB,UAAU;MAC1C,IAAIZ,MAAM,CAACa,iBAAiB,EAAE;QAC5B;QACA;QACA;QACA;QACAJ,UAAU,GAAGT,MAAM,CAACa,iBAAiB,CACnC,GAAG,GAAGF,QAAQ,CAACG,IAAI,GAAG,KAAK,EAC3BH,QAAQ,CAACI,QAAQ,EACjBJ,QAAQ,CAACK,SACX,CAA4B;MAC9B,CAAC,MAAM,IAAIhB,MAAM,CAACiB,iBAAiB,EAAE;QACnC;QACA;QACA;QACA;QACAR,UAAU,GAAGT,MAAM,CAACiB,iBAAiB,CACnC,GAAG,GAAGN,QAAQ,CAACG,IAAI,GAAG,KAAK,EAC1B,WAAUP,WAAY,EACzB,CAA4B;MAC9B,CAAC,MAAM;QACL;QACA;QACAE,UAAU,GAAGS,IAAI,CAAC,GAAG,GAAGP,QAAQ,CAACG,IAAI,GAAG,KAAK,CAErC;MACV;MACAf,aAAa,CAACoB,GAAG,CAACZ,WAAW,EAAEE,UAAU,CAAC;IAC5C;IACA,MAAMW,gBAAgB,GAAGX,UAAU,CAACY,IAAI,CAACzB,cAAc,CAAC;IACxDA,cAAc,CAAC0B,MAAM,GAAGF,gBAAgB;IACxC,OAAOA,gBAAgB;EACzB,CAAC,MAAM,IAAIxB,cAAc,CAAC2B,MAAM,KAAKnB,SAAS,EAAE;IAC9C,IAAIoB,KAAK,GAAGtB,WAAW,CAACQ,GAAG,CAACd,cAAc,CAAC;IAC3C,IAAI4B,KAAK,KAAKpB,SAAS,EAAE;MACvBoB,KAAK,GAAG5B,cAAc,CAAC2B,MAAM,CAAC,CAAC;MAC/BrB,WAAW,CAACiB,GAAG,CAACvB,cAAc,EAAE4B,KAAK,CAAC;IACxC;IACA,OAAOA,KAAK;EACd,CAAC,MAAM,IAAI3B,QAAQ,KAAK,gBAAgB,EAAE;IACxC,MAAM4B,GAAG,GAAGA,CAAA,KAAM;MAChB,MAAMC,KAAK,GAAG5B,kBAAkB,GAC3B,cAAaA,kBAAmB,IAAG,GACpC,oBAAoB;MACxB,MAAM,IAAI6B,KAAK,CAAE,0DAAyDD,KAAM;AACtF,yKAAyK,CAAC;IACtK,CAAC;IACDD,GAAG,CAACG,gBAAgB,GAAGhC,cAAc;IACrC,OAAO6B,GAAG;EACZ,CAAC,MAAM;IACL,MAAM,IAAIE,KAAK,CACZ,uCAAsC9B,QAAS,wCAAuCgC,SAAS,CAC9FjC,cACF,CAAE,IACJ,CAAC;EACH;AACF;AAOA,IAAIkC,OAAO,IAAI,CAACrC,cAAc,CAAC,CAAC,EAAE;EAChC,MAAMsC,WAAW,GAAIA,CAAA,KAAM;IACzB,SAAS;EACX,CAA+B;EAC/B,IAAI,CAACrC,iBAAiB,CAACqC,WAAW,CAAC,EAAE;IACnC,MAAM,IAAIJ,KAAK,CACZ,0KACH,CAAC;EACH;EACA,IAAI,CAACjC,iBAAiB,CAACC,aAAa,CAAC,EAAE;IACrC,MAAM,IAAIgC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,MAAMK,OAAO,GAAIrC,aAAa,CAAmBsC,SAAS;EAC1D,IAAID,OAAO,KAAK5B,SAAS,EAAE;IACzB,MAAM,IAAIuB,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAIO,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;IACrC,MAAM,IAAIT,KAAK,CAAC,sDAAsD,CAAC;EACzE;AACF;AAEA,OAAO,SAASU,oBAAoBA,CAAA,EAAG;EACrC,OAAQ1C,aAAa,CAAmBiB,UAAU,CAACE,IAAI;AACzD", "ignoreList": []}