{"name": "jest-expo", "version": "54.0.12", "description": "A Jest preset to painlessly test your Expo / React Native apps.", "license": "MIT", "main": "src/index.js", "types": "src/index.d.ts", "repository": {"type": "git", "url": "git://github.com/expo/expo.git", "directory": "packages/jest-expo"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/jest-expo", "files": ["rsc", "android", "bin", "config", "ios", "node", "src", "universal", "web", "jest-preset.js"], "bin": {"jest": "bin/jest.js"}, "scripts": {"lint": "eslint .", "test": "jest"}, "jest": {"preset": "jest-expo/universal"}, "dependencies": {"@expo/config": "~12.0.9", "@expo/json-file": "^10.0.7", "@jest/create-cache-key-function": "^29.2.1", "@jest/globals": "^29.2.1", "babel-jest": "^29.2.1", "jest-environment-jsdom": "^29.2.1", "jest-snapshot": "^29.2.1", "jest-watch-select-projects": "^2.0.0", "jest-watch-typeahead": "2.2.1", "json5": "^2.2.3", "lodash": "^4.17.19", "react-server-dom-webpack": "~19.0.0", "react-test-renderer": "19.1.0", "server-only": "^0.0.1", "stacktrace-js": "^2.0.2"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "0d9ae61f3dea2e2b854576859e5b50fca5503fc1"}