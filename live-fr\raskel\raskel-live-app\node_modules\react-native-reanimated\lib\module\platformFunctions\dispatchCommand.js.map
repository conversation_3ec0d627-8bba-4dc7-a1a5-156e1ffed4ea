{"version": 3, "names": ["isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "dispatchCommand", "dispatchCommandFabric", "animatedRef", "commandName", "args", "_WORKLET", "shadowNodeWrapper", "global", "_dispatchCommandFabric", "dispatchCommandPaper", "viewTag", "_dispatchCommandPaper", "dispatchCommandJest", "console", "warn", "dispatchCommandChromeDebugger", "dispatchCommandDefault"], "sources": ["dispatchCommand.ts"], "sourcesContent": ["'use strict';\nimport type { ShadowNodeWrapper } from '../commonTypes';\nimport {\n  isChromeDebugger,\n  isFabric,\n  isJest,\n  shouldBeUseWeb,\n} from '../PlatformChecker';\nimport type {\n  AnimatedRef,\n  AnimatedRefOnJS,\n  AnimatedRefOnUI,\n} from '../hook/commonTypes';\nimport type { Component } from 'react';\n\ntype DispatchCommand = <T extends Component>(\n  animatedRef: AnimatedRef<T>,\n  commandName: string,\n  args?: unknown[]\n) => void;\n\n/**\n * Lets you synchronously call a command of a native component.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns) connected to the component you'd want to call the command on.\n * @param commandName - The name of the command to dispatch (e.g. `\"focus\"` or `\"scrollToEnd\"`).\n * @param args - An optional array of arguments for the command.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/dispatchCommand\n */\nexport let dispatchCommand: DispatchCommand;\n\nfunction dispatchCommandFabric(\n  animatedRef: AnimatedRefOnJS | AnimatedRefOnUI,\n  commandName: string,\n  args: Array<unknown> = []\n) {\n  'worklet';\n  if (!_WORKLET) {\n    return;\n  }\n\n  const shadowNodeWrapper = animatedRef() as ShadowNodeWrapper;\n  global._dispatchCommandFabric!(shadowNodeWrapper, commandName, args);\n}\n\nfunction dispatchCommandPaper(\n  animatedRef: AnimatedRefOnJS | AnimatedRefOnUI,\n  commandName: string,\n  args: Array<unknown> = []\n) {\n  'worklet';\n  if (!_WORKLET) {\n    return;\n  }\n\n  const viewTag = animatedRef() as number;\n  global._dispatchCommandPaper!(viewTag, commandName, args);\n}\n\nfunction dispatchCommandJest() {\n  console.warn('[Reanimated] dispatchCommand() is not supported with Jest.');\n}\n\nfunction dispatchCommandChromeDebugger() {\n  console.warn(\n    '[Reanimated] dispatchCommand() is not supported with Chrome Debugger.'\n  );\n}\n\nfunction dispatchCommandDefault() {\n  console.warn(\n    '[Reanimated] dispatchCommand() is not supported on this configuration.'\n  );\n}\n\nif (!shouldBeUseWeb()) {\n  // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n  // mapped as a different function in `shareableMappingCache` and\n  // TypeScript is not able to infer that.\n  if (isFabric()) {\n    dispatchCommand = dispatchCommandFabric as unknown as DispatchCommand;\n  } else {\n    dispatchCommand = dispatchCommandPaper as unknown as DispatchCommand;\n  }\n} else if (isJest()) {\n  dispatchCommand = dispatchCommandJest;\n} else if (isChromeDebugger()) {\n  dispatchCommand = dispatchCommandChromeDebugger;\n} else {\n  dispatchCommand = dispatchCommandDefault;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SACEA,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,oBAAoB;AAc3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAgC;AAE3C,SAASC,qBAAqBA,CAC5BC,WAA8C,EAC9CC,WAAmB,EACnBC,IAAoB,GAAG,EAAE,EACzB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb;EACF;EAEA,MAAMC,iBAAiB,GAAGJ,WAAW,CAAC,CAAsB;EAC5DK,MAAM,CAACC,sBAAsB,CAAEF,iBAAiB,EAAEH,WAAW,EAAEC,IAAI,CAAC;AACtE;AAEA,SAASK,oBAAoBA,CAC3BP,WAA8C,EAC9CC,WAAmB,EACnBC,IAAoB,GAAG,EAAE,EACzB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb;EACF;EAEA,MAAMK,OAAO,GAAGR,WAAW,CAAC,CAAW;EACvCK,MAAM,CAACI,qBAAqB,CAAED,OAAO,EAAEP,WAAW,EAAEC,IAAI,CAAC;AAC3D;AAEA,SAASQ,mBAAmBA,CAAA,EAAG;EAC7BC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;AAC5E;AAEA,SAASC,6BAA6BA,CAAA,EAAG;EACvCF,OAAO,CAACC,IAAI,CACV,uEACF,CAAC;AACH;AAEA,SAASE,sBAAsBA,CAAA,EAAG;EAChCH,OAAO,CAACC,IAAI,CACV,wEACF,CAAC;AACH;AAEA,IAAI,CAACf,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdG,eAAe,GAAGC,qBAAmD;EACvE,CAAC,MAAM;IACLD,eAAe,GAAGS,oBAAkD;EACtE;AACF,CAAC,MAAM,IAAIX,MAAM,CAAC,CAAC,EAAE;EACnBE,eAAe,GAAGY,mBAAmB;AACvC,CAAC,MAAM,IAAIhB,gBAAgB,CAAC,CAAC,EAAE;EAC7BI,eAAe,GAAGe,6BAA6B;AACjD,CAAC,MAAM;EACLf,eAAe,GAAGgB,sBAAsB;AAC1C", "ignoreList": []}