{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "ComplexAnimationBuilder", "PinwheelIn", "constructor", "args", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "animations", "opacity", "transform", "scale", "rotate", "createInstance", "PinwheelOut"], "sources": ["Pinwheel.ts"], "sourcesContent": ["'use strict';\nimport type { BaseAnimationBuilder } from '../animationBuilder';\nimport { ComplexAnimationBuilder } from '../animationBuilder';\nimport type {\n  EntryExitAnimationFunction,\n  IEntryExitAnimationBuilder,\n} from '../animationBuilder/commonTypes';\n\n/**\n * Entry with change in rotation, scale, and opacity. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#pinwheel\n */\nexport class PinwheelIn\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'PinwheelIn';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new PinwheelIn() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return () => {\n      'worklet';\n      return {\n        animations: {\n          opacity: delayFunction(delay, animation(1, config)),\n          transform: [\n            {\n              scale: delayFunction(delay, animation(1, config)),\n            },\n            {\n              rotate: delayFunction(delay, animation('0', config)),\n            },\n          ],\n        },\n        initialValues: {\n          opacity: 0,\n          transform: [\n            {\n              scale: 0,\n            },\n            {\n              rotate: '5',\n            },\n          ],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Exit with change in rotation, scale, and opacity. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#pinwheel\n */\nexport class PinwheelOut\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'PinwheelOut';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new PinwheelOut() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return () => {\n      'worklet';\n      return {\n        animations: {\n          opacity: delayFunction(delay, animation(0, config)),\n          transform: [\n            {\n              scale: delayFunction(delay, animation(0, config)),\n            },\n            {\n              rotate: delayFunction(delay, animation('5', config)),\n            },\n          ],\n        },\n        initialValues: {\n          opacity: 1,\n          transform: [\n            {\n              scale: 1,\n            },\n            {\n              rotate: '0',\n            },\n          ],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAEb,SAASW,uBAAuB,QAAQ,qBAAqB;AAM7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SACbD,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAO,MAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEX,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDS,SAAS,EAAE,CACT;cACEC,KAAK,EAAEb,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAClD,CAAC,EACD;cACEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,GAAG,EAAEC,MAAM,CAAC;YACrD,CAAC;UAEL,CAAC;UACDM,aAAa,EAAE;YACbE,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CACT;cACEC,KAAK,EAAE;YACT,CAAC,EACD;cACEC,MAAM,EAAE;YACV,CAAC,CACF;YACD,GAAGL;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA1CD,OAAOQ,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIlB,UAAU,CAAC,CAAC;EACzB;AAuCF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAtB,eAAA,CAnDasB,UAAU,gBAID,YAAY;AAsDlC,OAAO,MAAMmB,WAAW,SACdpB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAO,MAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,OAAO,EAAEX,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC;YACnDS,SAAS,EAAE,CACT;cACEC,KAAK,EAAEb,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAClD,CAAC,EACD;cACEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,GAAG,EAAEC,MAAM,CAAC;YACrD,CAAC;UAEL,CAAC;UACDM,aAAa,EAAE;YACbE,OAAO,EAAE,CAAC;YACVC,SAAS,EAAE,CACT;cACEC,KAAK,EAAE;YACT,CAAC,EACD;cACEC,MAAM,EAAE;YACV,CAAC,CACF;YACD,GAAGL;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA1CD,OAAOQ,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIC,WAAW,CAAC,CAAC;EAC1B;AAuCF;AAACzC,eAAA,CAjDYyC,WAAW,gBAIF,aAAa", "ignoreList": []}