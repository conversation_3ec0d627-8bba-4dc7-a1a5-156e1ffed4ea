{"version": 3, "names": ["runOnUIImmediately", "prepareUIRegistry", "frameCallbackRegistry", "Map", "activeFrameCallbacks", "Set", "previousFrameTimestamp", "nextCallId", "runCallbacks", "callId", "loop", "timestamp", "delta", "for<PERSON>ach", "callbackId", "callbackDetails", "get", "startTime", "callback", "timeSincePreviousFrame", "timeSinceFirstFrame", "size", "requestAnimationFrame", "registerFrameCallback", "set", "unregisterFrameCallback", "manageStateFrameCallback", "delete", "state", "add", "global", "_frameCallbackRegistry"], "sources": ["FrameCallbackRegistryUI.ts"], "sourcesContent": ["'use strict';\nimport { runOnUIImmediately } from '../threads';\n\ntype CallbackDetails = {\n  callback: (frameInfo: FrameInfo) => void;\n  startTime: number | null;\n};\n\nexport type FrameInfo = {\n  timestamp: number;\n  timeSincePreviousFrame: number | null;\n  timeSinceFirstFrame: number;\n};\n\nexport interface FrameCallbackRegistryUI {\n  frameCallbackRegistry: Map<number, CallbackDetails>;\n  activeFrameCallbacks: Set<number>;\n  previousFrameTimestamp: number | null;\n  runCallbacks: (callId: number) => void;\n  nextCallId: number;\n  registerFrameCallback: (\n    callback: (frameInfo: FrameInfo) => void,\n    callbackId: number\n  ) => void;\n  unregisterFrameCallback: (callbackId: number) => void;\n  manageStateFrameCallback: (callbackId: number, state: boolean) => void;\n}\n\nexport const prepareUIRegistry = runOnUIImmediately(() => {\n  'worklet';\n\n  const frameCallbackRegistry: FrameCallbackRegistryUI = {\n    frameCallbackRegistry: new Map<number, CallbackDetails>(),\n    activeFrameCallbacks: new Set<number>(),\n    previousFrameTimestamp: null,\n    nextCallId: 0,\n\n    runCallbacks(callId) {\n      const loop = (timestamp: number) => {\n        if (callId !== this.nextCallId) {\n          return;\n        }\n        if (this.previousFrameTimestamp === null) {\n          this.previousFrameTimestamp = timestamp;\n        }\n\n        const delta = timestamp - this.previousFrameTimestamp;\n\n        this.activeFrameCallbacks.forEach((callbackId: number) => {\n          const callbackDetails = this.frameCallbackRegistry.get(callbackId)!;\n\n          const { startTime } = callbackDetails;\n\n          if (startTime === null) {\n            // First frame\n            callbackDetails.startTime = timestamp;\n\n            callbackDetails.callback({\n              timestamp,\n              timeSincePreviousFrame: null,\n              timeSinceFirstFrame: 0,\n            });\n          } else {\n            // Next frame\n            callbackDetails.callback({\n              timestamp,\n              timeSincePreviousFrame: delta,\n              timeSinceFirstFrame: timestamp - startTime,\n            });\n          }\n        });\n\n        if (this.activeFrameCallbacks.size > 0) {\n          this.previousFrameTimestamp = timestamp;\n          requestAnimationFrame(loop);\n        } else {\n          this.previousFrameTimestamp = null;\n        }\n      };\n\n      // runCallback() should only be called after registering a callback,\n      // so if there is only one active callback, then it means that there were\n      // zero previously and the loop isn't running yet.\n      if (this.activeFrameCallbacks.size === 1 && callId === this.nextCallId) {\n        requestAnimationFrame(loop);\n      }\n    },\n\n    registerFrameCallback(\n      callback: (frameInfo: FrameInfo) => void,\n      callbackId: number\n    ) {\n      this.frameCallbackRegistry.set(callbackId, {\n        callback,\n        startTime: null,\n      });\n    },\n\n    unregisterFrameCallback(callbackId: number) {\n      this.manageStateFrameCallback(callbackId, false);\n      this.frameCallbackRegistry.delete(callbackId);\n    },\n\n    manageStateFrameCallback(callbackId: number, state: boolean) {\n      if (callbackId === -1) {\n        return;\n      }\n      if (state) {\n        this.activeFrameCallbacks.add(callbackId);\n        this.runCallbacks(this.nextCallId);\n      } else {\n        const callback = this.frameCallbackRegistry.get(callbackId)!;\n        callback.startTime = null;\n\n        this.activeFrameCallbacks.delete(callbackId);\n        if (this.activeFrameCallbacks.size === 0) {\n          this.nextCallId += 1;\n        }\n      }\n    },\n  };\n\n  global._frameCallbackRegistry = frameCallbackRegistry;\n});\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,kBAAkB,QAAQ,YAAY;AA2B/C,OAAO,MAAMC,iBAAiB,GAAGD,kBAAkB,CAAC,MAAM;EACxD,SAAS;;EAET,MAAME,qBAA8C,GAAG;IACrDA,qBAAqB,EAAE,IAAIC,GAAG,CAA0B,CAAC;IACzDC,oBAAoB,EAAE,IAAIC,GAAG,CAAS,CAAC;IACvCC,sBAAsB,EAAE,IAAI;IAC5BC,UAAU,EAAE,CAAC;IAEbC,YAAYA,CAACC,MAAM,EAAE;MACnB,MAAMC,IAAI,GAAIC,SAAiB,IAAK;QAClC,IAAIF,MAAM,KAAK,IAAI,CAACF,UAAU,EAAE;UAC9B;QACF;QACA,IAAI,IAAI,CAACD,sBAAsB,KAAK,IAAI,EAAE;UACxC,IAAI,CAACA,sBAAsB,GAAGK,SAAS;QACzC;QAEA,MAAMC,KAAK,GAAGD,SAAS,GAAG,IAAI,CAACL,sBAAsB;QAErD,IAAI,CAACF,oBAAoB,CAACS,OAAO,CAAEC,UAAkB,IAAK;UACxD,MAAMC,eAAe,GAAG,IAAI,CAACb,qBAAqB,CAACc,GAAG,CAACF,UAAU,CAAE;UAEnE,MAAM;YAAEG;UAAU,CAAC,GAAGF,eAAe;UAErC,IAAIE,SAAS,KAAK,IAAI,EAAE;YACtB;YACAF,eAAe,CAACE,SAAS,GAAGN,SAAS;YAErCI,eAAe,CAACG,QAAQ,CAAC;cACvBP,SAAS;cACTQ,sBAAsB,EAAE,IAAI;cAC5BC,mBAAmB,EAAE;YACvB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAL,eAAe,CAACG,QAAQ,CAAC;cACvBP,SAAS;cACTQ,sBAAsB,EAAEP,KAAK;cAC7BQ,mBAAmB,EAAET,SAAS,GAAGM;YACnC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QAEF,IAAI,IAAI,CAACb,oBAAoB,CAACiB,IAAI,GAAG,CAAC,EAAE;UACtC,IAAI,CAACf,sBAAsB,GAAGK,SAAS;UACvCW,qBAAqB,CAACZ,IAAI,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAACJ,sBAAsB,GAAG,IAAI;QACpC;MACF,CAAC;;MAED;MACA;MACA;MACA,IAAI,IAAI,CAACF,oBAAoB,CAACiB,IAAI,KAAK,CAAC,IAAIZ,MAAM,KAAK,IAAI,CAACF,UAAU,EAAE;QACtEe,qBAAqB,CAACZ,IAAI,CAAC;MAC7B;IACF,CAAC;IAEDa,qBAAqBA,CACnBL,QAAwC,EACxCJ,UAAkB,EAClB;MACA,IAAI,CAACZ,qBAAqB,CAACsB,GAAG,CAACV,UAAU,EAAE;QACzCI,QAAQ;QACRD,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAEDQ,uBAAuBA,CAACX,UAAkB,EAAE;MAC1C,IAAI,CAACY,wBAAwB,CAACZ,UAAU,EAAE,KAAK,CAAC;MAChD,IAAI,CAACZ,qBAAqB,CAACyB,MAAM,CAACb,UAAU,CAAC;IAC/C,CAAC;IAEDY,wBAAwBA,CAACZ,UAAkB,EAAEc,KAAc,EAAE;MAC3D,IAAId,UAAU,KAAK,CAAC,CAAC,EAAE;QACrB;MACF;MACA,IAAIc,KAAK,EAAE;QACT,IAAI,CAACxB,oBAAoB,CAACyB,GAAG,CAACf,UAAU,CAAC;QACzC,IAAI,CAACN,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;MACpC,CAAC,MAAM;QACL,MAAMW,QAAQ,GAAG,IAAI,CAAChB,qBAAqB,CAACc,GAAG,CAACF,UAAU,CAAE;QAC5DI,QAAQ,CAACD,SAAS,GAAG,IAAI;QAEzB,IAAI,CAACb,oBAAoB,CAACuB,MAAM,CAACb,UAAU,CAAC;QAC5C,IAAI,IAAI,CAACV,oBAAoB,CAACiB,IAAI,KAAK,CAAC,EAAE;UACxC,IAAI,CAACd,UAAU,IAAI,CAAC;QACtB;MACF;IACF;EACF,CAAC;EAEDuB,MAAM,CAACC,sBAAsB,GAAG7B,qBAAqB;AACvD,CAAC,CAAC", "ignoreList": []}