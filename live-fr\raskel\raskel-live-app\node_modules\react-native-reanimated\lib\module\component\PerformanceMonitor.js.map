{"version": 3, "names": ["React", "useEffect", "useRef", "TextInput", "StyleSheet", "View", "useSharedValue", "useAnimatedProps", "useFrameCallback", "createAnimatedComponent", "addWhitelistedNativeProps", "createCircularDoublesBuffer", "size", "next", "buffer", "Float32Array", "count", "push", "value", "oldValue", "oldCount", "Math", "min", "front", "notEmpty", "current", "index", "back", "DEFAULT_BUFFER_SIZE", "text", "AnimatedTextInput", "loopAnimationFrame", "fn", "lastTime", "loop", "requestAnimationFrame", "time", "getFps", "renderTimeInMs", "completeBufferRoutine", "timestamp", "round", "droppedTimestamp", "measuredRangeDuration", "JsPerformance", "smoothingFrames", "jsFps", "totalRenderTime", "circular<PERSON>uffer", "_", "currentFps", "toFixed", "animatedProps", "defaultValue", "createElement", "style", "styles", "container", "editable", "UiPerformance", "uiFps", "PerformanceMonitor", "monitor", "create", "flexDirection", "position", "backgroundColor", "zIndex", "header", "fontSize", "color", "paddingHorizontal", "fontFamily", "alignItems", "justifyContent", "flexWrap"], "sources": ["PerformanceMonitor.tsx"], "sourcesContent": ["'use strict';\n\nimport React, { useEffect, useRef } from 'react';\nimport { TextInput, StyleSheet, View } from 'react-native';\n\nimport type { FrameInfo } from '../frameCallback';\nimport { useSharedValue, useAnimatedProps, useFrameCallback } from '../hook';\nimport { createAnimatedComponent } from '../createAnimatedComponent';\nimport { addWhitelistedNativeProps } from '../ConfigHelper';\n\ntype CircularBuffer = ReturnType<typeof createCircularDoublesBuffer>;\nfunction createCircularDoublesBuffer(size: number) {\n  'worklet';\n\n  return {\n    next: 0 as number,\n    buffer: new Float32Array(size),\n    size,\n    count: 0 as number,\n\n    push(value: number): number | null {\n      const oldValue = this.buffer[this.next];\n      const oldCount = this.count;\n      this.buffer[this.next] = value;\n\n      this.next = (this.next + 1) % this.size;\n      this.count = Math.min(this.size, this.count + 1);\n      return oldCount === this.size ? oldValue : null;\n    },\n\n    front(): number | null {\n      const notEmpty = this.count > 0;\n      if (notEmpty) {\n        const current = this.next - 1;\n        const index = current < 0 ? this.size - 1 : current;\n        return this.buffer[index];\n      }\n      return null;\n    },\n\n    back(): number | null {\n      const notEmpty = this.count > 0;\n      return notEmpty ? this.buffer[this.next] : null;\n    },\n  };\n}\n\nconst DEFAULT_BUFFER_SIZE = 20;\naddWhitelistedNativeProps({ text: true });\nconst AnimatedTextInput = createAnimatedComponent(TextInput);\n\nfunction loopAnimationFrame(fn: (lastTime: number, time: number) => void) {\n  let lastTime = 0;\n\n  function loop() {\n    requestAnimationFrame((time) => {\n      if (lastTime > 0) {\n        fn(lastTime, time);\n      }\n      lastTime = time;\n      requestAnimationFrame(loop);\n    });\n  }\n\n  loop();\n}\n\nfunction getFps(renderTimeInMs: number): number {\n  'worklet';\n  return 1000 / renderTimeInMs;\n}\n\nfunction completeBufferRoutine(\n  buffer: CircularBuffer,\n  timestamp: number\n): number {\n  'worklet';\n  timestamp = Math.round(timestamp);\n\n  const droppedTimestamp = buffer.push(timestamp) ?? timestamp;\n\n  const measuredRangeDuration = timestamp - droppedTimestamp;\n\n  return getFps(measuredRangeDuration / buffer.count);\n}\n\nfunction JsPerformance({ smoothingFrames }: { smoothingFrames: number }) {\n  const jsFps = useSharedValue<string | null>(null);\n  const totalRenderTime = useSharedValue(0);\n  const circularBuffer = useRef<CircularBuffer>(\n    createCircularDoublesBuffer(smoothingFrames)\n  );\n\n  useEffect(() => {\n    loopAnimationFrame((_, timestamp) => {\n      timestamp = Math.round(timestamp);\n\n      const currentFps = completeBufferRoutine(\n        circularBuffer.current,\n        timestamp\n      );\n\n      // JS fps have to be measured every 2nd frame,\n      // thus 2x multiplication has to occur here\n      jsFps.value = (currentFps * 2).toFixed(0);\n    });\n  }, [jsFps, totalRenderTime]);\n\n  const animatedProps = useAnimatedProps(() => {\n    const text = 'JS: ' + (jsFps.value ?? 'N/A') + ' ';\n    return { text, defaultValue: text };\n  });\n\n  return (\n    <View style={styles.container}>\n      <AnimatedTextInput\n        style={styles.text}\n        animatedProps={animatedProps}\n        editable={false}\n      />\n    </View>\n  );\n}\n\nfunction UiPerformance({ smoothingFrames }: { smoothingFrames: number }) {\n  const uiFps = useSharedValue<string | null>(null);\n  const circularBuffer = useSharedValue<CircularBuffer | null>(null);\n\n  useFrameCallback(({ timestamp }: FrameInfo) => {\n    if (circularBuffer.value === null) {\n      circularBuffer.value = createCircularDoublesBuffer(smoothingFrames);\n    }\n\n    timestamp = Math.round(timestamp);\n\n    const currentFps = completeBufferRoutine(circularBuffer.value, timestamp);\n\n    uiFps.value = currentFps.toFixed(0);\n  });\n\n  const animatedProps = useAnimatedProps(() => {\n    const text = 'UI: ' + (uiFps.value ?? 'N/A') + ' ';\n    return { text, defaultValue: text };\n  });\n\n  return (\n    <View style={styles.container}>\n      <AnimatedTextInput\n        style={styles.text}\n        animatedProps={animatedProps}\n        editable={false}\n      />\n    </View>\n  );\n}\n\nexport type PerformanceMonitorProps = {\n  /**\n   * Sets amount of previous frames used for smoothing at highest expectedFps.\n   *\n   * Automatically scales down at lower frame rates.\n   *\n   * Affects jumpiness of the FPS measurements value.\n   */\n  smoothingFrames?: number;\n};\n\n/**\n * A component that lets you measure fps values on JS and UI threads on both the Paper and Fabric architectures.\n *\n * @param smoothingFrames - Determines amount of saved frames which will be used for fps value smoothing.\n */\nexport function PerformanceMonitor({\n  smoothingFrames = DEFAULT_BUFFER_SIZE,\n}: PerformanceMonitorProps) {\n  return (\n    <View style={styles.monitor}>\n      <JsPerformance smoothingFrames={smoothingFrames} />\n      <UiPerformance smoothingFrames={smoothingFrames} />\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  monitor: {\n    flexDirection: 'row',\n    position: 'absolute',\n    backgroundColor: '#0006',\n    zIndex: 1000,\n  },\n  header: {\n    fontSize: 14,\n    color: '#ffff',\n    paddingHorizontal: 5,\n  },\n  text: {\n    fontSize: 13,\n    color: '#ffff',\n    fontFamily: 'monospace',\n    paddingHorizontal: 3,\n  },\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n  },\n});\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,SAAS,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAG1D,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAC5E,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,yBAAyB,QAAQ,iBAAiB;AAG3D,SAASC,2BAA2BA,CAACC,IAAY,EAAE;EACjD,SAAS;;EAET,OAAO;IACLC,IAAI,EAAE,CAAW;IACjBC,MAAM,EAAE,IAAIC,YAAY,CAACH,IAAI,CAAC;IAC9BA,IAAI;IACJI,KAAK,EAAE,CAAW;IAElBC,IAAIA,CAACC,KAAa,EAAiB;MACjC,MAAMC,QAAQ,GAAG,IAAI,CAACL,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC;MACvC,MAAMO,QAAQ,GAAG,IAAI,CAACJ,KAAK;MAC3B,IAAI,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAGK,KAAK;MAE9B,IAAI,CAACL,IAAI,GAAG,CAAC,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAACD,IAAI;MACvC,IAAI,CAACI,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,IAAI,CAACV,IAAI,EAAE,IAAI,CAACI,KAAK,GAAG,CAAC,CAAC;MAChD,OAAOI,QAAQ,KAAK,IAAI,CAACR,IAAI,GAAGO,QAAQ,GAAG,IAAI;IACjD,CAAC;IAEDI,KAAKA,CAAA,EAAkB;MACrB,MAAMC,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC;MAC/B,IAAIQ,QAAQ,EAAE;QACZ,MAAMC,OAAO,GAAG,IAAI,CAACZ,IAAI,GAAG,CAAC;QAC7B,MAAMa,KAAK,GAAGD,OAAO,GAAG,CAAC,GAAG,IAAI,CAACb,IAAI,GAAG,CAAC,GAAGa,OAAO;QACnD,OAAO,IAAI,CAACX,MAAM,CAACY,KAAK,CAAC;MAC3B;MACA,OAAO,IAAI;IACb,CAAC;IAEDC,IAAIA,CAAA,EAAkB;MACpB,MAAMH,QAAQ,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC;MAC/B,OAAOQ,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,GAAG,IAAI;IACjD;EACF,CAAC;AACH;AAEA,MAAMe,mBAAmB,GAAG,EAAE;AAC9BlB,yBAAyB,CAAC;EAAEmB,IAAI,EAAE;AAAK,CAAC,CAAC;AACzC,MAAMC,iBAAiB,GAAGrB,uBAAuB,CAACN,SAAS,CAAC;AAE5D,SAAS4B,kBAAkBA,CAACC,EAA4C,EAAE;EACxE,IAAIC,QAAQ,GAAG,CAAC;EAEhB,SAASC,IAAIA,CAAA,EAAG;IACdC,qBAAqB,CAAEC,IAAI,IAAK;MAC9B,IAAIH,QAAQ,GAAG,CAAC,EAAE;QAChBD,EAAE,CAACC,QAAQ,EAAEG,IAAI,CAAC;MACpB;MACAH,QAAQ,GAAGG,IAAI;MACfD,qBAAqB,CAACD,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAA,IAAI,CAAC,CAAC;AACR;AAEA,SAASG,MAAMA,CAACC,cAAsB,EAAU;EAC9C,SAAS;;EACT,OAAO,IAAI,GAAGA,cAAc;AAC9B;AAEA,SAASC,qBAAqBA,CAC5BzB,MAAsB,EACtB0B,SAAiB,EACT;EACR,SAAS;;EACTA,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACD,SAAS,CAAC;EAEjC,MAAME,gBAAgB,GAAG5B,MAAM,CAACG,IAAI,CAACuB,SAAS,CAAC,IAAIA,SAAS;EAE5D,MAAMG,qBAAqB,GAAGH,SAAS,GAAGE,gBAAgB;EAE1D,OAAOL,MAAM,CAACM,qBAAqB,GAAG7B,MAAM,CAACE,KAAK,CAAC;AACrD;AAEA,SAAS4B,aAAaA,CAAC;EAAEC;AAA6C,CAAC,EAAE;EACvE,MAAMC,KAAK,GAAGxC,cAAc,CAAgB,IAAI,CAAC;EACjD,MAAMyC,eAAe,GAAGzC,cAAc,CAAC,CAAC,CAAC;EACzC,MAAM0C,cAAc,GAAG9C,MAAM,CAC3BS,2BAA2B,CAACkC,eAAe,CAC7C,CAAC;EAED5C,SAAS,CAAC,MAAM;IACd8B,kBAAkB,CAAC,CAACkB,CAAC,EAAET,SAAS,KAAK;MACnCA,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACD,SAAS,CAAC;MAEjC,MAAMU,UAAU,GAAGX,qBAAqB,CACtCS,cAAc,CAACvB,OAAO,EACtBe,SACF,CAAC;;MAED;MACA;MACAM,KAAK,CAAC5B,KAAK,GAAG,CAACgC,UAAU,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,KAAK,EAAEC,eAAe,CAAC,CAAC;EAE5B,MAAMK,aAAa,GAAG7C,gBAAgB,CAAC,MAAM;IAC3C,MAAMsB,IAAI,GAAG,MAAM,IAAIiB,KAAK,CAAC5B,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;IAClD,OAAO;MAAEW,IAAI;MAAEwB,YAAY,EAAExB;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,oBACE7B,KAAA,CAAAsD,aAAA,CAACjD,IAAI;IAACkD,KAAK,EAAEC,MAAM,CAACC;EAAU,gBAC5BzD,KAAA,CAAAsD,aAAA,CAACxB,iBAAiB;IAChByB,KAAK,EAAEC,MAAM,CAAC3B,IAAK;IACnBuB,aAAa,EAAEA,aAAc;IAC7BM,QAAQ,EAAE;EAAM,CACjB,CACG,CAAC;AAEX;AAEA,SAASC,aAAaA,CAAC;EAAEd;AAA6C,CAAC,EAAE;EACvE,MAAMe,KAAK,GAAGtD,cAAc,CAAgB,IAAI,CAAC;EACjD,MAAM0C,cAAc,GAAG1C,cAAc,CAAwB,IAAI,CAAC;EAElEE,gBAAgB,CAAC,CAAC;IAAEgC;EAAqB,CAAC,KAAK;IAC7C,IAAIQ,cAAc,CAAC9B,KAAK,KAAK,IAAI,EAAE;MACjC8B,cAAc,CAAC9B,KAAK,GAAGP,2BAA2B,CAACkC,eAAe,CAAC;IACrE;IAEAL,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACD,SAAS,CAAC;IAEjC,MAAMU,UAAU,GAAGX,qBAAqB,CAACS,cAAc,CAAC9B,KAAK,EAAEsB,SAAS,CAAC;IAEzEoB,KAAK,CAAC1C,KAAK,GAAGgC,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAG7C,gBAAgB,CAAC,MAAM;IAC3C,MAAMsB,IAAI,GAAG,MAAM,IAAI+B,KAAK,CAAC1C,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG;IAClD,OAAO;MAAEW,IAAI;MAAEwB,YAAY,EAAExB;IAAK,CAAC;EACrC,CAAC,CAAC;EAEF,oBACE7B,KAAA,CAAAsD,aAAA,CAACjD,IAAI;IAACkD,KAAK,EAAEC,MAAM,CAACC;EAAU,gBAC5BzD,KAAA,CAAAsD,aAAA,CAACxB,iBAAiB;IAChByB,KAAK,EAAEC,MAAM,CAAC3B,IAAK;IACnBuB,aAAa,EAAEA,aAAc;IAC7BM,QAAQ,EAAE;EAAM,CACjB,CACG,CAAC;AAEX;AAaA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAAC;EACjChB,eAAe,GAAGjB;AACK,CAAC,EAAE;EAC1B,oBACE5B,KAAA,CAAAsD,aAAA,CAACjD,IAAI;IAACkD,KAAK,EAAEC,MAAM,CAACM;EAAQ,gBAC1B9D,KAAA,CAAAsD,aAAA,CAACV,aAAa;IAACC,eAAe,EAAEA;EAAgB,CAAE,CAAC,eACnD7C,KAAA,CAAAsD,aAAA,CAACK,aAAa;IAACd,eAAe,EAAEA;EAAgB,CAAE,CAC9C,CAAC;AAEX;AAEA,MAAMW,MAAM,GAAGpD,UAAU,CAAC2D,MAAM,CAAC;EAC/BD,OAAO,EAAE;IACPE,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,UAAU;IACpBC,eAAe,EAAE,OAAO;IACxBC,MAAM,EAAE;EACV,CAAC;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdC,iBAAiB,EAAE;EACrB,CAAC;EACD1C,IAAI,EAAE;IACJwC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,OAAO;IACdE,UAAU,EAAE,WAAW;IACvBD,iBAAiB,EAAE;EACrB,CAAC;EACDd,SAAS,EAAE;IACTgB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,aAAa,EAAE,KAAK;IACpBW,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}