# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# REANIMATED_COMMON_CPP_SOURCES at src/main/cpp/reanimated/CMakeLists.txt:3 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/PropsRegistry.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Tools/FeaturesConfig.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# WORKLETS_COMMON_CPP_SOURCES at src/main/cpp/worklets/CMakeLists.txt:3 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Registries/EventHandlerRegistry.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/SharedItems/Shareables.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/AsyncQueue.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSISerializer.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSLogger.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSScheduler.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/ReanimatedVersion.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/UIScheduler.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/WorkletEventHandler.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# REANIMATED_ANDROID_CPP_SOURCES at src/main/cpp/reanimated/CMakeLists.txt:5 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64/CMakeFiles/cmake.verify_globs")
endif()

# WORKLETS_ANDROID_CPP_SOURCES at src/main/cpp/worklets/CMakeLists.txt:5 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp"
  "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64/CMakeFiles/cmake.verify_globs")
endif()
