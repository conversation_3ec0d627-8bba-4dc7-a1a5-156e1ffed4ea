/**
 * Automatically generated file. DO NOT MODIFY
 */
package expo.modules;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String LIBRARY_PACKAGE_NAME = "expo.modules";
  public static final String BUILD_TYPE = "debug";
  // Field from default config.
  public static final String EXPO_MODULES_CORE_VERSION = "2.5.0";
  // Field from default config.
  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = false;
}
