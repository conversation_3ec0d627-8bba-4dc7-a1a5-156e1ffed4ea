{"version": 3, "names": ["WebEasings", "convertAnimationObjectToKeyframes", "animationObject", "keyframe", "name", "timestamp", "style", "Object", "entries", "step", "property", "values", "easingName", "toString", "for<PERSON>ach", "value", "transformProperty", "transformPropertyValue"], "sources": ["animationParser.ts"], "sourcesContent": ["'use strict';\n\nimport { WebEasings } from './Easing.web';\nimport type { WebEasingsNames } from './Easing.web';\n\nexport interface ReanimatedWebTransformProperties {\n  translateX?: string;\n  translateY?: string;\n  rotate?: string;\n  rotateX?: string;\n  rotateY?: string;\n  scale?: number | string;\n  scaleX?: number;\n  scaleY?: number;\n  perspective?: string;\n  skew?: string;\n  skewX?: string;\n}\n\nexport interface AnimationStyle {\n  opacity?: number;\n  transform?: ReanimatedWebTransformProperties[];\n}\n\nexport interface AnimationData {\n  name: string;\n  style: Record<number, AnimationStyle>;\n  duration: number;\n}\n\nexport interface TransitionData {\n  translateX: number;\n  translateY: number;\n  scaleX: number;\n  scaleY: number;\n  reversed?: boolean;\n  easingX?: string;\n  easingY?: string;\n  entering?: any;\n  exiting?: any;\n}\n\nexport function convertAnimationObjectToKeyframes(\n  animationObject: AnimationData\n) {\n  let keyframe = `@keyframes ${animationObject.name} { `;\n\n  for (const [timestamp, style] of Object.entries(animationObject.style)) {\n    const step =\n      timestamp === 'from' ? 0 : timestamp === 'to' ? 100 : timestamp;\n\n    keyframe += `${step}% { `;\n\n    for (const [property, values] of Object.entries(style)) {\n      if (property === 'easing') {\n        let easingName: WebEasingsNames = 'linear';\n\n        if (values in WebEasings) {\n          easingName = values;\n        } else if (values.name in WebEasings) {\n          easingName = values.name;\n        }\n\n        keyframe += `animation-timing-function: cubic-bezier(${WebEasings[\n          easingName\n        ].toString()});`;\n\n        continue;\n      }\n\n      if (property === 'originX') {\n        keyframe += `left: ${values}px; `;\n        continue;\n      }\n\n      if (property === 'originY') {\n        keyframe += `top: ${values}px; `;\n        continue;\n      }\n\n      if (property !== 'transform') {\n        keyframe += `${property}: ${values}; `;\n        continue;\n      }\n\n      keyframe += `transform:`;\n\n      values.forEach((value: ReanimatedWebTransformProperties) => {\n        for (const [\n          transformProperty,\n          transformPropertyValue,\n        ] of Object.entries(value)) {\n          keyframe += ` ${transformProperty}(${transformPropertyValue})`;\n        }\n      });\n      keyframe += `; `; // Property end\n    }\n    keyframe += `} `; // Timestamp end\n  }\n  keyframe += `} `; // Keyframe end\n\n  return keyframe;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,UAAU,QAAQ,cAAc;AAwCzC,OAAO,SAASC,iCAAiCA,CAC/CC,eAA8B,EAC9B;EACA,IAAIC,QAAQ,GAAI,cAAaD,eAAe,CAACE,IAAK,KAAI;EAEtD,KAAK,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,eAAe,CAACI,KAAK,CAAC,EAAE;IACtE,MAAMG,IAAI,GACRJ,SAAS,KAAK,MAAM,GAAG,CAAC,GAAGA,SAAS,KAAK,IAAI,GAAG,GAAG,GAAGA,SAAS;IAEjEF,QAAQ,IAAK,GAAEM,IAAK,MAAK;IAEzB,KAAK,MAAM,CAACC,QAAQ,EAAEC,MAAM,CAAC,IAAIJ,MAAM,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACtD,IAAII,QAAQ,KAAK,QAAQ,EAAE;QACzB,IAAIE,UAA2B,GAAG,QAAQ;QAE1C,IAAID,MAAM,IAAIX,UAAU,EAAE;UACxBY,UAAU,GAAGD,MAAM;QACrB,CAAC,MAAM,IAAIA,MAAM,CAACP,IAAI,IAAIJ,UAAU,EAAE;UACpCY,UAAU,GAAGD,MAAM,CAACP,IAAI;QAC1B;QAEAD,QAAQ,IAAK,2CAA0CH,UAAU,CAC/DY,UAAU,CACX,CAACC,QAAQ,CAAC,CAAE,IAAG;QAEhB;MACF;MAEA,IAAIH,QAAQ,KAAK,SAAS,EAAE;QAC1BP,QAAQ,IAAK,SAAQQ,MAAO,MAAK;QACjC;MACF;MAEA,IAAID,QAAQ,KAAK,SAAS,EAAE;QAC1BP,QAAQ,IAAK,QAAOQ,MAAO,MAAK;QAChC;MACF;MAEA,IAAID,QAAQ,KAAK,WAAW,EAAE;QAC5BP,QAAQ,IAAK,GAAEO,QAAS,KAAIC,MAAO,IAAG;QACtC;MACF;MAEAR,QAAQ,IAAK,YAAW;MAExBQ,MAAM,CAACG,OAAO,CAAEC,KAAuC,IAAK;QAC1D,KAAK,MAAM,CACTC,iBAAiB,EACjBC,sBAAsB,CACvB,IAAIV,MAAM,CAACC,OAAO,CAACO,KAAK,CAAC,EAAE;UAC1BZ,QAAQ,IAAK,IAAGa,iBAAkB,IAAGC,sBAAuB,GAAE;QAChE;MACF,CAAC,CAAC;MACFd,QAAQ,IAAK,IAAG,CAAC,CAAC;IACpB;IACAA,QAAQ,IAAK,IAAG,CAAC,CAAC;EACpB;EACAA,QAAQ,IAAK,IAAG,CAAC,CAAC;;EAElB,OAAOA,QAAQ;AACjB", "ignoreList": []}