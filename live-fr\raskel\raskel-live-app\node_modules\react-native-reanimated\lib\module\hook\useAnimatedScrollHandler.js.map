{"version": 3, "names": ["useHandler", "useEvent", "useAnimatedScrollHandler", "handlers", "dependencies", "scrollHandlers", "onScroll", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribeForEvents", "onBeginDrag", "undefined", "push", "onEndDrag", "onMomentumBegin", "onMomentumEnd", "event", "eventName", "endsWith"], "sources": ["useAnimatedScrollHandler.ts"], "sourcesContent": ["'use strict';\nimport type {\n  DependencyList,\n  RNNativeScrollEvent,\n  ReanimatedScrollEvent,\n} from './commonTypes';\nimport { useHandler } from './useHandler';\nimport type { EventHandlerInternal, EventHandlerProcessed } from './useEvent';\nimport { useEvent } from './useEvent';\n\nexport type ScrollHandler<\n  Context extends Record<string, unknown> = Record<string, unknown>\n> = (event: ReanimatedScrollEvent, context: Context) => void;\nexport interface ScrollHandlers<Context extends Record<string, unknown>> {\n  onScroll?: ScrollHandler<Context>;\n  onBeginDrag?: ScrollHandler<Context>;\n  onEndDrag?: ScrollHandler<Context>;\n  onMomentumBegin?: ScrollHandler<Context>;\n  onMomentumEnd?: ScrollHandler<Context>;\n}\n\nexport type ScrollHandlerProcessed<\n  Context extends Record<string, unknown> = Record<string, unknown>\n> = EventHandlerProcessed<RNNativeScrollEvent, Context>;\n\nexport type ScrollHandlerInternal = EventHandlerInternal<RNNativeScrollEvent>;\n\n/**\n * Lets you run callbacks on ScrollView events. Supports `onScroll`, `onBeginDrag`, `onEndDrag`, `onMomentumBegin`, and `onMomentumEnd` events.\n *\n * These callbacks are automatically workletized and ran on the UI thread.\n *\n * @param handlers - An object containing event handlers.\n * @param dependencies - An optional array of dependencies. Only relevant when using Reanimated without the Babel plugin on the Web.\n * @returns An object you need to pass to `onScroll` prop on the `Animated.ScrollView` component.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/useAnimatedScrollHandler\n */\n// @ts-expect-error This overload is required by our API.\nexport function useAnimatedScrollHandler<\n  Context extends Record<string, unknown>\n>(\n  handlers: ScrollHandler<Context> | ScrollHandlers<Context>,\n  dependencies?: DependencyList\n): ScrollHandlerProcessed<Context>;\n\nexport function useAnimatedScrollHandler<\n  Context extends Record<string, unknown>\n>(\n  handlers: ScrollHandlers<Context> | ScrollHandler<Context>,\n  dependencies?: DependencyList\n) {\n  // case when handlers is a function\n  const scrollHandlers: ScrollHandlers<Context> =\n    typeof handlers === 'function' ? { onScroll: handlers } : handlers;\n  const { context, doDependenciesDiffer } = useHandler<\n    RNNativeScrollEvent,\n    Context\n  >(scrollHandlers as Record<string, ScrollHandler<Context>>, dependencies);\n\n  // build event subscription array\n  const subscribeForEvents = ['onScroll'];\n  if (scrollHandlers.onBeginDrag !== undefined) {\n    subscribeForEvents.push('onScrollBeginDrag');\n  }\n  if (scrollHandlers.onEndDrag !== undefined) {\n    subscribeForEvents.push('onScrollEndDrag');\n  }\n  if (scrollHandlers.onMomentumBegin !== undefined) {\n    subscribeForEvents.push('onMomentumScrollBegin');\n  }\n  if (scrollHandlers.onMomentumEnd !== undefined) {\n    subscribeForEvents.push('onMomentumScrollEnd');\n  }\n\n  return useEvent<RNNativeScrollEvent, Context>(\n    (event: ReanimatedScrollEvent) => {\n      'worklet';\n      const {\n        onScroll,\n        onBeginDrag,\n        onEndDrag,\n        onMomentumBegin,\n        onMomentumEnd,\n      } = scrollHandlers;\n      if (onScroll && event.eventName.endsWith('onScroll')) {\n        onScroll(event, context);\n      } else if (onBeginDrag && event.eventName.endsWith('onScrollBeginDrag')) {\n        onBeginDrag(event, context);\n      } else if (onEndDrag && event.eventName.endsWith('onScrollEndDrag')) {\n        onEndDrag(event, context);\n      } else if (\n        onMomentumBegin &&\n        event.eventName.endsWith('onMomentumScrollBegin')\n      ) {\n        onMomentumBegin(event, context);\n      } else if (\n        onMomentumEnd &&\n        event.eventName.endsWith('onMomentumScrollEnd')\n      ) {\n        onMomentumEnd(event, context);\n      }\n    },\n    subscribeForEvents,\n    doDependenciesDiffer\n    // Read https://github.com/software-mansion/react-native-reanimated/pull/5056\n    // for more information about this cast.\n  ) as unknown as ScrollHandlerInternal;\n}\n"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,UAAU,QAAQ,cAAc;AAEzC,SAASC,QAAQ,QAAQ,YAAY;;AAmBrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA,OAAO,SAASC,wBAAwBA,CAGtCC,QAA0D,EAC1DC,YAA6B,EAC7B;EACA;EACA,MAAMC,cAAuC,GAC3C,OAAOF,QAAQ,KAAK,UAAU,GAAG;IAAEG,QAAQ,EAAEH;EAAS,CAAC,GAAGA,QAAQ;EACpE,MAAM;IAAEI,OAAO;IAAEC;EAAqB,CAAC,GAAGR,UAAU,CAGlDK,cAAc,EAA4CD,YAAY,CAAC;;EAEzE;EACA,MAAMK,kBAAkB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAIJ,cAAc,CAACK,WAAW,KAAKC,SAAS,EAAE;IAC5CF,kBAAkB,CAACG,IAAI,CAAC,mBAAmB,CAAC;EAC9C;EACA,IAAIP,cAAc,CAACQ,SAAS,KAAKF,SAAS,EAAE;IAC1CF,kBAAkB,CAACG,IAAI,CAAC,iBAAiB,CAAC;EAC5C;EACA,IAAIP,cAAc,CAACS,eAAe,KAAKH,SAAS,EAAE;IAChDF,kBAAkB,CAACG,IAAI,CAAC,uBAAuB,CAAC;EAClD;EACA,IAAIP,cAAc,CAACU,aAAa,KAAKJ,SAAS,EAAE;IAC9CF,kBAAkB,CAACG,IAAI,CAAC,qBAAqB,CAAC;EAChD;EAEA,OAAOX,QAAQ,CACZe,KAA4B,IAAK;IAChC,SAAS;;IACT,MAAM;MACJV,QAAQ;MACRI,WAAW;MACXG,SAAS;MACTC,eAAe;MACfC;IACF,CAAC,GAAGV,cAAc;IAClB,IAAIC,QAAQ,IAAIU,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACpDZ,QAAQ,CAACU,KAAK,EAAET,OAAO,CAAC;IAC1B,CAAC,MAAM,IAAIG,WAAW,IAAIM,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACvER,WAAW,CAACM,KAAK,EAAET,OAAO,CAAC;IAC7B,CAAC,MAAM,IAAIM,SAAS,IAAIG,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MACnEL,SAAS,CAACG,KAAK,EAAET,OAAO,CAAC;IAC3B,CAAC,MAAM,IACLO,eAAe,IACfE,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,uBAAuB,CAAC,EACjD;MACAJ,eAAe,CAACE,KAAK,EAAET,OAAO,CAAC;IACjC,CAAC,MAAM,IACLQ,aAAa,IACbC,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAC/C;MACAH,aAAa,CAACC,KAAK,EAAET,OAAO,CAAC;IAC/B;EACF,CAAC,EACDE,kBAAkB,EAClBD;EACA;EACA;EACF,CAAC;AACH", "ignoreList": []}