{"version": 3, "names": ["makeMutable", "makeViewDescriptorsSet", "shareableViewDescriptors", "data", "add", "item", "modify", "descriptors", "index", "findIndex", "descriptor", "tag", "push", "remove", "viewTag", "splice"], "sources": ["ViewDescriptorsSet.ts"], "sourcesContent": ["'use strict';\nimport { makeMutable } from './core';\nimport type { SharedValue } from './commonTypes';\nimport type { Descriptor } from './hook/commonTypes';\n\nexport interface ViewDescriptorsSet {\n  shareableViewDescriptors: SharedValue<Descriptor[]>;\n  add: (item: Descriptor) => void;\n  remove: (viewTag: number) => void;\n}\n\nexport function makeViewDescriptorsSet(): ViewDescriptorsSet {\n  const shareableViewDescriptors = makeMutable<Descriptor[]>([]);\n  const data: ViewDescriptorsSet = {\n    shareableViewDescriptors,\n    add: (item: Descriptor) => {\n      shareableViewDescriptors.modify((descriptors) => {\n        'worklet';\n        const index = descriptors.findIndex(\n          (descriptor) => descriptor.tag === item.tag\n        );\n        if (index !== -1) {\n          descriptors[index] = item;\n        } else {\n          descriptors.push(item);\n        }\n        return descriptors;\n      }, false);\n    },\n\n    remove: (viewTag: number) => {\n      shareableViewDescriptors.modify((descriptors) => {\n        'worklet';\n        const index = descriptors.findIndex(\n          (descriptor) => descriptor.tag === viewTag\n        );\n        if (index !== -1) {\n          descriptors.splice(index, 1);\n        }\n        return descriptors;\n      }, false);\n    },\n  };\n  return data;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,WAAW,QAAQ,QAAQ;AAUpC,OAAO,SAASC,sBAAsBA,CAAA,EAAuB;EAC3D,MAAMC,wBAAwB,GAAGF,WAAW,CAAe,EAAE,CAAC;EAC9D,MAAMG,IAAwB,GAAG;IAC/BD,wBAAwB;IACxBE,GAAG,EAAGC,IAAgB,IAAK;MACzBH,wBAAwB,CAACI,MAAM,CAAEC,WAAW,IAAK;QAC/C,SAAS;;QACT,MAAMC,KAAK,GAAGD,WAAW,CAACE,SAAS,CAChCC,UAAU,IAAKA,UAAU,CAACC,GAAG,KAAKN,IAAI,CAACM,GAC1C,CAAC;QACD,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,WAAW,CAACC,KAAK,CAAC,GAAGH,IAAI;QAC3B,CAAC,MAAM;UACLE,WAAW,CAACK,IAAI,CAACP,IAAI,CAAC;QACxB;QACA,OAAOE,WAAW;MACpB,CAAC,EAAE,KAAK,CAAC;IACX,CAAC;IAEDM,MAAM,EAAGC,OAAe,IAAK;MAC3BZ,wBAAwB,CAACI,MAAM,CAAEC,WAAW,IAAK;QAC/C,SAAS;;QACT,MAAMC,KAAK,GAAGD,WAAW,CAACE,SAAS,CAChCC,UAAU,IAAKA,UAAU,CAACC,GAAG,KAAKG,OACrC,CAAC;QACD,IAAIN,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,WAAW,CAACQ,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;QAC9B;QACA,OAAOD,WAAW;MACpB,CAAC,EAAE,KAAK,CAAC;IACX;EACF,CAAC;EACD,OAAOJ,IAAI;AACb", "ignoreList": []}