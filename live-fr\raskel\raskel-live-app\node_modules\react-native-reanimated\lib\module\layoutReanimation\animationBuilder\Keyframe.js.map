{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "Easing", "<PERSON><PERSON><PERSON><PERSON>", "withSequence", "withTiming", "ReduceMotion", "assertEasingIsWorklet", "getReduceMotionFromConfig", "InnerKeyframe", "constructor", "definitions", "System", "delay", "delayV", "delayFunction", "getDelayFunction", "keyframes", "initialValues", "parseDefinitions", "callback", "callbackV", "animations", "addAnimation", "keyframePoints", "length", "animation", "duration", "easing", "linear", "map", "keyframePoint", "includes", "transform", "push", "split", "keys", "for<PERSON>ach", "transformProp", "index", "transformPropKey", "makeKey<PERSON>ey", "parsedKeyframes", "from", "Error", "to", "styleProp", "Array", "isArray", "transformStyle", "durationV", "animationKeyPoints", "getAnimationDuration", "currentKeyPoint", "maxDuration", "currentDuration", "reduce", "acc", "addKeyPoint", "__DEV__", "filter", "parseInt", "sort", "a", "b", "keyPoint", "keyframe", "addKeyPointWith", "durationMs", "delayMs", "<PERSON><PERSON><PERSON><PERSON>", "reduceMotion", "reduceMotionV", "_", "Keyframe"], "sources": ["Keyframe.ts"], "sourcesContent": ["'use strict';\nimport { Easing } from '../../Easing';\nimport { withDelay, withSequence, withTiming } from '../../animation';\nimport type {\n  AnimationFunction,\n  EntryExitAnimationFunction,\n  IEntryExitAnimationBuilder,\n  KeyframeProps,\n  StylePropsWithArrayTransform,\n} from './commonTypes';\nimport type {\n  StyleProps,\n  EasingFunction,\n  TransformArrayItem,\n} from '../../commonTypes';\nimport { ReduceMotion } from '../../commonTypes';\nimport {\n  assertEasingIsWorklet,\n  getReduceMotionFromConfig,\n} from '../../animation/util';\n\ninterface KeyframePoint {\n  duration: number;\n  value: number | string;\n  easing?: EasingFunction;\n}\ninterface ParsedKeyframesDefinition {\n  initialValues: StyleProps;\n  keyframes: Record<string, KeyframePoint[]>;\n}\nclass InnerKeyframe implements IEntryExitAnimationBuilder {\n  durationV?: number;\n  delayV?: number;\n  reduceMotionV: ReduceMotion = ReduceMotion.System;\n  callbackV?: (finished: boolean) => void;\n  definitions: Record<string, KeyframeProps>;\n\n  /*\n    Keyframe definition should be passed in the constructor as the map\n    which keys are between range 0 - 100 (%) and correspond to the point in the animation progress.\n  */\n  constructor(definitions: Record<string, KeyframeProps>) {\n    this.definitions = definitions;\n  }\n\n  private parseDefinitions(): ParsedKeyframesDefinition {\n    /* \n        Each style property contain an array with all their key points: \n        value, duration of transition to that value, and optional easing function (defaults to Linear)\n    */\n    const parsedKeyframes: Record<string, KeyframePoint[]> = {};\n    /*\n      Parsing keyframes 'from' and 'to'.\n    */\n    if (this.definitions.from) {\n      if (this.definitions['0']) {\n        throw new Error(\n          \"[Reanimated] You cannot provide both keyframe 0 and 'from' as they both specified initial values.\"\n        );\n      }\n      this.definitions['0'] = this.definitions.from;\n      delete this.definitions.from;\n    }\n    if (this.definitions.to) {\n      if (this.definitions['100']) {\n        throw new Error(\n          \"[Reanimated] You cannot provide both keyframe 100 and 'to' as they both specified values at the end of the animation.\"\n        );\n      }\n      this.definitions['100'] = this.definitions.to;\n      delete this.definitions.to;\n    }\n    /* \n       One of the assumptions is that keyframe  0 is required to properly set initial values.\n       Every other keyframe should contain properties from the set provided as initial values.\n    */\n    if (!this.definitions['0']) {\n      throw new Error(\n        \"[Reanimated] Please provide 0 or 'from' keyframe with initial state of your object.\"\n      );\n    }\n    const initialValues: StyleProps = this.definitions['0'] as StyleProps;\n    /*\n      Initialize parsedKeyframes for properties provided in initial keyframe\n    */\n    Object.keys(initialValues).forEach((styleProp: string) => {\n      if (styleProp === 'transform') {\n        if (!Array.isArray(initialValues.transform)) {\n          return;\n        }\n        initialValues.transform.forEach((transformStyle, index) => {\n          Object.keys(transformStyle).forEach((transformProp: string) => {\n            parsedKeyframes[makeKeyframeKey(index, transformProp)] = [];\n          });\n        });\n      } else {\n        parsedKeyframes[styleProp] = [];\n      }\n    });\n\n    const duration: number = this.durationV ? this.durationV : 500;\n    const animationKeyPoints: Array<string> = Array.from(\n      Object.keys(this.definitions)\n    );\n\n    const getAnimationDuration = (\n      key: string,\n      currentKeyPoint: number\n    ): number => {\n      const maxDuration = (currentKeyPoint / 100) * duration;\n      const currentDuration = parsedKeyframes[key].reduce(\n        (acc: number, value: KeyframePoint) => acc + value.duration,\n        0\n      );\n      return maxDuration - currentDuration;\n    };\n\n    /* \n       Other keyframes can't contain properties that were not specified in initial keyframe.\n    */\n    const addKeyPoint = ({\n      key,\n      value,\n      currentKeyPoint,\n      easing,\n    }: {\n      key: string;\n      value: string | number;\n      currentKeyPoint: number;\n      easing?: EasingFunction;\n    }): void => {\n      if (!(key in parsedKeyframes)) {\n        throw new Error(\n          \"[Reanimated] Keyframe can contain only that set of properties that were provide with initial values (keyframe 0 or 'from')\"\n        );\n      }\n\n      if (__DEV__ && easing) {\n        assertEasingIsWorklet(easing);\n      }\n\n      parsedKeyframes[key].push({\n        duration: getAnimationDuration(key, currentKeyPoint),\n        value,\n        easing,\n      });\n    };\n    animationKeyPoints\n      .filter((value: string) => parseInt(value) !== 0)\n      .sort((a: string, b: string) => parseInt(a) - parseInt(b))\n      .forEach((keyPoint: string) => {\n        if (parseInt(keyPoint) < 0 || parseInt(keyPoint) > 100) {\n          throw new Error(\n            '[Reanimated] Keyframe should be in between range 0 - 100.'\n          );\n        }\n        const keyframe: KeyframeProps = this.definitions[keyPoint];\n        const easing = keyframe.easing;\n        delete keyframe.easing;\n        const addKeyPointWith = (key: string, value: string | number) =>\n          addKeyPoint({\n            key,\n            value,\n            currentKeyPoint: parseInt(keyPoint),\n            easing,\n          });\n        Object.keys(keyframe).forEach((key: string) => {\n          if (key === 'transform') {\n            if (!Array.isArray(keyframe.transform)) {\n              return;\n            }\n            keyframe.transform.forEach((transformStyle, index) => {\n              Object.keys(transformStyle).forEach((transformProp: string) => {\n                addKeyPointWith(\n                  makeKeyframeKey(index, transformProp),\n                  transformStyle[\n                    transformProp as keyof typeof transformStyle\n                  ] as number | string // Here we assume that user has passed props of proper type.\n                  // I don't think it's worthwhile to check if he passed i.e. `Animated.Node`.\n                );\n              });\n            });\n          } else {\n            addKeyPointWith(key, keyframe[key]);\n          }\n        });\n      });\n    return { initialValues, keyframes: parsedKeyframes };\n  }\n\n  duration(durationMs: number): InnerKeyframe {\n    this.durationV = durationMs;\n    return this;\n  }\n\n  delay(delayMs: number): InnerKeyframe {\n    this.delayV = delayMs;\n    return this;\n  }\n\n  withCallback(callback: (finsihed: boolean) => void): InnerKeyframe {\n    this.callbackV = callback;\n    return this;\n  }\n\n  reduceMotion(reduceMotionV: ReduceMotion): this {\n    this.reduceMotionV = reduceMotionV;\n    return this;\n  }\n\n  private getDelayFunction(): AnimationFunction {\n    const delay = this.delayV;\n    const reduceMotion = this.reduceMotionV;\n    return delay\n      ? // eslint-disable-next-line @typescript-eslint/no-shadow\n        (delay, animation) => {\n          'worklet';\n          return withDelay(delay, animation, reduceMotion);\n        }\n      : (_, animation) => {\n          'worklet';\n          animation.reduceMotion = getReduceMotionFromConfig(reduceMotion);\n          return animation;\n        };\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delay = this.delayV;\n    const delayFunction = this.getDelayFunction();\n    const { keyframes, initialValues } = this.parseDefinitions();\n    const callback = this.callbackV;\n\n    return () => {\n      'worklet';\n      const animations: StylePropsWithArrayTransform = {};\n\n      /* \n            For each style property, an animations sequence is created that corresponds with its key points.\n            Transform style properties require special handling because of their nested structure.\n      */\n      const addAnimation = (key: string) => {\n        const keyframePoints = keyframes[key];\n        // in case if property was only passed as initial value\n        if (keyframePoints.length === 0) {\n          return;\n        }\n        const animation = delayFunction(\n          delay,\n          keyframePoints.length === 1\n            ? withTiming(keyframePoints[0].value, {\n                duration: keyframePoints[0].duration,\n                easing: keyframePoints[0].easing\n                  ? keyframePoints[0].easing\n                  : Easing.linear,\n              })\n            : withSequence(\n                ...keyframePoints.map((keyframePoint: KeyframePoint) =>\n                  withTiming(keyframePoint.value, {\n                    duration: keyframePoint.duration,\n                    easing: keyframePoint.easing\n                      ? keyframePoint.easing\n                      : Easing.linear,\n                  })\n                )\n              )\n        );\n        if (key.includes('transform')) {\n          if (!('transform' in animations)) {\n            animations.transform = [];\n          }\n          animations.transform!.push(<TransformArrayItem>{\n            [key.split(':')[1]]: animation,\n          });\n        } else {\n          animations[key] = animation;\n        }\n      };\n      Object.keys(initialValues).forEach((key: string) => {\n        if (key.includes('transform')) {\n          initialValues[key].forEach(\n            (transformProp: Record<string, number | string>, index: number) => {\n              Object.keys(transformProp).forEach((transformPropKey: string) => {\n                addAnimation(makeKeyframeKey(index, transformPropKey));\n              });\n            }\n          );\n        } else {\n          addAnimation(key);\n        }\n      });\n      return {\n        animations,\n        initialValues,\n        callback,\n      };\n    };\n  };\n}\n\nfunction makeKeyframeKey(index: number, transformProp: string) {\n  'worklet';\n  return `${index}_transform:${transformProp}`;\n}\n\n// TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\nexport declare class ReanimatedKeyframe {\n  constructor(definitions: Record<string, KeyframeProps>);\n  duration(durationMs: number): ReanimatedKeyframe;\n  delay(delayMs: number): ReanimatedKeyframe;\n  reduceMotion(reduceMotionV: ReduceMotion): ReanimatedKeyframe;\n  withCallback(callback: (finished: boolean) => void): ReanimatedKeyframe;\n}\n\n// TODO TYPESCRIPT This temporary cast is to get rid of .d.ts file.\nexport const Keyframe = InnerKeyframe as unknown as typeof ReanimatedKeyframe;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,MAAM,QAAQ,cAAc;AACrC,SAASC,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAarE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SACEC,qBAAqB,EACrBC,yBAAyB,QACpB,sBAAsB;AAW7B,MAAMC,aAAa,CAAuC;EAOxD;AACF;AACA;AACA;EACEC,WAAWA,CAACC,WAA0C,EAAE;IAAA9B,eAAA;IAAAA,eAAA;IAAAA,eAAA,wBAR1ByB,YAAY,CAACM,MAAM;IAAA/B,eAAA;IAAAA,eAAA;IAAAA,eAAA,gBAiMzC,MAAkC;MACxC,MAAMgC,KAAK,GAAG,IAAI,CAACC,MAAM;MACzB,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM;QAAEC,SAAS;QAAEC;MAAc,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC5D,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAE/B,OAAO,MAAM;QACX,SAAS;;QACT,MAAMC,UAAwC,GAAG,CAAC,CAAC;;QAEnD;AACN;AACA;AACA;QACM,MAAMC,YAAY,GAAIxC,GAAW,IAAK;UACpC,MAAMyC,cAAc,GAAGP,SAAS,CAAClC,GAAG,CAAC;UACrC;UACA,IAAIyC,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;YAC/B;UACF;UACA,MAAMC,SAAS,GAAGX,aAAa,CAC7BF,KAAK,EACLW,cAAc,CAACC,MAAM,KAAK,CAAC,GACvBpB,UAAU,CAACmB,cAAc,CAAC,CAAC,CAAC,CAACxC,KAAK,EAAE;YAClC2C,QAAQ,EAAEH,cAAc,CAAC,CAAC,CAAC,CAACG,QAAQ;YACpCC,MAAM,EAAEJ,cAAc,CAAC,CAAC,CAAC,CAACI,MAAM,GAC5BJ,cAAc,CAAC,CAAC,CAAC,CAACI,MAAM,GACxB1B,MAAM,CAAC2B;UACb,CAAC,CAAC,GACFzB,YAAY,CACV,GAAGoB,cAAc,CAACM,GAAG,CAAEC,aAA4B,IACjD1B,UAAU,CAAC0B,aAAa,CAAC/C,KAAK,EAAE;YAC9B2C,QAAQ,EAAEI,aAAa,CAACJ,QAAQ;YAChCC,MAAM,EAAEG,aAAa,CAACH,MAAM,GACxBG,aAAa,CAACH,MAAM,GACpB1B,MAAM,CAAC2B;UACb,CAAC,CACH,CACF,CACN,CAAC;UACD,IAAI9C,GAAG,CAACiD,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC7B,IAAI,EAAE,WAAW,IAAIV,UAAU,CAAC,EAAE;cAChCA,UAAU,CAACW,SAAS,GAAG,EAAE;YAC3B;YACAX,UAAU,CAACW,SAAS,CAAEC,IAAI,CAAqB;cAC7C,CAACnD,GAAG,CAACoD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGT;YACvB,CAAC,CAAC;UACJ,CAAC,MAAM;YACLJ,UAAU,CAACvC,GAAG,CAAC,GAAG2C,SAAS;UAC7B;QACF,CAAC;QACDxC,MAAM,CAACkD,IAAI,CAAClB,aAAa,CAAC,CAACmB,OAAO,CAAEtD,GAAW,IAAK;UAClD,IAAIA,GAAG,CAACiD,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC7Bd,aAAa,CAACnC,GAAG,CAAC,CAACsD,OAAO,CACxB,CAACC,aAA8C,EAAEC,KAAa,KAAK;cACjErD,MAAM,CAACkD,IAAI,CAACE,aAAa,CAAC,CAACD,OAAO,CAAEG,gBAAwB,IAAK;gBAC/DjB,YAAY,CAACkB,eAAe,CAACF,KAAK,EAAEC,gBAAgB,CAAC,CAAC;cACxD,CAAC,CAAC;YACJ,CACF,CAAC;UACH,CAAC,MAAM;YACLjB,YAAY,CAACxC,GAAG,CAAC;UACnB;QACF,CAAC,CAAC;QACF,OAAO;UACLuC,UAAU;UACVJ,aAAa;UACbE;QACF,CAAC;MACH,CAAC;IACH,CAAC;IA9PC,IAAI,CAACT,WAAW,GAAGA,WAAW;EAChC;EAEQQ,gBAAgBA,CAAA,EAA8B;IACpD;AACJ;AACA;AACA;IACI,MAAMuB,eAAgD,GAAG,CAAC,CAAC;IAC3D;AACJ;AACA;IACI,IAAI,IAAI,CAAC/B,WAAW,CAACgC,IAAI,EAAE;MACzB,IAAI,IAAI,CAAChC,WAAW,CAAC,GAAG,CAAC,EAAE;QACzB,MAAM,IAAIiC,KAAK,CACb,mGACF,CAAC;MACH;MACA,IAAI,CAACjC,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,CAACgC,IAAI;MAC7C,OAAO,IAAI,CAAChC,WAAW,CAACgC,IAAI;IAC9B;IACA,IAAI,IAAI,CAAChC,WAAW,CAACkC,EAAE,EAAE;MACvB,IAAI,IAAI,CAAClC,WAAW,CAAC,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAIiC,KAAK,CACb,uHACF,CAAC;MACH;MACA,IAAI,CAACjC,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAACA,WAAW,CAACkC,EAAE;MAC7C,OAAO,IAAI,CAAClC,WAAW,CAACkC,EAAE;IAC5B;IACA;AACJ;AACA;AACA;IACI,IAAI,CAAC,IAAI,CAAClC,WAAW,CAAC,GAAG,CAAC,EAAE;MAC1B,MAAM,IAAIiC,KAAK,CACb,qFACF,CAAC;IACH;IACA,MAAM1B,aAAyB,GAAG,IAAI,CAACP,WAAW,CAAC,GAAG,CAAe;IACrE;AACJ;AACA;IACIzB,MAAM,CAACkD,IAAI,CAAClB,aAAa,CAAC,CAACmB,OAAO,CAAES,SAAiB,IAAK;MACxD,IAAIA,SAAS,KAAK,WAAW,EAAE;QAC7B,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC9B,aAAa,CAACe,SAAS,CAAC,EAAE;UAC3C;QACF;QACAf,aAAa,CAACe,SAAS,CAACI,OAAO,CAAC,CAACY,cAAc,EAAEV,KAAK,KAAK;UACzDrD,MAAM,CAACkD,IAAI,CAACa,cAAc,CAAC,CAACZ,OAAO,CAAEC,aAAqB,IAAK;YAC7DI,eAAe,CAACD,eAAe,CAACF,KAAK,EAAED,aAAa,CAAC,CAAC,GAAG,EAAE;UAC7D,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLI,eAAe,CAACI,SAAS,CAAC,GAAG,EAAE;MACjC;IACF,CAAC,CAAC;IAEF,MAAMnB,QAAgB,GAAG,IAAI,CAACuB,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,GAAG;IAC9D,MAAMC,kBAAiC,GAAGJ,KAAK,CAACJ,IAAI,CAClDzD,MAAM,CAACkD,IAAI,CAAC,IAAI,CAACzB,WAAW,CAC9B,CAAC;IAED,MAAMyC,oBAAoB,GAAGA,CAC3BrE,GAAW,EACXsE,eAAuB,KACZ;MACX,MAAMC,WAAW,GAAID,eAAe,GAAG,GAAG,GAAI1B,QAAQ;MACtD,MAAM4B,eAAe,GAAGb,eAAe,CAAC3D,GAAG,CAAC,CAACyE,MAAM,CACjD,CAACC,GAAW,EAAEzE,KAAoB,KAAKyE,GAAG,GAAGzE,KAAK,CAAC2C,QAAQ,EAC3D,CACF,CAAC;MACD,OAAO2B,WAAW,GAAGC,eAAe;IACtC,CAAC;;IAED;AACJ;AACA;IACI,MAAMG,WAAW,GAAGA,CAAC;MACnB3E,GAAG;MACHC,KAAK;MACLqE,eAAe;MACfzB;IAMF,CAAC,KAAW;MACV,IAAI,EAAE7C,GAAG,IAAI2D,eAAe,CAAC,EAAE;QAC7B,MAAM,IAAIE,KAAK,CACb,4HACF,CAAC;MACH;MAEA,IAAIe,OAAO,IAAI/B,MAAM,EAAE;QACrBrB,qBAAqB,CAACqB,MAAM,CAAC;MAC/B;MAEAc,eAAe,CAAC3D,GAAG,CAAC,CAACmD,IAAI,CAAC;QACxBP,QAAQ,EAAEyB,oBAAoB,CAACrE,GAAG,EAAEsE,eAAe,CAAC;QACpDrE,KAAK;QACL4C;MACF,CAAC,CAAC;IACJ,CAAC;IACDuB,kBAAkB,CACfS,MAAM,CAAE5E,KAAa,IAAK6E,QAAQ,CAAC7E,KAAK,CAAC,KAAK,CAAC,CAAC,CAChD8E,IAAI,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKH,QAAQ,CAACE,CAAC,CAAC,GAAGF,QAAQ,CAACG,CAAC,CAAC,CAAC,CACzD3B,OAAO,CAAE4B,QAAgB,IAAK;MAC7B,IAAIJ,QAAQ,CAACI,QAAQ,CAAC,GAAG,CAAC,IAAIJ,QAAQ,CAACI,QAAQ,CAAC,GAAG,GAAG,EAAE;QACtD,MAAM,IAAIrB,KAAK,CACb,2DACF,CAAC;MACH;MACA,MAAMsB,QAAuB,GAAG,IAAI,CAACvD,WAAW,CAACsD,QAAQ,CAAC;MAC1D,MAAMrC,MAAM,GAAGsC,QAAQ,CAACtC,MAAM;MAC9B,OAAOsC,QAAQ,CAACtC,MAAM;MACtB,MAAMuC,eAAe,GAAGA,CAACpF,GAAW,EAAEC,KAAsB,KAC1D0E,WAAW,CAAC;QACV3E,GAAG;QACHC,KAAK;QACLqE,eAAe,EAAEQ,QAAQ,CAACI,QAAQ,CAAC;QACnCrC;MACF,CAAC,CAAC;MACJ1C,MAAM,CAACkD,IAAI,CAAC8B,QAAQ,CAAC,CAAC7B,OAAO,CAAEtD,GAAW,IAAK;QAC7C,IAAIA,GAAG,KAAK,WAAW,EAAE;UACvB,IAAI,CAACgE,KAAK,CAACC,OAAO,CAACkB,QAAQ,CAACjC,SAAS,CAAC,EAAE;YACtC;UACF;UACAiC,QAAQ,CAACjC,SAAS,CAACI,OAAO,CAAC,CAACY,cAAc,EAAEV,KAAK,KAAK;YACpDrD,MAAM,CAACkD,IAAI,CAACa,cAAc,CAAC,CAACZ,OAAO,CAAEC,aAAqB,IAAK;cAC7D6B,eAAe,CACb1B,eAAe,CAACF,KAAK,EAAED,aAAa,CAAC,EACrCW,cAAc,CACZX,aAAa,CACd,CAAoB;cACrB;cACF,CAAC;YACH,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACL6B,eAAe,CAACpF,GAAG,EAAEmF,QAAQ,CAACnF,GAAG,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,OAAO;MAAEmC,aAAa;MAAED,SAAS,EAAEyB;IAAgB,CAAC;EACtD;EAEAf,QAAQA,CAACyC,UAAkB,EAAiB;IAC1C,IAAI,CAAClB,SAAS,GAAGkB,UAAU;IAC3B,OAAO,IAAI;EACb;EAEAvD,KAAKA,CAACwD,OAAe,EAAiB;IACpC,IAAI,CAACvD,MAAM,GAAGuD,OAAO;IACrB,OAAO,IAAI;EACb;EAEAC,YAAYA,CAAClD,QAAqC,EAAiB;IACjE,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,OAAO,IAAI;EACb;EAEAmD,YAAYA,CAACC,aAA2B,EAAQ;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACb;EAEQxD,gBAAgBA,CAAA,EAAsB;IAC5C,MAAMH,KAAK,GAAG,IAAI,CAACC,MAAM;IACzB,MAAMyD,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,OAAO3D,KAAK;IACR;IACA,CAACA,KAAK,EAAEa,SAAS,KAAK;MACpB,SAAS;;MACT,OAAOvB,SAAS,CAACU,KAAK,EAAEa,SAAS,EAAE6C,YAAY,CAAC;IAClD,CAAC,GACD,CAACE,CAAC,EAAE/C,SAAS,KAAK;MAChB,SAAS;;MACTA,SAAS,CAAC6C,YAAY,GAAG/D,yBAAyB,CAAC+D,YAAY,CAAC;MAChE,OAAO7C,SAAS;IAClB,CAAC;EACP;AAyEF;AAEA,SAASe,eAAeA,CAACF,KAAa,EAAED,aAAqB,EAAE;EAC7D,SAAS;;EACT,OAAQ,GAAEC,KAAM,cAAaD,aAAc,EAAC;AAC9C;;AAEA;;AASA;AACA,OAAO,MAAMoC,QAAQ,GAAGjE,aAAqD", "ignoreList": []}