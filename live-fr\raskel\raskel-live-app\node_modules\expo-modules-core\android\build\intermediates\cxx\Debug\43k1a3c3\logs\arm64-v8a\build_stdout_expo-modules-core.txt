ninja: Entering directory `C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\.cxx\Debug\43k1a3c3\arm64-v8a'
[1/42] Building CXX object CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch
[2/42] Building CXX object CMakeFiles/expo-modules-core.dir/7c4a5ceae71b990ef2f431e9039ca398/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o
[3/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o
[4/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o
[5/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o
[6/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o
[7/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o
[8/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o
[9/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o
[10/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o
[11/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o
[12/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o
[13/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o
[14/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o
[15/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o
[16/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o
[17/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o
[18/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o
[19/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o
[20/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o
[21/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o
[22/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o
[23/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o
[24/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o
[25/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o
[26/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o
[27/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o
[28/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o
[29/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o
[30/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o
[31/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o
[32/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o
[33/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o
[34/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o
[35/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o
[36/42] Building CXX object CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o
[37/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o
[38/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o
[39/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o
[40/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o
[41/42] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o
[42/42] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.so
