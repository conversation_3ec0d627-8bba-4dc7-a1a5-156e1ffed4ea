{"version": 3, "names": ["_workletStackDetails", "Map", "registerWorkletStackDetails", "hash", "stackDetails", "set", "getBundleOffset", "error", "_error$stack", "frame", "stack", "split", "parsedFrame", "exec", "file", "line", "col", "Number", "processStack", "workletStackEntries", "match", "result", "for<PERSON>ach", "origLine", "origCol", "map", "errorDetails", "get", "lineOffset", "colOffset", "bundleFile", "bundleLine", "bundleCol", "replace", "reportFatalErrorOnJS", "message", "Error", "undefined", "name", "jsEngine", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportFatalError"], "sources": ["errors.ts"], "sourcesContent": ["'use strict';\nimport type { WorkletStackDetails } from './commonTypes';\n\nconst _workletStackDetails = new Map<number, WorkletStackDetails>();\n\nexport function registerWorkletStackDetails(\n  hash: number,\n  stackDetails: WorkletStackDetails\n) {\n  _workletStackDetails.set(hash, stackDetails);\n}\n\nfunction getBundleOffset(error: Error): [string, number, number] {\n  const frame = error.stack?.split('\\n')?.[0];\n  if (frame) {\n    const parsedFrame = /@([^@]+):(\\d+):(\\d+)/.exec(frame);\n    if (parsedFrame) {\n      const [, file, line, col] = parsedFrame;\n      return [file, Number(line), Number(col)];\n    }\n  }\n  return ['unknown', 0, 0];\n}\n\nfunction processStack(stack: string): string {\n  const workletStackEntries = stack.match(/worklet_(\\d+):(\\d+):(\\d+)/g);\n  let result = stack;\n  workletStackEntries?.forEach((match) => {\n    const [, hash, origLine, origCol] = match.split(/:|_/).map(Number);\n    const errorDetails = _workletStackDetails.get(hash);\n    if (!errorDetails) {\n      return;\n    }\n    const [error, lineOffset, colOffset] = errorDetails;\n    const [bundleFile, bundleLine, bundleCol] = getBundleOffset(error);\n    const line = origLine + bundleLine + lineOffset;\n    const col = origCol + bundleCol + colOffset;\n\n    result = result.replace(match, `${bundleFile}:${line}:${col}`);\n  });\n  return result;\n}\n\nexport function reportFatalErrorOnJS({\n  message,\n  stack,\n}: {\n  message: string;\n  stack?: string;\n}) {\n  const error = new Error();\n  error.message = message;\n  error.stack = stack ? processStack(stack) : undefined;\n  error.name = 'ReanimatedError';\n  // @ts-ignore React Native's ErrorUtils implementation extends the Error type with jsEngine field\n  error.jsEngine = 'reanimated';\n  // @ts-ignore the reportFatalError method is an internal method of ErrorUtils not exposed in the type definitions\n  global.ErrorUtils.reportFatalError(error);\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,MAAMA,oBAAoB,GAAG,IAAIC,GAAG,CAA8B,CAAC;AAEnE,OAAO,SAASC,2BAA2BA,CACzCC,IAAY,EACZC,YAAiC,EACjC;EACAJ,oBAAoB,CAACK,GAAG,CAACF,IAAI,EAAEC,YAAY,CAAC;AAC9C;AAEA,SAASE,eAAeA,CAACC,KAAY,EAA4B;EAAA,IAAAC,YAAA;EAC/D,MAAMC,KAAK,IAAAD,YAAA,GAAGD,KAAK,CAACG,KAAK,cAAAF,YAAA,gBAAAA,YAAA,GAAXA,YAAA,CAAaG,KAAK,CAAC,IAAI,CAAC,cAAAH,YAAA,uBAAxBA,YAAA,CAA2B,CAAC,CAAC;EAC3C,IAAIC,KAAK,EAAE;IACT,MAAMG,WAAW,GAAG,sBAAsB,CAACC,IAAI,CAACJ,KAAK,CAAC;IACtD,IAAIG,WAAW,EAAE;MACf,MAAM,GAAGE,IAAI,EAAEC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,WAAW;MACvC,OAAO,CAACE,IAAI,EAAEG,MAAM,CAACF,IAAI,CAAC,EAAEE,MAAM,CAACD,GAAG,CAAC,CAAC;IAC1C;EACF;EACA,OAAO,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1B;AAEA,SAASE,YAAYA,CAACR,KAAa,EAAU;EAC3C,MAAMS,mBAAmB,GAAGT,KAAK,CAACU,KAAK,CAAC,4BAA4B,CAAC;EACrE,IAAIC,MAAM,GAAGX,KAAK;EAClBS,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAEG,OAAO,CAAEF,KAAK,IAAK;IACtC,MAAM,GAAGjB,IAAI,EAAEoB,QAAQ,EAAEC,OAAO,CAAC,GAAGJ,KAAK,CAACT,KAAK,CAAC,KAAK,CAAC,CAACc,GAAG,CAACR,MAAM,CAAC;IAClE,MAAMS,YAAY,GAAG1B,oBAAoB,CAAC2B,GAAG,CAACxB,IAAI,CAAC;IACnD,IAAI,CAACuB,YAAY,EAAE;MACjB;IACF;IACA,MAAM,CAACnB,KAAK,EAAEqB,UAAU,EAAEC,SAAS,CAAC,GAAGH,YAAY;IACnD,MAAM,CAACI,UAAU,EAAEC,UAAU,EAAEC,SAAS,CAAC,GAAG1B,eAAe,CAACC,KAAK,CAAC;IAClE,MAAMQ,IAAI,GAAGQ,QAAQ,GAAGQ,UAAU,GAAGH,UAAU;IAC/C,MAAMZ,GAAG,GAAGQ,OAAO,GAAGQ,SAAS,GAAGH,SAAS;IAE3CR,MAAM,GAAGA,MAAM,CAACY,OAAO,CAACb,KAAK,EAAG,GAAEU,UAAW,IAAGf,IAAK,IAAGC,GAAI,EAAC,CAAC;EAChE,CAAC,CAAC;EACF,OAAOK,MAAM;AACf;AAEA,OAAO,SAASa,oBAAoBA,CAAC;EACnCC,OAAO;EACPzB;AAIF,CAAC,EAAE;EACD,MAAMH,KAAK,GAAG,IAAI6B,KAAK,CAAC,CAAC;EACzB7B,KAAK,CAAC4B,OAAO,GAAGA,OAAO;EACvB5B,KAAK,CAACG,KAAK,GAAGA,KAAK,GAAGQ,YAAY,CAACR,KAAK,CAAC,GAAG2B,SAAS;EACrD9B,KAAK,CAAC+B,IAAI,GAAG,iBAAiB;EAC9B;EACA/B,KAAK,CAACgC,QAAQ,GAAG,YAAY;EAC7B;EACAC,MAAM,CAACC,UAAU,CAACC,gBAAgB,CAACnC,KAAK,CAAC;AAC3C", "ignoreList": []}