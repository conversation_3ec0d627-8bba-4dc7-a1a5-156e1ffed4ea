{"version": 3, "names": ["useEffect", "useRef", "makeMutable", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "KeyboardState", "useAnimatedKeyboard", "options", "isStatusBarTranslucentAndroid", "ref", "listenerId", "isSubscribed", "current", "keyboardEventData", "state", "UNKNOWN", "height", "value"], "sources": ["useAnimatedKeyboard.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useRef } from 'react';\nimport {\n  makeMutable,\n  subscribeForKeyboardEvents,\n  unsubscribeFromKeyboardEvents,\n} from '../core';\nimport type {\n  AnimatedKeyboardInfo,\n  AnimatedKeyboardOptions,\n} from '../commonTypes';\nimport { KeyboardState } from '../commonTypes';\n\n/**\n * Lets you synchronously get the position and state of the keyboard.\n *\n * @param options - An additional keyboard configuration options.\n * @returns An object with the current keyboard `height` and `state` as [shared values](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value).\n * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useAnimatedKeyboard\n */\nexport function useAnimatedKeyboard(\n  options: AnimatedKeyboardOptions = { isStatusBarTranslucentAndroid: false }\n): AnimatedKeyboardInfo {\n  const ref = useRef<AnimatedKeyboardInfo | null>(null);\n  const listenerId = useRef<number>(-1);\n  const isSubscribed = useRef<boolean>(false);\n\n  if (ref.current === null) {\n    const keyboardEventData: AnimatedKeyboardInfo = {\n      state: makeMutable<KeyboardState>(KeyboardState.UNKNOWN),\n      height: makeMutable(0),\n    };\n    listenerId.current = subscribeForKeyboardEvents((state, height) => {\n      'worklet';\n      keyboardEventData.state.value = state;\n      keyboardEventData.height.value = height;\n    }, options);\n    ref.current = keyboardEventData;\n    isSubscribed.current = true;\n  }\n  useEffect(() => {\n    if (isSubscribed.current === false && ref.current !== null) {\n      const keyboardEventData = ref.current;\n      // subscribe again after Fast Refresh\n      listenerId.current = subscribeForKeyboardEvents((state, height) => {\n        'worklet';\n        keyboardEventData.state.value = state;\n        keyboardEventData.height.value = height;\n      }, options);\n      isSubscribed.current = true;\n    }\n    return () => {\n      unsubscribeFromKeyboardEvents(listenerId.current);\n      isSubscribed.current = false;\n    };\n  }, []);\n  return ref.current;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SACEC,WAAW,EACXC,0BAA0B,EAC1BC,6BAA6B,QACxB,SAAS;AAKhB,SAASC,aAAa,QAAQ,gBAAgB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CACjCC,OAAgC,GAAG;EAAEC,6BAA6B,EAAE;AAAM,CAAC,EACrD;EACtB,MAAMC,GAAG,GAAGR,MAAM,CAA8B,IAAI,CAAC;EACrD,MAAMS,UAAU,GAAGT,MAAM,CAAS,CAAC,CAAC,CAAC;EACrC,MAAMU,YAAY,GAAGV,MAAM,CAAU,KAAK,CAAC;EAE3C,IAAIQ,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;IACxB,MAAMC,iBAAuC,GAAG;MAC9CC,KAAK,EAAEZ,WAAW,CAAgBG,aAAa,CAACU,OAAO,CAAC;MACxDC,MAAM,EAAEd,WAAW,CAAC,CAAC;IACvB,CAAC;IACDQ,UAAU,CAACE,OAAO,GAAGT,0BAA0B,CAAC,CAACW,KAAK,EAAEE,MAAM,KAAK;MACjE,SAAS;;MACTH,iBAAiB,CAACC,KAAK,CAACG,KAAK,GAAGH,KAAK;MACrCD,iBAAiB,CAACG,MAAM,CAACC,KAAK,GAAGD,MAAM;IACzC,CAAC,EAAET,OAAO,CAAC;IACXE,GAAG,CAACG,OAAO,GAAGC,iBAAiB;IAC/BF,YAAY,CAACC,OAAO,GAAG,IAAI;EAC7B;EACAZ,SAAS,CAAC,MAAM;IACd,IAAIW,YAAY,CAACC,OAAO,KAAK,KAAK,IAAIH,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;MAC1D,MAAMC,iBAAiB,GAAGJ,GAAG,CAACG,OAAO;MACrC;MACAF,UAAU,CAACE,OAAO,GAAGT,0BAA0B,CAAC,CAACW,KAAK,EAAEE,MAAM,KAAK;QACjE,SAAS;;QACTH,iBAAiB,CAACC,KAAK,CAACG,KAAK,GAAGH,KAAK;QACrCD,iBAAiB,CAACG,MAAM,CAACC,KAAK,GAAGD,MAAM;MACzC,CAAC,EAAET,OAAO,CAAC;MACXI,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B;IACA,OAAO,MAAM;MACXR,6BAA6B,CAACM,UAAU,CAACE,OAAO,CAAC;MACjDD,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,GAAG,CAACG,OAAO;AACpB", "ignoreList": []}