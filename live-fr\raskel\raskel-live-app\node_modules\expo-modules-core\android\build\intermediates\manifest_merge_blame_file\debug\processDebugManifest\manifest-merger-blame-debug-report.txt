1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="expo.modules" >
5
6    <uses-sdk android:minSdkVersion="24" />
7
8    <application>
8-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:4:5-12:19
9        <meta-data
9-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:5:9-7:89
10            android:name="org.unimodules.core.AppLoader#react-native-headless"
10-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:6:13-79
11            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
11-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:7:13-86
12        <meta-data
12-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:8:9-11:45
13            android:name="com.facebook.soloader.enabled"
13-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:9:13-57
14            android:value="true"
14-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:10:13-33
15            tools:replace="android:value" />
15-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\src\main\AndroidManifest.xml:11:13-42
16    </application>
17
18</manifest>
