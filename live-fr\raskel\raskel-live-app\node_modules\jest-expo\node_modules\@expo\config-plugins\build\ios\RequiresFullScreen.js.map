{"version": 3, "file": "RequiresFullScreen.js", "names": ["_iosPlugins", "data", "require", "_warnings", "withRequiresFullScreen", "exports", "createInfoPlistPlugin", "setRequiresFullScreen", "iPadInterfaceKey", "requiredIPadInterface", "isStringArray", "value", "Array", "isArray", "every", "hasMinimumOrientations", "masks", "mask", "includes", "resolveExistingIpadInterfaceOrientations", "interfaceOrientations", "length", "existingList", "join", "addWarningIOS", "config", "infoPlist", "requiresFullScreen", "ios", "requireFullScreen", "isTabletEnabled", "supportsTablet", "isTabletOnly", "existing", "Set", "concat", "UIRequiresFullScreen"], "sources": ["../../src/ios/RequiresFullScreen.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { createInfoPlistPlugin } from '../plugins/ios-plugins';\nimport { addWarningIOS } from '../utils/warnings';\n\nexport const withRequiresFullScreen = createInfoPlistPlugin(\n  setRequiresFullScreen,\n  'withRequiresFullScreen'\n);\n\nconst iPadInterfaceKey = 'UISupportedInterfaceOrientations~ipad';\n\nconst requiredIPadInterface = [\n  'UIInterfaceOrientationPortrait',\n  'UIInterfaceOrientationPortraitUpsideDown',\n  'UIInterfaceOrientationLandscapeLeft',\n  'UIInterfaceOrientationLandscapeRight',\n];\n\nfunction isStringArray(value: any): value is string[] {\n  return Array.isArray(value) && value.every((value) => typeof value === 'string');\n}\n\nfunction hasMinimumOrientations(masks: string[]): boolean {\n  return requiredIPadInterface.every((mask) => masks.includes(mask));\n}\n\n/**\n * Require full screen being disabled requires all ipad interfaces to to be added,\n * otherwise submissions to the iOS App Store will fail.\n *\n * ERROR ITMS-90474: \"Invalid Bundle. iPad Multitasking support requires these orientations: 'UIInterfaceOrientationPortrait,UIInterfaceOrientationPortraitUpsideDown,UIInterfaceOrientationLandscapeLeft,UIInterfaceOrientationLandscapeRight'. Found 'UIInterfaceOrientationPortrait,UIInterfaceOrientationPortraitUpsideDown' in bundle 'com.bacon.app'.\"\n *\n * @param interfaceOrientations\n * @returns\n */\nfunction resolveExistingIpadInterfaceOrientations(interfaceOrientations: any): string[] {\n  if (\n    // Ensure type.\n    isStringArray(interfaceOrientations) &&\n    // Don't warn if it's an empty array, this is invalid regardless.\n    interfaceOrientations.length &&\n    // Check if the minimum requirements are met.\n    !hasMinimumOrientations(interfaceOrientations)\n  ) {\n    const existingList = interfaceOrientations!.join(', ');\n    addWarningIOS(\n      'ios.requireFullScreen',\n      `iPad multitasking requires all \\`${iPadInterfaceKey}\\` orientations to be defined in the Info.plist. The Info.plist currently defines values that are incompatible with multitasking, these will be overwritten to prevent submission failure. Existing: ${existingList}`\n    );\n    return interfaceOrientations;\n  }\n  return [];\n}\n\n// Whether requires full screen on iPad\nexport function setRequiresFullScreen(\n  config: Pick<ExpoConfig, 'ios'>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const requiresFullScreen = !!config.ios?.requireFullScreen;\n  const isTabletEnabled = config.ios?.supportsTablet || config.ios?.isTabletOnly;\n  if (isTabletEnabled && !requiresFullScreen) {\n    const existing = resolveExistingIpadInterfaceOrientations(infoPlist[iPadInterfaceKey]);\n\n    // There currently exists no mechanism to safely undo this feature besides `npx expo prebuild --clear`,\n    // this seems ok though because anyone using `UISupportedInterfaceOrientations~ipad` probably\n    // wants them to be defined to this value anyways. This is also the default value used in the Xcode iOS template.\n\n    // Merge any previous interfaces with the required interfaces.\n    infoPlist[iPadInterfaceKey] = [...new Set(existing.concat(requiredIPadInterface))];\n  }\n\n  return {\n    ...infoPlist,\n    UIRequiresFullScreen: requiresFullScreen,\n  };\n}\n"], "mappings": ";;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,GAAG,IAAAE,mCAAqB,EACzDC,qBAAqB,EACrB,wBACF,CAAC;AAED,MAAMC,gBAAgB,GAAG,uCAAuC;AAEhE,MAAMC,qBAAqB,GAAG,CAC5B,gCAAgC,EAChC,0CAA0C,EAC1C,qCAAqC,EACrC,sCAAsC,CACvC;AAED,SAASC,aAAaA,CAACC,KAAU,EAAqB;EACpD,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,KAAK,CAAEH,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,CAAC;AAClF;AAEA,SAASI,sBAAsBA,CAACC,KAAe,EAAW;EACxD,OAAOP,qBAAqB,CAACK,KAAK,CAAEG,IAAI,IAAKD,KAAK,CAACE,QAAQ,CAACD,IAAI,CAAC,CAAC;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wCAAwCA,CAACC,qBAA0B,EAAY;EACtF;EACE;EACAV,aAAa,CAACU,qBAAqB,CAAC;EACpC;EACAA,qBAAqB,CAACC,MAAM;EAC5B;EACA,CAACN,sBAAsB,CAACK,qBAAqB,CAAC,EAC9C;IACA,MAAME,YAAY,GAAGF,qBAAqB,CAAEG,IAAI,CAAC,IAAI,CAAC;IACtD,IAAAC,yBAAa,EACX,uBAAuB,EACvB,oCAAoChB,gBAAgB,wMAAwMc,YAAY,EAC1Q,CAAC;IACD,OAAOF,qBAAqB;EAC9B;EACA,OAAO,EAAE;AACX;;AAEA;AACO,SAASb,qBAAqBA,CACnCkB,MAA+B,EAC/BC,SAAoB,EACT;EACX,MAAMC,kBAAkB,GAAG,CAAC,CAACF,MAAM,CAACG,GAAG,EAAEC,iBAAiB;EAC1D,MAAMC,eAAe,GAAGL,MAAM,CAACG,GAAG,EAAEG,cAAc,IAAIN,MAAM,CAACG,GAAG,EAAEI,YAAY;EAC9E,IAAIF,eAAe,IAAI,CAACH,kBAAkB,EAAE;IAC1C,MAAMM,QAAQ,GAAGd,wCAAwC,CAACO,SAAS,CAAClB,gBAAgB,CAAC,CAAC;;IAEtF;IACA;IACA;;IAEA;IACAkB,SAAS,CAAClB,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI0B,GAAG,CAACD,QAAQ,CAACE,MAAM,CAAC1B,qBAAqB,CAAC,CAAC,CAAC;EACpF;EAEA,OAAO;IACL,GAAGiB,SAAS;IACZU,oBAAoB,EAAET;EACxB,CAAC;AACH", "ignoreList": []}