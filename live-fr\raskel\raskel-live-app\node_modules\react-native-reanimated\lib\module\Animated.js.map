{"version": 3, "names": ["createAnimatedComponent", "AnimatedText", "Text", "AnimatedView", "View", "AnimatedScrollView", "ScrollView", "AnimatedImage", "Image", "ReanimatedFlatList", "FlatList", "addWhitelistedNativeProps", "addWhitelistedUIProps"], "sources": ["Animated.ts"], "sourcesContent": ["'use strict';\nimport type { Extrapolate as _Extrapolate } from './interpolateColor';\nimport type {\n  SharedValue as _SharedValue,\n  AnimatedTransform as _AnimatedTransform,\n  AnimateStyle as _AnimateStyle,\n  StylesOrDefault as _StylesOrDefault,\n  EasingFunction as _EasingFunction,\n} from './commonTypes';\nimport type { DerivedValue as _DerivedValue } from './hook/useDerivedValue';\nimport type {\n  TransformStyleTypes as _TransformStyleTypes,\n  Adaptable as _Adaptable,\n  AdaptTransforms as _AdaptTransforms,\n  AnimateProps as _AnimateProps,\n} from './helperTypes';\n\nimport type { AnimatedScrollViewProps as _AnimatedScrollViewProps } from './component/ScrollView';\nimport type { FlatListPropsWithLayout as _FlatListPropsWithLayout } from './component/FlatList';\n\nexport { createAnimatedComponent } from './createAnimatedComponent';\nexport { AnimatedText as Text } from './component/Text';\nexport { AnimatedView as View } from './component/View';\nexport { AnimatedScrollView as ScrollView } from './component/ScrollView';\nexport { AnimatedImage as Image } from './component/Image';\nexport { ReanimatedFlatList as FlatList } from './component/FlatList';\nexport {\n  addWhitelistedNativeProps,\n  addWhitelistedUIProps,\n} from './ConfigHelper';\n/**\n * @deprecated Please import `Extrapolate` directly from `react-native-reanimated` instead of `Animated` namespace.\n */\nexport type Extrapolate = typeof _Extrapolate;\n/**\n * @deprecated Please import `SharedValue` directly from `react-native-reanimated` instead of `Animated` namespace.\n */\n\nexport type SharedValue<T> = _SharedValue<T>;\n/**\n * @deprecated Please import `DerivedValue` directly from `react-native-reanimated` instead of `Animated` namespace.\n */\nexport type DerivedValue<T> = _DerivedValue<T>;\n/**\n * @deprecated Please import `Adaptable` directly from `react-native-reanimated` instead of `Animated` namespace.\n */\nexport type Adaptable<T> = _Adaptable<T>;\n/**\n * @deprecated Please import `TransformStyleTypes` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type TransformStyleTypes = _TransformStyleTypes;\n/**\n * @deprecated Please import `AdaptTransforms` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type AdaptTransforms<T> = _AdaptTransforms<T>;\n/**\n * @deprecated Please import `AnimatedTransform` directly from `react-native-reanimated` instead of `Animated` namespace.\n */\nexport type AnimatedTransform = _AnimatedTransform;\n/**\n * @deprecated Please import `AnimateStyle` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type AnimateStyle<S> = _AnimateStyle<S>;\n/**\n * @deprecated Please import `StylesOrDefault` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type StylesOrDefault<S> = _StylesOrDefault<S>;\n/**\n * @deprecated Please import `AnimateProps` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type AnimateProps<P extends object> = _AnimateProps<P>;\n/**\n * @deprecated Please import `EasingFunction` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type EasingFunction = _EasingFunction;\n/**\n * @deprecated Please import `AnimatedScrollViewProps` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type AnimatedScrollViewProps = _AnimatedScrollViewProps;\n/**\n * @deprecated Please import `FlatListPropsWithLayout` directly from `react-native-reanimated` instead of `Animated` namespace.\n * */\nexport type FlatListPropsWithLayout<T> = _FlatListPropsWithLayout<T>;\n"], "mappings": "AAAA,YAAY;;AAoBZ,SAASA,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,YAAY,IAAIC,IAAI,QAAQ,kBAAkB;AACvD,SAASC,YAAY,IAAIC,IAAI,QAAQ,kBAAkB;AACvD,SAASC,kBAAkB,IAAIC,UAAU,QAAQ,wBAAwB;AACzE,SAASC,aAAa,IAAIC,KAAK,QAAQ,mBAAmB;AAC1D,SAASC,kBAAkB,IAAIC,QAAQ,QAAQ,sBAAsB;AACrE,SACEC,yBAAyB,EACzBC,qBAAqB,QAChB,gBAAgB;AACvB;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "ignoreList": []}