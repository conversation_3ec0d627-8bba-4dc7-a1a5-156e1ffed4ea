{"version": 3, "file": "fs.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "e", "__esModule", "default", "copyFilePathToPathAsync", "src", "dest", "srcFile", "fs", "promises", "readFile", "mkdir", "path", "dirname", "recursive", "writeFile", "removeFile", "filePath", "unlinkSync", "error", "code"], "sources": ["../../src/utils/fs.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n/** A basic function that copies a single file to another file location. */\nexport async function copyFilePathToPathAsync(src: string, dest: string): Promise<void> {\n  const srcFile = await fs.promises.readFile(src);\n  await fs.promises.mkdir(path.dirname(dest), { recursive: true });\n  await fs.promises.writeFile(dest, srcFile);\n}\n\n/** Remove a single file (not directory). Returns `true` if a file was actually deleted. */\nexport function removeFile(filePath: string): boolean {\n  try {\n    fs.unlinkSync(filePath);\n    return true;\n  } catch (error: any) {\n    // Skip if the remove did nothing.\n    if (error.code === 'ENOENT') {\n      return false;\n    }\n    throw error;\n  }\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwB,SAAAC,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAExB;AACO,eAAeG,uBAAuBA,CAACC,GAAW,EAAEC,IAAY,EAAiB;EACtF,MAAMC,OAAO,GAAG,MAAMC,aAAE,CAACC,QAAQ,CAACC,QAAQ,CAACL,GAAG,CAAC;EAC/C,MAAMG,aAAE,CAACC,QAAQ,CAACE,KAAK,CAACC,eAAI,CAACC,OAAO,CAACP,IAAI,CAAC,EAAE;IAAEQ,SAAS,EAAE;EAAK,CAAC,CAAC;EAChE,MAAMN,aAAE,CAACC,QAAQ,CAACM,SAAS,CAACT,IAAI,EAAEC,OAAO,CAAC;AAC5C;;AAEA;AACO,SAASS,UAAUA,CAACC,QAAgB,EAAW;EACpD,IAAI;IACFT,aAAE,CAACU,UAAU,CAACD,QAAQ,CAAC;IACvB,OAAO,IAAI;EACb,CAAC,CAAC,OAAOE,KAAU,EAAE;IACnB;IACA,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAC3B,OAAO,KAAK;IACd;IACA,MAAMD,KAAK;EACb;AACF", "ignoreList": []}