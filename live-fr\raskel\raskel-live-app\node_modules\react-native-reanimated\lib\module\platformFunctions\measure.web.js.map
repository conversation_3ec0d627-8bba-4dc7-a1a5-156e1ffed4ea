{"version": 3, "names": ["measure", "animatedRef", "element", "console", "warn", "viewportOffset", "getBoundingClientRect", "width", "offsetWidth", "height", "offsetHeight", "x", "offsetLeft", "y", "offsetTop", "pageX", "left", "pageY", "top"], "sources": ["measure.web.ts"], "sourcesContent": ["'use strict';\nimport type { MeasuredDimensions } from '../commonTypes';\nimport type { AnimatedRef } from '../hook/commonTypes';\nimport type { Component } from 'react';\n\nexport function measure<T extends Component>(\n  animatedRef: AnimatedRef<T>\n): MeasuredDimensions | null {\n  const element = animatedRef() as HTMLElement | -1;\n\n  if (element === -1) {\n    console.warn(\n      `[Reanimated] The view with tag ${element} is not a valid argument for measure(). This may be because the view is not currently rendered, which may not be a bug (e.g. an off-screen FlatList item).`\n    );\n    return null;\n  }\n\n  const viewportOffset = element.getBoundingClientRect();\n  return {\n    width: element.offsetWidth,\n    height: element.offsetHeight,\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    pageX: viewportOffset.left,\n    pageY: viewportOffset.top,\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAKZ,OAAO,SAASA,OAAOA,CACrBC,WAA2B,EACA;EAC3B,MAAMC,OAAO,GAAGD,WAAW,CAAC,CAAqB;EAEjD,IAAIC,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBC,OAAO,CAACC,IAAI,CACT,kCAAiCF,OAAQ,4JAC5C,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAMG,cAAc,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC;EACtD,OAAO;IACLC,KAAK,EAAEL,OAAO,CAACM,WAAW;IAC1BC,MAAM,EAAEP,OAAO,CAACQ,YAAY;IAC5BC,CAAC,EAAET,OAAO,CAACU,UAAU;IACrBC,CAAC,EAAEX,OAAO,CAACY,SAAS;IACpBC,KAAK,EAAEV,cAAc,CAACW,IAAI;IAC1BC,KAAK,EAAEZ,cAAc,CAACa;EACxB,CAAC;AACH", "ignoreList": []}