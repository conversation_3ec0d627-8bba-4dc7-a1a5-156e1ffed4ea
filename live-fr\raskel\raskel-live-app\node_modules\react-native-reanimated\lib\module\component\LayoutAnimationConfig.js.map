{"version": 3, "names": ["React", "Children", "Component", "createContext", "useEffect", "useRef", "setShouldAnimateExitingForTag", "findNodeHandle", "SkipEnteringContext", "SkipEntering", "props", "skipV<PERSON>ue<PERSON>ef", "shouldSkip", "current", "createElement", "Provider", "value", "children", "LayoutAnimationConfig", "getMaybeWrappedChildren", "count", "skipExiting", "map", "child", "setShouldAnimateExiting", "tag", "componentWillUnmount", "undefined", "render", "skipEntering"], "sources": ["LayoutAnimationConfig.tsx"], "sourcesContent": ["'use strict';\nimport React, {\n  Children,\n  Component,\n  createContext,\n  useEffect,\n  useRef,\n} from 'react';\nimport type { ReactNode } from 'react';\nimport { setShouldAnimateExitingForTag } from '../core';\nimport { findNodeHandle } from 'react-native';\n\nexport const SkipEnteringContext =\n  createContext<React.MutableRefObject<boolean> | null>(null);\n\n// skipEntering - don't animate entering of children on wrapper mount\n// skipExiting - don't animate exiting of children on wrapper unmount\ninterface LayoutAnimationConfigProps {\n  skipEntering?: boolean;\n  skipExiting?: boolean;\n  children: ReactNode;\n}\n\nfunction SkipEntering(props: { shouldSkip: boolean; children: ReactNode }) {\n  const skipValueRef = useRef(props.shouldSkip);\n\n  useEffect(() => {\n    skipValueRef.current = false;\n  }, [skipValueRef]);\n\n  return (\n    <SkipEnteringContext.Provider value={skipValueRef}>\n      {props.children}\n    </SkipEnteringContext.Provider>\n  );\n}\n\n// skipExiting (unlike skipEntering) cannot be done by conditionally\n// configuring the animation in `createAnimatedComponent`, since at this stage\n// we don't know if the wrapper is going to be unmounted or not.\n// That's why we need to pass the skipExiting flag to the native side\n// when the wrapper is unmounted to prevent the animation.\n// Since `ReactNode` can be a list of nodes, we wrap every child with our wrapper\n// so we are able to access its tag with `findNodeHandle`.\n/**\n * A component that lets you skip entering and exiting animations.\n *\n * @param skipEntering - A boolean indicating whether children's entering animations should be skipped when `LayoutAnimationConfig` is mounted.\n * @param skipExiting - A boolean indicating whether children's exiting animations should be skipped when LayoutAnimationConfig is unmounted.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-animation-config/\n */\nexport class LayoutAnimationConfig extends Component<LayoutAnimationConfigProps> {\n  getMaybeWrappedChildren() {\n    return Children.count(this.props.children) > 1 && this.props.skipExiting\n      ? Children.map(this.props.children, (child) => (\n          <LayoutAnimationConfig skipExiting>{child}</LayoutAnimationConfig>\n        ))\n      : this.props.children;\n  }\n\n  setShouldAnimateExiting() {\n    if (Children.count(this.props.children) === 1) {\n      const tag = findNodeHandle(this);\n      if (tag) {\n        setShouldAnimateExitingForTag(tag, !this.props.skipExiting);\n      }\n    }\n  }\n\n  componentWillUnmount(): void {\n    if (this.props.skipExiting !== undefined) {\n      this.setShouldAnimateExiting();\n    }\n  }\n\n  render(): ReactNode {\n    const children = this.getMaybeWrappedChildren();\n\n    if (this.props.skipEntering === undefined) {\n      return children;\n    }\n\n    return (\n      <SkipEntering shouldSkip={this.props.skipEntering}>\n        {children}\n      </SkipEntering>\n    );\n  }\n}\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,KAAK,IACVC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,SAAS,EACTC,MAAM,QACD,OAAO;AAEd,SAASC,6BAA6B,QAAQ,SAAS;AACvD,SAASC,cAAc,QAAQ,cAAc;AAE7C,OAAO,MAAMC,mBAAmB,gBAC9BL,aAAa,CAAyC,IAAI,CAAC;;AAE7D;AACA;;AAOA,SAASM,YAAYA,CAACC,KAAmD,EAAE;EACzE,MAAMC,YAAY,GAAGN,MAAM,CAACK,KAAK,CAACE,UAAU,CAAC;EAE7CR,SAAS,CAAC,MAAM;IACdO,YAAY,CAACE,OAAO,GAAG,KAAK;EAC9B,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;EAElB,oBACEX,KAAA,CAAAc,aAAA,CAACN,mBAAmB,CAACO,QAAQ;IAACC,KAAK,EAAEL;EAAa,GAC/CD,KAAK,CAACO,QACqB,CAAC;AAEnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,SAAShB,SAAS,CAA6B;EAC/EiB,uBAAuBA,CAAA,EAAG;IACxB,OAAOlB,QAAQ,CAACmB,KAAK,CAAC,IAAI,CAACV,KAAK,CAACO,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAACP,KAAK,CAACW,WAAW,GACpEpB,QAAQ,CAACqB,GAAG,CAAC,IAAI,CAACZ,KAAK,CAACO,QAAQ,EAAGM,KAAK,iBACtCvB,KAAA,CAAAc,aAAA,CAACI,qBAAqB;MAACG,WAAW;IAAA,GAAEE,KAA6B,CAClE,CAAC,GACF,IAAI,CAACb,KAAK,CAACO,QAAQ;EACzB;EAEAO,uBAAuBA,CAAA,EAAG;IACxB,IAAIvB,QAAQ,CAACmB,KAAK,CAAC,IAAI,CAACV,KAAK,CAACO,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,MAAMQ,GAAG,GAAGlB,cAAc,CAAC,IAAI,CAAC;MAChC,IAAIkB,GAAG,EAAE;QACPnB,6BAA6B,CAACmB,GAAG,EAAE,CAAC,IAAI,CAACf,KAAK,CAACW,WAAW,CAAC;MAC7D;IACF;EACF;EAEAK,oBAAoBA,CAAA,EAAS;IAC3B,IAAI,IAAI,CAAChB,KAAK,CAACW,WAAW,KAAKM,SAAS,EAAE;MACxC,IAAI,CAACH,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEAI,MAAMA,CAAA,EAAc;IAClB,MAAMX,QAAQ,GAAG,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAE/C,IAAI,IAAI,CAACT,KAAK,CAACmB,YAAY,KAAKF,SAAS,EAAE;MACzC,OAAOV,QAAQ;IACjB;IAEA,oBACEjB,KAAA,CAAAc,aAAA,CAACL,YAAY;MAACG,UAAU,EAAE,IAAI,CAACF,KAAK,CAACmB;IAAa,GAC/CZ,QACW,CAAC;EAEnB;AACF", "ignoreList": []}