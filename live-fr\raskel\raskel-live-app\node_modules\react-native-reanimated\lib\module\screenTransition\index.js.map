{"version": 3, "names": ["startScreenTransition", "finishScreenTransition", "ScreenTransition"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\n\nexport {\n  startScreenTransition,\n  finishScreenTransition,\n} from './animationManager';\nexport { ScreenTransition } from './presets';\nexport type {\n  AnimatedScreenTransition,\n  GoBackGesture,\n  ScreenTransitionConfig,\n} from './commonTypes';\n"], "mappings": "AAAA,YAAY;;AAEZ,SACEA,qBAAqB,EACrBC,sBAAsB,QACjB,oBAAoB;AAC3B,SAASC,gBAAgB,QAAQ,WAAW", "ignoreList": []}