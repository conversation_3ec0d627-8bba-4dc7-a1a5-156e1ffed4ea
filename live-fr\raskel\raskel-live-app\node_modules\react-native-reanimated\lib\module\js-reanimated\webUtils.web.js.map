{"version": 3, "names": ["createReactDOMStyle", "createTransformValue", "createTextShadowValue", "require", "default", "e"], "sources": ["webUtils.web.ts"], "sourcesContent": ["'use strict';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let createReactDOMStyle: (style: any) => any;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let createTransformValue: (transform: any) => any;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let createTextShadowValue: (style: any) => void | string;\n\ntry {\n  createReactDOMStyle =\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    require('react-native-web/dist/exports/StyleSheet/compiler/createReactDOMStyle').default;\n} catch (e) {}\n\ntry {\n  // React Native Web 0.19+\n  createTransformValue =\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    require('react-native-web/dist/exports/StyleSheet/preprocess').createTransformValue;\n} catch (e) {}\n\ntry {\n  createTextShadowValue =\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    require('react-native-web/dist/exports/StyleSheet/preprocess').createTextShadowValue;\n} catch (e) {}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,IAAIA,mBAAwC;AACnD;AACA,OAAO,IAAIC,oBAA6C;AACxD;AACA,OAAO,IAAIC,qBAAoD;AAE/D,IAAI;EACFF,mBAAmB;EACjB;EACAG,OAAO,CAAC,uEAAuE,CAAC,CAACC,OAAO;AAC5F,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;AAEb,IAAI;EACF;EACAJ,oBAAoB;EAClB;EACAE,OAAO,CAAC,qDAAqD,CAAC,CAACF,oBAAoB;AACvF,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;AAEb,IAAI;EACFH,qBAAqB;EACnB;EACAC,OAAO,CAAC,qDAAqD,CAAC,CAACD,qBAAqB;AACxF,CAAC,CAAC,OAAOG,CAAC,EAAE,CAAC", "ignoreList": []}