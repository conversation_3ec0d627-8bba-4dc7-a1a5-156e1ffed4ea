{"buildFiles": ["C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\hermes-engine\\hermes-engineConfig.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\hermes-engine\\hermes-engineConfigVersion.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\CMakeLists.txt", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"reanimated::@89a6a9b85fb42923616c": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "reanimated", "output": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6l266c2n\\obj\\x86_64\\libreanimated.so", "runtimeFiles": ["C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6l266c2n\\obj\\x86_64\\libworklets.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9595d0aeab151a9804a8af82bcc909\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aab9761a5bc22bd9c4bd696ca1cf1810\\transformed\\hermes-android-0.79.5-debug\\prefab\\modules\\libhermes\\libs\\android.x86_64\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\hermestooling\\libs\\android.x86_64\\libhermestooling.so"]}, "worklets::@a0394df2d94e5212d8bd": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "worklets", "output": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6l266c2n\\obj\\x86_64\\libworklets.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9595d0aeab151a9804a8af82bcc909\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aab9761a5bc22bd9c4bd696ca1cf1810\\transformed\\hermes-android-0.79.5-debug\\prefab\\modules\\libhermes\\libs\\android.x86_64\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\hermestooling\\libs\\android.x86_64\\libhermestooling.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}