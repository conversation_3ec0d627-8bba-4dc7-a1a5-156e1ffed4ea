1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="expo.modules.devlauncher" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <queries>
7-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:24:3-26:13
8        <package android:name="host.exp.exponent" />
8-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:25:5-49
8-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:25:14-46
9    </queries>
10
11    <application>
11-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:2:3-22:17
12        <activity
12-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:3:5-16:16
13            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
13-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:4:7-75
14            android:exported="true"
14-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:5:7-30
15            android:launchMode="singleTask"
15-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:6:7-38
16            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
16-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:7:7-64
17            <intent-filter>
17-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:8:7-15:23
18                <action android:name="android.intent.action.VIEW" />
18-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:9:9-61
18-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:9:17-58
19
20                <category android:name="android.intent.category.DEFAULT" />
20-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:11:9-68
20-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:11:19-65
21                <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:12:9-70
21-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:12:19-67
22
23                <data android:scheme="expo-dev-launcher" />
23-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:14:9-52
23-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:14:15-49
24            </intent-filter>
25        </activity>
26        <activity
26-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:18:5-21:64
27            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
27-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:19:7-87
28            android:screenOrientation="portrait"
28-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:20:7-43
29            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
29-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-launcher\android\src\debug\AndroidManifest.xml:21:7-61
30    </application>
31
32</manifest>
