{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_FADE_TIME", "FadeInData", "FadeIn", "name", "style", "opacity", "duration", "FadeInRight", "transform", "translateX", "FadeInLeft", "FadeInUp", "translateY", "FadeInDown", "FadeOutData", "FadeOut", "FadeOutRight", "FadeOutLeft", "FadeOutUp", "FadeOutDown"], "sources": ["Fade.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_FADE_TIME = 0.3;\n\nexport const FadeInData = {\n  FadeIn: {\n    name: 'FadeIn',\n    style: {\n      0: { opacity: 0 },\n      100: { opacity: 1 },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeInRight: {\n    name: 'FadeInRight',\n    style: {\n      0: {\n        opacity: 0,\n        transform: [{ translateX: '25px' }],\n      },\n      100: {\n        opacity: 1,\n        transform: [{ translateX: '0px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeInLeft: {\n    name: 'FadeInLeft',\n    style: {\n      0: {\n        opacity: 0,\n        transform: [{ translateX: '-25px' }],\n      },\n      100: {\n        opacity: 1,\n        transform: [{ translateX: '0px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeInUp: {\n    name: 'FadeInUp',\n    style: {\n      0: {\n        opacity: 0,\n        transform: [{ translateY: '-25px' }],\n      },\n      100: {\n        opacity: 1,\n        transform: [{ translateY: '0px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeInDown: {\n    name: 'FadeInDown',\n    style: {\n      0: {\n        opacity: 0,\n        transform: [{ translateY: '25px' }],\n      },\n      100: {\n        opacity: 1,\n        transform: [{ translateY: '0px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n};\n\nexport const FadeOutData = {\n  FadeOut: {\n    name: 'FadeOut',\n    style: {\n      0: { opacity: 1 },\n      100: { opacity: 0 },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeOutRight: {\n    name: 'FadeOutRight',\n    style: {\n      0: {\n        opacity: 1,\n        transform: [{ translateX: '0px' }],\n      },\n      100: {\n        opacity: 0,\n        transform: [{ translateX: '25px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeOutLeft: {\n    name: 'FadeOutLeft',\n    style: {\n      0: {\n        opacity: 1,\n        transform: [{ translateX: '0px' }],\n      },\n      100: {\n        opacity: 0,\n        transform: [{ translateX: '-25px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeOutUp: {\n    name: 'FadeOutUp',\n    style: {\n      0: {\n        opacity: 1,\n        transform: [{ translateY: '0px' }],\n      },\n      100: {\n        opacity: 0,\n        transform: [{ translateY: '-25px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n\n  FadeOutDown: {\n    name: 'FadeOutDown',\n    style: {\n      0: {\n        opacity: 1,\n        transform: [{ translateY: '0px' }],\n      },\n      100: {\n        opacity: 0,\n        transform: [{ translateY: '25px' }],\n      },\n    },\n    duration: DEFAULT_FADE_TIME,\n  },\n};\n\nexport const FadeIn = {\n  FadeIn: {\n    style: convertAnimationObjectToKeyframes(FadeInData.FadeIn),\n    duration: FadeInData.FadeIn.duration,\n  },\n  FadeInRight: {\n    style: convertAnimationObjectToKeyframes(FadeInData.FadeInRight),\n    duration: FadeInData.FadeInRight.duration,\n  },\n  FadeInLeft: {\n    style: convertAnimationObjectToKeyframes(FadeInData.FadeInLeft),\n    duration: FadeInData.FadeInLeft.duration,\n  },\n  FadeInUp: {\n    style: convertAnimationObjectToKeyframes(FadeInData.FadeInUp),\n    duration: FadeInData.FadeInUp.duration,\n  },\n  FadeInDown: {\n    style: convertAnimationObjectToKeyframes(FadeInData.FadeInDown),\n    duration: FadeInData.FadeInDown.duration,\n  },\n};\n\nexport const FadeOut = {\n  FadeOut: {\n    style: convertAnimationObjectToKeyframes(FadeOutData.FadeOut),\n    duration: FadeOutData.FadeOut.duration,\n  },\n  FadeOutRight: {\n    style: convertAnimationObjectToKeyframes(FadeOutData.FadeOutRight),\n    duration: FadeOutData.FadeOutRight.duration,\n  },\n  FadeOutLeft: {\n    style: convertAnimationObjectToKeyframes(FadeOutData.FadeOutLeft),\n    duration: FadeOutData.FadeOutLeft.duration,\n  },\n  FadeOutUp: {\n    style: convertAnimationObjectToKeyframes(FadeOutData.FadeOutUp),\n    duration: FadeOutData.FadeOutUp.duration,\n  },\n  FadeOutDown: {\n    style: convertAnimationObjectToKeyframes(FadeOutData.FadeOutDown),\n    duration: FadeOutData.FadeOutDown.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,iBAAiB,GAAG,GAAG;AAE7B,OAAO,MAAMC,UAAU,GAAG;EACxBC,MAAM,EAAE;IACNC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACjB,GAAG,EAAE;QAAEA,OAAO,EAAE;MAAE;IACpB,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDO,WAAW,EAAE;IACXJ,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAC;MACpC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDH,QAAQ,EAAEN;EACZ,CAAC;EAEDU,UAAU,EAAE;IACVP,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MACrC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDH,QAAQ,EAAEN;EACZ,CAAC;EAEDW,QAAQ,EAAE;IACRR,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MACrC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDN,QAAQ,EAAEN;EACZ,CAAC;EAEDa,UAAU,EAAE;IACVV,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MACpC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC;IACF,CAAC;IACDN,QAAQ,EAAEN;EACZ;AACF,CAAC;AAED,OAAO,MAAMc,WAAW,GAAG;EACzBC,OAAO,EAAE;IACPZ,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACjB,GAAG,EAAE;QAAEA,OAAO,EAAE;MAAE;IACpB,CAAC;IACDC,QAAQ,EAAEN;EACZ,CAAC;EAEDgB,YAAY,EAAE;IACZb,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAC;MACpC;IACF,CAAC;IACDH,QAAQ,EAAEN;EACZ,CAAC;EAEDiB,WAAW,EAAE;IACXd,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHJ,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAQ,CAAC;MACrC;IACF,CAAC;IACDH,QAAQ,EAAEN;EACZ,CAAC;EAEDkB,SAAS,EAAE;IACTf,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MACrC;IACF,CAAC;IACDN,QAAQ,EAAEN;EACZ,CAAC;EAEDmB,WAAW,EAAE;IACXhB,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MACnC,CAAC;MACD,GAAG,EAAE;QACHP,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MACpC;IACF,CAAC;IACDN,QAAQ,EAAEN;EACZ;AACF,CAAC;AAED,OAAO,MAAME,MAAM,GAAG;EACpBA,MAAM,EAAE;IACNE,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACC,MAAM,CAAC;IAC3DI,QAAQ,EAAEL,UAAU,CAACC,MAAM,CAACI;EAC9B,CAAC;EACDC,WAAW,EAAE;IACXH,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACM,WAAW,CAAC;IAChED,QAAQ,EAAEL,UAAU,CAACM,WAAW,CAACD;EACnC,CAAC;EACDI,UAAU,EAAE;IACVN,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACS,UAAU,CAAC;IAC/DJ,QAAQ,EAAEL,UAAU,CAACS,UAAU,CAACJ;EAClC,CAAC;EACDK,QAAQ,EAAE;IACRP,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACU,QAAQ,CAAC;IAC7DL,QAAQ,EAAEL,UAAU,CAACU,QAAQ,CAACL;EAChC,CAAC;EACDO,UAAU,EAAE;IACVT,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACY,UAAU,CAAC;IAC/DP,QAAQ,EAAEL,UAAU,CAACY,UAAU,CAACP;EAClC;AACF,CAAC;AAED,OAAO,MAAMS,OAAO,GAAG;EACrBA,OAAO,EAAE;IACPX,KAAK,EAAEL,iCAAiC,CAACe,WAAW,CAACC,OAAO,CAAC;IAC7DT,QAAQ,EAAEQ,WAAW,CAACC,OAAO,CAACT;EAChC,CAAC;EACDU,YAAY,EAAE;IACZZ,KAAK,EAAEL,iCAAiC,CAACe,WAAW,CAACE,YAAY,CAAC;IAClEV,QAAQ,EAAEQ,WAAW,CAACE,YAAY,CAACV;EACrC,CAAC;EACDW,WAAW,EAAE;IACXb,KAAK,EAAEL,iCAAiC,CAACe,WAAW,CAACG,WAAW,CAAC;IACjEX,QAAQ,EAAEQ,WAAW,CAACG,WAAW,CAACX;EACpC,CAAC;EACDY,SAAS,EAAE;IACTd,KAAK,EAAEL,iCAAiC,CAACe,WAAW,CAACI,SAAS,CAAC;IAC/DZ,QAAQ,EAAEQ,WAAW,CAACI,SAAS,CAACZ;EAClC,CAAC;EACDa,WAAW,EAAE;IACXf,KAAK,EAAEL,iCAAiC,CAACe,WAAW,CAACK,WAAW,CAAC;IACjEb,QAAQ,EAAEQ,WAAW,CAACK,WAAW,CAACb;EACpC;AACF,CAAC", "ignoreList": []}