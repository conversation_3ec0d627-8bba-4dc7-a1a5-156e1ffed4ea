{"version": 3, "file": "PredictiveBackGesture.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "ANDROID_ENABLE_ON_BACK_INVOKED_CALLBACK", "withPredictiveBackGesture", "config", "withAndroidManifest", "modResults", "setPredictiveBackGesture", "exports", "androidManifest", "app", "getMainApplicationOrThrow", "$", "getPredictiveBackGestureValue", "value", "android", "predictiveBackGestureEnabled"], "sources": ["../../src/android/PredictiveBackGesture.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, getMainApplicationOrThrow } from './Manifest';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidManifest } from '../plugins/android-plugins';\n\nconst ANDROID_ENABLE_ON_BACK_INVOKED_CALLBACK = 'android:enableOnBackInvokedCallback';\n\nexport const withPredictiveBackGesture: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, async (config) => {\n    config.modResults = setPredictiveBackGesture(config, config.modResults);\n    return config;\n  });\n};\n\nexport function setPredictiveBackGesture(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const app = getMainApplicationOrThrow(androidManifest);\n  app.$[ANDROID_ENABLE_ON_BACK_INVOKED_CALLBACK] = getPredictiveBackGestureValue(config);\n  return androidManifest;\n}\n\nexport function getPredictiveBackGestureValue(config: Pick<ExpoConfig, 'android'>) {\n  const value = config.android?.predictiveBackGestureEnabled;\n  return value === true ? 'true' : 'false';\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,uCAAuC,GAAG,qCAAqC;AAE9E,MAAMC,yBAAuC,GAAIC,MAAM,IAAK;EACjE,OAAO,IAAAC,qCAAmB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnDA,MAAM,CAACE,UAAU,GAAGC,wBAAwB,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IACvE,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACI,OAAA,CAAAL,yBAAA,GAAAA,yBAAA;AAEK,SAASI,wBAAwBA,CACtCH,MAAmC,EACnCK,eAAgC,EAChC;EACA,MAAMC,GAAG,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EACtDC,GAAG,CAACE,CAAC,CAACV,uCAAuC,CAAC,GAAGW,6BAA6B,CAACT,MAAM,CAAC;EACtF,OAAOK,eAAe;AACxB;AAEO,SAASI,6BAA6BA,CAACT,MAAmC,EAAE;EACjF,MAAMU,KAAK,GAAGV,MAAM,CAACW,OAAO,EAAEC,4BAA4B;EAC1D,OAAOF,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;AAC1C", "ignoreList": []}