{"version": 3, "names": ["isF<PERSON><PERSON>", "shouldBeUseWeb", "configureLayoutAnimationBatch", "makeShareableCloneRecursive", "createUpdateManager", "animations", "deferredAnimations", "update", "batchItem", "isUnmounting", "push", "length", "flush", "setImmediate", "concat", "updateLayoutAnimations", "updateLayoutAnimationsManager", "viewTag", "type", "config", "sharedTransitionTag", "undefined"], "sources": ["UpdateLayoutAnimations.ts"], "sourcesContent": ["'use strict';\nimport { isFabric, shouldBeUseWeb } from './PlatformChecker';\nimport {\n  configureLayoutAnimationBatch,\n  makeShareableCloneRecursive,\n} from './core';\nimport type {\n  LayoutAnimationFunction,\n  LayoutAnimationType,\n} from './layoutReanimation';\nimport type {\n  LayoutAnimationBatchItem,\n  ProgressAnimationCallback,\n  SharedTransitionAnimationsFunction,\n} from './layoutReanimation/animationBuilder/commonTypes';\n\nfunction createUpdateManager() {\n  const animations: LayoutAnimationBatchItem[] = [];\n  // When a stack is rerendered we reconfigure all the shared elements.\n  // To do that we want them to appear in our batch in the correct order,\n  // so we defer some of the updates to appear at the end of the batch.\n  const deferredAnimations: LayoutAnimationBatchItem[] = [];\n\n  return {\n    update(batchItem: LayoutAnimationBatchItem, isUnmounting?: boolean) {\n      if (isUnmounting) {\n        deferredAnimations.push(batchItem);\n      } else {\n        animations.push(batchItem);\n      }\n      if (animations.length + deferredAnimations.length === 1) {\n        isFabric() ? this.flush() : setImmediate(this.flush);\n      }\n    },\n    flush(this: void) {\n      configureLayoutAnimationBatch(animations.concat(deferredAnimations));\n      animations.length = 0;\n      deferredAnimations.length = 0;\n    },\n  };\n}\n\n/**\n * Lets you update the current configuration of the layout animation or shared element transition for a given component.\n * Configurations are batched and applied at the end of the current execution block, right before sending the response back to native.\n *\n * @param viewTag - The tag of the component you'd like to configure.\n * @param type - The type of the animation you'd like to configure - {@link LayoutAnimationType}.\n * @param config - The animation configuration - {@link LayoutAnimationFunction}, {@link SharedTransitionAnimationsFunction}, {@link ProgressAnimationCallback} or {@link Keyframe}. Passing `undefined` will remove the animation.\n * @param sharedTransitionTag - The tag of the shared element transition you'd like to configure. Passing `undefined` will remove the transition.\n * @param isUnmounting - Determines whether the configuration should be included at the end of the batch, after all the non-deferred configurations (even those that were updated later). This is used to retain the correct ordering of shared elements. Defaults to `false`.\n */\nexport let updateLayoutAnimations: (\n  viewTag: number,\n  type: LayoutAnimationType,\n  config?:\n    | Keyframe\n    | LayoutAnimationFunction\n    | SharedTransitionAnimationsFunction\n    | ProgressAnimationCallback,\n  sharedTransitionTag?: string,\n  isUnmounting?: boolean\n) => void;\n\nif (shouldBeUseWeb()) {\n  updateLayoutAnimations = () => {\n    // no-op\n  };\n} else {\n  const updateLayoutAnimationsManager = createUpdateManager();\n  updateLayoutAnimations = (\n    viewTag,\n    type,\n    config,\n    sharedTransitionTag,\n    isUnmounting\n  ) =>\n    updateLayoutAnimationsManager.update(\n      {\n        viewTag,\n        type,\n        config: config ? makeShareableCloneRecursive(config) : undefined,\n        sharedTransitionTag,\n      },\n      isUnmounting\n    );\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,EAAEC,cAAc,QAAQ,mBAAmB;AAC5D,SACEC,6BAA6B,EAC7BC,2BAA2B,QACtB,QAAQ;AAWf,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,MAAMC,UAAsC,GAAG,EAAE;EACjD;EACA;EACA;EACA,MAAMC,kBAA8C,GAAG,EAAE;EAEzD,OAAO;IACLC,MAAMA,CAACC,SAAmC,EAAEC,YAAsB,EAAE;MAClE,IAAIA,YAAY,EAAE;QAChBH,kBAAkB,CAACI,IAAI,CAACF,SAAS,CAAC;MACpC,CAAC,MAAM;QACLH,UAAU,CAACK,IAAI,CAACF,SAAS,CAAC;MAC5B;MACA,IAAIH,UAAU,CAACM,MAAM,GAAGL,kBAAkB,CAACK,MAAM,KAAK,CAAC,EAAE;QACvDX,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACY,KAAK,CAAC,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACD,KAAK,CAAC;MACtD;IACF,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBV,6BAA6B,CAACG,UAAU,CAACS,MAAM,CAACR,kBAAkB,CAAC,CAAC;MACpED,UAAU,CAACM,MAAM,GAAG,CAAC;MACrBL,kBAAkB,CAACK,MAAM,GAAG,CAAC;IAC/B;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,sBAUF;AAET,IAAId,cAAc,CAAC,CAAC,EAAE;EACpBc,sBAAsB,GAAGA,CAAA,KAAM;IAC7B;EAAA,CACD;AACH,CAAC,MAAM;EACL,MAAMC,6BAA6B,GAAGZ,mBAAmB,CAAC,CAAC;EAC3DW,sBAAsB,GAAGA,CACvBE,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,mBAAmB,EACnBX,YAAY,KAEZO,6BAA6B,CAACT,MAAM,CAClC;IACEU,OAAO;IACPC,IAAI;IACJC,MAAM,EAAEA,MAAM,GAAGhB,2BAA2B,CAACgB,MAAM,CAAC,GAAGE,SAAS;IAChED;EACF,CAAC,EACDX,YACF,CAAC;AACL", "ignoreList": []}