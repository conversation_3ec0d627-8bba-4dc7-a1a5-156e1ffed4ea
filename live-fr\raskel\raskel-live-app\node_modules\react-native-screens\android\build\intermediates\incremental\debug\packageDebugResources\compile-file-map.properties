#Fri Sep 19 20:39:09 PDT 2025
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_right_foreground_close.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_right_foreground_close.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_fade_in.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_fade_in.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_standard_accelerate_interpolator.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_standard_accelerate_interpolator.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_default_exit_out.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_default_exit_out.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_right_background_open.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_right_background_open.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_default_enter_out.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_default_enter_out.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/drawable/rns_rounder_top_corners_shape.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rns_rounder_top_corners_shape.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_default_exit_in.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_default_exit_in.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_left_foreground_close.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_left_foreground_close.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_default_enter_in.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_default_enter_in.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_no_animation_250.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_no_animation_250.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim-v33/rns_default_exit_in.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim-v33\\rns_default_exit_in.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim-v33/rns_default_exit_out.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim-v33\\rns_default_exit_out.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_fade_to_bottom.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_fade_to_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim-v33/rns_default_enter_out.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim-v33\\rns_default_enter_out.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_slide_in_from_left.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_slide_in_from_left.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_left_foreground_open.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_left_foreground_open.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_fade_out.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_fade_out.xml
com.swmansion.rnscreens.react-native-screens-res-7\:/anim-v33/rns_default_enter_in.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim-v33\\rns_default_enter_in.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_slide_in_from_bottom.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_slide_in_from_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_slide_out_to_bottom.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_slide_out_to_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_no_animation_20.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_no_animation_20.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_left_background_open.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_left_background_open.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_right_background_close.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_right_background_close.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_left_background_close.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_left_background_close.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_slide_out_to_right.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_slide_out_to_right.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_fade_from_bottom.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_fade_from_bottom.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_slide_in_from_right.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_slide_in_from_right.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_slide_out_to_left.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_slide_out_to_left.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_ios_from_right_foreground_open.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_ios_from_right_foreground_open.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_no_animation_350.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_no_animation_350.xml
com.swmansion.rnscreens.react-native-screens-res-6\:/anim/rns_no_animation_medium.xml=C\:\\Users\\natha\\rovsapps\\go\\haskel\\node_modules\\react-native-screens\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rns_no_animation_medium.xml
