{"version": 3, "names": ["SLOPE_FACTOR", "VELOCITY_EPS", "rigidDecay", "animation", "now", "config", "lastTimestamp", "startTimestamp", "initialVelocity", "current", "velocity", "deltaTime", "Math", "min", "v", "exp", "deceleration", "velocityFactor", "clamp", "abs"], "sources": ["rigidDecay.ts"], "sourcesContent": ["'use strict';\nimport { SLOPE_FACTOR, VELOCITY_EPS } from './utils';\nimport type { DefaultDecayConfig, InnerDecayAnimation } from './utils';\n\nexport function rigidDecay(\n  animation: InnerDecayAnimation,\n  now: number,\n  config: DefaultDecayConfig\n): boolean {\n  'worklet';\n  const { lastTimestamp, startTimestamp, initialVelocity, current, velocity } =\n    animation;\n\n  const deltaTime = Math.min(now - lastTimestamp, 64);\n  const v =\n    velocity *\n    Math.exp(\n      -(1 - config.deceleration) * (now - startTimestamp) * SLOPE_FACTOR\n    );\n  animation.current = current + (v * config.velocityFactor * deltaTime) / 1000;\n  animation.velocity = v;\n  animation.lastTimestamp = now;\n\n  if (config.clamp) {\n    if (initialVelocity < 0 && animation.current <= config.clamp[0]) {\n      animation.current = config.clamp[0];\n      return true;\n    } else if (initialVelocity > 0 && animation.current >= config.clamp[1]) {\n      animation.current = config.clamp[1];\n      return true;\n    }\n  }\n  return Math.abs(v) < VELOCITY_EPS;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,YAAY,EAAEC,YAAY,QAAQ,SAAS;AAGpD,OAAO,SAASC,UAAUA,CACxBC,SAA8B,EAC9BC,GAAW,EACXC,MAA0B,EACjB;EACT,SAAS;;EACT,MAAM;IAAEC,aAAa;IAAEC,cAAc;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAS,CAAC,GACzEP,SAAS;EAEX,MAAMQ,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACT,GAAG,GAAGE,aAAa,EAAE,EAAE,CAAC;EACnD,MAAMQ,CAAC,GACLJ,QAAQ,GACRE,IAAI,CAACG,GAAG,CACN,EAAE,CAAC,GAAGV,MAAM,CAACW,YAAY,CAAC,IAAIZ,GAAG,GAAGG,cAAc,CAAC,GAAGP,YACxD,CAAC;EACHG,SAAS,CAACM,OAAO,GAAGA,OAAO,GAAIK,CAAC,GAAGT,MAAM,CAACY,cAAc,GAAGN,SAAS,GAAI,IAAI;EAC5ER,SAAS,CAACO,QAAQ,GAAGI,CAAC;EACtBX,SAAS,CAACG,aAAa,GAAGF,GAAG;EAE7B,IAAIC,MAAM,CAACa,KAAK,EAAE;IAChB,IAAIV,eAAe,GAAG,CAAC,IAAIL,SAAS,CAACM,OAAO,IAAIJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC,EAAE;MAC/Df,SAAS,CAACM,OAAO,GAAGJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC;MACnC,OAAO,IAAI;IACb,CAAC,MAAM,IAAIV,eAAe,GAAG,CAAC,IAAIL,SAAS,CAACM,OAAO,IAAIJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC,EAAE;MACtEf,SAAS,CAACM,OAAO,GAAGJ,MAAM,CAACa,KAAK,CAAC,CAAC,CAAC;MACnC,OAAO,IAAI;IACb;EACF;EACA,OAAON,IAAI,CAACO,GAAG,CAACL,CAAC,CAAC,GAAGb,YAAY;AACnC", "ignoreList": []}