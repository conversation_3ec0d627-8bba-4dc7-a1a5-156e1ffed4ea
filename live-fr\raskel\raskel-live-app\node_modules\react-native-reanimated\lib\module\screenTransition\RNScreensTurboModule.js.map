{"version": 3, "names": ["noopFactory", "defaultReturnValue", "console", "warn", "RNScreensTurboModule", "global", "startTransition", "topScreenId", "belowTopScreenId", "canStartTransition", "updateTransition", "finishTransition"], "sources": ["RNScreensTurboModule.ts"], "sourcesContent": ["'use strict';\nimport type { RNScreensTurboModuleType } from './commonTypes';\n\nfunction noopFactory<T>(defaultReturnValue?: T): () => T {\n  return () => {\n    'worklet';\n    console.warn(\n      '[Reanimated] RNScreensTurboModule has not been found. Check that you have installed `react-native-screens@3.30.0` or newer in your project and rebuilt your app.'\n    );\n    return defaultReturnValue as T;\n  };\n}\n\ntype TransactionConfig = {\n  topScreenId: number;\n  belowTopScreenId: number;\n  canStartTransition: boolean;\n};\n\nexport const RNScreensTurboModule: RNScreensTurboModuleType =\n  global.RNScreensTurboModule || {\n    startTransition: noopFactory<TransactionConfig>({\n      topScreenId: -1,\n      belowTopScreenId: -1,\n      canStartTransition: false,\n    }),\n    updateTransition: noopFactory(),\n    finishTransition: noopFactory(),\n  };\n"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,WAAWA,CAAIC,kBAAsB,EAAW;EACvD,OAAO,MAAM;IACX,SAAS;;IACTC,OAAO,CAACC,IAAI,CACV,kKACF,CAAC;IACD,OAAOF,kBAAkB;EAC3B,CAAC;AACH;AAQA,OAAO,MAAMG,oBAA8C,GACzDC,MAAM,CAACD,oBAAoB,IAAI;EAC7BE,eAAe,EAAEN,WAAW,CAAoB;IAC9CO,WAAW,EAAE,CAAC,CAAC;IACfC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACFC,gBAAgB,EAAEV,WAAW,CAAC,CAAC;EAC/BW,gBAAgB,EAAEX,WAAW,CAAC;AAChC,CAAC", "ignoreList": []}