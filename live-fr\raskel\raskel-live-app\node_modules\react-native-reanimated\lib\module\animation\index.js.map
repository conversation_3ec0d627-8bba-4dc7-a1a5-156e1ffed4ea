{"version": 3, "names": ["cancelAnimation", "defineAnimation", "initialUpdaterRun", "withTiming", "with<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "withClamp", "<PERSON><PERSON><PERSON><PERSON>", "withRepeat", "withSequence", "withStyleAnimation"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nexport type {\n  HigherOrderAnimation,\n  NextAnimation,\n  DelayAnimation,\n  RepeatAnimation,\n  SequenceAnimation,\n  StyleLayoutAnimation,\n} from './commonTypes';\nexport { cancelAnimation, defineAnimation, initialUpdaterRun } from './util';\nexport { withTiming } from './timing';\nexport type { TimingAnimation, WithTimingConfig } from './timing';\nexport { withSpring } from './spring';\nexport type { SpringAnimation, WithSpringConfig } from './springUtils';\nexport { withDecay } from './decay';\nexport type { DecayAnimation, WithDecayConfig } from './decay';\nexport { withClamp } from './clamp';\nexport { withDelay } from './delay';\nexport { withRepeat } from './repeat';\nexport { withSequence } from './sequence';\nexport { withStyleAnimation } from './styleAnimation';\n"], "mappings": "AAAA,YAAY;;AASZ,SAASA,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,QAAQ;AAC5E,SAASC,UAAU,QAAQ,UAAU;AAErC,SAASC,UAAU,QAAQ,UAAU;AAErC,SAASC,SAAS,QAAQ,SAAS;AAEnC,SAASC,SAAS,QAAQ,SAAS;AACnC,SAASC,SAAS,QAAQ,SAAS;AACnC,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,kBAAkB,QAAQ,kBAAkB", "ignoreList": []}