{"version": 3, "names": ["TurboModuleRegistry", "get"], "sources": ["NativeReanimatedModule.ts"], "sourcesContent": ["'use strict';\nimport type { TurboModule } from 'react-native';\nimport { TurboModuleRegistry } from 'react-native';\n\ninterface Spec extends TurboModule {\n  installTurboModule: (valueUnpackerCode: string) => boolean;\n}\n\nexport default TurboModuleRegistry.get<Spec>('ReanimatedModule');\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,mBAAmB,QAAQ,cAAc;AAMlD,eAAeA,mBAAmB,CAACC,GAAG,CAAO,kBAAkB,CAAC", "ignoreList": []}