{"version": 3, "names": ["useEvent", "useHandler", "WorkletEventHandler", "useComposedEventHandler", "handlers", "workletsRecord", "composedEventNames", "Set", "workletsMap", "filter", "h", "for<PERSON>ach", "handler", "workletEventHandler", "eventNames", "eventName", "add", "push", "worklet", "handler<PERSON>ame", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "Array", "from"], "sources": ["useComposedEventHandler.ts"], "sourcesContent": ["'use strict';\nimport { useEvent } from './useEvent';\nimport { useHandler } from './useHandler';\nimport { WorkletEventHandler } from '../WorkletEventHandler';\nimport type { ReanimatedEvent } from './commonTypes';\nimport type { WorkletFunction } from '../commonTypes';\nimport type { EventHandlerProcessed, EventHandlerInternal } from './useEvent';\n\ntype ComposedHandlerProcessed<\n  Event extends object,\n  Context extends Record<string, unknown> = Record<string, unknown>\n> = EventHandlerProcessed<Event, Context>;\n\ntype ComposedHandlerInternal<Event extends object> =\n  EventHandlerInternal<Event>;\n\n/**\n * Lets you compose multiple event handlers based on [useEvent](https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent) hook.\n *\n * @param handlers - An array of event handlers created using [useEvent](https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent) hook.\n * @returns An object you need to pass to a coresponding \"onEvent\" prop on an `Animated` component (for example handlers responsible for `onScroll` event go to `onScroll` prop).\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useComposedEventHandler\n */\n// @ts-expect-error This overload is required by our API.\nexport function useComposedEventHandler<\n  Event extends object,\n  Context extends Record<string, unknown>\n>(\n  handlers: (EventHandlerProcessed<Event, Context> | null)[]\n): ComposedHandlerProcessed<Event, Context>;\n\nexport function useComposedEventHandler<\n  Event extends object,\n  Context extends Record<string, unknown>\n>(handlers: (EventHandlerProcessed<Event, Context> | null)[]) {\n  // Record of handlers' worklets to calculate deps diffs. We use the record type to match the useHandler API requirements\n  const workletsRecord: Record<string, WorkletFunction> = {};\n  // Summed event names for registration\n  const composedEventNames = new Set<string>();\n  // Map that holds worklets for specific handled events\n  const workletsMap: {\n    [key: string]: ((event: ReanimatedEvent<Event>) => void)[];\n  } = {};\n\n  handlers\n    .filter((h) => h !== null)\n    .forEach((handler) => {\n      // EventHandlerProcessed is the return type of useEvent and has to be force casted to EventHandlerInternal, because we need WorkletEventHandler object\n      const { workletEventHandler } =\n        handler as unknown as EventHandlerInternal<Context>;\n      if (workletEventHandler instanceof WorkletEventHandler) {\n        workletEventHandler.eventNames.forEach((eventName) => {\n          composedEventNames.add(eventName);\n\n          if (workletsMap[eventName]) {\n            workletsMap[eventName].push(workletEventHandler.worklet);\n          } else {\n            workletsMap[eventName] = [workletEventHandler.worklet];\n          }\n\n          const handlerName = eventName + `${workletsMap[eventName].length}`;\n          workletsRecord[handlerName] =\n            workletEventHandler.worklet as WorkletFunction;\n        });\n      }\n    });\n\n  const { doDependenciesDiffer } = useHandler(workletsRecord);\n\n  return useEvent<Event, Context>(\n    (event) => {\n      'worklet';\n      if (workletsMap[event.eventName]) {\n        workletsMap[event.eventName].forEach((worklet) => worklet(event));\n      }\n    },\n    Array.from(composedEventNames),\n    doDependenciesDiffer\n  ) as unknown as ComposedHandlerInternal<Event>;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,mBAAmB,QAAQ,wBAAwB;;AAa5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA,OAAO,SAASC,uBAAuBA,CAGrCC,QAA0D,EAAE;EAC5D;EACA,MAAMC,cAA+C,GAAG,CAAC,CAAC;EAC1D;EACA,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAC5C;EACA,MAAMC,WAEL,GAAG,CAAC,CAAC;EAENJ,QAAQ,CACLK,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAC,CACzBC,OAAO,CAAEC,OAAO,IAAK;IACpB;IACA,MAAM;MAAEC;IAAoB,CAAC,GAC3BD,OAAmD;IACrD,IAAIC,mBAAmB,YAAYX,mBAAmB,EAAE;MACtDW,mBAAmB,CAACC,UAAU,CAACH,OAAO,CAAEI,SAAS,IAAK;QACpDT,kBAAkB,CAACU,GAAG,CAACD,SAAS,CAAC;QAEjC,IAAIP,WAAW,CAACO,SAAS,CAAC,EAAE;UAC1BP,WAAW,CAACO,SAAS,CAAC,CAACE,IAAI,CAACJ,mBAAmB,CAACK,OAAO,CAAC;QAC1D,CAAC,MAAM;UACLV,WAAW,CAACO,SAAS,CAAC,GAAG,CAACF,mBAAmB,CAACK,OAAO,CAAC;QACxD;QAEA,MAAMC,WAAW,GAAGJ,SAAS,GAAI,GAAEP,WAAW,CAACO,SAAS,CAAC,CAACK,MAAO,EAAC;QAClEf,cAAc,CAACc,WAAW,CAAC,GACzBN,mBAAmB,CAACK,OAA0B;MAClD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEJ,MAAM;IAAEG;EAAqB,CAAC,GAAGpB,UAAU,CAACI,cAAc,CAAC;EAE3D,OAAOL,QAAQ,CACZsB,KAAK,IAAK;IACT,SAAS;;IACT,IAAId,WAAW,CAACc,KAAK,CAACP,SAAS,CAAC,EAAE;MAChCP,WAAW,CAACc,KAAK,CAACP,SAAS,CAAC,CAACJ,OAAO,CAAEO,OAAO,IAAKA,OAAO,CAACI,KAAK,CAAC,CAAC;IACnE;EACF,CAAC,EACDC,KAAK,CAACC,IAAI,CAAClB,kBAAkB,CAAC,EAC9Be,oBACF,CAAC;AACH", "ignoreList": []}