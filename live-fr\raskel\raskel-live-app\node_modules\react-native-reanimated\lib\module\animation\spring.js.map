{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "initialCalculations", "calculateNewMassToMatchDuration", "underDampedSpringCalculations", "criticallyDampedSpringCalculations", "isAnimationTerminatingCalculation", "scaleZetaToMatchClamps", "checkIfConfigIsValid", "with<PERSON><PERSON><PERSON>", "toValue", "userConfig", "callback", "defaultConfig", "damping", "mass", "stiffness", "overshootClamping", "restDisplacementThreshold", "restSpeedThreshold", "velocity", "duration", "dampingRatio", "reduceMotion", "undefined", "clamp", "config", "useDuration", "skipAnimation", "springOnFrame", "animation", "now", "startTimestamp", "current", "timeFromStart", "lastTimestamp", "deltaTime", "Math", "min", "t", "v0", "x0", "zeta", "omega0", "omega1", "position", "newPosition", "newVelocity", "isOvershooting", "isVelocity", "isDisplacement", "springIsNotInMove", "isTriggeredTwice", "previousAnimation", "onStart", "value", "startValue", "triggeredTwice", "Number", "actualDuration", "onFrame"], "sources": ["spring.ts"], "sourcesContent": ["'use strict';\nimport { defineAnimation, getReduceMotionForAnimation } from './util';\nimport type {\n  Animation,\n  AnimationCallback,\n  AnimatableValue,\n  Timestamp,\n} from '../commonTypes';\nimport type {\n  SpringConfig,\n  SpringAnimation,\n  InnerSpringAnimation,\n  SpringConfigInner,\n  DefaultSpringConfig,\n} from './springUtils';\nimport {\n  initialCalculations,\n  calculateNewMassToMatchDuration,\n  underDampedSpringCalculations,\n  criticallyDampedSpringCalculations,\n  isAnimationTerminatingCalculation,\n  scaleZetaToMatchClamps,\n  checkIfConfigIsValid,\n} from './springUtils';\n\n// TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\ntype withSpringType = <T extends AnimatableValue>(\n  toValue: T,\n  userConfig?: SpringConfig,\n  callback?: AnimationCallback\n) => T;\n\n/**\n * Lets you create spring-based animations.\n *\n * @param toValue - the value at which the animation will come to rest - {@link AnimatableValue}\n * @param config - the spring animation configuration - {@link SpringConfig}\n * @param callback - a function called on animation complete - {@link AnimationCallback}\n * @returns an [animation object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object) which holds the current state of the animation\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSpring\n */\nexport const withSpring = ((\n  toValue: AnimatableValue,\n  userConfig?: SpringConfig,\n  callback?: AnimationCallback\n): Animation<SpringAnimation> => {\n  'worklet';\n\n  return defineAnimation<SpringAnimation>(toValue, () => {\n    'worklet';\n    const defaultConfig: DefaultSpringConfig = {\n      damping: 10,\n      mass: 1,\n      stiffness: 100,\n      overshootClamping: false,\n      restDisplacementThreshold: 0.01,\n      restSpeedThreshold: 2,\n      velocity: 0,\n      duration: 2000,\n      dampingRatio: 0.5,\n      reduceMotion: undefined,\n      clamp: undefined,\n    } as const;\n\n    const config: DefaultSpringConfig & SpringConfigInner = {\n      ...defaultConfig,\n      ...userConfig,\n      useDuration: !!(userConfig?.duration || userConfig?.dampingRatio),\n      skipAnimation: false,\n    };\n\n    config.skipAnimation = !checkIfConfigIsValid(config);\n\n    if (config.duration === 0) {\n      config.skipAnimation = true;\n    }\n\n    function springOnFrame(\n      animation: InnerSpringAnimation,\n      now: Timestamp\n    ): boolean {\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      const { toValue, startTimestamp, current } = animation;\n\n      const timeFromStart = now - startTimestamp;\n\n      if (config.useDuration && timeFromStart >= config.duration) {\n        animation.current = toValue;\n        // clear lastTimestamp to avoid using stale value by the next spring animation that starts after this one\n        animation.lastTimestamp = 0;\n        return true;\n      }\n\n      if (config.skipAnimation) {\n        animation.current = toValue;\n        animation.lastTimestamp = 0;\n        return true;\n      }\n      const { lastTimestamp, velocity } = animation;\n\n      const deltaTime = Math.min(now - lastTimestamp, 64);\n      animation.lastTimestamp = now;\n\n      const t = deltaTime / 1000;\n      const v0 = -velocity;\n      const x0 = toValue - current;\n\n      const { zeta, omega0, omega1 } = animation;\n\n      const { position: newPosition, velocity: newVelocity } =\n        zeta < 1\n          ? underDampedSpringCalculations(animation, {\n              zeta,\n              v0,\n              x0,\n              omega0,\n              omega1,\n              t,\n            })\n          : criticallyDampedSpringCalculations(animation, {\n              v0,\n              x0,\n              omega0,\n              t,\n            });\n\n      animation.current = newPosition;\n      animation.velocity = newVelocity;\n\n      const { isOvershooting, isVelocity, isDisplacement } =\n        isAnimationTerminatingCalculation(animation, config);\n\n      const springIsNotInMove =\n        isOvershooting || (isVelocity && isDisplacement);\n\n      if (!config.useDuration && springIsNotInMove) {\n        animation.velocity = 0;\n        animation.current = toValue;\n        // clear lastTimestamp to avoid using stale value by the next spring animation that starts after this one\n        animation.lastTimestamp = 0;\n        return true;\n      }\n\n      return false;\n    }\n\n    function isTriggeredTwice(\n      previousAnimation: SpringAnimation | undefined,\n      animation: SpringAnimation\n    ) {\n      return (\n        previousAnimation?.lastTimestamp &&\n        previousAnimation?.startTimestamp &&\n        previousAnimation?.toValue === animation.toValue &&\n        previousAnimation?.duration === animation.duration &&\n        previousAnimation?.dampingRatio === animation.dampingRatio\n      );\n    }\n\n    function onStart(\n      animation: SpringAnimation,\n      value: number,\n      now: Timestamp,\n      previousAnimation: SpringAnimation | undefined\n    ): void {\n      animation.current = value;\n      animation.startValue = value;\n\n      let mass = config.mass;\n      const triggeredTwice = isTriggeredTwice(previousAnimation, animation);\n\n      const duration = config.duration;\n\n      const x0 = triggeredTwice\n        ? // If animation is triggered twice we want to continue the previous animation\n          // form the previous starting point\n          previousAnimation?.startValue\n        : Number(animation.toValue) - value;\n\n      if (previousAnimation) {\n        animation.velocity =\n          (triggeredTwice\n            ? previousAnimation?.velocity\n            : previousAnimation?.velocity + config.velocity) || 0;\n      } else {\n        animation.velocity = config.velocity || 0;\n      }\n\n      if (triggeredTwice) {\n        animation.zeta = previousAnimation?.zeta || 0;\n        animation.omega0 = previousAnimation?.omega0 || 0;\n        animation.omega1 = previousAnimation?.omega1 || 0;\n      } else {\n        if (config.useDuration) {\n          const actualDuration = triggeredTwice\n            ? // If animation is triggered twice we want to continue the previous animation\n              // so we need to include the time that already elapsed\n              duration -\n              ((previousAnimation?.lastTimestamp || 0) -\n                (previousAnimation?.startTimestamp || 0))\n            : duration;\n\n          config.duration = actualDuration;\n          mass = calculateNewMassToMatchDuration(\n            x0 as number,\n            config,\n            animation.velocity\n          );\n        }\n\n        const { zeta, omega0, omega1 } = initialCalculations(mass, config);\n        animation.zeta = zeta;\n        animation.omega0 = omega0;\n        animation.omega1 = omega1;\n\n        if (config.clamp !== undefined) {\n          animation.zeta = scaleZetaToMatchClamps(animation, config.clamp);\n        }\n      }\n\n      animation.lastTimestamp = previousAnimation?.lastTimestamp || now;\n\n      animation.startTimestamp = triggeredTwice\n        ? previousAnimation?.startTimestamp || now\n        : now;\n    }\n\n    return {\n      onFrame: springOnFrame,\n      onStart,\n      toValue,\n      velocity: config.velocity || 0,\n      current: toValue,\n      startValue: 0,\n      callback,\n      lastTimestamp: 0,\n      startTimestamp: 0,\n      zeta: 0,\n      omega0: 0,\n      omega1: 0,\n      reduceMotion: getReduceMotionForAnimation(config.reduceMotion),\n    } as SpringAnimation;\n  });\n}) as withSpringType;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,QAAQ;AAcrE,SACEC,mBAAmB,EACnBC,+BAA+B,EAC/BC,6BAA6B,EAC7BC,kCAAkC,EAClCC,iCAAiC,EACjCC,sBAAsB,EACtBC,oBAAoB,QACf,eAAe;;AAEtB;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAIA,CACzBC,OAAwB,EACxBC,UAAyB,EACzBC,QAA4B,KACG;EAC/B,SAAS;;EAET,OAAOZ,eAAe,CAAkBU,OAAO,EAAE,MAAM;IACrD,SAAS;;IACT,MAAMG,aAAkC,GAAG;MACzCC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,CAAC;MACPC,SAAS,EAAE,GAAG;MACdC,iBAAiB,EAAE,KAAK;MACxBC,yBAAyB,EAAE,IAAI;MAC/BC,kBAAkB,EAAE,CAAC;MACrBC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,GAAG;MACjBC,YAAY,EAAEC,SAAS;MACvBC,KAAK,EAAED;IACT,CAAU;IAEV,MAAME,MAA+C,GAAG;MACtD,GAAGb,aAAa;MAChB,GAAGF,UAAU;MACbgB,WAAW,EAAE,CAAC,EAAEhB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEU,QAAQ,IAAIV,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEW,YAAY,CAAC;MACjEM,aAAa,EAAE;IACjB,CAAC;IAEDF,MAAM,CAACE,aAAa,GAAG,CAACpB,oBAAoB,CAACkB,MAAM,CAAC;IAEpD,IAAIA,MAAM,CAACL,QAAQ,KAAK,CAAC,EAAE;MACzBK,MAAM,CAACE,aAAa,GAAG,IAAI;IAC7B;IAEA,SAASC,aAAaA,CACpBC,SAA+B,EAC/BC,GAAc,EACL;MACT;MACA,MAAM;QAAErB,OAAO;QAAEsB,cAAc;QAAEC;MAAQ,CAAC,GAAGH,SAAS;MAEtD,MAAMI,aAAa,GAAGH,GAAG,GAAGC,cAAc;MAE1C,IAAIN,MAAM,CAACC,WAAW,IAAIO,aAAa,IAAIR,MAAM,CAACL,QAAQ,EAAE;QAC1DS,SAAS,CAACG,OAAO,GAAGvB,OAAO;QAC3B;QACAoB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MAEA,IAAIT,MAAM,CAACE,aAAa,EAAE;QACxBE,SAAS,CAACG,OAAO,GAAGvB,OAAO;QAC3BoB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MACA,MAAM;QAAEA,aAAa;QAAEf;MAAS,CAAC,GAAGU,SAAS;MAE7C,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,GAAGI,aAAa,EAAE,EAAE,CAAC;MACnDL,SAAS,CAACK,aAAa,GAAGJ,GAAG;MAE7B,MAAMQ,CAAC,GAAGH,SAAS,GAAG,IAAI;MAC1B,MAAMI,EAAE,GAAG,CAACpB,QAAQ;MACpB,MAAMqB,EAAE,GAAG/B,OAAO,GAAGuB,OAAO;MAE5B,MAAM;QAAES,IAAI;QAAEC,MAAM;QAAEC;MAAO,CAAC,GAAGd,SAAS;MAE1C,MAAM;QAAEe,QAAQ,EAAEC,WAAW;QAAE1B,QAAQ,EAAE2B;MAAY,CAAC,GACpDL,IAAI,GAAG,CAAC,GACJtC,6BAA6B,CAAC0B,SAAS,EAAE;QACvCY,IAAI;QACJF,EAAE;QACFC,EAAE;QACFE,MAAM;QACNC,MAAM;QACNL;MACF,CAAC,CAAC,GACFlC,kCAAkC,CAACyB,SAAS,EAAE;QAC5CU,EAAE;QACFC,EAAE;QACFE,MAAM;QACNJ;MACF,CAAC,CAAC;MAERT,SAAS,CAACG,OAAO,GAAGa,WAAW;MAC/BhB,SAAS,CAACV,QAAQ,GAAG2B,WAAW;MAEhC,MAAM;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAe,CAAC,GAClD5C,iCAAiC,CAACwB,SAAS,EAAEJ,MAAM,CAAC;MAEtD,MAAMyB,iBAAiB,GACrBH,cAAc,IAAKC,UAAU,IAAIC,cAAe;MAElD,IAAI,CAACxB,MAAM,CAACC,WAAW,IAAIwB,iBAAiB,EAAE;QAC5CrB,SAAS,CAACV,QAAQ,GAAG,CAAC;QACtBU,SAAS,CAACG,OAAO,GAAGvB,OAAO;QAC3B;QACAoB,SAAS,CAACK,aAAa,GAAG,CAAC;QAC3B,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;IAEA,SAASiB,gBAAgBA,CACvBC,iBAA8C,EAC9CvB,SAA0B,EAC1B;MACA,OACE,CAAAuB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAElB,aAAa,MAChCkB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAErB,cAAc,KACjC,CAAAqB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE3C,OAAO,MAAKoB,SAAS,CAACpB,OAAO,IAChD,CAAA2C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEhC,QAAQ,MAAKS,SAAS,CAACT,QAAQ,IAClD,CAAAgC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE/B,YAAY,MAAKQ,SAAS,CAACR,YAAY;IAE9D;IAEA,SAASgC,OAAOA,CACdxB,SAA0B,EAC1ByB,KAAa,EACbxB,GAAc,EACdsB,iBAA8C,EACxC;MACNvB,SAAS,CAACG,OAAO,GAAGsB,KAAK;MACzBzB,SAAS,CAAC0B,UAAU,GAAGD,KAAK;MAE5B,IAAIxC,IAAI,GAAGW,MAAM,CAACX,IAAI;MACtB,MAAM0C,cAAc,GAAGL,gBAAgB,CAACC,iBAAiB,EAAEvB,SAAS,CAAC;MAErE,MAAMT,QAAQ,GAAGK,MAAM,CAACL,QAAQ;MAEhC,MAAMoB,EAAE,GAAGgB,cAAc,GACrB;MACA;MACAJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEG,UAAU,GAC7BE,MAAM,CAAC5B,SAAS,CAACpB,OAAO,CAAC,GAAG6C,KAAK;MAErC,IAAIF,iBAAiB,EAAE;QACrBvB,SAAS,CAACV,QAAQ,GAChB,CAACqC,cAAc,GACXJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEjC,QAAQ,GAC3B,CAAAiC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEjC,QAAQ,IAAGM,MAAM,CAACN,QAAQ,KAAK,CAAC;MAC3D,CAAC,MAAM;QACLU,SAAS,CAACV,QAAQ,GAAGM,MAAM,CAACN,QAAQ,IAAI,CAAC;MAC3C;MAEA,IAAIqC,cAAc,EAAE;QAClB3B,SAAS,CAACY,IAAI,GAAG,CAAAW,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEX,IAAI,KAAI,CAAC;QAC7CZ,SAAS,CAACa,MAAM,GAAG,CAAAU,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEV,MAAM,KAAI,CAAC;QACjDb,SAAS,CAACc,MAAM,GAAG,CAAAS,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAET,MAAM,KAAI,CAAC;MACnD,CAAC,MAAM;QACL,IAAIlB,MAAM,CAACC,WAAW,EAAE;UACtB,MAAMgC,cAAc,GAAGF,cAAc;UACjC;UACA;UACApC,QAAQ,IACP,CAAC,CAAAgC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAElB,aAAa,KAAI,CAAC,KACpC,CAAAkB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAErB,cAAc,KAAI,CAAC,CAAC,CAAC,GAC3CX,QAAQ;UAEZK,MAAM,CAACL,QAAQ,GAAGsC,cAAc;UAChC5C,IAAI,GAAGZ,+BAA+B,CACpCsC,EAAE,EACFf,MAAM,EACNI,SAAS,CAACV,QACZ,CAAC;QACH;QAEA,MAAM;UAAEsB,IAAI;UAAEC,MAAM;UAAEC;QAAO,CAAC,GAAG1C,mBAAmB,CAACa,IAAI,EAAEW,MAAM,CAAC;QAClEI,SAAS,CAACY,IAAI,GAAGA,IAAI;QACrBZ,SAAS,CAACa,MAAM,GAAGA,MAAM;QACzBb,SAAS,CAACc,MAAM,GAAGA,MAAM;QAEzB,IAAIlB,MAAM,CAACD,KAAK,KAAKD,SAAS,EAAE;UAC9BM,SAAS,CAACY,IAAI,GAAGnC,sBAAsB,CAACuB,SAAS,EAAEJ,MAAM,CAACD,KAAK,CAAC;QAClE;MACF;MAEAK,SAAS,CAACK,aAAa,GAAG,CAAAkB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAElB,aAAa,KAAIJ,GAAG;MAEjED,SAAS,CAACE,cAAc,GAAGyB,cAAc,GACrC,CAAAJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAErB,cAAc,KAAID,GAAG,GACxCA,GAAG;IACT;IAEA,OAAO;MACL6B,OAAO,EAAE/B,aAAa;MACtByB,OAAO;MACP5C,OAAO;MACPU,QAAQ,EAAEM,MAAM,CAACN,QAAQ,IAAI,CAAC;MAC9Ba,OAAO,EAAEvB,OAAO;MAChB8C,UAAU,EAAE,CAAC;MACb5C,QAAQ;MACRuB,aAAa,EAAE,CAAC;MAChBH,cAAc,EAAE,CAAC;MACjBU,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTrB,YAAY,EAAEtB,2BAA2B,CAACyB,MAAM,CAACH,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAAoB", "ignoreList": []}