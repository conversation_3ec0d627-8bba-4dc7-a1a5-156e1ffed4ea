1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="expo.modules.devmenu" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <application>
7-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:2:3-17:17
8        <activity
8-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:3:5-16:16
9            android:name="expo.modules.devmenu.DevMenuActivity"
9-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:4:7-38
10            android:exported="true"
10-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:5:7-30
11            android:launchMode="singleTask"
11-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:6:7-38
12            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
12-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:7:7-69
13            <intent-filter>
13-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:8:7-15:23
14                <action android:name="android.intent.action.VIEW" />
14-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:9:9-61
14-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:9:17-58
15
16                <category android:name="android.intent.category.DEFAULT" />
16-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:11:9-68
16-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:11:19-65
17                <category android:name="android.intent.category.BROWSABLE" />
17-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:12:9-70
17-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:12:19-67
18
19                <data android:scheme="expo-dev-menu" />
19-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:14:9-48
19-->C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-dev-menu\android\src\debug\AndroidManifest.xml:14:15-45
20            </intent-filter>
21        </activity>
22    </application>
23
24</manifest>
