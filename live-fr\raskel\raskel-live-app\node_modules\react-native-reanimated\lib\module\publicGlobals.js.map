{"version": 3, "names": [], "sources": ["publicGlobals.ts"], "sourcesContent": ["'use strict';\n/* eslint-disable no-var */\nexport {};\n\ndeclare global {\n  /**\n   * This global variable is a diagnostic/development tool.\n   *\n   * It is `true` on the UI thread and `false` on the JS thread.\n   *\n   * It used to be necessary in the past for some of the\n   * functionalities of react-native-reanimated to work\n   * properly but it's no longer the case. Your code\n   * shouldn't depend on it, we keep it here\n   * mainly for backward compatibility for our users.\n   */\n  var _WORKLET: boolean | undefined;\n\n  /**\n   * This ArrayBuffer contains the memory address of `jsi::Runtime`\n   * which is the Reanimated UI runtime.\n   */\n  var _WORKLET_RUNTIME: ArrayBuffer;\n}\n"], "mappings": "AAAA,YAAY;;AACZ;AACA", "ignoreList": []}