  AndroidSourceDirectorySet com.android.build.api.dsl  srcDir 3com.android.build.api.dsl.AndroidSourceDirectorySet  java *com.android.build.api.dsl.AndroidSourceSet  
sourceSets )com.android.build.api.dsl.CommonExtension  AndroidComponentsExtension com.android.build.api.variant  finalizeDsl 8com.android.build.api.variant.AndroidComponentsExtension  	dependsOn /com.android.build.gradle.internal.tasks.factory  AndroidComponentsExtension expo.modules.plugin  Any expo.modules.plugin  AutolinkingCommandBuilder expo.modules.plugin  AutolinkingOptions expo.modules.plugin  Colors expo.modules.plugin  	Directory expo.modules.plugin  Exec expo.modules.plugin  ExpoAutolinkingPlugin expo.modules.plugin  ExpoGradleExtension expo.modules.plugin  ExpoRootProjectPlugin expo.modules.plugin  ExtraPropertiesExtension expo.modules.plugin  File expo.modules.plugin  GeneratePackagesListTask expo.modules.plugin  
GradleProject expo.modules.plugin  IllegalStateException expo.modules.plugin  Input expo.modules.plugin  Integer expo.modules.plugin  	KSPLookup expo.modules.plugin  List expo.modules.plugin  Optional expo.modules.plugin  
OutputFile expo.modules.plugin  Paths expo.modules.plugin  Plugin expo.modules.plugin  Project expo.modules.plugin  Property expo.modules.plugin  Provider expo.modules.plugin  RegularFile expo.modules.plugin  RegularFileProperty expo.modules.plugin  String expo.modules.plugin  Task expo.modules.plugin  TaskProvider expo.modules.plugin  	Throwable expo.modules.plugin  Unit expo.modules.plugin  VersionCatalog expo.modules.plugin  VersionCatalogsExtension expo.modules.plugin  defineDefaultProperties expo.modules.plugin  	dependsOn expo.modules.plugin  forEach expo.modules.plugin  fromJson expo.modules.plugin  generatedFilesSrcDir expo.modules.plugin  generatedPackageListFilename expo.modules.plugin  generatedPackageListNamespace expo.modules.plugin  	getOrNull expo.modules.plugin  getValue expo.modules.plugin  getVersionOrDefault expo.modules.plugin  java expo.modules.plugin  joinToString expo.modules.plugin  mapOf expo.modules.plugin  	partition expo.modules.plugin  replace expo.modules.plugin  requireNotNull expo.modules.plugin  
setIfNotExist expo.modules.plugin  to expo.modules.plugin  
trimIndent expo.modules.plugin  with expo.modules.plugin  	withColor expo.modules.plugin  withSubproject expo.modules.plugin  withSubprojects expo.modules.plugin  build -expo.modules.plugin.AutolinkingCommandBuilder  command -expo.modules.plugin.AutolinkingCommandBuilder  option -expo.modules.plugin.AutolinkingCommandBuilder  useAutolinkingOptions -expo.modules.plugin.AutolinkingCommandBuilder  	Companion &expo.modules.plugin.AutolinkingOptions  fromJson &expo.modules.plugin.AutolinkingOptions  toJson &expo.modules.plugin.AutolinkingOptions  fromJson 0expo.modules.plugin.AutolinkingOptions.Companion  AndroidComponentsExtension )expo.modules.plugin.ExpoAutolinkingPlugin  Colors )expo.modules.plugin.ExpoAutolinkingPlugin  ExpoGradleExtension )expo.modules.plugin.ExpoAutolinkingPlugin  GeneratePackagesListTask )expo.modules.plugin.ExpoAutolinkingPlugin  IllegalStateException )expo.modules.plugin.ExpoAutolinkingPlugin  Paths )expo.modules.plugin.ExpoAutolinkingPlugin  Task )expo.modules.plugin.ExpoAutolinkingPlugin  createGeneratePackagesListTask )expo.modules.plugin.ExpoAutolinkingPlugin  	dependsOn )expo.modules.plugin.ExpoAutolinkingPlugin  generatedFilesSrcDir )expo.modules.plugin.ExpoAutolinkingPlugin  generatedPackageListFilename )expo.modules.plugin.ExpoAutolinkingPlugin  generatedPackageListNamespace )expo.modules.plugin.ExpoAutolinkingPlugin  getPackageListDir )expo.modules.plugin.ExpoAutolinkingPlugin  getPackageListFile )expo.modules.plugin.ExpoAutolinkingPlugin  java )expo.modules.plugin.ExpoAutolinkingPlugin  	partition )expo.modules.plugin.ExpoAutolinkingPlugin  replace )expo.modules.plugin.ExpoAutolinkingPlugin  requireNotNull )expo.modules.plugin.ExpoAutolinkingPlugin  	withColor )expo.modules.plugin.ExpoAutolinkingPlugin  withSubprojects )expo.modules.plugin.ExpoAutolinkingPlugin  config 'expo.modules.plugin.ExpoGradleExtension  hash 'expo.modules.plugin.ExpoGradleExtension  options 'expo.modules.plugin.ExpoGradleExtension  projectRoot 'expo.modules.plugin.ExpoGradleExtension  VersionCatalogsExtension )expo.modules.plugin.ExpoRootProjectPlugin  defineDefaultProperties )expo.modules.plugin.ExpoRootProjectPlugin  java )expo.modules.plugin.ExpoRootProjectPlugin  with )expo.modules.plugin.ExpoRootProjectPlugin  AutolinkingCommandBuilder ,expo.modules.plugin.GeneratePackagesListTask  AutolinkingOptions ,expo.modules.plugin.GeneratePackagesListTask  commandLine ,expo.modules.plugin.GeneratePackagesListTask  fromJson ,expo.modules.plugin.GeneratePackagesListTask  group ,expo.modules.plugin.GeneratePackagesListTask  hash ,expo.modules.plugin.GeneratePackagesListTask  	namespace ,expo.modules.plugin.GeneratePackagesListTask  options ,expo.modules.plugin.GeneratePackagesListTask  
outputFile ,expo.modules.plugin.GeneratePackagesListTask  
workingDir ,expo.modules.plugin.GeneratePackagesListTask  ExpoAutolinkingConfig !expo.modules.plugin.configuration  
GradleProject !expo.modules.plugin.configuration  Publication !expo.modules.plugin.configuration  allProjects 7expo.modules.plugin.configuration.ExpoAutolinkingConfig  name /expo.modules.plugin.configuration.GradleProject  publication /expo.modules.plugin.configuration.GradleProject  usePublication /expo.modules.plugin.configuration.GradleProject  
artifactId -expo.modules.plugin.configuration.Publication  groupId -expo.modules.plugin.configuration.Publication  version -expo.modules.plugin.configuration.Publication  Colors expo.modules.plugin.text  	withColor expo.modules.plugin.text  GREEN expo.modules.plugin.text.Colors  YELLOW expo.modules.plugin.text.Colors  File java.io  absolutePath java.io.File  Class 	java.lang  IllegalStateException 	java.lang  parseInt java.lang.Integer  Path 
java.nio.file  Paths 
java.nio.file  toString java.nio.file.Path  get java.nio.file.Paths  Optional 	java.util  	getOrNull java.util.Optional  getVersionOrDefault java.util.Optional  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  	Throwable kotlin  getValue kotlin  requireNotNull kotlin  to kotlin  with kotlin  toString 
kotlin.Any  	withColor 
kotlin.Any  not kotlin.Boolean  invoke kotlin.Function0  invoke kotlin.Function1  
component1 kotlin.Pair  
component2 kotlin.Pair  replace 
kotlin.String  to 
kotlin.String  
trimIndent 
kotlin.String  	withColor 
kotlin.String  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  joinToString kotlin.collections  mapOf kotlin.collections  	partition kotlin.collections  	partition kotlin.collections.List  getValue kotlin.collections.Map  keys kotlin.collections.Map  joinToString kotlin.collections.Set  java 
kotlin.jvm  	getOrNull kotlin.jvm.optionals  java kotlin.reflect.KClass  forEach kotlin.sequences  joinToString kotlin.sequences  	partition kotlin.sequences  forEach kotlin.text  	partition kotlin.text  replace kotlin.text  
trimIndent kotlin.text  Action org.gradle.api  NamedDomainObjectContainer org.gradle.api  Plugin org.gradle.api  Project org.gradle.api  Task org.gradle.api  <SAM-CONSTRUCTOR> org.gradle.api.Action  AutolinkingCommandBuilder org.gradle.api.DefaultTask  AutolinkingOptions org.gradle.api.DefaultTask  fromJson org.gradle.api.DefaultTask  group org.gradle.api.DefaultTask  	getByName )org.gradle.api.NamedDomainObjectContainer  Colors org.gradle.api.Project  IllegalStateException org.gradle.api.Project  Integer org.gradle.api.Project  	KSPLookup org.gradle.api.Project  defineDefaultProperties org.gradle.api.Project  dependencies org.gradle.api.Project  evaluationDependsOn org.gradle.api.Project  
extensions org.gradle.api.Project  extra org.gradle.api.Project  findProject org.gradle.api.Project  getValue org.gradle.api.Project  getVersionOrDefault org.gradle.api.Project  gradle org.gradle.api.Project  joinToString org.gradle.api.Project  layout org.gradle.api.Project  logger org.gradle.api.Project  name org.gradle.api.Project  path org.gradle.api.Project  project org.gradle.api.Project  
setIfNotExist org.gradle.api.Project  tasks org.gradle.api.Project  
trimIndent org.gradle.api.Project  version org.gradle.api.Project  	withColor org.gradle.api.Project  withSubproject org.gradle.api.Project  withSubprojects org.gradle.api.Project  
Dependency org.gradle.api.artifacts  VersionCatalog org.gradle.api.artifacts  VersionCatalogsExtension org.gradle.api.artifacts  VersionConstraint org.gradle.api.artifacts  findVersion 'org.gradle.api.artifacts.VersionCatalog  find 1org.gradle.api.artifacts.VersionCatalogsExtension  requiredVersion *org.gradle.api.artifacts.VersionConstraint  DependencyHandler org.gradle.api.artifacts.dsl  add .org.gradle.api.artifacts.dsl.DependencyHandler  	Directory org.gradle.api.file  DirectoryProperty org.gradle.api.file  
ProjectLayout org.gradle.api.file  RegularFile org.gradle.api.file  RegularFileProperty org.gradle.api.file  dir %org.gradle.api.file.DirectoryProperty  file %org.gradle.api.file.DirectoryProperty  buildDirectory !org.gradle.api.file.ProjectLayout  asFile org.gradle.api.file.RegularFile  get 'org.gradle.api.file.RegularFileProperty  set 'org.gradle.api.file.RegularFileProperty  AutolinkingCommandBuilder $org.gradle.api.internal.AbstractTask  AutolinkingOptions $org.gradle.api.internal.AbstractTask  fromJson $org.gradle.api.internal.AbstractTask  group $org.gradle.api.internal.AbstractTask  AutolinkingCommandBuilder &org.gradle.api.internal.ConventionTask  AutolinkingOptions &org.gradle.api.internal.ConventionTask  fromJson &org.gradle.api.internal.ConventionTask  Gradle org.gradle.api.invocation  
extensions  org.gradle.api.invocation.Gradle  Logger org.gradle.api.logging  quiet org.gradle.api.logging.Logger  warn org.gradle.api.logging.Logger  ExtensionContainer org.gradle.api.plugins  ExtraPropertiesExtension org.gradle.api.plugins  
extensions %org.gradle.api.plugins.ExtensionAware  
findByType )org.gradle.api.plugins.ExtensionContainer  	getByType )org.gradle.api.plugins.ExtensionContainer  get /org.gradle.api.plugins.ExtraPropertiesExtension  has /org.gradle.api.plugins.ExtraPropertiesExtension  set /org.gradle.api.plugins.ExtraPropertiesExtension  
setIfNotExist /org.gradle.api.plugins.ExtraPropertiesExtension  Property org.gradle.api.provider  Provider org.gradle.api.provider  get  org.gradle.api.provider.Property  set  org.gradle.api.provider.Property  Exec org.gradle.api.tasks  Input org.gradle.api.tasks  
OutputFile org.gradle.api.tasks  TaskCollection org.gradle.api.tasks  
TaskContainer org.gradle.api.tasks  TaskProvider org.gradle.api.tasks  AutolinkingCommandBuilder %org.gradle.api.tasks.AbstractExecTask  AutolinkingOptions %org.gradle.api.tasks.AbstractExecTask  fromJson %org.gradle.api.tasks.AbstractExecTask  
workingDir %org.gradle.api.tasks.AbstractExecTask  AutolinkingCommandBuilder org.gradle.api.tasks.Exec  AutolinkingOptions org.gradle.api.tasks.Exec  commandLine org.gradle.api.tasks.Exec  exec org.gradle.api.tasks.Exec  fromJson org.gradle.api.tasks.Exec  named "org.gradle.api.tasks.TaskContainer  register "org.gradle.api.tasks.TaskContainer  	dependsOn !org.gradle.api.tasks.TaskProvider  extra #org.gradle.internal.extensions.core  warn org.slf4j.Logger                                                                                                                                                                                                                                                                                                                                                                                                                                                                          