{"logs": [{"outputFile": "com.msrfi.liveapp-mergeDebugResources-77:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f47581e38898051b48ece556f9194c7b\\transformed\\browser-1.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "83,138,139,140", "startColumns": "4,4,4,4", "startOffsets": "7426,11664,11772,11884", "endColumns": "111,107,111,111", "endOffsets": "7533,11767,11879,11991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d21d690b6df8bd5196af38182335d85\\transformed\\material-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1075,1142,1245,1320,1383,1475,1546,1611,1678,1750,1822,1876,1997,2056,2120,2174,2251,2383,2468,2545,2635,2715,2796,2945,3032,3115,3257,3349,3427,3483,3541,3607,3679,3756,3827,3910,3990,4069,4144,4223,4327,4417,4490,4584,4681,4755,4828,4927,4982,5066,5134,5222,5311,5373,5437,5500,5571,5680,5791,5894,6002,6062,6124,6206,6289,6365", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "266,344,421,498,592,680,792,918,999,1070,1137,1240,1315,1378,1470,1541,1606,1673,1745,1817,1871,1992,2051,2115,2169,2246,2378,2463,2540,2630,2710,2791,2940,3027,3110,3252,3344,3422,3478,3536,3602,3674,3751,3822,3905,3985,4064,4139,4218,4322,4412,4485,4579,4676,4750,4823,4922,4977,5061,5129,5217,5306,5368,5432,5495,5566,5675,5786,5889,5997,6057,6119,6201,6284,6360,6443"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,84,85,137,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "821,3678,3756,3833,3910,4004,4822,4934,5060,7538,7609,11561,11996,12071,12134,12226,12297,12362,12429,12501,12573,12627,12748,12807,12871,12925,13002,13134,13219,13296,13386,13466,13547,13696,13783,13866,14008,14100,14178,14234,14292,14358,14430,14507,14578,14661,14741,14820,14895,14974,15078,15168,15241,15335,15432,15506,15579,15678,15733,15817,15885,15973,16062,16124,16188,16251,16322,16431,16542,16645,16753,16813,16875,17039,17122,17198", "endLines": "22,50,51,52,53,54,62,63,64,84,85,137,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,205", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "987,3751,3828,3905,3999,4087,4929,5055,5136,7604,7671,11659,12066,12129,12221,12292,12357,12424,12496,12568,12622,12743,12802,12866,12920,12997,13129,13214,13291,13381,13461,13542,13691,13778,13861,14003,14095,14173,14229,14287,14353,14425,14502,14573,14656,14736,14815,14890,14969,15073,15163,15236,15330,15427,15501,15574,15673,15728,15812,15880,15968,16057,16119,16183,16246,16317,16426,16537,16640,16748,16808,16870,16952,17117,17193,17276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33d72ac5290f8957ef564231fbd82b02\\transformed\\play-services-base-18.1.0\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5141,5252,5428,5563,5667,5835,5964,6088,6330,6490,6600,6765,6896,7054,7211,7272,7341", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "5247,5423,5558,5662,5830,5959,6083,6193,6485,6595,6760,6891,7049,7206,7267,7336,7421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1076c57e2b6fc3bba30fdb23eb82909e\\transformed\\exoplayer-ui-2.18.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,726,823,914,998,1092,1187,1259,1330,1429,1529,1596,1660,1726,1806,1924,2048,2166,2241,2333,2407,2480,2574,2662,2725,2794,2847,2905,2957,3018,3078,3140,3205,3273,3343,3402,3470,3537,3605,3659,3727,3814,3901", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "288,517,721,818,909,993,1087,1182,1254,1325,1424,1524,1591,1655,1721,1801,1919,2043,2161,2236,2328,2402,2475,2569,2657,2720,2789,2842,2900,2952,3013,3073,3135,3200,3268,3338,3397,3465,3532,3600,3654,3722,3809,3896,3951"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,617,7676,7773,7864,7948,8042,8137,8209,8280,8379,8479,8546,8610,8676,8756,8874,8998,9116,9191,9283,9357,9430,9524,9612,9675,10399,10452,10510,10562,10623,10683,10745,10810,10878,10948,11007,11075,11142,11210,11264,11332,11419,11506", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "383,612,816,7768,7859,7943,8037,8132,8204,8275,8374,8474,8541,8605,8671,8751,8869,8993,9111,9186,9278,9352,9425,9519,9607,9670,9739,10447,10505,10557,10618,10678,10740,10805,10873,10943,11002,11070,11137,11205,11259,11327,11414,11501,11556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d6cd5f4345895639e9dea77e62a721a7\\transformed\\appcompat-1.7.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "992,1100,1207,1319,1407,1510,1625,1704,1781,1872,1965,2060,2154,2254,2347,2442,2536,2627,2720,2801,2905,3008,3106,3213,3320,3425,3582,16957", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "1095,1202,1314,1402,1505,1620,1699,1776,1867,1960,2055,2149,2249,2342,2437,2531,2622,2715,2796,2900,3003,3101,3208,3315,3420,3577,3673,17034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5cc0c55bacd14953f77d87885006a8d9\\transformed\\play-services-basement-18.1.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6198", "endColumns": "131", "endOffsets": "6325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b5b22b250867a7891421e198eb95eaa0\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "55,56,57,58,59,60,61,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4092,4190,4294,4393,4496,4602,4709,17281", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "4185,4289,4388,4491,4597,4704,4817,17377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dea44b29830a29c4d2a116dadfb42f8b\\transformed\\exoplayer-core-2.18.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,183,245,314,391,471,560,641", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "119,178,240,309,386,466,555,636,705"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9744,9813,9872,9934,10003,10080,10160,10249,10330", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "9808,9867,9929,9998,10075,10155,10244,10325,10394"}}]}]}