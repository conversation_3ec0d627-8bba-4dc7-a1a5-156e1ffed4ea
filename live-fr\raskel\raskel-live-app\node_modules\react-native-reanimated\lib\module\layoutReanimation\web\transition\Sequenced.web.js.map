{"version": 3, "names": ["SequencedTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "reversed", "scaleValue", "sequencedTransition", "style", "transform", "scale", "duration"], "sources": ["Sequenced.web.ts"], "sourcesContent": ["'use strict';\nimport type { TransitionData } from '../animationParser';\n\nexport function SequencedTransition(\n  name: string,\n  transitionData: TransitionData\n) {\n  const { translateX, translateY, scaleX, scaleY, reversed } = transitionData;\n\n  const scaleValue = reversed ? `1,${scaleX}` : `${scaleY},1`;\n\n  const sequencedTransition = {\n    name,\n    style: {\n      0: {\n        transform: [\n          {\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`,\n          },\n        ],\n      },\n      50: {\n        transform: [\n          {\n            translateX: reversed ? `${translateX}px` : '0px',\n            translateY: reversed ? '0px' : `${translateY}px`,\n            scale: scaleValue,\n          },\n        ],\n      },\n      100: {\n        transform: [{ translateX: '0px', translateY: '0px', scale: '1,1' }],\n      },\n    },\n    duration: 300,\n  };\n\n  return sequencedTransition;\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,mBAAmBA,CACjCC,IAAY,EACZC,cAA8B,EAC9B;EACA,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGL,cAAc;EAE3E,MAAMM,UAAU,GAAGD,QAAQ,GAAI,KAAIF,MAAO,EAAC,GAAI,GAAEC,MAAO,IAAG;EAE3D,MAAMG,mBAAmB,GAAG;IAC1BR,IAAI;IACJS,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACER,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BC,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BQ,KAAK,EAAG,GAAEP,MAAO,IAAGC,MAAO;QAC7B,CAAC;MAEL,CAAC;MACD,EAAE,EAAE;QACFK,SAAS,EAAE,CACT;UACER,UAAU,EAAEI,QAAQ,GAAI,GAAEJ,UAAW,IAAG,GAAG,KAAK;UAChDC,UAAU,EAAEG,QAAQ,GAAG,KAAK,GAAI,GAAEH,UAAW,IAAG;UAChDQ,KAAK,EAAEJ;QACT,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHG,SAAS,EAAE,CAAC;UAAER,UAAU,EAAE,KAAK;UAAEC,UAAU,EAAE,KAAK;UAAEQ,KAAK,EAAE;QAAM,CAAC;MACpE;IACF,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC;EAED,OAAOJ,mBAAmB;AAC5B", "ignoreList": []}