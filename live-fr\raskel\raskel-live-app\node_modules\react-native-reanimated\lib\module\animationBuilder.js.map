{"version": 3, "names": ["mockTargetValues", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "targetGlobalOriginX", "targetGlobalOriginY", "targetBorderRadius", "windowWidth", "windowHeight", "currentOriginX", "currentOriginY", "currentWidth", "currentHeight", "currentGlobalOriginX", "currentGlobalOriginY", "currentBorderRadius", "getCommonProperties", "layoutStyle", "componentStyle", "componentStyleFlat", "Array", "isArray", "flat", "filter", "Boolean", "map", "style", "initial", "value", "componentStylesKeys", "flatMap", "Object", "keys", "commonKeys", "key", "includes", "maybeReportOverwrittenProperties", "layoutAnimationStyle", "displayName", "commonProperties", "length", "console", "warn", "join", "maybeBuild", "layoutAnimationOrBuilder", "isAnimationBuilder", "build", "animationFactory", "__DEV__", "layoutAnimation", "animations"], "sources": ["animationBuilder.tsx"], "sourcesContent": ["'use strict';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationFunction,\n  LayoutAnimationsValues,\n} from './layoutReanimation';\nimport type { StyleProps } from './commonTypes';\nimport type { NestedArray } from './createAnimatedComponent/commonTypes';\n\nconst mockTargetValues: LayoutAnimationsValues = {\n  targetOriginX: 0,\n  targetOriginY: 0,\n  targetWidth: 0,\n  targetHeight: 0,\n  targetGlobalOriginX: 0,\n  targetGlobalOriginY: 0,\n  targetBorderRadius: 0,\n  windowWidth: 0,\n  windowHeight: 0,\n  currentOriginX: 0,\n  currentOriginY: 0,\n  currentWidth: 0,\n  currentHeight: 0,\n  currentGlobalOriginX: 0,\n  currentGlobalOriginY: 0,\n  currentBorderRadius: 0,\n};\n\nfunction getCommonProperties(\n  layoutStyle: StyleProps,\n  componentStyle: StyleProps | Array<StyleProps>\n) {\n  let componentStyleFlat = Array.isArray(componentStyle)\n    ? componentStyle.flat()\n    : [componentStyle];\n\n  componentStyleFlat = componentStyleFlat.filter(Boolean);\n\n  componentStyleFlat = componentStyleFlat.map((style) =>\n    'initial' in style\n      ? style.initial.value // Include properties of animated style\n      : style\n  );\n\n  const componentStylesKeys = componentStyleFlat.flatMap((style) =>\n    Object.keys(style)\n  );\n\n  const commonKeys = Object.keys(layoutStyle).filter((key) =>\n    componentStylesKeys.includes(key)\n  );\n\n  return commonKeys;\n}\n\nfunction maybeReportOverwrittenProperties(\n  layoutAnimationStyle: StyleProps,\n  style: NestedArray<StyleProps>,\n  displayName: string\n) {\n  const commonProperties = getCommonProperties(layoutAnimationStyle, style);\n\n  if (commonProperties.length > 0) {\n    console.warn(\n      `[Reanimated] ${\n        commonProperties.length === 1 ? 'Property' : 'Properties'\n      } \"${commonProperties.join(\n        ', '\n      )}\" of ${displayName} may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`\n    );\n  }\n}\n\nexport function maybeBuild(\n  layoutAnimationOrBuilder:\n    | ILayoutAnimationBuilder\n    | LayoutAnimationFunction\n    | Keyframe,\n  style: NestedArray<StyleProps> | undefined,\n  displayName: string\n): LayoutAnimationFunction | Keyframe {\n  const isAnimationBuilder = (\n    value: ILayoutAnimationBuilder | LayoutAnimationFunction | Keyframe\n  ): value is ILayoutAnimationBuilder =>\n    'build' in layoutAnimationOrBuilder &&\n    typeof layoutAnimationOrBuilder.build === 'function';\n\n  if (isAnimationBuilder(layoutAnimationOrBuilder)) {\n    const animationFactory = layoutAnimationOrBuilder.build();\n\n    if (__DEV__ && style) {\n      const layoutAnimation = animationFactory(mockTargetValues);\n      maybeReportOverwrittenProperties(\n        layoutAnimation.animations,\n        style,\n        displayName\n      );\n    }\n\n    return animationFactory;\n  } else {\n    return layoutAnimationOrBuilder;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AASZ,MAAMA,gBAAwC,GAAG;EAC/CC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,mBAAmB,EAAE,CAAC;EACtBC,mBAAmB,EAAE,CAAC;EACtBC,kBAAkB,EAAE,CAAC;EACrBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,CAAC;EACjBC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,CAAC;EAChBC,oBAAoB,EAAE,CAAC;EACvBC,oBAAoB,EAAE,CAAC;EACvBC,mBAAmB,EAAE;AACvB,CAAC;AAED,SAASC,mBAAmBA,CAC1BC,WAAuB,EACvBC,cAA8C,EAC9C;EACA,IAAIC,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAClDA,cAAc,CAACI,IAAI,CAAC,CAAC,GACrB,CAACJ,cAAc,CAAC;EAEpBC,kBAAkB,GAAGA,kBAAkB,CAACI,MAAM,CAACC,OAAO,CAAC;EAEvDL,kBAAkB,GAAGA,kBAAkB,CAACM,GAAG,CAAEC,KAAK,IAChD,SAAS,IAAIA,KAAK,GACdA,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;EAAA,EACpBF,KACN,CAAC;EAED,MAAMG,mBAAmB,GAAGV,kBAAkB,CAACW,OAAO,CAAEJ,KAAK,IAC3DK,MAAM,CAACC,IAAI,CAACN,KAAK,CACnB,CAAC;EAED,MAAMO,UAAU,GAAGF,MAAM,CAACC,IAAI,CAACf,WAAW,CAAC,CAACM,MAAM,CAAEW,GAAG,IACrDL,mBAAmB,CAACM,QAAQ,CAACD,GAAG,CAClC,CAAC;EAED,OAAOD,UAAU;AACnB;AAEA,SAASG,gCAAgCA,CACvCC,oBAAgC,EAChCX,KAA8B,EAC9BY,WAAmB,EACnB;EACA,MAAMC,gBAAgB,GAAGvB,mBAAmB,CAACqB,oBAAoB,EAAEX,KAAK,CAAC;EAEzE,IAAIa,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;IAC/BC,OAAO,CAACC,IAAI,CACT,gBACCH,gBAAgB,CAACC,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAC9C,KAAID,gBAAgB,CAACI,IAAI,CACxB,IACF,CAAE,QAAOL,WAAY,4IACvB,CAAC;EACH;AACF;AAEA,OAAO,SAASM,UAAUA,CACxBC,wBAGY,EACZnB,KAA0C,EAC1CY,WAAmB,EACiB;EACpC,MAAMQ,kBAAkB,GACtBlB,KAAmE,IAEnE,OAAO,IAAIiB,wBAAwB,IACnC,OAAOA,wBAAwB,CAACE,KAAK,KAAK,UAAU;EAEtD,IAAID,kBAAkB,CAACD,wBAAwB,CAAC,EAAE;IAChD,MAAMG,gBAAgB,GAAGH,wBAAwB,CAACE,KAAK,CAAC,CAAC;IAEzD,IAAIE,OAAO,IAAIvB,KAAK,EAAE;MACpB,MAAMwB,eAAe,GAAGF,gBAAgB,CAACjD,gBAAgB,CAAC;MAC1DqC,gCAAgC,CAC9Bc,eAAe,CAACC,UAAU,EAC1BzB,KAAK,EACLY,WACF,CAAC;IACH;IAEA,OAAOU,gBAAgB;EACzB,CAAC,MAAM;IACL,OAAOH,wBAAwB;EACjC;AACF", "ignoreList": []}