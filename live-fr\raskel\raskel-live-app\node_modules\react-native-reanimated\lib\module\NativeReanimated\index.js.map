{"version": 3, "names": ["reanimatedJS", "shouldBeUseWeb", "NativeReanimated"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nimport reanimatedJS from '../js-reanimated';\nimport { shouldBeUseWeb } from '../PlatformChecker';\nimport { NativeReanimated } from './NativeReanimated';\n\nexport default shouldBeUseWeb() ? reanimatedJS : new NativeReanimated();\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,YAAY,MAAM,kBAAkB;AAC3C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,eAAeD,cAAc,CAAC,CAAC,GAAGD,YAAY,GAAG,IAAIE,gBAAgB,CAAC,CAAC", "ignoreList": []}