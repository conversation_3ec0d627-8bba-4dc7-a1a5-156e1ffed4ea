{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "<PERSON><PERSON><PERSON><PERSON>", "delayMs", "_nextAnimation", "reduceMotion", "nextAnimation", "delay", "animation", "now", "startTime", "started", "previousAnimation", "current", "onStart", "finished", "onFrame", "value", "undefined", "callback", "isHigherOrder"], "sources": ["delay.ts"], "sourcesContent": ["'use strict';\nimport { defineAnimation, getReduceMotionForAnimation } from './util';\nimport type {\n  Animation,\n  Timestamp,\n  AnimatableValue,\n  AnimationObject,\n  ReduceMotion,\n} from '../commonTypes';\nimport type { DelayAnimation } from './commonTypes';\n\n// TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\ntype withDelayType = <T extends AnimatableValue>(\n  delayMs: number,\n  delayedAnimation: T,\n  reduceMotion?: ReduceMotion\n) => T;\n\n/**\n * An animation modifier that lets you start an animation with a delay.\n *\n * @param delayMs - Duration (in milliseconds) before the animation starts.\n * @param nextAnimation - The animation to delay.\n * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n * @returns An [animation object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object) which holds the current state of the animation.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withDelay\n */\nexport const withDelay = function <T extends AnimationObject>(\n  delayMs: number,\n  _nextAnimation: T | (() => T),\n  reduceMotion?: ReduceMotion\n): Animation<DelayAnimation> {\n  'worklet';\n  return defineAnimation<DelayAnimation, T>(\n    _nextAnimation,\n    (): DelayAnimation => {\n      'worklet';\n      const nextAnimation =\n        typeof _nextAnimation === 'function'\n          ? _nextAnimation()\n          : _nextAnimation;\n\n      function delay(animation: DelayAnimation, now: Timestamp): boolean {\n        const { startTime, started, previousAnimation } = animation;\n        const current: AnimatableValue = animation.current;\n\n        if (now - startTime > delayMs || animation.reduceMotion) {\n          if (!started) {\n            nextAnimation.onStart(\n              nextAnimation,\n              current,\n              now,\n              previousAnimation!\n            );\n            animation.previousAnimation = null;\n            animation.started = true;\n          }\n          const finished = nextAnimation.onFrame(nextAnimation, now);\n          animation.current = nextAnimation.current!;\n          return finished;\n        } else if (previousAnimation) {\n          const finished =\n            previousAnimation.finished ||\n            previousAnimation.onFrame(previousAnimation, now);\n          animation.current = previousAnimation.current;\n          if (finished) {\n            animation.previousAnimation = null;\n          }\n        }\n        return false;\n      }\n\n      function onStart(\n        animation: Animation<any>,\n        value: AnimatableValue,\n        now: Timestamp,\n        previousAnimation: Animation<any> | null\n      ): void {\n        animation.startTime = now;\n        animation.started = false;\n        animation.current = value;\n        if (previousAnimation === animation) {\n          animation.previousAnimation = previousAnimation.previousAnimation;\n        } else {\n          animation.previousAnimation = previousAnimation;\n        }\n\n        // child animations inherit the setting, unless they already have it defined\n        // they will have it defined only if the user used the `reduceMotion` prop\n        if (nextAnimation.reduceMotion === undefined) {\n          nextAnimation.reduceMotion = animation.reduceMotion;\n        }\n      }\n\n      const callback = (finished?: boolean): void => {\n        if (nextAnimation.callback) {\n          nextAnimation.callback(finished);\n        }\n      };\n\n      return {\n        isHigherOrder: true,\n        onFrame: delay,\n        onStart,\n        current: nextAnimation.current!,\n        callback,\n        previousAnimation: null,\n        startTime: 0,\n        started: false,\n        reduceMotion: getReduceMotionForAnimation(reduceMotion),\n      };\n    }\n  );\n} as withDelayType;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,QAAQ;;AAUrE;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG,SAAAA,CACvBC,OAAe,EACfC,cAA6B,EAC7BC,YAA2B,EACA;EAC3B,SAAS;;EACT,OAAOL,eAAe,CACpBI,cAAc,EACd,MAAsB;IACpB,SAAS;;IACT,MAAME,aAAa,GACjB,OAAOF,cAAc,KAAK,UAAU,GAChCA,cAAc,CAAC,CAAC,GAChBA,cAAc;IAEpB,SAASG,KAAKA,CAACC,SAAyB,EAAEC,GAAc,EAAW;MACjE,MAAM;QAAEC,SAAS;QAAEC,OAAO;QAAEC;MAAkB,CAAC,GAAGJ,SAAS;MAC3D,MAAMK,OAAwB,GAAGL,SAAS,CAACK,OAAO;MAElD,IAAIJ,GAAG,GAAGC,SAAS,GAAGP,OAAO,IAAIK,SAAS,CAACH,YAAY,EAAE;QACvD,IAAI,CAACM,OAAO,EAAE;UACZL,aAAa,CAACQ,OAAO,CACnBR,aAAa,EACbO,OAAO,EACPJ,GAAG,EACHG,iBACF,CAAC;UACDJ,SAAS,CAACI,iBAAiB,GAAG,IAAI;UAClCJ,SAAS,CAACG,OAAO,GAAG,IAAI;QAC1B;QACA,MAAMI,QAAQ,GAAGT,aAAa,CAACU,OAAO,CAACV,aAAa,EAAEG,GAAG,CAAC;QAC1DD,SAAS,CAACK,OAAO,GAAGP,aAAa,CAACO,OAAQ;QAC1C,OAAOE,QAAQ;MACjB,CAAC,MAAM,IAAIH,iBAAiB,EAAE;QAC5B,MAAMG,QAAQ,GACZH,iBAAiB,CAACG,QAAQ,IAC1BH,iBAAiB,CAACI,OAAO,CAACJ,iBAAiB,EAAEH,GAAG,CAAC;QACnDD,SAAS,CAACK,OAAO,GAAGD,iBAAiB,CAACC,OAAO;QAC7C,IAAIE,QAAQ,EAAE;UACZP,SAAS,CAACI,iBAAiB,GAAG,IAAI;QACpC;MACF;MACA,OAAO,KAAK;IACd;IAEA,SAASE,OAAOA,CACdN,SAAyB,EACzBS,KAAsB,EACtBR,GAAc,EACdG,iBAAwC,EAClC;MACNJ,SAAS,CAACE,SAAS,GAAGD,GAAG;MACzBD,SAAS,CAACG,OAAO,GAAG,KAAK;MACzBH,SAAS,CAACK,OAAO,GAAGI,KAAK;MACzB,IAAIL,iBAAiB,KAAKJ,SAAS,EAAE;QACnCA,SAAS,CAACI,iBAAiB,GAAGA,iBAAiB,CAACA,iBAAiB;MACnE,CAAC,MAAM;QACLJ,SAAS,CAACI,iBAAiB,GAAGA,iBAAiB;MACjD;;MAEA;MACA;MACA,IAAIN,aAAa,CAACD,YAAY,KAAKa,SAAS,EAAE;QAC5CZ,aAAa,CAACD,YAAY,GAAGG,SAAS,CAACH,YAAY;MACrD;IACF;IAEA,MAAMc,QAAQ,GAAIJ,QAAkB,IAAW;MAC7C,IAAIT,aAAa,CAACa,QAAQ,EAAE;QAC1Bb,aAAa,CAACa,QAAQ,CAACJ,QAAQ,CAAC;MAClC;IACF,CAAC;IAED,OAAO;MACLK,aAAa,EAAE,IAAI;MACnBJ,OAAO,EAAET,KAAK;MACdO,OAAO;MACPD,OAAO,EAAEP,aAAa,CAACO,OAAQ;MAC/BM,QAAQ;MACRP,iBAAiB,EAAE,IAAI;MACvBF,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,KAAK;MACdN,YAAY,EAAEJ,2BAA2B,CAACI,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH,CAAkB", "ignoreList": []}