{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_ROLL_TIME", "RollInData", "RollInLeft", "name", "style", "transform", "translateX", "rotate", "duration", "RollInRight", "RollOutData", "RollOutLeft", "RollOutRight", "RollIn", "RollOut"], "sources": ["Roll.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_ROLL_TIME = 0.3;\n\nexport const RollInData = {\n  RollInLeft: {\n    name: 'RollInLeft',\n    style: {\n      0: {\n        transform: [{ translateX: '-100vw', rotate: '-180deg' }],\n      },\n      100: {\n        transform: [{ translateX: '0vw', rotate: '0deg' }],\n      },\n    },\n    duration: DEFAULT_ROLL_TIME,\n  },\n\n  RollInRight: {\n    name: 'RollInRight',\n    style: {\n      0: {\n        transform: [{ translateX: '100vw', rotate: '180deg' }],\n      },\n      100: {\n        transform: [{ translateX: '0vw', rotate: '0deg' }],\n      },\n    },\n    duration: DEFAULT_ROLL_TIME,\n  },\n};\n\nexport const RollOutData = {\n  RollOutLeft: {\n    name: 'RollOutLeft',\n    style: {\n      0: {\n        transform: [{ translateX: '0vw', rotate: '0deg' }],\n      },\n      100: {\n        transform: [{ translateX: '-100vw', rotate: '-180deg' }],\n      },\n    },\n    duration: DEFAULT_ROLL_TIME,\n  },\n\n  RollOutRight: {\n    name: 'RollOutRight',\n    style: {\n      0: {\n        transform: [{ translateX: '0vw', rotate: '0deg' }],\n      },\n      100: {\n        transform: [{ translateX: '100vw', rotate: '180deg' }],\n      },\n    },\n    duration: DEFAULT_ROLL_TIME,\n  },\n};\n\nexport const RollIn = {\n  RollInLeft: {\n    style: convertAnimationObjectToKeyframes(RollInData.RollInLeft),\n    duration: RollInData.RollInLeft.duration,\n  },\n  RollInRight: {\n    style: convertAnimationObjectToKeyframes(RollInData.RollInRight),\n    duration: RollInData.RollInRight.duration,\n  },\n};\n\nexport const RollOut = {\n  RollOutLeft: {\n    style: convertAnimationObjectToKeyframes(RollOutData.RollOutLeft),\n    duration: RollOutData.RollOutLeft.duration,\n  },\n  RollOutRight: {\n    style: convertAnimationObjectToKeyframes(RollOutData.RollOutRight),\n    duration: RollOutData.RollOutRight.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,iBAAiB,GAAG,GAAG;AAE7B,OAAO,MAAMC,UAAU,GAAG;EACxBC,UAAU,EAAE;IACVC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAU,CAAC;MACzD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDS,WAAW,EAAE;IACXN,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAC;MACvD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ;AACF,CAAC;AAED,OAAO,MAAMU,WAAW,GAAG;EACzBC,WAAW,EAAE;IACXR,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAU,CAAC;MACzD;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ,CAAC;EAEDY,YAAY,EAAE;IACZT,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAO,CAAC;MACnD,CAAC;MACD,GAAG,EAAE;QACHF,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAC;MACvD;IACF,CAAC;IACDC,QAAQ,EAAER;EACZ;AACF,CAAC;AAED,OAAO,MAAMa,MAAM,GAAG;EACpBX,UAAU,EAAE;IACVE,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACC,UAAU,CAAC;IAC/DM,QAAQ,EAAEP,UAAU,CAACC,UAAU,CAACM;EAClC,CAAC;EACDC,WAAW,EAAE;IACXL,KAAK,EAAEL,iCAAiC,CAACE,UAAU,CAACQ,WAAW,CAAC;IAChED,QAAQ,EAAEP,UAAU,CAACQ,WAAW,CAACD;EACnC;AACF,CAAC;AAED,OAAO,MAAMM,OAAO,GAAG;EACrBH,WAAW,EAAE;IACXP,KAAK,EAAEL,iCAAiC,CAACW,WAAW,CAACC,WAAW,CAAC;IACjEH,QAAQ,EAAEE,WAAW,CAACC,WAAW,CAACH;EACpC,CAAC;EACDI,YAAY,EAAE;IACZR,KAAK,EAAEL,iCAAiC,CAACW,WAAW,CAACE,YAAY,CAAC;IAClEJ,QAAQ,EAAEE,WAAW,CAACE,YAAY,CAACJ;EACrC;AACF,CAAC", "ignoreList": []}