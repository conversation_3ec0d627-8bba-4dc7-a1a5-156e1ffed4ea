{"version": 3, "file": "Colors.js", "names": ["_Paths", "data", "require", "_Resources", "getProjectColorsXMLPathAsync", "projectRoot", "kind", "getResourceXMLPathAsync", "name", "setColorItem", "itemToAdd", "colorFileContentsJSON", "resources", "color", "colorNameExists", "filter", "e", "$", "_", "push", "removeColorItem", "named", "contents", "index", "findIndex", "splice", "assignColorValue", "xml", "value", "buildResourceItem", "getColorsAsObject", "getResourceItemsAsObject", "getObjectAsColorsXml", "obj", "getObjectAsResourceItems"], "sources": ["../../src/android/Colors.ts"], "sourcesContent": ["import { getResourceXMLPathAsync } from './Paths';\nimport {\n  buildResourceItem,\n  getObjectAsResourceItems,\n  getResourceItemsAsObject,\n  ResourceItemXML,\n  ResourceKind,\n  ResourceXML,\n} from './Resources';\n\nexport function getProjectColorsXMLPathAsync(\n  projectRoot: string,\n  { kind }: { kind?: ResourceKind } = {}\n) {\n  return getResourceXMLPathAsync(projectRoot, { kind, name: 'colors' });\n}\n\nexport function setColorItem(itemToAdd: ResourceItemXML, colorFileContentsJSON: ResourceXML) {\n  if (colorFileContentsJSON.resources?.color) {\n    const colorNameExists = colorFileContentsJSON.resources.color.filter(\n      (e: ResourceItemXML) => e.$.name === itemToAdd.$.name\n    )[0];\n    if (colorNameExists) {\n      colorNameExists._ = itemToAdd._;\n    } else {\n      colorFileContentsJSON.resources.color.push(itemToAdd);\n    }\n  } else {\n    if (!colorFileContentsJSON.resources || typeof colorFileContentsJSON.resources === 'string') {\n      //file was empty and JSON is `{resources : ''}`\n      colorFileContentsJSON.resources = {};\n    }\n    colorFileContentsJSON.resources.color = [itemToAdd];\n  }\n  return colorFileContentsJSON;\n}\n\nexport function removeColorItem(named: string, contents: ResourceXML) {\n  if (contents.resources?.color) {\n    const index = contents.resources.color.findIndex((e: ResourceItemXML) => e.$.name === named);\n    if (index > -1) {\n      // replace the previous value\n      contents.resources.color.splice(index, 1);\n    }\n  }\n  return contents;\n}\n\n/**\n * Set or remove value in XML based on nullish factor of the `value` property.\n */\nexport function assignColorValue(\n  xml: ResourceXML,\n  {\n    value,\n    name,\n  }: {\n    value?: string | null;\n    name: string;\n  }\n) {\n  if (value) {\n    return setColorItem(\n      buildResourceItem({\n        name,\n        value,\n      }),\n      xml\n    );\n  }\n\n  return removeColorItem(name, xml);\n}\n\n/**\n * Helper to convert a basic XML object into a simple k/v pair.\n * `colors.xml` is a very basic XML file so this is pretty safe to do.\n * Added for testing purposes.\n *\n * @param xml\n * @returns\n */\nexport function getColorsAsObject(xml: ResourceXML): Record<string, string> | null {\n  if (!xml?.resources?.color) {\n    return null;\n  }\n\n  return getResourceItemsAsObject(xml.resources.color);\n}\n\n/**\n * Helper to convert a basic k/v object to a colors XML object.\n *\n * @param xml\n * @returns\n */\nexport function getObjectAsColorsXml(obj: Record<string, string>): ResourceXML {\n  return {\n    resources: {\n      color: getObjectAsResourceItems(obj),\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASO,SAASG,4BAA4BA,CAC1CC,WAAmB,EACnB;EAAEC;AAA8B,CAAC,GAAG,CAAC,CAAC,EACtC;EACA,OAAO,IAAAC,gCAAuB,EAACF,WAAW,EAAE;IAAEC,IAAI;IAAEE,IAAI,EAAE;EAAS,CAAC,CAAC;AACvE;AAEO,SAASC,YAAYA,CAACC,SAA0B,EAAEC,qBAAkC,EAAE;EAC3F,IAAIA,qBAAqB,CAACC,SAAS,EAAEC,KAAK,EAAE;IAC1C,MAAMC,eAAe,GAAGH,qBAAqB,CAACC,SAAS,CAACC,KAAK,CAACE,MAAM,CACjEC,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACT,IAAI,KAAKE,SAAS,CAACO,CAAC,CAACT,IACnD,CAAC,CAAC,CAAC,CAAC;IACJ,IAAIM,eAAe,EAAE;MACnBA,eAAe,CAACI,CAAC,GAAGR,SAAS,CAACQ,CAAC;IACjC,CAAC,MAAM;MACLP,qBAAqB,CAACC,SAAS,CAACC,KAAK,CAACM,IAAI,CAACT,SAAS,CAAC;IACvD;EACF,CAAC,MAAM;IACL,IAAI,CAACC,qBAAqB,CAACC,SAAS,IAAI,OAAOD,qBAAqB,CAACC,SAAS,KAAK,QAAQ,EAAE;MAC3F;MACAD,qBAAqB,CAACC,SAAS,GAAG,CAAC,CAAC;IACtC;IACAD,qBAAqB,CAACC,SAAS,CAACC,KAAK,GAAG,CAACH,SAAS,CAAC;EACrD;EACA,OAAOC,qBAAqB;AAC9B;AAEO,SAASS,eAAeA,CAACC,KAAa,EAAEC,QAAqB,EAAE;EACpE,IAAIA,QAAQ,CAACV,SAAS,EAAEC,KAAK,EAAE;IAC7B,MAAMU,KAAK,GAAGD,QAAQ,CAACV,SAAS,CAACC,KAAK,CAACW,SAAS,CAAER,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACT,IAAI,KAAKa,KAAK,CAAC;IAC5F,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;MACd;MACAD,QAAQ,CAACV,SAAS,CAACC,KAAK,CAACY,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;EACF;EACA,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACO,SAASI,gBAAgBA,CAC9BC,GAAgB,EAChB;EACEC,KAAK;EACLpB;AAIF,CAAC,EACD;EACA,IAAIoB,KAAK,EAAE;IACT,OAAOnB,YAAY,CACjB,IAAAoB,8BAAiB,EAAC;MAChBrB,IAAI;MACJoB;IACF,CAAC,CAAC,EACFD,GACF,CAAC;EACH;EAEA,OAAOP,eAAe,CAACZ,IAAI,EAAEmB,GAAG,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,iBAAiBA,CAACH,GAAgB,EAAiC;EACjF,IAAI,CAACA,GAAG,EAAEf,SAAS,EAAEC,KAAK,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAO,IAAAkB,qCAAwB,EAACJ,GAAG,CAACf,SAAS,CAACC,KAAK,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASmB,oBAAoBA,CAACC,GAA2B,EAAe;EAC7E,OAAO;IACLrB,SAAS,EAAE;MACTC,KAAK,EAAE,IAAAqB,qCAAwB,EAACD,GAAG;IACrC;EACF,CAAC;AACH", "ignoreList": []}