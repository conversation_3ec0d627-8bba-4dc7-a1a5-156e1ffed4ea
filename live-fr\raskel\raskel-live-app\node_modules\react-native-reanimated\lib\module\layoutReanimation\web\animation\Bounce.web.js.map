{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_BOUNCE_TIME", "BounceInData", "BounceIn", "name", "style", "transform", "scale", "duration", "BounceInRight", "translateX", "BounceInLeft", "BounceInUp", "translateY", "BounceInDown", "BounceOutData", "BounceOut", "BounceOutRight", "BounceOutLeft", "BounceOutUp", "BounceOutDown"], "sources": ["Bounce.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_BOUNCE_TIME = 0.6;\n\nexport const BounceInData = {\n  BounceIn: {\n    name: 'BounceIn',\n    style: {\n      0: { transform: [{ scale: 0 }] },\n      55: { transform: [{ scale: 1.2 }] },\n      70: { transform: [{ scale: 0.9 }] },\n      85: { transform: [{ scale: 1.1 }] },\n      100: { transform: [{ scale: 1 }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceInRight: {\n    name: 'BounceInRight',\n    style: {\n      0: { transform: [{ translateX: '100vw' }] },\n      55: { transform: [{ translateX: '-20px' }] },\n      70: { transform: [{ translateX: '10px' }] },\n      85: { transform: [{ translateX: '-10px' }] },\n      100: { transform: [{ translateX: '0px' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceInLeft: {\n    name: 'BounceInLeft',\n    style: {\n      0: { transform: [{ translateX: '-100vw' }] },\n      55: { transform: [{ translateX: '20px' }] },\n      70: { transform: [{ translateX: '-10px' }] },\n      85: { transform: [{ translateX: '10px' }] },\n      100: { transform: [{ translateX: '0px' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceInUp: {\n    name: 'BounceInUp',\n    style: {\n      0: { transform: [{ translateY: '-100vh' }] },\n      55: { transform: [{ translateY: '20px' }] },\n      70: { transform: [{ translateY: '-10px' }] },\n      85: { transform: [{ translateY: '10px' }] },\n      100: { transform: [{ translateY: '0px' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceInDown: {\n    name: 'BounceInDown',\n    style: {\n      0: { transform: [{ translateY: '100vh' }] },\n      55: { transform: [{ translateY: '-20px' }] },\n      70: { transform: [{ translateY: '10px' }] },\n      85: { transform: [{ translateY: '-10px' }] },\n      100: { transform: [{ translateY: '0px' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n};\n\nexport const BounceOutData = {\n  BounceOut: {\n    name: 'BounceOut',\n    style: {\n      0: { transform: [{ scale: 1 }] },\n      15: { transform: [{ scale: 1.1 }] },\n      30: { transform: [{ scale: 0.9 }] },\n      45: { transform: [{ scale: 1.2 }] },\n      100: { transform: [{ scale: 0.1 }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceOutRight: {\n    name: 'BounceOutRight',\n    style: {\n      0: { transform: [{ translateX: '0px' }] },\n      15: { transform: [{ translateX: '-10px' }] },\n      30: { transform: [{ translateX: '10px' }] },\n      45: { transform: [{ translateX: '-20px' }] },\n      100: { transform: [{ translateX: '100vh' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceOutLeft: {\n    name: 'BounceOutLeft',\n    style: {\n      0: { transform: [{ translateX: '0px' }] },\n      15: { transform: [{ translateX: '10px' }] },\n      30: { transform: [{ translateX: '-10px' }] },\n      45: { transform: [{ translateX: '20px' }] },\n      100: { transform: [{ translateX: '-100vh' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceOutUp: {\n    name: 'BounceOutUp',\n    style: {\n      0: { transform: [{ translateY: '0px' }] },\n      15: { transform: [{ translateY: '10px' }] },\n      30: { transform: [{ translateY: '-10px' }] },\n      45: { transform: [{ translateY: '20px' }] },\n      100: { transform: [{ translateY: '-100vh' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n\n  BounceOutDown: {\n    name: 'BounceOutDown',\n    style: {\n      0: { transform: [{ translateY: '0px' }] },\n      15: { transform: [{ translateY: '-10px' }] },\n      30: { transform: [{ translateY: '10px' }] },\n      45: { transform: [{ translateY: '-20px' }] },\n      100: { transform: [{ translateY: '100vh' }] },\n    },\n    duration: DEFAULT_BOUNCE_TIME,\n  },\n};\n\nexport const BounceIn = {\n  BounceIn: {\n    style: convertAnimationObjectToKeyframes(BounceInData.BounceIn),\n    duration: BounceInData.BounceIn.duration,\n  },\n  BounceInRight: {\n    style: convertAnimationObjectToKeyframes(BounceInData.BounceInRight),\n    duration: BounceInData.BounceInRight.duration,\n  },\n  BounceInLeft: {\n    style: convertAnimationObjectToKeyframes(BounceInData.BounceInLeft),\n    duration: BounceInData.BounceInLeft.duration,\n  },\n  BounceInUp: {\n    style: convertAnimationObjectToKeyframes(BounceInData.BounceInUp),\n    duration: BounceInData.BounceInUp.duration,\n  },\n  BounceInDown: {\n    style: convertAnimationObjectToKeyframes(BounceInData.BounceInDown),\n    duration: BounceInData.BounceInDown.duration,\n  },\n};\n\nexport const BounceOut = {\n  BounceOut: {\n    style: convertAnimationObjectToKeyframes(BounceOutData.BounceOut),\n    duration: BounceOutData.BounceOut.duration,\n  },\n  BounceOutRight: {\n    style: convertAnimationObjectToKeyframes(BounceOutData.BounceOutRight),\n    duration: BounceOutData.BounceOutRight.duration,\n  },\n  BounceOutLeft: {\n    style: convertAnimationObjectToKeyframes(BounceOutData.BounceOutLeft),\n    duration: BounceOutData.BounceOutLeft.duration,\n  },\n  BounceOutUp: {\n    style: convertAnimationObjectToKeyframes(BounceOutData.BounceOutUp),\n    duration: BounceOutData.BounceOutUp.duration,\n  },\n  BounceOutDown: {\n    style: convertAnimationObjectToKeyframes(BounceOutData.BounceOutDown),\n    duration: BounceOutData.BounceOutDown.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,mBAAmB,GAAG,GAAG;AAE/B,OAAO,MAAMC,YAAY,GAAG;EAC1BC,QAAQ,EAAE;IACRC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE,CAAC;MAChC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE;IACnC,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDQ,aAAa,EAAE;IACbL,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDU,YAAY,EAAE;IACZP,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDW,UAAU,EAAE;IACVR,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDL,QAAQ,EAAEP;EACZ,CAAC;EAEDa,YAAY,EAAE;IACZV,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDL,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAMc,aAAa,GAAG;EAC3BC,SAAS,EAAE;IACTZ,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE,CAAC;MAChC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE;IACrC,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDgB,cAAc,EAAE;IACdb,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDiB,aAAa,EAAE;IACbd,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDkB,WAAW,EAAE;IACXf,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDL,QAAQ,EAAEP;EACZ,CAAC;EAEDmB,aAAa,EAAE;IACbhB,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDL,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBA,QAAQ,EAAE;IACRE,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACC,QAAQ,CAAC;IAC/DK,QAAQ,EAAEN,YAAY,CAACC,QAAQ,CAACK;EAClC,CAAC;EACDC,aAAa,EAAE;IACbJ,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACO,aAAa,CAAC;IACpED,QAAQ,EAAEN,YAAY,CAACO,aAAa,CAACD;EACvC,CAAC;EACDG,YAAY,EAAE;IACZN,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACS,YAAY,CAAC;IACnEH,QAAQ,EAAEN,YAAY,CAACS,YAAY,CAACH;EACtC,CAAC;EACDI,UAAU,EAAE;IACVP,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACU,UAAU,CAAC;IACjEJ,QAAQ,EAAEN,YAAY,CAACU,UAAU,CAACJ;EACpC,CAAC;EACDM,YAAY,EAAE;IACZT,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACY,YAAY,CAAC;IACnEN,QAAQ,EAAEN,YAAY,CAACY,YAAY,CAACN;EACtC;AACF,CAAC;AAED,OAAO,MAAMQ,SAAS,GAAG;EACvBA,SAAS,EAAE;IACTX,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACC,SAAS,CAAC;IACjER,QAAQ,EAAEO,aAAa,CAACC,SAAS,CAACR;EACpC,CAAC;EACDS,cAAc,EAAE;IACdZ,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACE,cAAc,CAAC;IACtET,QAAQ,EAAEO,aAAa,CAACE,cAAc,CAACT;EACzC,CAAC;EACDU,aAAa,EAAE;IACbb,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACG,aAAa,CAAC;IACrEV,QAAQ,EAAEO,aAAa,CAACG,aAAa,CAACV;EACxC,CAAC;EACDW,WAAW,EAAE;IACXd,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACI,WAAW,CAAC;IACnEX,QAAQ,EAAEO,aAAa,CAACI,WAAW,CAACX;EACtC,CAAC;EACDY,aAAa,EAAE;IACbf,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACK,aAAa,CAAC;IACrEZ,QAAQ,EAAEO,aAAa,CAACK,aAAa,CAACZ;EACxC;AACF,CAAC", "ignoreList": []}