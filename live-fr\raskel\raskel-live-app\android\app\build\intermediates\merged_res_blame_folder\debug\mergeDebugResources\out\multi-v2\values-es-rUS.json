{"logs": [{"outputFile": "com.msrfi.liveapp-mergeDebugResources-76:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1813,1908,2003,2074,2158,2234,2314,2412,2511,2577,2641,2694,2752,2800,2861,2926,2988,3054,3126,3190,3251,3317,3382,3448,3501,3566,3645,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1808,1903,1998,2069,2153,2229,2309,2407,2506,2572,2636,2689,2747,2795,2856,2921,2983,3049,3121,3185,3246,3312,3377,3443,3496,3561,3640,3719,3777"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,570,7708,7794,7881,7954,8050,8146,8226,8294,8393,8492,8558,8627,8693,8764,8859,8954,9049,9120,9204,9280,9360,9458,9557,9623,10356,10409,10467,10515,10576,10641,10703,10769,10841,10905,10966,11032,11097,11163,11216,11281,11360,11439", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "375,565,752,7789,7876,7949,8045,8141,8221,8289,8388,8487,8553,8622,8688,8759,8854,8949,9044,9115,9199,9275,9355,9453,9552,9618,9682,10404,10462,10510,10571,10636,10698,10764,10836,10900,10961,11027,11092,11158,11211,11276,11355,11434,11492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1055,1164,1272,1357,1459,1575,1660,1740,1831,1924,2019,2113,2212,2305,2404,2500,2591,2682,2764,2871,2970,3069,3177,3285,3392,3551,16881", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "1050,1159,1267,1352,1454,1570,1655,1735,1826,1919,2014,2108,2207,2300,2399,2495,2586,2677,2759,2866,2965,3064,3172,3280,3387,3546,3646,16959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "55,56,57,58,59,60,61,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4100,4199,4301,4401,4499,4606,4712,17200", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4194,4296,4396,4494,4601,4707,4827,17296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "83,138,139,140", "startColumns": "4,4,4,4", "startOffsets": "7472,11592,11694,11809", "endColumns": "108,101,114,105", "endOffsets": "7576,11689,11804,11910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\962ce58a92e31b97c141ac06310bb23c\\transformed\\play-services-base-18.1.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5143,5251,5417,5549,5657,5818,5949,6072,6324,6495,6604,6774,6907,7084,7262,7332,7394", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "5246,5412,5544,5652,5813,5944,6067,6173,6490,6599,6769,6902,7079,7257,7327,7389,7467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\107af08ab037182b56f33201f0856ef3\\transformed\\play-services-basement-18.1.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6178", "endColumns": "145", "endOffsets": "6319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1100,1165,1260,1330,1393,1486,1550,1622,1685,1759,1823,1879,1997,2055,2117,2173,2253,2387,2476,2552,2650,2731,2812,2953,3034,3114,3265,3355,3432,3488,3544,3610,3689,3771,3842,3931,4004,4081,4151,4228,4334,4423,4497,4591,4693,4765,4846,4950,5003,5088,5155,5248,5337,5399,5463,5526,5594,5705,5816,5918,6023,6083,6143,6226,6309,6385", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "273,355,435,521,626,722,824,952,1033,1095,1160,1255,1325,1388,1481,1545,1617,1680,1754,1818,1874,1992,2050,2112,2168,2248,2382,2471,2547,2645,2726,2807,2948,3029,3109,3260,3350,3427,3483,3539,3605,3684,3766,3837,3926,3999,4076,4146,4223,4329,4418,4492,4586,4688,4760,4841,4945,4998,5083,5150,5243,5332,5394,5458,5521,5589,5700,5811,5913,6018,6078,6138,6221,6304,6380,6457"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,84,85,137,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "757,3651,3733,3813,3899,4004,4832,4934,5062,7581,7643,11497,11915,11985,12048,12141,12205,12277,12340,12414,12478,12534,12652,12710,12772,12828,12908,13042,13131,13207,13305,13386,13467,13608,13689,13769,13920,14010,14087,14143,14199,14265,14344,14426,14497,14586,14659,14736,14806,14883,14989,15078,15152,15246,15348,15420,15501,15605,15658,15743,15810,15903,15992,16054,16118,16181,16249,16360,16471,16573,16678,16738,16798,16964,17047,17123", "endLines": "22,50,51,52,53,54,62,63,64,84,85,137,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,205", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "930,3728,3808,3894,3999,4095,4929,5057,5138,7638,7703,11587,11980,12043,12136,12200,12272,12335,12409,12473,12529,12647,12705,12767,12823,12903,13037,13126,13202,13300,13381,13462,13603,13684,13764,13915,14005,14082,14138,14194,14260,14339,14421,14492,14581,14654,14731,14801,14878,14984,15073,15147,15241,15343,15415,15496,15600,15653,15738,15805,15898,15987,16049,16113,16176,16244,16355,16466,16568,16673,16733,16793,16876,17042,17118,17195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1fa8e62cff35f0924d9735e7c1c52928\\transformed\\exoplayer-core-2.18.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9687,9762,9825,9890,9959,10036,10110,10199,10287", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "9757,9820,9885,9954,10031,10105,10194,10282,10351"}}]}]}