{"version": 3, "names": [], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\nimport type { Ref, Component } from 'react';\nimport type {\n  ShadowNodeWrapper,\n  SharedValue,\n  StyleProps,\n} from '../commonTypes';\nimport type { ViewConfig } from '../ConfigHelper';\nimport type { ViewDescriptorsSet } from '../ViewDescriptorsSet';\nimport type {\n  BaseAnimationBuilder,\n  EntryExitAnimationFunction,\n  ILayoutAnimationBuilder,\n  SharedTransition,\n} from '../layoutReanimation';\nimport type { SkipEnteringContext } from '../component/LayoutAnimationConfig';\n\nexport interface AnimatedProps extends Record<string, unknown> {\n  viewDescriptors?: ViewDescriptorsSet;\n  initial?: SharedValue<StyleProps>;\n}\n\nexport interface ViewInfo {\n  viewTag: number | HTMLElement | null;\n  viewName: string | null;\n  shadowNodeWrapper: ShadowNodeWrapper | null;\n  viewConfig: ViewConfig;\n}\n\nexport interface IInlinePropManager {\n  attachInlineProps(\n    animatedComponent: React.Component<unknown, unknown>,\n    viewInfo: ViewInfo\n  ): void;\n  detachInlineProps(): void;\n}\n\nexport interface IPropsFilter {\n  filterNonAnimatedProps: (\n    component: React.Component<unknown, unknown> & IAnimatedComponentInternal\n  ) => Record<string, unknown>;\n}\n\nexport interface IJSPropsUpdater {\n  addOnJSPropsChangeListener(\n    animatedComponent: React.Component<unknown, unknown> &\n      IAnimatedComponentInternal\n  ): void;\n  removeOnJSPropsChangeListener(\n    animatedComponent: React.Component<unknown, unknown> &\n      IAnimatedComponentInternal\n  ): void;\n}\n\nexport interface INativeEventsManager {\n  attachEvents(): void;\n  detachEvents(): void;\n  updateEvents(prevProps: AnimatedComponentProps<InitialComponentProps>): void;\n}\n\nexport type LayoutAnimationStaticContext = {\n  presetName: string;\n};\n\nexport type AnimatedComponentProps<P extends Record<string, unknown>> = P & {\n  forwardedRef?: Ref<Component>;\n  style?: NestedArray<StyleProps>;\n  animatedProps?: Partial<AnimatedComponentProps<AnimatedProps>>;\n  animatedStyle?: StyleProps;\n  layout?: (\n    | BaseAnimationBuilder\n    | ILayoutAnimationBuilder\n    | typeof BaseAnimationBuilder\n  ) &\n    LayoutAnimationStaticContext;\n  entering?: (\n    | BaseAnimationBuilder\n    | typeof BaseAnimationBuilder\n    | EntryExitAnimationFunction\n    | Keyframe\n  ) &\n    LayoutAnimationStaticContext;\n  exiting?: (\n    | BaseAnimationBuilder\n    | typeof BaseAnimationBuilder\n    | EntryExitAnimationFunction\n    | Keyframe\n  ) &\n    LayoutAnimationStaticContext;\n  sharedTransitionTag?: string;\n  sharedTransitionStyle?: SharedTransition;\n};\n\nexport interface AnimatedComponentRef extends Component {\n  setNativeProps?: (props: Record<string, unknown>) => void;\n  getScrollableNode?: () => AnimatedComponentRef;\n  getAnimatableRef?: () => AnimatedComponentRef;\n}\n\nexport interface IAnimatedComponentInternal {\n  _styles: StyleProps[] | null;\n  _animatedProps?: Partial<AnimatedComponentProps<AnimatedProps>>;\n  /**\n   * Used for Shared Element Transitions, Layout Animations and Animated Styles. It is not related to event handling.\n   */\n  _componentViewTag: number;\n  _isFirstRender: boolean;\n  jestAnimatedStyle: { value: StyleProps };\n  _component: AnimatedComponentRef | HTMLElement | null;\n  _sharedElementTransition: SharedTransition | null;\n  _jsPropsUpdater: IJSPropsUpdater;\n  _InlinePropManager: IInlinePropManager;\n  _PropsFilter: IPropsFilter;\n  /**\n   * Doesn't exist on web.\n   */\n  _NativeEventsManager?: INativeEventsManager;\n  _viewInfo?: ViewInfo;\n  context: React.ContextType<typeof SkipEnteringContext>;\n}\n\nexport type NestedArray<T> = T | NestedArray<T>[];\n\nexport interface InitialComponentProps extends Record<string, unknown> {\n  ref?: Ref<Component>;\n  collapsable?: boolean;\n}\n"], "mappings": "AAAA,YAAY;;AAAC", "ignoreList": []}