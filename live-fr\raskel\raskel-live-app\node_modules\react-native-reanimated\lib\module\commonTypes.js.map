{"version": 3, "names": ["isWorkletFunction", "value", "__workletHash", "SensorType", "IOSReferenceFrame", "InterfaceOrientation", "KeyboardState", "ReduceMotion"], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\nimport type {\n  ViewStyle,\n  TextStyle,\n  TransformsStyle,\n  ImageStyle,\n} from 'react-native';\n\nexport type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;\nexport interface StyleProps extends ViewStyle, TextStyle {\n  originX?: number;\n  originY?: number;\n  [key: string]: any;\n}\n\n/**\n * A value that can be used both on the [JavaScript thread](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#javascript-thread) and the [UI thread](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#ui-thread).\n *\n * Shared values are defined using [useSharedValue](https://docs.swmansion.com/react-native-reanimated/docs/core/useSharedValue) hook. You access and modify shared values by their `.value` property.\n */\nexport interface SharedValue<Value = unknown> {\n  value: Value;\n  addListener: (listenerID: number, listener: (value: Value) => void) => void;\n  removeListener: (listenerID: number) => void;\n  modify: (\n    modifier?: <T extends Value>(value: T) => T,\n    forceUpdate?: boolean\n  ) => void;\n}\n\nexport interface Mutable<Value = unknown> extends SharedValue<Value> {\n  _isReanimatedSharedValue: true;\n  _animation?: AnimationObject<Value> | null; // only in Native\n  _value: Value;\n}\n\n// The below type is used for HostObjects returned by the JSI API that don't have\n// any accessible fields or methods but can carry data that is accessed from the\n// c++ side. We add a field to the type to make it possible for typescript to recognize\n// which JSI methods accept those types as arguments and to be able to correctly type\n// check other methods that may use them. However, this field is not actually defined\n// nor should be used for anything else as assigning any data to those objects will\n// throw an error.\nexport type ShareableRef<T = unknown> = {\n  __hostObjectShareableJSRef: T;\n};\n\n// In case of objects with depth or arrays of objects or arrays of arrays etc.\n// we add this utility type that makes it a SharaebleRef of the outermost type.\nexport type FlatShareableRef<T> = T extends ShareableRef<infer U>\n  ? ShareableRef<U>\n  : ShareableRef<T>;\n\nexport type MapperRawInputs = unknown[];\n\nexport type MapperOutputs = SharedValue[];\n\nexport type MapperRegistry = {\n  start: (\n    mapperID: number,\n    worklet: () => void,\n    inputs: MapperRawInputs,\n    outputs?: MapperOutputs\n  ) => void;\n  stop: (mapperID: number) => void;\n};\n\nexport type WorkletStackDetails = [\n  error: Error,\n  lineOffset: number,\n  columnOffset: number\n];\n\ntype WorkletClosure = Record<string, unknown>;\n\ninterface WorkletInitDataCommon {\n  code: string;\n}\n\ntype WorkletInitDataRelease = WorkletInitDataCommon;\n\ninterface WorkletInitDataDev extends WorkletInitDataCommon {\n  location: string;\n  sourceMap: string;\n  version: string;\n}\n\ninterface WorkletBaseCommon {\n  __closure: WorkletClosure;\n  __workletHash: number;\n}\n\ninterface WorkletBaseRelease extends WorkletBaseCommon {\n  __initData: WorkletInitDataRelease;\n}\n\ninterface WorkletBaseDev extends WorkletBaseCommon {\n  __initData: WorkletInitDataDev;\n  /**\n   * `__stackDetails` is removed after parsing.\n   */\n  __stackDetails?: WorkletStackDetails;\n}\n\nexport type WorkletFunction<\n  Args extends unknown[] = unknown[],\n  ReturnValue = unknown\n> = ((...args: Args) => ReturnValue) & (WorkletBaseRelease | WorkletBaseDev);\n\n/**\n * This function allows you to determine if a given function is a worklet. It only works\n * with Reanimated Babel plugin enabled. Unless you are doing something with internals of\n * Reanimated you shouldn't need to use this function.\n *\n * ### Note\n * Do not call it before the worklet is declared, as it will always return false then. E.g.:\n *\n * ```ts\n * isWorkletFunction(myWorklet); // Will always return false.\n *\n * function myWorklet() {\n *   'worklet';\n * };\n * ```\n *\n * ### Maintainer note\n * This function works well on the JS thread performance-wise, since the JIT can inline it.\n * However, on other threads it will not get optimized and we will get a function call overhead.\n * We want to change it in the future, but it's not feasible at the moment.\n */\nexport function isWorkletFunction<\n  Args extends unknown[] = unknown[],\n  ReturnValue = unknown,\n  BuildType extends WorkletBaseDev | WorkletBaseRelease = WorkletBaseDev\n>(value: unknown): value is WorkletFunction<Args, ReturnValue> & BuildType {\n  'worklet';\n  // Since host objects always return true for `in` operator, we have to use dot notation to check if the property exists.\n  // See https://github.com/facebook/hermes/blob/340726ef8cf666a7cce75bc60b02fa56b3e54560/lib/VM/JSObject.cpp#L1276.\n  return (\n    typeof value === 'function' &&\n    !!(value as unknown as Record<string, unknown>).__workletHash\n  );\n}\n\nexport type AnimatedPropsAdapterFunction = (\n  props: Record<string, unknown>\n) => void;\n\nexport type AnimatedPropsAdapterWorklet = WorkletFunction<\n  [props: Record<string, unknown>],\n  void\n>;\n\nexport interface NestedObject<T> {\n  [key: string]: NestedObjectValues<T>;\n}\n\nexport type NestedObjectValues<T> =\n  | T\n  | Array<NestedObjectValues<T>>\n  | NestedObject<T>;\n\ntype Animatable = number | string | Array<number>;\n\nexport type AnimatableValueObject = { [key: string]: Animatable };\n\nexport type AnimatableValue = Animatable | AnimatableValueObject;\n\nexport interface AnimationObject<T = AnimatableValue> {\n  [key: string]: any;\n  callback?: AnimationCallback;\n  current?: T;\n  toValue?: AnimationObject<T>['current'];\n  startValue?: AnimationObject<T>['current'];\n  finished?: boolean;\n  strippedCurrent?: number;\n  cancelled?: boolean;\n  reduceMotion?: boolean;\n\n  __prefix?: string;\n  __suffix?: string;\n  onFrame: (animation: any, timestamp: Timestamp) => boolean;\n  onStart: (\n    nextAnimation: any,\n    current: any,\n    timestamp: Timestamp,\n    previousAnimation: any\n  ) => void;\n}\n\nexport interface Animation<T extends AnimationObject> extends AnimationObject {\n  onFrame: (animation: T, timestamp: Timestamp) => boolean;\n  onStart: (\n    nextAnimation: T,\n    current: AnimatableValue,\n    timestamp: Timestamp,\n    previousAnimation: Animation<any> | null | T\n  ) => void;\n}\n\nexport enum SensorType {\n  ACCELEROMETER = 1,\n  GYROSCOPE = 2,\n  GRAVITY = 3,\n  MAGNETIC_FIELD = 4,\n  ROTATION = 5,\n}\nexport enum IOSReferenceFrame {\n  XArbitraryZVertical,\n  XArbitraryCorrectedZVertical,\n  XMagneticNorthZVertical,\n  XTrueNorthZVertical,\n  Auto,\n}\n\nexport type SensorConfig = {\n  interval: number | 'auto';\n  adjustToInterfaceOrientation: boolean;\n  iosReferenceFrame: IOSReferenceFrame;\n};\n\nexport type AnimatedSensor<T extends Value3D | ValueRotation> = {\n  sensor: SharedValue<T>;\n  unregister: () => void;\n  isAvailable: boolean;\n  config: SensorConfig;\n};\n\n/**\n * A function called upon animation completion. If the animation is cancelled, the callback will receive `false` as the argument; otherwise, it will receive `true`.\n */\nexport type AnimationCallback = (\n  finished?: boolean,\n  current?: AnimatableValue\n) => void;\n\nexport type Timestamp = number;\n\nexport type Value3D = {\n  x: number;\n  y: number;\n  z: number;\n  interfaceOrientation: InterfaceOrientation;\n};\n\nexport type ValueRotation = {\n  qw: number;\n  qx: number;\n  qy: number;\n  qz: number;\n  yaw: number;\n  pitch: number;\n  roll: number;\n  interfaceOrientation: InterfaceOrientation;\n};\n\nexport enum InterfaceOrientation {\n  ROTATION_0 = 0,\n  ROTATION_90 = 90,\n  ROTATION_180 = 180,\n  ROTATION_270 = 270,\n}\n\nexport type ShadowNodeWrapper = {\n  __hostObjectShadowNodeWrapper: never;\n};\n\nexport enum KeyboardState {\n  UNKNOWN = 0,\n  OPENING = 1,\n  OPEN = 2,\n  CLOSING = 3,\n  CLOSED = 4,\n}\n\nexport type AnimatedKeyboardInfo = {\n  height: SharedValue<number>;\n  state: SharedValue<KeyboardState>;\n};\n\n/**\n * @param x - A number representing X coordinate relative to the parent component.\n * @param y - A number representing Y coordinate relative to the parent component.\n * @param width - A number representing the width of the component.\n * @param height - A number representing the height of the component.\n * @param pageX - A number representing X coordinate relative to the screen.\n * @param pageY - A number representing Y coordinate relative to the screen.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/measure#returns\n */\nexport interface MeasuredDimensions {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  pageX: number;\n  pageY: number;\n}\n\nexport interface AnimatedKeyboardOptions {\n  isStatusBarTranslucentAndroid?: boolean;\n}\n\n/**\n * @param System - If the `Reduce motion` accessibility setting is enabled on the device, disable the animation. Otherwise, enable the animation.\n * @param Always - Disable the animation.\n * @param Never - Enable the animation.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/guides/accessibility\n */\nexport enum ReduceMotion {\n  System = 'system',\n  Always = 'always',\n  Never = 'never',\n}\n\nexport type EasingFunction = (t: number) => number;\n\nexport type TransformArrayItem = Extract<\n  TransformsStyle['transform'],\n  Array<unknown>\n>[number];\n\ntype MaybeSharedValue<Value> =\n  | Value\n  | (Value extends AnimatableValue ? SharedValue<Value> : never);\n\ntype MaybeSharedValueRecursive<Value> = Value extends (infer Item)[]\n  ? SharedValue<Item[]> | (MaybeSharedValueRecursive<Item> | Item)[]\n  : Value extends object\n  ?\n      | SharedValue<Value>\n      | {\n          [Key in keyof Value]:\n            | MaybeSharedValueRecursive<Value[Key]>\n            | Value[Key];\n        }\n  : MaybeSharedValue<Value>;\n\ntype DefaultStyle = ViewStyle & ImageStyle & TextStyle;\n\n// Ideally we want AnimatedStyle to not be generic, but there are\n// so many depenedencies on it being generic that it's not feasible at the moment.\nexport type AnimatedStyle<Style = DefaultStyle> =\n  | Style\n  | MaybeSharedValueRecursive<Style>;\n\nexport type AnimatedTransform = MaybeSharedValueRecursive<\n  TransformsStyle['transform']\n>;\n\n/**\n * @deprecated Please use {@link AnimatedStyle} type instead.\n */\nexport type AnimateStyle<Style = DefaultStyle> = AnimatedStyle<Style>;\n\n/**\n * @deprecated This type is no longer relevant.\n */\nexport type StylesOrDefault<T> = 'style' extends keyof T\n  ? MaybeSharedValueRecursive<T['style']>\n  : Record<string, unknown>;\n"], "mappings": "AAAA,YAAY;;AAeZ;AACA;AACA;AACA;AACA;;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;;AA6DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAI/BC,KAAc,EAA2D;EACzE,SAAS;;EACT;EACA;EACA,OACE,OAAOA,KAAK,KAAK,UAAU,IAC3B,CAAC,CAAEA,KAAK,CAAwCC,aAAa;AAEjE;AA0DA,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAOtB,WAAYC,iBAAiB,0BAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EAAA,OAAjBA,iBAAiB;AAAA;;AAqB7B;AACA;AACA;;AA0BA,WAAYC,oBAAoB,0BAApBA,oBAAoB;EAApBA,oBAAoB,CAApBA,oBAAoB;EAApBA,oBAAoB,CAApBA,oBAAoB;EAApBA,oBAAoB,CAApBA,oBAAoB;EAApBA,oBAAoB,CAApBA,oBAAoB;EAAA,OAApBA,oBAAoB;AAAA;AAWhC,WAAYC,aAAa,0BAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;;AAazB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA,WAAYC,YAAY,0BAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA;;AA+BxB;AACA;;AASA;AACA;AACA;;AAGA;AACA;AACA", "ignoreList": []}