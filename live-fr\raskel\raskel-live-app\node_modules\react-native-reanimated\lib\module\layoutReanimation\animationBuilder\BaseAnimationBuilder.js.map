{"version": 3, "names": ["_BaseAnimationBuilder", "_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON>", "ReduceMotion", "getReduceMotionFromConfig", "BaseAnimationBuilder", "constructor", "System", "Error", "duration", "durationMs", "instance", "createInstance", "durationV", "delay", "delayMs", "delayV", "<PERSON><PERSON><PERSON><PERSON>", "callback", "callbackV", "reduceMotion", "reduceMotionV", "getDuration", "randomDelay", "randomizeDelay", "get<PERSON>elay", "Math", "random", "getReduceMotion", "getDelayFunction", "isDelayProvided", "animation", "_", "build"], "sources": ["BaseAnimationBuilder.ts"], "sourcesContent": ["'use strict';\nimport { withDelay } from '../../animation';\nimport type {\n  EntryExitAnimationFunction,\n  AnimationFunction,\n  LayoutAnimationFunction,\n} from './commonTypes';\n\nimport { ReduceMotion } from '../../commonTypes';\nimport { getReduceMotionFromConfig } from '../../animation/util';\n\nexport class BaseAnimationBuilder {\n  durationV?: number;\n  delayV?: number;\n  reduceMotionV: ReduceMotion = ReduceMotion.System;\n  randomizeDelay = false;\n  callbackV?: (finished: boolean) => void;\n\n  static createInstance: <T extends typeof BaseAnimationBuilder>(\n    this: T\n  ) => InstanceType<T>;\n\n  build = (): EntryExitAnimationFunction | LayoutAnimationFunction => {\n    throw new Error('[Reanimated] Unimplemented method in child class.');\n  };\n\n  /**\n   * Lets you adjust the animation duration. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param durationMs - Length of the animation (in milliseconds).\n   */\n  static duration<T extends typeof BaseAnimationBuilder>(\n    this: T,\n    durationMs: number\n  ): InstanceType<T> {\n    const instance = this.createInstance();\n    return instance.duration(durationMs);\n  }\n\n  duration(durationMs: number): this {\n    this.durationV = durationMs;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the delay before the animation starts (in milliseconds). Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param delayMs - Delay before the animation starts (in milliseconds).\n   */\n  static delay<T extends typeof BaseAnimationBuilder>(\n    this: T,\n    delayMs: number\n  ): InstanceType<T> {\n    const instance = this.createInstance();\n    return instance.delay(delayMs);\n  }\n\n  delay(delayMs: number): this {\n    this.delayV = delayMs;\n    return this;\n  }\n\n  /**\n   * The callback that will fire after the animation ends. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param callback - Callback that will fire after the animation ends.\n   */\n  static withCallback<T extends typeof BaseAnimationBuilder>(\n    this: T,\n    callback: (finished: boolean) => void\n  ): InstanceType<T> {\n    const instance = this.createInstance();\n    return instance.withCallback(callback);\n  }\n\n  withCallback(callback: (finished: boolean) => void): this {\n    this.callbackV = callback;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the behavior when the device's reduced motion accessibility setting is turned on.  Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n   */\n  static reduceMotion<T extends typeof BaseAnimationBuilder>(\n    this: T,\n    reduceMotion: ReduceMotion\n  ): InstanceType<T> {\n    const instance = this.createInstance();\n    return instance.reduceMotion(reduceMotion);\n  }\n\n  reduceMotion(reduceMotionV: ReduceMotion): this {\n    this.reduceMotionV = reduceMotionV;\n    return this;\n  }\n\n  // 300ms is the default animation duration. If any animation has different default has to override this method.\n  static getDuration(): number {\n    return 300;\n  }\n\n  getDuration(): number {\n    return this.durationV ?? 300;\n  }\n\n  /**\n   * @deprecated Use `.delay()` with `Math.random()` instead\n   */\n  static randomDelay<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    const instance = this.createInstance();\n    return instance.randomDelay();\n  }\n\n  randomDelay(): this {\n    this.randomizeDelay = true;\n    return this;\n  }\n\n  // when randomizeDelay is set to true, randomize delay between 0 and provided value (or 1000ms if delay is not provided)\n  getDelay(): number {\n    return this.randomizeDelay\n      ? Math.random() * (this.delayV ?? 1000)\n      : this.delayV ?? 0;\n  }\n\n  getReduceMotion(): ReduceMotion {\n    return this.reduceMotionV;\n  }\n\n  getDelayFunction(): AnimationFunction {\n    const isDelayProvided = this.randomizeDelay || this.delayV;\n    const reduceMotion = this.getReduceMotion();\n    return isDelayProvided\n      ? (delay, animation) => {\n          'worklet';\n          return withDelay(delay, animation, reduceMotion);\n        }\n      : (_, animation) => {\n          'worklet';\n          animation.reduceMotion = getReduceMotionFromConfig(reduceMotion);\n          return animation;\n        };\n  }\n\n  static build<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): EntryExitAnimationFunction | LayoutAnimationFunction {\n    const instance = this.createInstance();\n    return instance.build();\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,qBAAA;AAAA,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,SAAS,QAAQ,iBAAiB;AAO3C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,yBAAyB,QAAQ,sBAAsB;AAEhE,OAAO,MAAMC,oBAAoB,CAAC;EAAAC,YAAA;IAAAzB,eAAA;IAAAA,eAAA;IAAAA,eAAA,wBAGFsB,YAAY,CAACI,MAAM;IAAA1B,eAAA,yBAChC,KAAK;IAAAA,eAAA;IAAAA,eAAA,gBAOd,MAA4D;MAClE,MAAM,IAAI2B,KAAK,CAAC,mDAAmD,CAAC;IACtE,CAAC;EAAA;EAED;AACF;AACA;AACA;AACA;EACE,OAAOC,QAAQA,CAEbC,UAAkB,EACD;IACjB,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACF,QAAQ,CAACC,UAAU,CAAC;EACtC;EAEAD,QAAQA,CAACC,UAAkB,EAAQ;IACjC,IAAI,CAACG,SAAS,GAAGH,UAAU;IAC3B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOI,KAAKA,CAEVC,OAAe,EACE;IACjB,MAAMJ,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACG,KAAK,CAACC,OAAO,CAAC;EAChC;EAEAD,KAAKA,CAACC,OAAe,EAAQ;IAC3B,IAAI,CAACC,MAAM,GAAGD,OAAO;IACrB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,YAAYA,CAEjBC,QAAqC,EACpB;IACjB,MAAMP,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACM,YAAY,CAACC,QAAQ,CAAC;EACxC;EAEAD,YAAYA,CAACC,QAAqC,EAAQ;IACxD,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,YAAYA,CAEjBA,YAA0B,EACT;IACjB,MAAMT,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACS,YAAY,CAACA,YAAY,CAAC;EAC5C;EAEAA,YAAYA,CAACC,aAA2B,EAAQ;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACb;;EAEA;EACA,OAAOC,WAAWA,CAAA,EAAW;IAC3B,OAAO,GAAG;EACZ;EAEAA,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAACT,SAAS,IAAI,GAAG;EAC9B;;EAEA;AACF;AACA;EACE,OAAOU,WAAWA,CAAA,EAEC;IACjB,MAAMZ,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACY,WAAW,CAAC,CAAC;EAC/B;EAEAA,WAAWA,CAAA,EAAS;IAClB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,OAAO,IAAI;EACb;;EAEA;EACAC,QAAQA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACD,cAAc,GACtBE,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI,IAAI,CAACX,MAAM,IAAI,IAAI,CAAC,GACrC,IAAI,CAACA,MAAM,IAAI,CAAC;EACtB;EAEAY,eAAeA,CAAA,EAAiB;IAC9B,OAAO,IAAI,CAACP,aAAa;EAC3B;EAEAQ,gBAAgBA,CAAA,EAAsB;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACN,cAAc,IAAI,IAAI,CAACR,MAAM;IAC1D,MAAMI,YAAY,GAAG,IAAI,CAACQ,eAAe,CAAC,CAAC;IAC3C,OAAOE,eAAe,GAClB,CAAChB,KAAK,EAAEiB,SAAS,KAAK;MACpB,SAAS;;MACT,OAAO7B,SAAS,CAACY,KAAK,EAAEiB,SAAS,EAAEX,YAAY,CAAC;IAClD,CAAC,GACD,CAACY,CAAC,EAAED,SAAS,KAAK;MAChB,SAAS;;MACTA,SAAS,CAACX,YAAY,GAAGhB,yBAAyB,CAACgB,YAAY,CAAC;MAChE,OAAOW,SAAS;IAClB,CAAC;EACP;EAEA,OAAOE,KAAKA,CAAA,EAE4C;IACtD,MAAMtB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACsB,KAAK,CAAC,CAAC;EACzB;AACF;AAACrD,qBAAA,GA/IYyB,oBAAoB;AAAAxB,eAAA,CAApBwB,oBAAoB", "ignoreList": []}