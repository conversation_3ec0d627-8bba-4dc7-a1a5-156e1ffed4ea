{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "withSequence", "withTiming", "Easing", "BaseAnimationBuilder", "JumpingTransition", "constructor", "args", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "duration", "durationV", "halfDuration", "config", "values", "d", "Math", "max", "abs", "targetOriginX", "currentOriginX", "targetOriginY", "currentOriginY", "initialValues", "originX", "originY", "width", "currentWidth", "height", "currentHeight", "animations", "min", "easing", "out", "exp", "bounce", "targetWidth", "targetHeight", "createInstance"], "sources": ["JumpingTransition.ts"], "sourcesContent": ["'use strict';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationFunction,\n} from '../animationBuilder/commonTypes';\nimport { withSequence, withTiming } from '../../animation';\nimport { Easing } from '../../Easing';\nimport { BaseAnimationBuilder } from '../animationBuilder';\n\n/**\n * Layout jumps - quite literally - from one position to another. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `layout` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions#jumping-transition\n */\nexport class JumpingTransition\n  extends BaseAnimationBuilder\n  implements ILayoutAnimationBuilder\n{\n  static presetName = 'JumpingTransition';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new JumpingTransition() as InstanceType<T>;\n  }\n\n  build = (): LayoutAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const callback = this.callbackV;\n    const delay = this.getDelay();\n    const duration = this.durationV ?? 300;\n    const halfDuration = duration / 2;\n    const config = { duration };\n\n    return (values) => {\n      'worklet';\n      const d = Math.max(\n        Math.abs(values.targetOriginX - values.currentOriginX),\n        Math.abs(values.targetOriginY - values.currentOriginY)\n      );\n      return {\n        initialValues: {\n          originX: values.currentOriginX,\n          originY: values.currentOriginY,\n          width: values.currentWidth,\n          height: values.currentHeight,\n        },\n        animations: {\n          originX: delayFunction(\n            delay,\n            withTiming(values.targetOriginX, config)\n          ),\n          originY: delayFunction(\n            delay,\n            withSequence(\n              withTiming(\n                Math.min(values.targetOriginY, values.currentOriginY) - d,\n                {\n                  duration: halfDuration,\n                  easing: Easing.out(Easing.exp),\n                }\n              ),\n              withTiming(values.targetOriginY, {\n                ...config,\n                duration: halfDuration,\n                easing: Easing.bounce,\n              })\n            )\n          ),\n          width: delayFunction(delay, withTiming(values.targetWidth, config)),\n          height: delayFunction(delay, withTiming(values.targetHeight, config)),\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAKb,SAASW,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,oBAAoB,QAAQ,qBAAqB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,SACpBD,oBAAoB,CAE9B;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA3B,eAAA,gBASU,MAA+B;MACrC,MAAM4B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS,IAAI,GAAG;MACtC,MAAMC,YAAY,GAAGF,QAAQ,GAAG,CAAC;MACjC,MAAMG,MAAM,GAAG;QAAEH;MAAS,CAAC;MAE3B,OAAQI,MAAM,IAAK;QACjB,SAAS;;QACT,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAChBD,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACK,aAAa,GAAGL,MAAM,CAACM,cAAc,CAAC,EACtDJ,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACO,aAAa,GAAGP,MAAM,CAACQ,cAAc,CACvD,CAAC;QACD,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEV,MAAM,CAACM,cAAc;YAC9BK,OAAO,EAAEX,MAAM,CAACQ,cAAc;YAC9BI,KAAK,EAAEZ,MAAM,CAACa,YAAY;YAC1BC,MAAM,EAAEd,MAAM,CAACe;UACjB,CAAC;UACDC,UAAU,EAAE;YACVN,OAAO,EAAEpB,aAAa,CACpBI,KAAK,EACLV,UAAU,CAACgB,MAAM,CAACK,aAAa,EAAEN,MAAM,CACzC,CAAC;YACDY,OAAO,EAAErB,aAAa,CACpBI,KAAK,EACLX,YAAY,CACVC,UAAU,CACRkB,IAAI,CAACe,GAAG,CAACjB,MAAM,CAACO,aAAa,EAAEP,MAAM,CAACQ,cAAc,CAAC,GAAGP,CAAC,EACzD;cACEL,QAAQ,EAAEE,YAAY;cACtBoB,MAAM,EAAEjC,MAAM,CAACkC,GAAG,CAAClC,MAAM,CAACmC,GAAG;YAC/B,CACF,CAAC,EACDpC,UAAU,CAACgB,MAAM,CAACO,aAAa,EAAE;cAC/B,GAAGR,MAAM;cACTH,QAAQ,EAAEE,YAAY;cACtBoB,MAAM,EAAEjC,MAAM,CAACoC;YACjB,CAAC,CACH,CACF,CAAC;YACDT,KAAK,EAAEtB,aAAa,CAACI,KAAK,EAAEV,UAAU,CAACgB,MAAM,CAACsB,WAAW,EAAEvB,MAAM,CAAC,CAAC;YACnEe,MAAM,EAAExB,aAAa,CAACI,KAAK,EAAEV,UAAU,CAACgB,MAAM,CAACuB,YAAY,EAAExB,MAAM,CAAC;UACtE,CAAC;UACDP;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAvDD,OAAOgC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIrC,iBAAiB,CAAC,CAAC;EAChC;AAoDF;AAACzB,eAAA,CA9DYyB,iBAAiB,gBAIR,mBAAmB", "ignoreList": []}