{"version": 3, "names": ["LayoutAnimationType", "SharedTransitionType"], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\nimport type {\n  ShareableRef,\n  StyleProps,\n  TransformArrayItem,\n  EasingFunction,\n} from '../../commonTypes';\n\nexport type LayoutAnimationsOptions =\n  | 'originX'\n  | 'originY'\n  | 'width'\n  | 'height'\n  | 'borderRadius'\n  | 'globalOriginX'\n  | 'globalOriginY';\n\ntype CurrentLayoutAnimationsValues = {\n  [K in LayoutAnimationsOptions as `current${Capitalize<string & K>}`]: number;\n};\n\ntype TargetLayoutAnimationsValues = {\n  [K in LayoutAnimationsOptions as `target${Capitalize<string & K>}`]: number;\n};\n\ninterface WindowDimensions {\n  windowWidth: number;\n  windowHeight: number;\n}\n\nexport interface KeyframeProps extends StyleProps {\n  easing?: EasingFunction;\n}\n\nexport type LayoutAnimation = {\n  initialValues: StyleProps;\n  animations: StyleProps;\n  callback?: (finished: boolean) => void;\n};\n\nexport type AnimationFunction = (a?: any, b?: any, c?: any) => any; // this is just a temporary mock\n\nexport type EntryAnimationsValues = TargetLayoutAnimationsValues &\n  WindowDimensions;\n\nexport type ExitAnimationsValues = CurrentLayoutAnimationsValues &\n  WindowDimensions;\n\nexport type EntryExitAnimationFunction =\n  | ((targetValues: EntryAnimationsValues) => LayoutAnimation)\n  | ((targetValues: ExitAnimationsValues) => LayoutAnimation)\n  | (() => LayoutAnimation);\n\nexport type AnimationConfigFunction<T> = (targetValues: T) => LayoutAnimation;\n\nexport type LayoutAnimationsValues = CurrentLayoutAnimationsValues &\n  TargetLayoutAnimationsValues &\n  WindowDimensions;\n\nexport interface SharedTransitionAnimationsValues\n  extends LayoutAnimationsValues {\n  currentTransformMatrix: number[];\n  targetTransformMatrix: number[];\n}\n\nexport type SharedTransitionAnimationsFunction = (\n  values: SharedTransitionAnimationsValues\n) => LayoutAnimation;\n\nexport enum LayoutAnimationType {\n  ENTERING = 1,\n  EXITING = 2,\n  LAYOUT = 3,\n  SHARED_ELEMENT_TRANSITION = 4,\n  SHARED_ELEMENT_TRANSITION_PROGRESS = 5,\n}\n\nexport type LayoutAnimationFunction = (\n  targetValues: LayoutAnimationsValues\n) => LayoutAnimation;\n\nexport type LayoutAnimationStartFunction = (\n  tag: number,\n  type: LayoutAnimationType,\n  yogaValues: Partial<SharedTransitionAnimationsValues>,\n  config: (arg: Partial<SharedTransitionAnimationsValues>) => LayoutAnimation\n) => void;\n\nexport interface ILayoutAnimationBuilder {\n  build: () => LayoutAnimationFunction;\n}\n\nexport interface BaseLayoutAnimationConfig {\n  duration?: number;\n  easing?: EasingFunction;\n  type?: AnimationFunction;\n  damping?: number;\n  dampingRatio?: number;\n  mass?: number;\n  stiffness?: number;\n  overshootClamping?: number;\n  restDisplacementThreshold?: number;\n  restSpeedThreshold?: number;\n}\n\nexport interface BaseBuilderAnimationConfig extends BaseLayoutAnimationConfig {\n  rotate?: number | string;\n}\n\nexport type LayoutAnimationAndConfig = [\n  AnimationFunction,\n  BaseBuilderAnimationConfig\n];\n\nexport interface IEntryExitAnimationBuilder {\n  build: () => EntryExitAnimationFunction;\n}\n\nexport interface IEntryAnimationBuilder {\n  build: () => AnimationConfigFunction<EntryAnimationsValues>;\n}\n\nexport interface IExitAnimationBuilder {\n  build: () => AnimationConfigFunction<ExitAnimationsValues>;\n}\n\nexport type ProgressAnimationCallback = (\n  viewTag: number,\n  progress: number\n) => void;\n\nexport type ProgressAnimation = (\n  viewTag: number,\n  values: SharedTransitionAnimationsValues,\n  progress: number\n) => void;\n\nexport type CustomProgressAnimation = (\n  values: SharedTransitionAnimationsValues,\n  progress: number\n) => StyleProps;\n\n/**\n * Used to configure the `.defaultTransitionType()` shared transition modifier.\n * @experimental\n */\nexport enum SharedTransitionType {\n  ANIMATION = 'animation',\n  PROGRESS_ANIMATION = 'progressAnimation',\n}\n\nexport type EntryExitAnimationsValues =\n  | EntryAnimationsValues\n  | ExitAnimationsValues;\n\nexport type StylePropsWithArrayTransform = StyleProps & {\n  transform?: TransformArrayItem[];\n};\n\nexport interface LayoutAnimationBatchItem {\n  viewTag: number;\n  type: LayoutAnimationType;\n  config:\n    | ShareableRef<\n        | Keyframe\n        | LayoutAnimationFunction\n        | SharedTransitionAnimationsFunction\n        | ProgressAnimationCallback\n      >\n    | undefined;\n  sharedTransitionTag?: string;\n}\n"], "mappings": "AAAA,YAAY;;AAwCwD;;AA6BpE,WAAYA,mBAAmB,0BAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAnBA,mBAAmB,CAAnBA,mBAAmB;EAAA,OAAnBA,mBAAmB;AAAA;AAyE/B;AACA;AACA;AACA;AACA,WAAYC,oBAAoB,0BAApBA,oBAAoB;EAApBA,oBAAoB;EAApBA,oBAAoB;EAAA,OAApBA,oBAAoB;AAAA", "ignoreList": []}