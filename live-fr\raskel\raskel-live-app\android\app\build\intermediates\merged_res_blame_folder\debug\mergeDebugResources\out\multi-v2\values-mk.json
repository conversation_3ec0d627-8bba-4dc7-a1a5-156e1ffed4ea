{"logs": [{"outputFile": "com.msrfi.liveapp-mergeDebugResources-76:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\107af08ab037182b56f33201f0856ef3\\transformed\\play-services-basement-18.1.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6318", "endColumns": "136", "endOffsets": "6450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,216,289,358,440,508,575,651,734,821,901,974,1058,1142,1219,1300,1382,1458,1535,1610,1703,1775,1859,1929", "endColumns": "77,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "128,211,284,353,435,503,570,646,729,816,896,969,1053,1137,1214,1295,1377,1453,1530,1605,1698,1770,1854,1924,2005"}, "to": {"startLines": "50,66,143,145,146,148,162,163,164,211,212,213,214,219,220,221,222,223,224,225,226,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3683,5216,12031,12174,12243,12389,13451,13518,13594,17454,17541,17621,17694,18118,18202,18279,18360,18442,18518,18595,18670,18864,18936,19020,19090", "endColumns": "77,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "3756,5294,12099,12238,12320,12452,13513,13589,13672,17536,17616,17689,17773,18197,18274,18355,18437,18513,18590,18665,18758,18931,19015,19085,19166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1221,1286,1389,1494,1559,1623,1686,1758,1876,1992,2107,2184,2273,2344,2423,2513,2604,2668,2736,2789,2847,2895,2956,3022,3089,3152,3222,3286,3344,3410,3475,3541,3593,3658,3737,3816", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,786,876,957,1047,1138,1216,1281,1384,1489,1554,1618,1681,1753,1871,1987,2102,2179,2268,2339,2418,2508,2599,2663,2731,2784,2842,2890,2951,3017,3084,3147,3217,3281,3339,3405,3470,3536,3588,3653,3732,3811,3867"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,406,611,7784,7873,7963,8044,8134,8225,8303,8368,8471,8576,8641,8705,8768,8840,8958,9074,9189,9266,9355,9426,9505,9595,9686,9750,10483,10536,10594,10642,10703,10769,10836,10899,10969,11033,11091,11157,11222,11288,11340,11405,11484,11563", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "401,606,792,7868,7958,8039,8129,8220,8298,8363,8466,8571,8636,8700,8763,8835,8953,9069,9184,9261,9350,9421,9500,9590,9681,9745,9813,10531,10589,10637,10698,10764,10831,10894,10964,11028,11086,11152,11217,11283,11335,11400,11479,11558,11614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1070,1136,1227,1297,1361,1464,1527,1592,1652,1720,1783,1838,1966,2023,2085,2140,2215,2355,2442,2521,2614,2700,2783,2916,2998,3083,3229,3316,3393,3447,3502,3568,3641,3717,3788,3866,3939,4015,4090,4160,4269,4357,4432,4524,4616,4690,4764,4856,4909,4991,5058,5141,5228,5290,5354,5417,5487,5601,5716,5818,5930,5988,6047,6132,6221,6305", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "266,347,427,509,606,695,791,915,1002,1065,1131,1222,1292,1356,1459,1522,1587,1647,1715,1778,1833,1961,2018,2080,2135,2210,2350,2437,2516,2609,2695,2778,2911,2993,3078,3224,3311,3388,3442,3497,3563,3636,3712,3783,3861,3934,4010,4085,4155,4264,4352,4427,4519,4611,4685,4759,4851,4904,4986,5053,5136,5223,5285,5349,5412,5482,5596,5711,5813,5925,5983,6042,6127,6216,6300,6379"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,3761,3842,3922,4004,4101,4909,5005,5129,7655,7718,11619,12104,12325,12457,12560,12623,12688,12748,12816,12879,12934,13062,13119,13181,13236,13311,13677,13764,13843,13936,14022,14105,14238,14320,14405,14551,14638,14715,14769,14824,14890,14963,15039,15110,15188,15261,15337,15412,15482,15591,15679,15754,15846,15938,16012,16086,16178,16231,16313,16380,16463,16550,16612,16676,16739,16809,16923,17038,17140,17252,17310,17369,17866,17955,18039", "endLines": "22,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "963,3837,3917,3999,4096,4185,5000,5124,5211,7713,7779,11705,12169,12384,12555,12618,12683,12743,12811,12874,12929,13057,13114,13176,13231,13306,13446,13759,13838,13931,14017,14100,14233,14315,14400,14546,14633,14710,14764,14819,14885,14958,15034,15105,15183,15256,15332,15407,15477,15586,15674,15749,15841,15933,16007,16081,16173,16226,16308,16375,16458,16545,16607,16671,16734,16804,16918,17033,17135,17247,17305,17364,17449,17950,18034,18113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "968,1076,1180,1288,1374,1482,1601,1685,1766,1857,1950,2046,2140,2240,2333,2428,2524,2615,2706,2793,2899,3005,3106,3213,3325,3429,3585,17778", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "1071,1175,1283,1369,1477,1596,1680,1761,1852,1945,2041,2135,2235,2328,2423,2519,2610,2701,2788,2894,3000,3101,3208,3320,3424,3580,3678,17861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "56,57,58,59,60,61,62,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4190,4288,4390,4487,4585,4690,4793,18763", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "4283,4385,4482,4580,4685,4788,4904,18859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\962ce58a92e31b97c141ac06310bb23c\\transformed\\play-services-base-18.1.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5299,5406,5567,5700,5810,5955,6088,6208,6455,6612,6719,6885,7018,7171,7330,7399,7463", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "5401,5562,5695,5805,5950,6083,6203,6313,6607,6714,6880,7013,7166,7325,7394,7458,7537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1fa8e62cff35f0924d9735e7c1c52928\\transformed\\exoplayer-core-2.18.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9818,9888,9949,10013,10081,10158,10231,10320,10405", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "9883,9944,10008,10076,10153,10226,10315,10400,10478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "85,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7542,11710,11815,11930", "endColumns": "112,104,114,100", "endOffsets": "7650,11810,11925,12026"}}]}]}