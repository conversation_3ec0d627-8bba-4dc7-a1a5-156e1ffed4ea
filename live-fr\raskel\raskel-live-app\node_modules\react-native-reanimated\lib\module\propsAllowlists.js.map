{"version": 3, "names": ["PropsAllowlists", "UI_THREAD_PROPS_WHITELIST", "opacity", "transform", "backgroundColor", "borderRightColor", "borderBottomColor", "borderColor", "borderEndColor", "borderLeftColor", "borderStartColor", "borderTopColor", "shadowOpacity", "shadowRadius", "scaleX", "scaleY", "translateX", "translateY", "NATIVE_THREAD_PROPS_WHITELIST", "borderBottomWidth", "borderEndWidth", "borderLeftWidth", "borderRightWidth", "borderStartWidth", "borderTopWidth", "borderWidth", "bottom", "flex", "flexGrow", "flexShrink", "height", "left", "margin", "marginBottom", "marginEnd", "marginHorizontal", "marginLeft", "marginRight", "marginStart", "marginTop", "marginVertical", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "padding", "paddingBottom", "paddingEnd", "paddingHorizontal", "paddingLeft", "paddingRight", "paddingStart", "paddingTop", "paddingVertical", "right", "start", "top", "width", "zIndex", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderRadius", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "elevation", "fontSize", "lineHeight", "textShadowRadius", "textShadowOffset", "letterSpacing", "aspectRatio", "columnGap", "end", "flexBasis", "gap", "rowGap", "display", "backfaceVisibility", "overflow", "resizeMode", "fontStyle", "fontWeight", "textAlign", "textDecorationLine", "fontFamily", "textAlignVertical", "fontVariant", "textDecorationStyle", "textTransform", "writingDirection", "align<PERSON><PERSON><PERSON>", "alignItems", "alignSelf", "direction", "flexDirection", "flexWrap", "justifyContent", "position", "color", "tintColor", "shadowColor", "placeholderTextColor"], "sources": ["propsAllowlists.ts"], "sourcesContent": ["'use strict';\ntype AllowlistsHolder = {\n  UI_THREAD_PROPS_WHITELIST: Record<string, boolean>;\n  NATIVE_THREAD_PROPS_WHITELIST: Record<string, boolean>;\n};\n\nexport const PropsAllowlists: AllowlistsHolder = {\n  /**\n   * Styles allowed to be direcly updated in UI thread\n   */\n  UI_THREAD_PROPS_WHITELIST: {\n    opacity: true,\n    transform: true,\n    /* colors */\n    backgroundColor: true,\n    borderRightColor: true,\n    borderBottomColor: true,\n    borderColor: true,\n    borderEndColor: true,\n    borderLeftColor: true,\n    borderStartColor: true,\n    borderTopColor: true,\n    /* ios styles */\n    shadowOpacity: true,\n    shadowRadius: true,\n    /* legacy android transform properties */\n    scaleX: true,\n    scaleY: true,\n    translateX: true,\n    translateY: true,\n  },\n  /**\n   * Whitelist of view props that can be updated in native thread via UIManagerModule\n   */\n  NATIVE_THREAD_PROPS_WHITELIST: {\n    borderBottomWidth: true,\n    borderEndWidth: true,\n    borderLeftWidth: true,\n    borderRightWidth: true,\n    borderStartWidth: true,\n    borderTopWidth: true,\n    borderWidth: true,\n    bottom: true,\n    flex: true,\n    flexGrow: true,\n    flexShrink: true,\n    height: true,\n    left: true,\n    margin: true,\n    marginBottom: true,\n    marginEnd: true,\n    marginHorizontal: true,\n    marginLeft: true,\n    marginRight: true,\n    marginStart: true,\n    marginTop: true,\n    marginVertical: true,\n    maxHeight: true,\n    maxWidth: true,\n    minHeight: true,\n    minWidth: true,\n    padding: true,\n    paddingBottom: true,\n    paddingEnd: true,\n    paddingHorizontal: true,\n    paddingLeft: true,\n    paddingRight: true,\n    paddingStart: true,\n    paddingTop: true,\n    paddingVertical: true,\n    right: true,\n    start: true,\n    top: true,\n    width: true,\n    zIndex: true,\n    borderBottomEndRadius: true,\n    borderBottomLeftRadius: true,\n    borderBottomRightRadius: true,\n    borderBottomStartRadius: true,\n    borderRadius: true,\n    borderTopEndRadius: true,\n    borderTopLeftRadius: true,\n    borderTopRightRadius: true,\n    borderTopStartRadius: true,\n    elevation: true,\n    fontSize: true,\n    lineHeight: true,\n    textShadowRadius: true,\n    textShadowOffset: true,\n    letterSpacing: true,\n    aspectRatio: true,\n    columnGap: true, // iOS only\n    end: true, // number or string\n    flexBasis: true, // number or string\n    gap: true,\n    rowGap: true,\n    /* strings */\n    display: true,\n    backfaceVisibility: true,\n    overflow: true,\n    resizeMode: true,\n    fontStyle: true,\n    fontWeight: true,\n    textAlign: true,\n    textDecorationLine: true,\n    fontFamily: true,\n    textAlignVertical: true,\n    fontVariant: true,\n    textDecorationStyle: true,\n    textTransform: true,\n    writingDirection: true,\n    alignContent: true,\n    alignItems: true,\n    alignSelf: true,\n    direction: true, // iOS only\n    flexDirection: true,\n    flexWrap: true,\n    justifyContent: true,\n    position: true,\n    /* text color */\n    color: true,\n    tintColor: true,\n    shadowColor: true,\n    placeholderTextColor: true,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AAMZ,OAAO,MAAMA,eAAiC,GAAG;EAC/C;AACF;AACA;EACEC,yBAAyB,EAAE;IACzBC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACf;IACAC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpB;IACAC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClB;IACAC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE;EACd,CAAC;EACD;AACF;AACA;EACEC,6BAA6B,EAAE;IAC7BC,iBAAiB,EAAE,IAAI;IACvBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,IAAI;IACtBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,IAAI;IACnBC,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,qBAAqB,EAAE,IAAI;IAC3BC,sBAAsB,EAAE,IAAI;IAC5BC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAE,IAAI;IAC7BC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,mBAAmB,EAAE,IAAI;IACzBC,oBAAoB,EAAE,IAAI;IAC1BC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IAAE;IACjBC,GAAG,EAAE,IAAI;IAAE;IACXC,SAAS,EAAE,IAAI;IAAE;IACjBC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZ;IACAC,OAAO,EAAE,IAAI;IACbC,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,kBAAkB,EAAE,IAAI;IACxBC,UAAU,EAAE,IAAI;IAChBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,mBAAmB,EAAE,IAAI;IACzBC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IAAE;IACjBC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,IAAI;IACd;IACAC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,oBAAoB,EAAE;EACxB;AACF,CAAC", "ignoreList": []}