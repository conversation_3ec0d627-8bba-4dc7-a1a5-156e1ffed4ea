{"version": 3, "names": [], "sources": ["helperTypes.ts"], "sourcesContent": ["'use strict';\n/*\nThis file is a legacy remainder of manual types from react-native-reanimated.d.ts file. \nI wasn't able to get rid of all of them from the code. \nThey should be treated as a temporary solution\nuntil time comes to refactor the code and get necessary types right. \nThis will not be easy though! \n*/\n\nimport type { RegisteredStyle, StyleProp } from 'react-native';\nimport type {\n  AnimatedStyle,\n  SharedValue,\n  TransformArrayItem,\n} from './commonTypes';\nimport type { BaseAnimationBuilder } from './layoutReanimation/animationBuilder/BaseAnimationBuilder';\nimport type {\n  EntryExitAnimationFunction,\n  LayoutAnimationFunction,\n} from './layoutReanimation/animationBuilder/commonTypes';\nimport type { ReanimatedKeyframe } from './layoutReanimation/animationBuilder/Keyframe';\nimport type { SharedTransition } from './layoutReanimation/sharedTransitions';\n\ntype EntryOrExitLayoutType =\n  | BaseAnimationBuilder\n  | typeof BaseAnimationBuilder\n  | EntryExitAnimationFunction\n  | ReanimatedKeyframe;\n\n/*\n  Style type properties (properties that extends StyleProp<ViewStyle>)\n  can be defined with other property names than \"style\". For example `contentContainerStyle` in FlatList.\n  Type definition for all style type properties should act similarly, hence we\n  pick keys with 'Style' substring with the use of this utility type.\n*/\ntype PickStyleProps<Props> = Pick<\n  Props,\n  {\n    [Key in keyof Props]-?: Key extends `${string}Style` | 'style'\n      ? Key\n      : never;\n  }[keyof Props]\n>;\n\ntype AnimatedStyleProps<Props extends object> = {\n  [Key in keyof PickStyleProps<Props>]: StyleProp<AnimatedStyle<Props[Key]>>;\n};\n\n/**\n * Component props that are not specially handled by us.\n */\ntype RestProps<Props extends object> = {\n  [K in keyof Omit<Props, keyof PickStyleProps<Props> | 'style'>]:\n    | Props[K]\n    | SharedValue<Props[K]>;\n};\n\ntype LayoutProps = {\n  /**\n   * Lets you animate the layout changes when components are added to or removed from the view hierarchy.\n   *\n   * You can use the predefined layout transitions (eg. `LinearTransition`, `FadingTransition`) or create your own ones.\n   *\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions\n   */\n  layout?:\n    | BaseAnimationBuilder\n    | LayoutAnimationFunction\n    | typeof BaseAnimationBuilder;\n  /**\n   * Lets you animate an element when it's added to or removed from the view hierarchy.\n   *\n   * You can use the predefined entering animations (eg. `FadeIn`, `SlideInLeft`) or create your own ones.\n   *\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations\n   */\n  entering?: EntryOrExitLayoutType;\n  /**\n   * Lets you animate an element when it's added to or removed from the view hierarchy.\n   *\n   * You can use the predefined entering animations (eg. `FadeOut`, `SlideOutRight`) or create your own ones.\n   *\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations\n   */\n  exiting?: EntryOrExitLayoutType;\n};\n\ntype SharedTransitionProps = {\n  /**\n   * Lets you animate components between two navigation screens.\n   *\n   * Assign the same `sharedTransitionTag` to [animated components](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component) on two different navigation screens to create a shared transition.\n   *\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  sharedTransitionTag?: string;\n  /**\n   * Lets you create a custom shared transition animation.\n   *\n   * Used alongside `SharedTransition.custom()` method.\n   *\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  sharedTransitionStyle?: SharedTransition;\n};\n\ntype AnimatedPropsProp<Props extends object> = RestProps<Props> &\n  AnimatedStyleProps<Props> &\n  LayoutProps &\n  SharedTransitionProps;\n\nexport type AnimatedProps<Props extends object> = RestProps<Props> &\n  AnimatedStyleProps<Props> &\n  LayoutProps &\n  SharedTransitionProps & {\n    /**\n     * Lets you animate component props.\n     *\n     * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedProps\n     */\n    animatedProps?: Partial<AnimatedPropsProp<Props>>;\n  };\n\n// THE LAND OF THE DEPRECATED\n\n/**\n * @deprecated This type is no longer relevant.\n */\nexport type Adaptable<T> =\n  | T\n  | ReadonlyArray<T | ReadonlyArray<T>>\n  | SharedValue<T>;\n\n/**\n * @deprecated This type is no longer relevant.\n */\nexport type AdaptTransforms<T> = {\n  [P in keyof T]: Adaptable<T[P]>;\n};\n\n/**\n * @deprecated Please use {@link TransformArrayItem} type instead.\n */\nexport type TransformStyleTypes = TransformArrayItem;\n\n/**\n * @deprecated This type is no longer relevant.\n */\nexport type AnimatedStyleProp<T> =\n  | AnimatedStyle<T>\n  | RegisteredStyle<AnimatedStyle<T>>;\n\n/**\n * @deprecated Please use {@link AnimatedProps} type instead.\n */\nexport type AnimateProps<Props extends object> = AnimatedProps<Props>;\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AANA", "ignoreList": []}