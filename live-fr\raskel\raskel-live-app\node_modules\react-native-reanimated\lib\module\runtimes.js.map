{"version": 3, "names": ["isWorkletFunction", "setupCallGuard", "setupConsole", "NativeReanimatedModule", "shouldBeUseWeb", "makeShareableCloneOnUIRecursive", "makeShareableCloneRecursive", "SHOULD_BE_USE_WEB", "createWorkletRuntime", "name", "initializer", "runOnRuntime", "workletRuntime", "worklet", "__DEV__", "Error", "_WORKLET", "args", "global", "_scheduleOnRuntime", "scheduleOnRuntime"], "sources": ["runtimes.ts"], "sourcesContent": ["'use strict';\nimport { isWorkletFunction } from './commonTypes';\nimport type { WorkletFunction } from './commonTypes';\nimport { setupCallGuard, setupConsole } from './initializers';\nimport NativeReanimatedModule from './NativeReanimated';\nimport { shouldBeUseWeb } from './PlatformChecker';\nimport {\n  makeShareableCloneOnUIRecursive,\n  makeShareableCloneRecursive,\n} from './shareables';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\nexport type WorkletRuntime = {\n  __hostObjectWorkletRuntime: never;\n  readonly name: string;\n};\n\n/**\n * Lets you create a new JS runtime which can be used to run worklets possibly on different threads than JS or UI thread.\n *\n * @param name - A name used to identify the runtime which will appear in devices list in Chrome DevTools.\n * @param initializer - An optional worklet that will be run synchronously on the same thread immediately after the runtime is created.\n * @returns WorkletRuntime which is a jsi::HostObject\\<reanimated::WorkletRuntime\\> - {@link WorkletRuntime}\n * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/createWorkletRuntime\n */\n// @ts-expect-error Check `runOnUI` overload.\nexport function createWorkletRuntime(\n  name: string,\n  initializer?: () => void\n): WorkletRuntime;\n\nexport function createWorkletRuntime(\n  name: string,\n  initializer?: WorkletFunction<[], void>\n): WorkletRuntime {\n  return NativeReanimatedModule.createWorkletRuntime(\n    name,\n    makeShareableCloneRecursive(() => {\n      'worklet';\n      setupCallGuard();\n      setupConsole();\n      initializer?.();\n    })\n  );\n}\n\n// @ts-expect-error Check `runOnUI` overload.\nexport function runOnRuntime<Args extends unknown[], ReturnValue>(\n  workletRuntime: WorkletRuntime,\n  worklet: (...args: Args) => ReturnValue\n): WorkletFunction<Args, ReturnValue>;\n/**\n * Schedule a worklet to execute on the background queue.\n */\nexport function runOnRuntime<Args extends unknown[], ReturnValue>(\n  workletRuntime: WorkletRuntime,\n  worklet: WorkletFunction<Args, ReturnValue>\n): (...args: Args) => void {\n  'worklet';\n  if (__DEV__ && !SHOULD_BE_USE_WEB && !isWorkletFunction(worklet)) {\n    throw new Error(\n      '[Reanimated] The function passed to `runOnRuntime` is not a worklet.' +\n        (_WORKLET\n          ? ' Please make sure that `processNestedWorklets` option in Reanimated Babel plugin is enabled.'\n          : '')\n    );\n  }\n  if (_WORKLET) {\n    return (...args) =>\n      global._scheduleOnRuntime(\n        workletRuntime,\n        makeShareableCloneOnUIRecursive(() => {\n          'worklet';\n          worklet(...args);\n        })\n      );\n  }\n  return (...args) =>\n    NativeReanimatedModule.scheduleOnRuntime(\n      workletRuntime,\n      makeShareableCloneRecursive(() => {\n        'worklet';\n        worklet(...args);\n      })\n    );\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iBAAiB,QAAQ,eAAe;AAEjD,SAASC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC7D,OAAOC,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SACEC,+BAA+B,EAC/BC,2BAA2B,QACtB,cAAc;AAErB,MAAMC,iBAAiB,GAAGH,cAAc,CAAC,CAAC;;AAO1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASI,oBAAoBA,CAClCC,IAAY,EACZC,WAAuC,EACvB;EAChB,OAAOP,sBAAsB,CAACK,oBAAoB,CAChDC,IAAI,EACJH,2BAA2B,CAAC,MAAM;IAChC,SAAS;;IACTL,cAAc,CAAC,CAAC;IAChBC,YAAY,CAAC,CAAC;IACdQ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAG,CAAC;EACjB,CAAC,CACH,CAAC;AACH;;AAEA;;AAKA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAC1BC,cAA8B,EAC9BC,OAA2C,EAClB;EACzB,SAAS;;EACT,IAAIC,OAAO,IAAI,CAACP,iBAAiB,IAAI,CAACP,iBAAiB,CAACa,OAAO,CAAC,EAAE;IAChE,MAAM,IAAIE,KAAK,CACb,sEAAsE,IACnEC,QAAQ,GACL,8FAA8F,GAC9F,EAAE,CACV,CAAC;EACH;EACA,IAAIA,QAAQ,EAAE;IACZ,OAAO,CAAC,GAAGC,IAAI,KACbC,MAAM,CAACC,kBAAkB,CACvBP,cAAc,EACdP,+BAA+B,CAAC,MAAM;MACpC,SAAS;;MACTQ,OAAO,CAAC,GAAGI,IAAI,CAAC;IAClB,CAAC,CACH,CAAC;EACL;EACA,OAAO,CAAC,GAAGA,IAAI,KACbd,sBAAsB,CAACiB,iBAAiB,CACtCR,cAAc,EACdN,2BAA2B,CAAC,MAAM;IAChC,SAAS;;IACTO,OAAO,CAAC,GAAGI,IAAI,CAAC;EAClB,CAAC,CACH,CAAC;AACL", "ignoreList": []}