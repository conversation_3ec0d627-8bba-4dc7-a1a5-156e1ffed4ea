{"version": 3, "names": ["Extrapolation", "getVal", "type", "coef", "val", "leftEdgeOutput", "rightEdgeOutput", "x", "IDENTITY", "CLAMP", "EXTEND", "isExtrapolate", "value", "validateType", "extrapolationConfig", "extrapolateLeft", "extrapolateRight", "Error", "Object", "assign", "internalInterpolate", "narrowedInput", "leftEdgeInput", "rightEdgeInput", "progress", "interpolate", "inputRange", "outputRange", "length", "i", "clamp", "min", "max", "Math"], "sources": ["interpolation.ts"], "sourcesContent": ["'use strict';\n\n/**\n * Extrapolation type.\n *\n * @param IDENTITY - Returns the provided value as is.\n * @param CLAMP - Clamps the value to the edge of the output range.\n * @param EXTEND - Predicts the values beyond the output range.\n */\nexport enum Extrapolation {\n  IDENTITY = 'identity',\n  CLAMP = 'clamp',\n  EXTEND = 'extend',\n}\n\n/**\n * Represents the possible values for extrapolation as a string.\n */\ntype ExtrapolationAsString = 'identity' | 'clamp' | 'extend';\n\ninterface InterpolationNarrowedInput {\n  leftEdgeInput: number;\n  rightEdgeInput: number;\n  leftEdgeOutput: number;\n  rightEdgeOutput: number;\n}\n\n/**\n * Allows to specify extrapolation for left and right edge of the interpolation.\n */\nexport interface ExtrapolationConfig {\n  extrapolateLeft?: Extrapolation | string;\n  extrapolateRight?: Extrapolation | string;\n}\n\ninterface RequiredExtrapolationConfig {\n  extrapolateLeft: Extrapolation;\n  extrapolateRight: Extrapolation;\n}\n\n/**\n * Configuration options for extrapolation.\n */\nexport type ExtrapolationType =\n  | ExtrapolationConfig\n  | Extrapolation\n  | ExtrapolationAsString\n  | undefined;\n\nfunction getVal(\n  type: Extrapolation,\n  coef: number,\n  val: number,\n  leftEdgeOutput: number,\n  rightEdgeOutput: number,\n  x: number\n): number {\n  'worklet';\n\n  switch (type) {\n    case Extrapolation.IDENTITY:\n      return x;\n    case Extrapolation.CLAMP:\n      if (coef * val < coef * leftEdgeOutput) {\n        return leftEdgeOutput;\n      }\n      return rightEdgeOutput;\n    case Extrapolation.EXTEND:\n    default:\n      return val;\n  }\n}\n\nfunction isExtrapolate(value: string): value is Extrapolation {\n  'worklet';\n\n  return (\n    /* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\n    value === Extrapolation.EXTEND ||\n    value === Extrapolation.CLAMP ||\n    value === Extrapolation.IDENTITY\n    /* eslint-enable @typescript-eslint/no-unsafe-enum-comparison */\n  );\n}\n\n// validates extrapolations type\n// if type is correct, converts it to ExtrapolationConfig\nfunction validateType(type: ExtrapolationType): RequiredExtrapolationConfig {\n  'worklet';\n  // initialize extrapolationConfig with default extrapolation\n  const extrapolationConfig: RequiredExtrapolationConfig = {\n    extrapolateLeft: Extrapolation.EXTEND,\n    extrapolateRight: Extrapolation.EXTEND,\n  };\n\n  if (!type) {\n    return extrapolationConfig;\n  }\n\n  if (typeof type === 'string') {\n    if (!isExtrapolate(type)) {\n      throw new Error(\n        `[Reanimated] Unsupported value for \"interpolate\" \\nSupported values: [\"extend\", \"clamp\", \"identity\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\n Valid example:\n        interpolate(value, [inputRange], [outputRange], \"clamp\")`\n      );\n    }\n    extrapolationConfig.extrapolateLeft = type;\n    extrapolationConfig.extrapolateRight = type;\n    return extrapolationConfig;\n  }\n\n  // otherwise type is extrapolation config object\n  if (\n    (type.extrapolateLeft && !isExtrapolate(type.extrapolateLeft)) ||\n    (type.extrapolateRight && !isExtrapolate(type.extrapolateRight))\n  ) {\n    throw new Error(\n      `[Reanimated] Unsupported value for \"interpolate\" \\nSupported values: [\"extend\", \"clamp\", \"identity\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\n Valid example:\n      interpolate(value, [inputRange], [outputRange], {\n        extrapolateLeft: Extrapolation.CLAMP,\n        extrapolateRight: Extrapolation.IDENTITY\n      }})`\n    );\n  }\n\n  Object.assign(extrapolationConfig, type);\n  return extrapolationConfig;\n}\n\nfunction internalInterpolate(\n  x: number,\n  narrowedInput: InterpolationNarrowedInput,\n  extrapolationConfig: RequiredExtrapolationConfig\n) {\n  'worklet';\n  const { leftEdgeInput, rightEdgeInput, leftEdgeOutput, rightEdgeOutput } =\n    narrowedInput;\n  if (rightEdgeInput - leftEdgeInput === 0) {\n    return leftEdgeOutput;\n  }\n  const progress = (x - leftEdgeInput) / (rightEdgeInput - leftEdgeInput);\n  const val = leftEdgeOutput + progress * (rightEdgeOutput - leftEdgeOutput);\n  const coef = rightEdgeOutput >= leftEdgeOutput ? 1 : -1;\n\n  if (coef * val < coef * leftEdgeOutput) {\n    return getVal(\n      extrapolationConfig.extrapolateLeft,\n      coef,\n      val,\n      leftEdgeOutput,\n      rightEdgeOutput,\n      x\n    );\n  } else if (coef * val > coef * rightEdgeOutput) {\n    return getVal(\n      extrapolationConfig.extrapolateRight,\n      coef,\n      val,\n      leftEdgeOutput,\n      rightEdgeOutput,\n      x\n    );\n  }\n\n  return val;\n}\n\n/**\n * Lets you map a value from one range to another using linear interpolation.\n *\n * @param value - A number from the `input` range that is going to be mapped to the `output` range.\n * @param inputRange - An array of numbers specifying the input range of the interpolation.\n * @param outputRange - An array of numbers specifying the output range of the interpolation.\n * @param extrapolate - determines what happens when the `value` goes beyond the `input` range. Defaults to `Extrapolation.EXTEND` - {@link ExtrapolationType}.\n * @returns A mapped value within the output range.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/interpolate\n */\nexport function interpolate(\n  x: number,\n  inputRange: readonly number[],\n  outputRange: readonly number[],\n  type?: ExtrapolationType\n): number {\n  'worklet';\n  if (inputRange.length < 2 || outputRange.length < 2) {\n    throw new Error(\n      '[Reanimated] Interpolation input and output ranges should contain at least two values.'\n    );\n  }\n\n  const extrapolationConfig = validateType(type);\n  const length = inputRange.length;\n  const narrowedInput: InterpolationNarrowedInput = {\n    leftEdgeInput: inputRange[0],\n    rightEdgeInput: inputRange[1],\n    leftEdgeOutput: outputRange[0],\n    rightEdgeOutput: outputRange[1],\n  };\n  if (length > 2) {\n    if (x > inputRange[length - 1]) {\n      narrowedInput.leftEdgeInput = inputRange[length - 2];\n      narrowedInput.rightEdgeInput = inputRange[length - 1];\n      narrowedInput.leftEdgeOutput = outputRange[length - 2];\n      narrowedInput.rightEdgeOutput = outputRange[length - 1];\n    } else {\n      for (let i = 1; i < length; ++i) {\n        if (x <= inputRange[i]) {\n          narrowedInput.leftEdgeInput = inputRange[i - 1];\n          narrowedInput.rightEdgeInput = inputRange[i];\n          narrowedInput.leftEdgeOutput = outputRange[i - 1];\n          narrowedInput.rightEdgeOutput = outputRange[i];\n          break;\n        }\n      }\n    }\n  }\n\n  return internalInterpolate(x, narrowedInput, extrapolationConfig);\n}\n\n/**\n * Lets you limit a value within a specified range.\n *\n * @param value - A number that will be returned as long as the provided value is in range between `min` and `max`.\n * @param min - A number which will be returned when provided `value` is lower than `min`.\n * @param max - A number which will be returned when provided `value` is higher than `max`.\n * @returns A number between min and max bounds.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/utilities/clamp/\n */\nexport function clamp(value: number, min: number, max: number) {\n  'worklet';\n  return Math.min(Math.max(value, min), max);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAYA,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;;AAMzB;AACA;AACA;;AAUA;AACA;AACA;;AAWA;AACA;AACA;;AAOA,SAASC,MAAMA,CACbC,IAAmB,EACnBC,IAAY,EACZC,GAAW,EACXC,cAAsB,EACtBC,eAAuB,EACvBC,CAAS,EACD;EACR,SAAS;;EAET,QAAQL,IAAI;IACV,KAAKF,aAAa,CAACQ,QAAQ;MACzB,OAAOD,CAAC;IACV,KAAKP,aAAa,CAACS,KAAK;MACtB,IAAIN,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;QACtC,OAAOA,cAAc;MACvB;MACA,OAAOC,eAAe;IACxB,KAAKN,aAAa,CAACU,MAAM;IACzB;MACE,OAAON,GAAG;EACd;AACF;AAEA,SAASO,aAAaA,CAACC,KAAa,EAA0B;EAC5D,SAAS;;EAET,OACE;IACAA,KAAK,KAAKZ,aAAa,CAACU,MAAM,IAC9BE,KAAK,KAAKZ,aAAa,CAACS,KAAK,IAC7BG,KAAK,KAAKZ,aAAa,CAACQ;IACxB;EAAA;AAEJ;;AAEA;AACA;AACA,SAASK,YAAYA,CAACX,IAAuB,EAA+B;EAC1E,SAAS;;EACT;EACA,MAAMY,mBAAgD,GAAG;IACvDC,eAAe,EAAEf,aAAa,CAACU,MAAM;IACrCM,gBAAgB,EAAEhB,aAAa,CAACU;EAClC,CAAC;EAED,IAAI,CAACR,IAAI,EAAE;IACT,OAAOY,mBAAmB;EAC5B;EAEA,IAAI,OAAOZ,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAI,CAACS,aAAa,CAACT,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIe,KAAK,CACZ;AACT,iEACM,CAAC;IACH;IACAH,mBAAmB,CAACC,eAAe,GAAGb,IAAI;IAC1CY,mBAAmB,CAACE,gBAAgB,GAAGd,IAAI;IAC3C,OAAOY,mBAAmB;EAC5B;;EAEA;EACA,IACGZ,IAAI,CAACa,eAAe,IAAI,CAACJ,aAAa,CAACT,IAAI,CAACa,eAAe,CAAC,IAC5Db,IAAI,CAACc,gBAAgB,IAAI,CAACL,aAAa,CAACT,IAAI,CAACc,gBAAgB,CAAE,EAChE;IACA,MAAM,IAAIC,KAAK,CACZ;AACP;AACA;AACA;AACA,UACI,CAAC;EACH;EAEAC,MAAM,CAACC,MAAM,CAACL,mBAAmB,EAAEZ,IAAI,CAAC;EACxC,OAAOY,mBAAmB;AAC5B;AAEA,SAASM,mBAAmBA,CAC1Bb,CAAS,EACTc,aAAyC,EACzCP,mBAAgD,EAChD;EACA,SAAS;;EACT,MAAM;IAAEQ,aAAa;IAAEC,cAAc;IAAElB,cAAc;IAAEC;EAAgB,CAAC,GACtEe,aAAa;EACf,IAAIE,cAAc,GAAGD,aAAa,KAAK,CAAC,EAAE;IACxC,OAAOjB,cAAc;EACvB;EACA,MAAMmB,QAAQ,GAAG,CAACjB,CAAC,GAAGe,aAAa,KAAKC,cAAc,GAAGD,aAAa,CAAC;EACvE,MAAMlB,GAAG,GAAGC,cAAc,GAAGmB,QAAQ,IAAIlB,eAAe,GAAGD,cAAc,CAAC;EAC1E,MAAMF,IAAI,GAAGG,eAAe,IAAID,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;EAEvD,IAAIF,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;IACtC,OAAOJ,MAAM,CACXa,mBAAmB,CAACC,eAAe,EACnCZ,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH,CAAC,MAAM,IAAIJ,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGG,eAAe,EAAE;IAC9C,OAAOL,MAAM,CACXa,mBAAmB,CAACE,gBAAgB,EACpCb,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH;EAEA,OAAOH,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,WAAWA,CACzBlB,CAAS,EACTmB,UAA6B,EAC7BC,WAA8B,EAC9BzB,IAAwB,EAChB;EACR,SAAS;;EACT,IAAIwB,UAAU,CAACE,MAAM,GAAG,CAAC,IAAID,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;IACnD,MAAM,IAAIX,KAAK,CACb,wFACF,CAAC;EACH;EAEA,MAAMH,mBAAmB,GAAGD,YAAY,CAACX,IAAI,CAAC;EAC9C,MAAM0B,MAAM,GAAGF,UAAU,CAACE,MAAM;EAChC,MAAMP,aAAyC,GAAG;IAChDC,aAAa,EAAEI,UAAU,CAAC,CAAC,CAAC;IAC5BH,cAAc,EAAEG,UAAU,CAAC,CAAC,CAAC;IAC7BrB,cAAc,EAAEsB,WAAW,CAAC,CAAC,CAAC;IAC9BrB,eAAe,EAAEqB,WAAW,CAAC,CAAC;EAChC,CAAC;EACD,IAAIC,MAAM,GAAG,CAAC,EAAE;IACd,IAAIrB,CAAC,GAAGmB,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE;MAC9BP,aAAa,CAACC,aAAa,GAAGI,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MACpDP,aAAa,CAACE,cAAc,GAAGG,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MACrDP,aAAa,CAAChB,cAAc,GAAGsB,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;MACtDP,aAAa,CAACf,eAAe,GAAGqB,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAE,EAAEC,CAAC,EAAE;QAC/B,IAAItB,CAAC,IAAImB,UAAU,CAACG,CAAC,CAAC,EAAE;UACtBR,aAAa,CAACC,aAAa,GAAGI,UAAU,CAACG,CAAC,GAAG,CAAC,CAAC;UAC/CR,aAAa,CAACE,cAAc,GAAGG,UAAU,CAACG,CAAC,CAAC;UAC5CR,aAAa,CAAChB,cAAc,GAAGsB,WAAW,CAACE,CAAC,GAAG,CAAC,CAAC;UACjDR,aAAa,CAACf,eAAe,GAAGqB,WAAW,CAACE,CAAC,CAAC;UAC9C;QACF;MACF;IACF;EACF;EAEA,OAAOT,mBAAmB,CAACb,CAAC,EAAEc,aAAa,EAAEP,mBAAmB,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,KAAKA,CAAClB,KAAa,EAAEmB,GAAW,EAAEC,GAAW,EAAE;EAC7D,SAAS;;EACT,OAAOC,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACpB,KAAK,EAAEmB,GAAG,CAAC,EAAEC,GAAG,CAAC;AAC5C", "ignoreList": []}