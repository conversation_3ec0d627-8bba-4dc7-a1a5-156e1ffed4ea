# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: expo-modules-core
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target expo-modules-core


#############################################
# Order-only phony target for expo-modules-core

build cmake_object_order_depends_target_expo-modules-core: phony || CMakeFiles/expo-modules-core.dir

build CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.cxx | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\cmake_pch.hxx.pch.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -fpch-instantiate-templates -Xclang -emit-pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx -x c++-header
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\EventEmitter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\JSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\LazyObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\NativeModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/7c4a5ceae71b990ef2f431e9039ca398/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\7c4a5ceae71b990ef2f431e9039ca398\expo-modules-core\common\cpp\ObjectDeallocator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\7c4a5ceae71b990ef2f431e9039ca398\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\SharedObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\SharedRef.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp\TypedArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\f91fac959c0e0da1b9b9ae28e08e7938\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/Exceptions.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\Exceptions.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/ExpoModulesHostObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\ExpoModulesHostObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JNIDeallocator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIDeallocator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JNIFunctionBody.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIFunctionBody.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JNIInjector.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIInjector.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JNIUtils.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JSIContext.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JSIContext.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JSReferencesCache.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JSReferencesCache.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JSharedObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JSharedObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaCallback.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaCallback.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaReferencesCache.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaReferencesCache.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptFunction.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptFunction.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptModuleObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptModuleObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptRuntime.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptTypedArray.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptTypedArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptValue.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptValue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptWeakObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptWeakObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/MethodMetadata.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\MethodMetadata.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/RuntimeHolder.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\RuntimeHolder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/WeakRuntimeHolder.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\WeakRuntimeHolder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/types/AnyType.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\AnyType.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/types/ExpectedType.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\ExpectedType.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverter.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\FrontendConverter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverterProvider.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\FrontendConverterProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/types/JNIToJSIConverter.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\JNIToJSIConverter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSClassesDecorator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSClassesDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSConstantsDecorator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSConstantsDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSDecoratorsBridgingObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSFunctionsDecorator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSFunctionsDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSObjectDecorator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSObjectDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSPropertiesDecorator.cpp | CMakeFiles/expo-modules-core.dir/cmake_pch.hxx CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSPropertiesDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -Winvalid-pch -Xclang -include-pch -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx.pch -Xclang -include -Xclang C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a/CMakeFiles/expo-modules-core.dir/cmake_pch.hxx
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/../common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target expo-modules-core


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.so

build ../../../../build/intermediates/cxx/Debug/43k1a3c3/obj/arm64-v8a/libexpo-modules-core.so: CXX_SHARED_LIBRARY_LINKER__expo-modules-core_Debug CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o CMakeFiles/expo-modules-core.dir/7c4a5ceae71b990ef2f431e9039ca398/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o CMakeFiles/expo-modules-core.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o | C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  -landroid  C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libexpo-modules-core.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\43k1a3c3\obj\arm64-v8a\libexpo-modules-core.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\.cxx\Debug\43k1a3c3\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\.cxx\Debug\43k1a3c3\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android -BC:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\expo-modules-core\android\.cxx\Debug\43k1a3c3\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build expo-modules-core: phony ../../../../build/intermediates/cxx/Debug/43k1a3c3/obj/arm64-v8a/libexpo-modules-core.so

build libexpo-modules-core.so: phony ../../../../build/intermediates/cxx/Debug/43k1a3c3/obj/arm64-v8a/libexpo-modules-core.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a

build all: phony ../../../../build/intermediates/cxx/Debug/43k1a3c3/obj/arm64-v8a/libexpo-modules-core.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../../../../CMakeLists.txt ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
