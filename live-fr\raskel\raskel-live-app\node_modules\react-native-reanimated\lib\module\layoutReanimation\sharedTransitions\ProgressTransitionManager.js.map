{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "runOnUIImmediately", "registerEventHandler", "unregisterEventHandler", "Platform", "isJest", "shouldBeUseWeb", "IS_ANDROID", "OS", "ProgressTransitionManager", "constructor", "isRegistered", "onTransitionProgress", "onAppear", "onDisappear", "onSwipeDismiss", "addProgressAnimation", "viewTag", "progressAnimation", "global", "ProgressTransitionRegister", "registerEventHandlers", "removeProgressAnimation", "isUnmounting", "unregisterEventHandlers", "_sharedElementCount", "<PERSON><PERSON><PERSON><PERSON>", "_event<PERSON><PERSON><PERSON>", "eventPrefix", "lastProgressValue", "event", "progress", "frame", "onTransitionEnd", "onAndroidFinishTransitioning", "createProgressTransitionRegister", "progressAnimations", "Map", "snapshots", "currentTransitions", "Set", "toRemove", "skipCleaning", "isTransitionRestart", "progressTransitionManager", "size", "has", "set", "add", "delete", "onTransitionStart", "snapshot", "get", "removeViews", "clear", "_notifyAboutEnd", "maybeThrowError", "Error", "Proxy"], "sources": ["ProgressTransitionManager.ts"], "sourcesContent": ["'use strict';\nimport { runOnUIImmediately } from '../../threads';\nimport type {\n  ProgressAnimation,\n  SharedTransitionAnimationsValues,\n} from '../animationBuilder/commonTypes';\nimport { registerEventHandler, unregisterEventHandler } from '../../core';\nimport { Platform } from 'react-native';\nimport { isJest, shouldBeUseWeb } from '../../PlatformChecker';\n\ntype TransitionProgressEvent = {\n  closing: number;\n  goingForward: number;\n  eventName: string;\n  progress: number;\n  target: number;\n};\n\nconst IS_ANDROID = Platform.OS === 'android';\n\nexport class ProgressTransitionManager {\n  private _sharedElementCount = 0;\n  private _eventHandler = {\n    isRegistered: false,\n    onTransitionProgress: -1,\n    onAppear: -1,\n    onDisappear: -1,\n    onSwipeDismiss: -1,\n  };\n\n  public addProgressAnimation(\n    viewTag: number,\n    progressAnimation: ProgressAnimation\n  ) {\n    runOnUIImmediately(() => {\n      'worklet';\n      global.ProgressTransitionRegister.addProgressAnimation(\n        viewTag,\n        progressAnimation\n      );\n    })();\n\n    this.registerEventHandlers();\n  }\n\n  public removeProgressAnimation(viewTag: number, isUnmounting = true) {\n    this.unregisterEventHandlers();\n    runOnUIImmediately(() => {\n      'worklet';\n      global.ProgressTransitionRegister.removeProgressAnimation(\n        viewTag,\n        isUnmounting\n      );\n    })();\n  }\n\n  private registerEventHandlers() {\n    this._sharedElementCount++;\n    const eventHandler = this._eventHandler;\n    if (!eventHandler.isRegistered) {\n      eventHandler.isRegistered = true;\n      const eventPrefix = IS_ANDROID ? 'on' : 'top';\n      let lastProgressValue = -1;\n      eventHandler.onTransitionProgress = registerEventHandler(\n        (event: TransitionProgressEvent) => {\n          'worklet';\n          const progress = event.progress;\n          if (progress === lastProgressValue) {\n            // During screen transition, handler receives two events with the same progress\n            // value for both screens, but for modals, there is only one event. To optimize\n            // performance and avoid unnecessary worklet calls, let's skip the second event.\n            return;\n          }\n          lastProgressValue = progress;\n          global.ProgressTransitionRegister.frame(progress);\n        },\n        eventPrefix + 'TransitionProgress'\n      );\n      eventHandler.onAppear = registerEventHandler(() => {\n        'worklet';\n        global.ProgressTransitionRegister.onTransitionEnd();\n      }, eventPrefix + 'Appear');\n\n      if (IS_ANDROID) {\n        // onFinishTransitioning event is available only on Android and\n        // is used to handle closing modals\n        eventHandler.onDisappear = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onAndroidFinishTransitioning();\n        }, 'onFinishTransitioning');\n      } else if (Platform.OS === 'ios') {\n        // topDisappear event is required to handle closing modals on iOS\n        eventHandler.onDisappear = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onTransitionEnd(true);\n        }, 'topDisappear');\n        eventHandler.onSwipeDismiss = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onTransitionEnd();\n        }, 'topGestureCancel');\n      }\n    }\n  }\n\n  private unregisterEventHandlers(): void {\n    this._sharedElementCount--;\n    if (this._sharedElementCount === 0) {\n      const eventHandler = this._eventHandler;\n      eventHandler.isRegistered = false;\n      if (eventHandler.onTransitionProgress !== -1) {\n        unregisterEventHandler(eventHandler.onTransitionProgress);\n        eventHandler.onTransitionProgress = -1;\n      }\n      if (eventHandler.onAppear !== -1) {\n        unregisterEventHandler(eventHandler.onAppear);\n        eventHandler.onAppear = -1;\n      }\n      if (eventHandler.onDisappear !== -1) {\n        unregisterEventHandler(eventHandler.onDisappear);\n        eventHandler.onDisappear = -1;\n      }\n      if (eventHandler.onSwipeDismiss !== -1) {\n        unregisterEventHandler(eventHandler.onSwipeDismiss);\n        eventHandler.onSwipeDismiss = -1;\n      }\n    }\n  }\n}\n\nfunction createProgressTransitionRegister() {\n  'worklet';\n  const progressAnimations = new Map<number, ProgressAnimation>();\n  const snapshots = new Map<\n    number,\n    Partial<SharedTransitionAnimationsValues>\n  >();\n  const currentTransitions = new Set<number>();\n  const toRemove = new Set<number>();\n\n  let skipCleaning = false;\n  let isTransitionRestart = false;\n\n  const progressTransitionManager = {\n    addProgressAnimation: (\n      viewTag: number,\n      progressAnimation: ProgressAnimation\n    ) => {\n      if (currentTransitions.size > 0 && !progressAnimations.has(viewTag)) {\n        // there is no need to prevent cleaning on android\n        isTransitionRestart = !IS_ANDROID;\n      }\n      progressAnimations.set(viewTag, progressAnimation);\n    },\n    removeProgressAnimation: (viewTag: number, isUnmounting: boolean) => {\n      if (currentTransitions.size > 0) {\n        // there is no need to prevent cleaning on android\n        isTransitionRestart = !IS_ANDROID;\n      }\n      if (isUnmounting) {\n        // Remove the animation config after the transition is finished\n        toRemove.add(viewTag);\n      } else {\n        // if the animation is removed, without ever being started, it can be removed immediately\n        progressAnimations.delete(viewTag);\n      }\n    },\n    onTransitionStart: (\n      viewTag: number,\n      snapshot: Partial<SharedTransitionAnimationsValues>\n    ) => {\n      skipCleaning = isTransitionRestart;\n      snapshots.set(viewTag, snapshot);\n      currentTransitions.add(viewTag);\n      // set initial style for re-parented components\n      progressTransitionManager.frame(0);\n    },\n    frame: (progress: number) => {\n      for (const viewTag of currentTransitions) {\n        const progressAnimation = progressAnimations.get(viewTag);\n        if (!progressAnimation) {\n          continue;\n        }\n        const snapshot = snapshots.get(\n          viewTag\n        )! as SharedTransitionAnimationsValues;\n        progressAnimation(viewTag, snapshot, progress);\n      }\n    },\n    onAndroidFinishTransitioning: () => {\n      if (toRemove.size > 0) {\n        // it should be ran only on modal closing\n        progressTransitionManager.onTransitionEnd();\n      }\n    },\n    onTransitionEnd: (removeViews = false) => {\n      if (currentTransitions.size === 0) {\n        toRemove.clear();\n        return;\n      }\n      if (skipCleaning) {\n        skipCleaning = false;\n        isTransitionRestart = false;\n        return;\n      }\n      for (const viewTag of currentTransitions) {\n        global._notifyAboutEnd(viewTag, removeViews);\n      }\n      currentTransitions.clear();\n      if (isTransitionRestart) {\n        // on transition restart, progressAnimations should be saved\n        // because they potentially can be used in the next transition\n        return;\n      }\n      snapshots.clear();\n      if (toRemove.size > 0) {\n        for (const viewTag of toRemove) {\n          progressAnimations.delete(viewTag);\n          global._notifyAboutEnd(viewTag, removeViews);\n        }\n        toRemove.clear();\n      }\n    },\n  };\n  return progressTransitionManager;\n}\n\nif (shouldBeUseWeb()) {\n  const maybeThrowError = () => {\n    // Jest attempts to access a property of this object to check if it is a Jest mock\n    // so we can't throw an error in the getter.\n    if (!isJest()) {\n      throw new Error(\n        '[Reanimated] `ProgressTransitionRegister` is not available on non-native platform.'\n      );\n    }\n  };\n  global.ProgressTransitionRegister = new Proxy(\n    {} as ProgressTransitionRegister,\n    {\n      get: maybeThrowError,\n      set: () => {\n        maybeThrowError();\n        return false;\n      },\n    }\n  );\n} else {\n  runOnUIImmediately(() => {\n    'worklet';\n    global.ProgressTransitionRegister = createProgressTransitionRegister();\n  })();\n}\n\nexport type ProgressTransitionRegister = ReturnType<\n  typeof createProgressTransitionRegister\n>;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,kBAAkB,QAAQ,eAAe;AAKlD,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,YAAY;AACzE,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAU9D,MAAMC,UAAU,GAAGH,QAAQ,CAACI,EAAE,KAAK,SAAS;AAE5C,OAAO,MAAMC,yBAAyB,CAAC;EAAAC,YAAA;IAAA9B,eAAA,8BACP,CAAC;IAAAA,eAAA,wBACP;MACtB+B,YAAY,EAAE,KAAK;MACnBC,oBAAoB,EAAE,CAAC,CAAC;MACxBC,QAAQ,EAAE,CAAC,CAAC;MACZC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC;IACnB,CAAC;EAAA;EAEMC,oBAAoBA,CACzBC,OAAe,EACfC,iBAAoC,EACpC;IACAjB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTkB,MAAM,CAACC,0BAA0B,CAACJ,oBAAoB,CACpDC,OAAO,EACPC,iBACF,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IAEJ,IAAI,CAACG,qBAAqB,CAAC,CAAC;EAC9B;EAEOC,uBAAuBA,CAACL,OAAe,EAAEM,YAAY,GAAG,IAAI,EAAE;IACnE,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9BvB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTkB,MAAM,CAACC,0BAA0B,CAACE,uBAAuB,CACvDL,OAAO,EACPM,YACF,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;EACN;EAEQF,qBAAqBA,CAAA,EAAG;IAC9B,IAAI,CAACI,mBAAmB,EAAE;IAC1B,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,IAAI,CAACD,YAAY,CAACf,YAAY,EAAE;MAC9Be,YAAY,CAACf,YAAY,GAAG,IAAI;MAChC,MAAMiB,WAAW,GAAGrB,UAAU,GAAG,IAAI,GAAG,KAAK;MAC7C,IAAIsB,iBAAiB,GAAG,CAAC,CAAC;MAC1BH,YAAY,CAACd,oBAAoB,GAAGV,oBAAoB,CACrD4B,KAA8B,IAAK;QAClC,SAAS;;QACT,MAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;QAC/B,IAAIA,QAAQ,KAAKF,iBAAiB,EAAE;UAClC;UACA;UACA;UACA;QACF;QACAA,iBAAiB,GAAGE,QAAQ;QAC5BZ,MAAM,CAACC,0BAA0B,CAACY,KAAK,CAACD,QAAQ,CAAC;MACnD,CAAC,EACDH,WAAW,GAAG,oBAChB,CAAC;MACDF,YAAY,CAACb,QAAQ,GAAGX,oBAAoB,CAAC,MAAM;QACjD,SAAS;;QACTiB,MAAM,CAACC,0BAA0B,CAACa,eAAe,CAAC,CAAC;MACrD,CAAC,EAAEL,WAAW,GAAG,QAAQ,CAAC;MAE1B,IAAIrB,UAAU,EAAE;QACd;QACA;QACAmB,YAAY,CAACZ,WAAW,GAAGZ,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACc,4BAA4B,CAAC,CAAC;QAClE,CAAC,EAAE,uBAAuB,CAAC;MAC7B,CAAC,MAAM,IAAI9B,QAAQ,CAACI,EAAE,KAAK,KAAK,EAAE;QAChC;QACAkB,YAAY,CAACZ,WAAW,GAAGZ,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACa,eAAe,CAAC,IAAI,CAAC;QACzD,CAAC,EAAE,cAAc,CAAC;QAClBP,YAAY,CAACX,cAAc,GAAGb,oBAAoB,CAAC,MAAM;UACvD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACa,eAAe,CAAC,CAAC;QACrD,CAAC,EAAE,kBAAkB,CAAC;MACxB;IACF;EACF;EAEQT,uBAAuBA,CAAA,EAAS;IACtC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,IAAI,CAACA,mBAAmB,KAAK,CAAC,EAAE;MAClC,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;MACvCD,YAAY,CAACf,YAAY,GAAG,KAAK;MACjC,IAAIe,YAAY,CAACd,oBAAoB,KAAK,CAAC,CAAC,EAAE;QAC5CT,sBAAsB,CAACuB,YAAY,CAACd,oBAAoB,CAAC;QACzDc,YAAY,CAACd,oBAAoB,GAAG,CAAC,CAAC;MACxC;MACA,IAAIc,YAAY,CAACb,QAAQ,KAAK,CAAC,CAAC,EAAE;QAChCV,sBAAsB,CAACuB,YAAY,CAACb,QAAQ,CAAC;QAC7Ca,YAAY,CAACb,QAAQ,GAAG,CAAC,CAAC;MAC5B;MACA,IAAIa,YAAY,CAACZ,WAAW,KAAK,CAAC,CAAC,EAAE;QACnCX,sBAAsB,CAACuB,YAAY,CAACZ,WAAW,CAAC;QAChDY,YAAY,CAACZ,WAAW,GAAG,CAAC,CAAC;MAC/B;MACA,IAAIY,YAAY,CAACX,cAAc,KAAK,CAAC,CAAC,EAAE;QACtCZ,sBAAsB,CAACuB,YAAY,CAACX,cAAc,CAAC;QACnDW,YAAY,CAACX,cAAc,GAAG,CAAC,CAAC;MAClC;IACF;EACF;AACF;AAEA,SAASoB,gCAAgCA,CAAA,EAAG;EAC1C,SAAS;;EACT,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAA4B,CAAC;EAC/D,MAAMC,SAAS,GAAG,IAAID,GAAG,CAGvB,CAAC;EACH,MAAME,kBAAkB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAC5C,MAAMC,QAAQ,GAAG,IAAID,GAAG,CAAS,CAAC;EAElC,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIC,mBAAmB,GAAG,KAAK;EAE/B,MAAMC,yBAAyB,GAAG;IAChC5B,oBAAoB,EAAEA,CACpBC,OAAe,EACfC,iBAAoC,KACjC;MACH,IAAIqB,kBAAkB,CAACM,IAAI,GAAG,CAAC,IAAI,CAACT,kBAAkB,CAACU,GAAG,CAAC7B,OAAO,CAAC,EAAE;QACnE;QACA0B,mBAAmB,GAAG,CAACpC,UAAU;MACnC;MACA6B,kBAAkB,CAACW,GAAG,CAAC9B,OAAO,EAAEC,iBAAiB,CAAC;IACpD,CAAC;IACDI,uBAAuB,EAAEA,CAACL,OAAe,EAAEM,YAAqB,KAAK;MACnE,IAAIgB,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAC/B;QACAF,mBAAmB,GAAG,CAACpC,UAAU;MACnC;MACA,IAAIgB,YAAY,EAAE;QAChB;QACAkB,QAAQ,CAACO,GAAG,CAAC/B,OAAO,CAAC;MACvB,CAAC,MAAM;QACL;QACAmB,kBAAkB,CAACa,MAAM,CAAChC,OAAO,CAAC;MACpC;IACF,CAAC;IACDiC,iBAAiB,EAAEA,CACjBjC,OAAe,EACfkC,QAAmD,KAChD;MACHT,YAAY,GAAGC,mBAAmB;MAClCL,SAAS,CAACS,GAAG,CAAC9B,OAAO,EAAEkC,QAAQ,CAAC;MAChCZ,kBAAkB,CAACS,GAAG,CAAC/B,OAAO,CAAC;MAC/B;MACA2B,yBAAyB,CAACZ,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC;IACDA,KAAK,EAAGD,QAAgB,IAAK;MAC3B,KAAK,MAAMd,OAAO,IAAIsB,kBAAkB,EAAE;QACxC,MAAMrB,iBAAiB,GAAGkB,kBAAkB,CAACgB,GAAG,CAACnC,OAAO,CAAC;QACzD,IAAI,CAACC,iBAAiB,EAAE;UACtB;QACF;QACA,MAAMiC,QAAQ,GAAGb,SAAS,CAACc,GAAG,CAC5BnC,OACF,CAAsC;QACtCC,iBAAiB,CAACD,OAAO,EAAEkC,QAAQ,EAAEpB,QAAQ,CAAC;MAChD;IACF,CAAC;IACDG,4BAA4B,EAAEA,CAAA,KAAM;MAClC,IAAIO,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB;QACAD,yBAAyB,CAACX,eAAe,CAAC,CAAC;MAC7C;IACF,CAAC;IACDA,eAAe,EAAEA,CAACoB,WAAW,GAAG,KAAK,KAAK;MACxC,IAAId,kBAAkB,CAACM,IAAI,KAAK,CAAC,EAAE;QACjCJ,QAAQ,CAACa,KAAK,CAAC,CAAC;QAChB;MACF;MACA,IAAIZ,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QACpBC,mBAAmB,GAAG,KAAK;QAC3B;MACF;MACA,KAAK,MAAM1B,OAAO,IAAIsB,kBAAkB,EAAE;QACxCpB,MAAM,CAACoC,eAAe,CAACtC,OAAO,EAAEoC,WAAW,CAAC;MAC9C;MACAd,kBAAkB,CAACe,KAAK,CAAC,CAAC;MAC1B,IAAIX,mBAAmB,EAAE;QACvB;QACA;QACA;MACF;MACAL,SAAS,CAACgB,KAAK,CAAC,CAAC;MACjB,IAAIb,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB,KAAK,MAAM5B,OAAO,IAAIwB,QAAQ,EAAE;UAC9BL,kBAAkB,CAACa,MAAM,CAAChC,OAAO,CAAC;UAClCE,MAAM,CAACoC,eAAe,CAACtC,OAAO,EAAEoC,WAAW,CAAC;QAC9C;QACAZ,QAAQ,CAACa,KAAK,CAAC,CAAC;MAClB;IACF;EACF,CAAC;EACD,OAAOV,yBAAyB;AAClC;AAEA,IAAItC,cAAc,CAAC,CAAC,EAAE;EACpB,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAACnD,MAAM,CAAC,CAAC,EAAE;MACb,MAAM,IAAIoD,KAAK,CACb,oFACF,CAAC;IACH;EACF,CAAC;EACDtC,MAAM,CAACC,0BAA0B,GAAG,IAAIsC,KAAK,CAC3C,CAAC,CAAC,EACF;IACEN,GAAG,EAAEI,eAAe;IACpBT,GAAG,EAAEA,CAAA,KAAM;MACTS,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CACF,CAAC;AACH,CAAC,MAAM;EACLvD,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTkB,MAAM,CAACC,0BAA0B,GAAGe,gCAAgC,CAAC,CAAC;EACxE,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}