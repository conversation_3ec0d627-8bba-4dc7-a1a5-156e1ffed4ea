{"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "React", "forwardRef", "ScrollView", "createAnimatedComponent", "useAnimatedRef", "useScrollViewOffset", "AnimatedScrollViewComponent", "AnimatedScrollView", "props", "ref", "scrollViewOffset", "restProps", "animatedRef", "scrollEventThrottle", "createElement"], "sources": ["ScrollView.tsx"], "sourcesContent": ["'use strict';\nimport type { ForwardedRef } from 'react';\nimport React, { forwardRef } from 'react';\nimport type { ScrollViewProps } from 'react-native';\nimport { ScrollView } from 'react-native';\nimport { createAnimatedComponent } from '../createAnimatedComponent';\nimport type { SharedValue } from '../commonTypes';\nimport type { AnimatedRef } from '../hook';\nimport { useAnimatedRef, useScrollViewOffset } from '../hook';\nimport type { AnimatedProps } from '../helperTypes';\n\nexport interface AnimatedScrollViewProps\n  extends AnimatedProps<ScrollViewProps> {\n  scrollViewOffset?: SharedValue<number>;\n}\n\n// Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n// but not things like NativeMethods, etc. we need to add them manually by extending the type.\ninterface AnimatedScrollViewComplement extends ScrollView {\n  getNode(): ScrollView;\n}\n\nconst AnimatedScrollViewComponent = createAnimatedComponent(ScrollView);\n\nexport const AnimatedScrollView = forwardRef(\n  (props: AnimatedScrollViewProps, ref: ForwardedRef<AnimatedScrollView>) => {\n    const { scrollViewOffset, ...restProps } = props;\n    const animatedRef = (\n      ref === null\n        ? // eslint-disable-next-line react-hooks/rules-of-hooks\n          useAnimatedRef<ScrollView>()\n        : ref\n    ) as AnimatedRef<AnimatedScrollView>;\n\n    if (scrollViewOffset) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useScrollViewOffset(animatedRef, scrollViewOffset);\n    }\n\n    // Set default scrollEventThrottle, because user expects\n    // to have continuous scroll events.\n    // We set it to 1 so we have peace until\n    // there are 960 fps screens.\n    if (!('scrollEventThrottle' in restProps)) {\n      restProps.scrollEventThrottle = 1;\n    }\n\n    return <AnimatedScrollViewComponent ref={animatedRef} {...restProps} />;\n  }\n);\n\nexport type AnimatedScrollView = AnimatedScrollViewComplement &\n  typeof AnimatedScrollViewComponent;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAEb,OAAOQ,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAEzC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,uBAAuB,QAAQ,4BAA4B;AAGpE,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,SAAS;;AAQ7D;AACA;;AAKA,MAAMC,2BAA2B,GAAGH,uBAAuB,CAACD,UAAU,CAAC;AAEvE,OAAO,MAAMK,kBAAkB,gBAAGN,UAAU,CAC1C,CAACO,KAA8B,EAAEC,GAAqC,KAAK;EACzE,MAAM;IAAEC,gBAAgB;IAAE,GAAGC;EAAU,CAAC,GAAGH,KAAK;EAChD,MAAMI,WAAW,GACfH,GAAG,KAAK,IAAI;EACR;EACAL,cAAc,CAAa,CAAC,GAC5BK,GAC8B;EAEpC,IAAIC,gBAAgB,EAAE;IACpB;IACAL,mBAAmB,CAACO,WAAW,EAAEF,gBAAgB,CAAC;EACpD;;EAEA;EACA;EACA;EACA;EACA,IAAI,EAAE,qBAAqB,IAAIC,SAAS,CAAC,EAAE;IACzCA,SAAS,CAACE,mBAAmB,GAAG,CAAC;EACnC;EAEA,oBAAOb,KAAA,CAAAc,aAAA,CAACR,2BAA2B,EAAApB,QAAA;IAACuB,GAAG,EAAEG;EAAY,GAAKD,SAAS,CAAG,CAAC;AACzE,CACF,CAAC", "ignoreList": []}