{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "ComplexAnimationBuilder", "RollInLeft", "constructor", "args", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "values", "animations", "transform", "translateX", "rotate", "windowWidth", "createInstance", "RollInRight", "RollOutLeft", "RollOutRight"], "sources": ["Roll.ts"], "sourcesContent": ["'use strict';\nimport type { BaseAnimationBuilder } from '../animationBuilder';\nimport { ComplexAnimationBuilder } from '../animationBuilder';\nimport type {\n  EntryExitAnimationsValues,\n  EntryExitAnimationFunction,\n  IEntryExitAnimationBuilder,\n} from '../animationBuilder/commonTypes';\n\n/**\n * Roll from left animation. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#roll\n */\nexport class RollInLeft\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'RollInLeft';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new RollInLeft() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          transform: [\n            { translateX: delayFunction(delay, animation(0, config)) },\n            { rotate: delayFunction(delay, animation('0deg', config)) },\n          ],\n        },\n        initialValues: {\n          transform: [\n            { translateX: -values.windowWidth },\n            { rotate: '-180deg' },\n          ],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Roll from right animation. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#roll\n */\nexport class RollInRight\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'RollInRight';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new RollInRight() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          transform: [\n            { translateX: delayFunction(delay, animation(0, config)) },\n            { rotate: delayFunction(delay, animation('0deg', config)) },\n          ],\n        },\n        initialValues: {\n          transform: [{ translateX: values.windowWidth }, { rotate: '180deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Roll to left animation. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#roll\n */\nexport class RollOutLeft\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'RollOutLeft';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new RollOutLeft() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          transform: [\n            {\n              translateX: delayFunction(\n                delay,\n                animation(-values.windowWidth, config)\n              ),\n            },\n            { rotate: delayFunction(delay, animation('-180deg', config)) },\n          ],\n        },\n        initialValues: {\n          transform: [{ translateX: 0 }, { rotate: '0deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Roll to right animation. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations#roll\n */\nexport class RollOutRight\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'RollOutRight';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new RollOutRight() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return (values: EntryExitAnimationsValues) => {\n      'worklet';\n      return {\n        animations: {\n          transform: [\n            {\n              translateX: delayFunction(\n                delay,\n                animation(values.windowWidth, config)\n              ),\n            },\n            { rotate: delayFunction(delay, animation('180deg', config)) },\n          ],\n        },\n        initialValues: {\n          transform: [{ translateX: 0 }, { rotate: '0deg' }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAEb,SAASW,uBAAuB,QAAQ,qBAAqB;AAO7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SACbD,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cAAEC,UAAU,EAAEb,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC,EAC1D;cAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;YAAE,CAAC;UAE/D,CAAC;UACDM,aAAa,EAAE;YACbG,SAAS,EAAE,CACT;cAAEC,UAAU,EAAE,CAACH,MAAM,CAACK;YAAY,CAAC,EACnC;cAAED,MAAM,EAAE;YAAU,CAAC,CACtB;YACD,GAAGL;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAhCD,OAAOS,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAInB,UAAU,CAAC,CAAC;EACzB;AA6BF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAtB,eAAA,CAzCasB,UAAU,gBAID,YAAY;AA4ClC,OAAO,MAAMoB,WAAW,SACdrB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cAAEC,UAAU,EAAEb,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC,EAC1D;cAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;YAAE,CAAC;UAE/D,CAAC;UACDM,aAAa,EAAE;YACbG,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAEH,MAAM,CAACK;YAAY,CAAC,EAAE;cAAED,MAAM,EAAE;YAAS,CAAC,CAAC;YACrE,GAAGL;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA7BD,OAAOS,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIC,WAAW,CAAC,CAAC;EAC1B;AA0BF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA1C,eAAA,CAtCa0C,WAAW,gBAIF,aAAa;AAyCnC,OAAO,MAAMC,WAAW,SACdtB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cACEC,UAAU,EAAEb,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAACQ,MAAM,CAACK,WAAW,EAAEZ,MAAM,CACvC;YACF,CAAC,EACD;cAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,SAAS,EAAEC,MAAM,CAAC;YAAE,CAAC;UAElE,CAAC;UACDM,aAAa,EAAE;YACbG,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAC,CAAC;YAClD,GAAGL;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAlCD,OAAOS,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIE,WAAW,CAAC,CAAC;EAC1B;AA+BF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA3C,eAAA,CA3Ca2C,WAAW,gBAIF,aAAa;AA8CnC,OAAO,MAAMC,YAAY,SACfvB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAQC,MAAiC,IAAK;QAC5C,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CACT;cACEC,UAAU,EAAEb,aAAa,CACvBK,KAAK,EACLH,SAAS,CAACQ,MAAM,CAACK,WAAW,EAAEZ,MAAM,CACtC;YACF,CAAC,EACD;cAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;YAAE,CAAC;UAEjE,CAAC;UACDM,aAAa,EAAE;YACbG,SAAS,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAE,CAAC,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAC,CAAC;YAClD,GAAGL;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAlCD,OAAOS,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIG,YAAY,CAAC,CAAC;EAC3B;AA+BF;AAAC5C,eAAA,CAzCY4C,YAAY,gBAIH,cAAc", "ignoreList": []}