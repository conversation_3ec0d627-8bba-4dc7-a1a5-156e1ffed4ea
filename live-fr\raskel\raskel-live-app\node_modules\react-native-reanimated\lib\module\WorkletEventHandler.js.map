{"version": 3, "names": ["_classPrivateFieldInitSpec", "obj", "privateMap", "value", "_checkPrivateRedeclaration", "set", "privateCollection", "has", "TypeError", "_defineProperty", "key", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "String", "Number", "_classPrivateFieldGet", "s", "a", "get", "_assert<PERSON>lassBrand", "_classPrivateFieldSet", "n", "arguments", "length", "registerEventHandler", "unregisterEventHandler", "shouldBeUseWeb", "SHOULD_BE_USE_WEB", "jsListener", "eventName", "handler", "evt", "nativeEvent", "_viewTags", "WeakMap", "_registrations", "WorkletEventHandlerNative", "constructor", "worklet", "eventNames", "Set", "Map", "updateEventHandler", "newWorklet", "newEvents", "for<PERSON>ach", "registrationIDs", "id", "Array", "from", "tag", "newRegistrations", "map", "registerForEvents", "viewTag", "fallbackEventName", "add", "newRegistration", "unregisterFromEvents", "_classPrivateFieldGet2", "delete", "WorkletEventHandlerWeb", "listeners", "setupWebListeners", "_viewTag", "_fallbackEventName", "WorkletEventHandler"], "sources": ["WorkletEventHandler.ts"], "sourcesContent": ["'use strict';\nimport type { NativeSyntheticEvent } from 'react-native';\nimport { registerEventHandler, unregisterEventHandler } from './core';\nimport type {\n  EventPayload,\n  ReanimatedEvent,\n  IWorkletEventHandler,\n} from './hook/commonTypes';\nimport { shouldBeUseWeb } from './PlatformChecker';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\ntype JSEvent<Event extends object> = NativeSyntheticEvent<EventPayload<Event>>;\n\n// In JS implementation (e.g. for web) we don't use Reanimated's\n// event emitter, therefore we have to handle here\n// the event that came from React Native and convert it.\nfunction jsListener<Event extends object>(\n  eventName: string,\n  handler: (event: ReanimatedEvent<Event>) => void\n) {\n  return (evt: JSEvent<Event>) => {\n    handler({ ...evt.nativeEvent, eventName } as ReanimatedEvent<Event>);\n  };\n}\n\nclass WorkletEventHandlerNative<Event extends object>\n  implements IWorkletEventHandler<Event>\n{\n  eventNames: string[];\n  worklet: (event: ReanimatedEvent<Event>) => void;\n  #viewTags: Set<number>;\n  #registrations: Map<number, number[]>; // keys are viewTags, values are arrays of registration ID's for each viewTag\n  constructor(\n    worklet: (event: ReanimatedEvent<Event>) => void,\n    eventNames: string[]\n  ) {\n    this.worklet = worklet;\n    this.eventNames = eventNames;\n    this.#viewTags = new Set<number>();\n    this.#registrations = new Map<number, number[]>();\n  }\n\n  updateEventHandler(\n    newWorklet: (event: ReanimatedEvent<Event>) => void,\n    newEvents: string[]\n  ): void {\n    // Update worklet and event names\n    this.worklet = newWorklet;\n    this.eventNames = newEvents;\n\n    // Detach all events\n    this.#registrations.forEach((registrationIDs) => {\n      registrationIDs.forEach((id) => unregisterEventHandler(id));\n      // No need to remove registrationIDs from map, since it gets overwritten when attaching\n    });\n\n    // Attach new events with new worklet\n    Array.from(this.#viewTags).forEach((tag) => {\n      const newRegistrations = this.eventNames.map((eventName) =>\n        registerEventHandler(this.worklet, eventName, tag)\n      );\n      this.#registrations.set(tag, newRegistrations);\n    });\n  }\n\n  registerForEvents(viewTag: number, fallbackEventName?: string): void {\n    this.#viewTags.add(viewTag);\n\n    const newRegistrations = this.eventNames.map((eventName) =>\n      registerEventHandler(this.worklet, eventName, viewTag)\n    );\n    this.#registrations.set(viewTag, newRegistrations);\n\n    if (this.eventNames.length === 0 && fallbackEventName) {\n      const newRegistration = registerEventHandler(\n        this.worklet,\n        fallbackEventName,\n        viewTag\n      );\n      this.#registrations.set(viewTag, [newRegistration]);\n    }\n  }\n\n  unregisterFromEvents(viewTag: number): void {\n    this.#viewTags.delete(viewTag);\n    this.#registrations.get(viewTag)?.forEach((id) => {\n      unregisterEventHandler(id);\n    });\n    this.#registrations.delete(viewTag);\n  }\n}\n\nclass WorkletEventHandlerWeb<Event extends object>\n  implements IWorkletEventHandler<Event>\n{\n  eventNames: string[];\n  listeners:\n    | Record<string, (event: ReanimatedEvent<ReanimatedEvent<Event>>) => void>\n    | Record<string, (event: JSEvent<Event>) => void>;\n\n  worklet: (event: ReanimatedEvent<Event>) => void;\n\n  constructor(\n    worklet: (event: ReanimatedEvent<Event>) => void,\n    eventNames: string[] = []\n  ) {\n    this.worklet = worklet;\n    this.eventNames = eventNames;\n    this.listeners = {};\n    this.setupWebListeners();\n  }\n\n  setupWebListeners() {\n    this.listeners = {};\n    this.eventNames.forEach((eventName) => {\n      this.listeners[eventName] = jsListener(eventName, this.worklet);\n    });\n  }\n\n  updateEventHandler(\n    newWorklet: (event: ReanimatedEvent<Event>) => void,\n    newEvents: string[]\n  ): void {\n    // Update worklet and event names\n    this.worklet = newWorklet;\n    this.eventNames = newEvents;\n    this.setupWebListeners();\n  }\n\n  registerForEvents(_viewTag: number, _fallbackEventName?: string): void {\n    // noop\n  }\n\n  unregisterFromEvents(_viewTag: number): void {\n    // noop\n  }\n}\n\nexport const WorkletEventHandler = SHOULD_BE_USE_WEB\n  ? WorkletEventHandlerWeb\n  : WorkletEventHandlerNative;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,2BAAAC,GAAA,EAAAC,UAAA,EAAAC,KAAA,IAAAC,0BAAA,CAAAH,GAAA,EAAAC,UAAA,GAAAA,UAAA,CAAAG,GAAA,CAAAJ,GAAA,EAAAE,KAAA;AAAA,SAAAC,2BAAAH,GAAA,EAAAK,iBAAA,QAAAA,iBAAA,CAAAC,GAAA,CAAAN,GAAA,eAAAO,SAAA;AAAA,SAAAC,gBAAAR,GAAA,EAAAS,GAAA,EAAAP,KAAA,IAAAO,GAAA,GAAAC,cAAA,CAAAD,GAAA,OAAAA,GAAA,IAAAT,GAAA,IAAAW,MAAA,CAAAC,cAAA,CAAAZ,GAAA,EAAAS,GAAA,IAAAP,KAAA,EAAAA,KAAA,EAAAW,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAf,GAAA,CAAAS,GAAA,IAAAP,KAAA,WAAAF,GAAA;AAAA,SAAAU,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAV,SAAA,yEAAAY,CAAA,GAAAK,MAAA,GAAAC,MAAA,EAAAT,CAAA;AAAA,SAAAU,sBAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,CAAAE,GAAA,CAAAC,iBAAA,CAAAH,CAAA,EAAAC,CAAA;AAAA,SAAAG,sBAAAJ,CAAA,EAAAC,CAAA,EAAAT,CAAA,WAAAQ,CAAA,CAAAvB,GAAA,CAAA0B,iBAAA,CAAAH,CAAA,EAAAC,CAAA,GAAAT,CAAA,GAAAA,CAAA;AAAA,SAAAW,kBAAAV,CAAA,EAAAJ,CAAA,EAAAgB,CAAA,6BAAAZ,CAAA,GAAAA,CAAA,KAAAJ,CAAA,GAAAI,CAAA,CAAAd,GAAA,CAAAU,CAAA,UAAAiB,SAAA,CAAAC,MAAA,OAAAlB,CAAA,GAAAgB,CAAA,YAAAzB,SAAA;AAEb,SAAS4B,oBAAoB,EAAEC,sBAAsB,QAAQ,QAAQ;AAMrE,SAASC,cAAc,QAAQ,mBAAmB;AAElD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,CAAC;AAI1C;AACA;AACA;AACA,SAASE,UAAUA,CACjBC,SAAiB,EACjBC,OAAgD,EAChD;EACA,OAAQC,GAAmB,IAAK;IAC9BD,OAAO,CAAC;MAAE,GAAGC,GAAG,CAACC,WAAW;MAAEH;IAAU,CAA2B,CAAC;EACtE,CAAC;AACH;AAAC,IAAAI,SAAA,oBAAAC,OAAA;AAAA,IAAAC,cAAA,oBAAAD,OAAA;AAED,MAAME,yBAAyB,CAE/B;EAIyC;EACvCC,WAAWA,CACTC,OAAgD,EAChDC,UAAoB,EACpB;IAAA1C,eAAA;IAAAA,eAAA;IAAAT,0BAAA,OAAA6C,SAAA;IAAA7C,0BAAA,OAAA+C,cAAA;IACA,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5BnB,qBAAA,CAAAa,SAAA,MAAI,EAAa,IAAIO,GAAG,CAAS,CAAC;IAClCpB,qBAAA,CAAAe,cAAA,MAAI,EAAkB,IAAIM,GAAG,CAAmB,CAAC;EACnD;EAEAC,kBAAkBA,CAChBC,UAAmD,EACnDC,SAAmB,EACb;IACN;IACA,IAAI,CAACN,OAAO,GAAGK,UAAU;IACzB,IAAI,CAACJ,UAAU,GAAGK,SAAS;;IAE3B;IACA7B,qBAAA,CAAAoB,cAAA,MAAI,EAAgBU,OAAO,CAAEC,eAAe,IAAK;MAC/CA,eAAe,CAACD,OAAO,CAAEE,EAAE,IAAKtB,sBAAsB,CAACsB,EAAE,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC;;IAEF;IACAC,KAAK,CAACC,IAAI,CAAAlC,qBAAA,CAAAkB,SAAA,EAAC,IAAI,CAAU,CAAC,CAACY,OAAO,CAAEK,GAAG,IAAK;MAC1C,MAAMC,gBAAgB,GAAG,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAEvB,SAAS,IACrDL,oBAAoB,CAAC,IAAI,CAACc,OAAO,EAAET,SAAS,EAAEqB,GAAG,CACnD,CAAC;MACDnC,qBAAA,CAAAoB,cAAA,MAAI,EAAgB1C,GAAG,CAACyD,GAAG,EAAEC,gBAAgB,CAAC;IAChD,CAAC,CAAC;EACJ;EAEAE,iBAAiBA,CAACC,OAAe,EAAEC,iBAA0B,EAAQ;IACnExC,qBAAA,CAAAkB,SAAA,MAAI,EAAWuB,GAAG,CAACF,OAAO,CAAC;IAE3B,MAAMH,gBAAgB,GAAG,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAEvB,SAAS,IACrDL,oBAAoB,CAAC,IAAI,CAACc,OAAO,EAAET,SAAS,EAAEyB,OAAO,CACvD,CAAC;IACDvC,qBAAA,CAAAoB,cAAA,MAAI,EAAgB1C,GAAG,CAAC6D,OAAO,EAAEH,gBAAgB,CAAC;IAElD,IAAI,IAAI,CAACZ,UAAU,CAAChB,MAAM,KAAK,CAAC,IAAIgC,iBAAiB,EAAE;MACrD,MAAME,eAAe,GAAGjC,oBAAoB,CAC1C,IAAI,CAACc,OAAO,EACZiB,iBAAiB,EACjBD,OACF,CAAC;MACDvC,qBAAA,CAAAoB,cAAA,MAAI,EAAgB1C,GAAG,CAAC6D,OAAO,EAAE,CAACG,eAAe,CAAC,CAAC;IACrD;EACF;EAEAC,oBAAoBA,CAACJ,OAAe,EAAQ;IAAA,IAAAK,sBAAA;IAC1C5C,qBAAA,CAAAkB,SAAA,MAAI,EAAW2B,MAAM,CAACN,OAAO,CAAC;IAC9B,CAAAK,sBAAA,GAAA5C,qBAAA,CAAAoB,cAAA,MAAI,EAAgBjB,GAAG,CAACoC,OAAO,CAAC,cAAAK,sBAAA,eAAhCA,sBAAA,CAAkCd,OAAO,CAAEE,EAAE,IAAK;MAChDtB,sBAAsB,CAACsB,EAAE,CAAC;IAC5B,CAAC,CAAC;IACFhC,qBAAA,CAAAoB,cAAA,MAAI,EAAgByB,MAAM,CAACN,OAAO,CAAC;EACrC;AACF;AAEA,MAAMO,sBAAsB,CAE5B;EAQExB,WAAWA,CACTC,OAAgD,EAChDC,UAAoB,GAAG,EAAE,EACzB;IAAA1C,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACA,IAAI,CAACyC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACuB,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEAA,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACD,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACvB,UAAU,CAACM,OAAO,CAAEhB,SAAS,IAAK;MACrC,IAAI,CAACiC,SAAS,CAACjC,SAAS,CAAC,GAAGD,UAAU,CAACC,SAAS,EAAE,IAAI,CAACS,OAAO,CAAC;IACjE,CAAC,CAAC;EACJ;EAEAI,kBAAkBA,CAChBC,UAAmD,EACnDC,SAAmB,EACb;IACN;IACA,IAAI,CAACN,OAAO,GAAGK,UAAU;IACzB,IAAI,CAACJ,UAAU,GAAGK,SAAS;IAC3B,IAAI,CAACmB,iBAAiB,CAAC,CAAC;EAC1B;EAEAV,iBAAiBA,CAACW,QAAgB,EAAEC,kBAA2B,EAAQ;IACrE;EAAA;EAGFP,oBAAoBA,CAACM,QAAgB,EAAQ;IAC3C;EAAA;AAEJ;AAEA,OAAO,MAAME,mBAAmB,GAAGvC,iBAAiB,GAChDkC,sBAAsB,GACtBzB,yBAAyB", "ignoreList": []}