{"version": 3, "names": ["View", "createAnimatedComponent", "AnimatedView"], "sources": ["View.ts"], "sourcesContent": ["'use strict';\nimport { View } from 'react-native';\nimport { createAnimatedComponent } from '../createAnimatedComponent';\n\n// Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n// but not things like NativeMethods, etc. we need to add them manually by extending the type.\ninterface AnimatedViewComplement extends View {\n  getNode(): View;\n}\n\nexport const AnimatedView = createAnimatedComponent(View);\n\nexport type AnimatedView = typeof AnimatedView & AnimatedViewComplement;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,uBAAuB,QAAQ,4BAA4B;;AAEpE;AACA;;AAKA,OAAO,MAAMC,YAAY,GAAGD,uBAAuB,CAACD,IAAI,CAAC", "ignoreList": []}