{"version": 3, "names": ["useEffect", "useMemo", "useRef", "initializeSensor", "registerSensor", "unregisterSensor", "SensorType", "IOSReferenceFrame", "InterfaceOrientation", "callMicrotasks", "eulerToQuaternion", "pitch", "roll", "yaw", "c1", "Math", "cos", "s1", "sin", "c2", "s2", "c3", "s3", "adjustRotationToInterfaceOrientation", "data", "interfaceOrientation", "ROTATION_90", "PI", "ROTATION_270", "ROTATION_180", "q", "qx", "qy", "qz", "qw", "adjustVectorToInterfaceOrientation", "x", "y", "useAnimatedSensor", "sensorType", "userConfig", "_userConfigRef$curren", "_userConfigRef$curren2", "_userConfigRef$curren3", "userConfigRef", "hasConfigChanged", "current", "adjustToInterfaceOrientation", "interval", "iosReferenceFrame", "config", "Auto", "ref", "sensor", "unregister", "isAvailable", "sensorData", "id", "ROTATION", "value"], "sources": ["useAnimatedSensor.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useMemo, useRef } from 'react';\nimport { initializeSensor, registerSensor, unregisterSensor } from '../core';\nimport type {\n  SensorConfig,\n  AnimatedSensor,\n  Value3D,\n  ValueRotation,\n} from '../commonTypes';\nimport {\n  SensorType,\n  IOSReferenceFrame,\n  InterfaceOrientation,\n} from '../commonTypes';\nimport { callMicrotasks } from '../threads';\n\n// euler angles are in order ZXY, z = yaw, x = pitch, y = roll\n// https://github.com/mrdoob/three.js/blob/dev/src/math/Quaternion.js#L237\nfunction eulerToQuaternion(pitch: number, roll: number, yaw: number) {\n  'worklet';\n  const c1 = Math.cos(pitch / 2);\n  const s1 = Math.sin(pitch / 2);\n  const c2 = Math.cos(roll / 2);\n  const s2 = Math.sin(roll / 2);\n  const c3 = Math.cos(yaw / 2);\n  const s3 = Math.sin(yaw / 2);\n\n  return [\n    s1 * c2 * c3 - c1 * s2 * s3,\n    c1 * s2 * c3 + s1 * c2 * s3,\n    c1 * c2 * s3 + s1 * s2 * c3,\n    c1 * c2 * c3 - s1 * s2 * s3,\n  ];\n}\n\nfunction adjustRotationToInterfaceOrientation(data: ValueRotation) {\n  'worklet';\n  const { interfaceOrientation, pitch, roll, yaw } = data;\n  if (interfaceOrientation === InterfaceOrientation.ROTATION_90) {\n    data.pitch = roll;\n    data.roll = -pitch;\n    data.yaw = yaw - Math.PI / 2;\n  } else if (interfaceOrientation === InterfaceOrientation.ROTATION_270) {\n    data.pitch = -roll;\n    data.roll = pitch;\n    data.yaw = yaw + Math.PI / 2;\n  } else if (interfaceOrientation === InterfaceOrientation.ROTATION_180) {\n    data.pitch *= -1;\n    data.roll *= -1;\n    data.yaw *= -1;\n  }\n\n  const q = eulerToQuaternion(data.pitch, data.roll, data.yaw);\n  data.qx = q[0];\n  data.qy = q[1];\n  data.qz = q[2];\n  data.qw = q[3];\n  return data;\n}\n\nfunction adjustVectorToInterfaceOrientation(data: Value3D) {\n  'worklet';\n  const { interfaceOrientation, x, y } = data;\n  if (interfaceOrientation === InterfaceOrientation.ROTATION_90) {\n    data.x = -y;\n    data.y = x;\n  } else if (interfaceOrientation === InterfaceOrientation.ROTATION_270) {\n    data.x = y;\n    data.y = -x;\n  } else if (interfaceOrientation === InterfaceOrientation.ROTATION_180) {\n    data.x *= -1;\n    data.y *= -1;\n  }\n  return data;\n}\n\n/**\n * Lets you create animations based on data from the device's sensors.\n *\n * @param sensorType - Type of the sensor to use. Configured with {@link SensorType} enum.\n * @param config - The sensor configuration - {@link SensorConfig}.\n * @returns An object containing the sensor measurements [shared value](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value) and a function to unregister the sensor\n * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useAnimatedSensor\n */\nexport function useAnimatedSensor(\n  sensorType: SensorType.ROTATION,\n  userConfig?: Partial<SensorConfig>\n): AnimatedSensor<ValueRotation>;\nexport function useAnimatedSensor(\n  sensorType: Exclude<SensorType, SensorType.ROTATION>,\n  userConfig?: Partial<SensorConfig>\n): AnimatedSensor<Value3D>;\nexport function useAnimatedSensor(\n  sensorType: SensorType,\n  userConfig?: Partial<SensorConfig>\n): AnimatedSensor<ValueRotation> | AnimatedSensor<Value3D> {\n  const userConfigRef = useRef(userConfig);\n\n  const hasConfigChanged =\n    userConfigRef.current?.adjustToInterfaceOrientation !==\n      userConfig?.adjustToInterfaceOrientation ||\n    userConfigRef.current?.interval !== userConfig?.interval ||\n    userConfigRef.current?.iosReferenceFrame !== userConfig?.iosReferenceFrame;\n\n  if (hasConfigChanged) {\n    userConfigRef.current = { ...userConfig };\n  }\n\n  const config: SensorConfig = useMemo(\n    () => ({\n      interval: 'auto',\n      adjustToInterfaceOrientation: true,\n      iosReferenceFrame: IOSReferenceFrame.Auto,\n      ...userConfigRef.current,\n    }),\n    [userConfigRef.current]\n  );\n\n  const ref = useRef<AnimatedSensor<Value3D | ValueRotation>>({\n    sensor: initializeSensor(sensorType, config),\n    unregister: () => {\n      // NOOP\n    },\n    isAvailable: false,\n    config,\n  });\n\n  useEffect(() => {\n    ref.current = {\n      sensor: initializeSensor(sensorType, config),\n      unregister: () => {\n        // NOOP\n      },\n      isAvailable: false,\n      config,\n    };\n\n    const sensorData = ref.current.sensor;\n    const adjustToInterfaceOrientation =\n      ref.current.config.adjustToInterfaceOrientation;\n\n    const id = registerSensor(sensorType, config, (data) => {\n      'worklet';\n      if (adjustToInterfaceOrientation) {\n        if (sensorType === SensorType.ROTATION) {\n          data = adjustRotationToInterfaceOrientation(data as ValueRotation);\n        } else {\n          data = adjustVectorToInterfaceOrientation(data as Value3D);\n        }\n      }\n      sensorData.value = data;\n      callMicrotasks();\n    });\n\n    if (id !== -1) {\n      // if sensor is available\n      ref.current.unregister = () => unregisterSensor(id);\n      ref.current.isAvailable = true;\n    } else {\n      // if sensor is unavailable\n      ref.current.unregister = () => {\n        // NOOP\n      };\n      ref.current.isAvailable = false;\n    }\n\n    return () => {\n      ref.current.unregister();\n    };\n  }, [sensorType, config]);\n\n  return ref.current as AnimatedSensor<ValueRotation> | AnimatedSensor<Value3D>;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAClD,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,SAAS;AAO5E,SACEC,UAAU,EACVC,iBAAiB,EACjBC,oBAAoB,QACf,gBAAgB;AACvB,SAASC,cAAc,QAAQ,YAAY;;AAE3C;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAa,EAAEC,IAAY,EAAEC,GAAW,EAAE;EACnE,SAAS;;EACT,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,GAAG,CAAC,CAAC;EAC9B,MAAMM,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACP,KAAK,GAAG,CAAC,CAAC;EAC9B,MAAMQ,EAAE,GAAGJ,IAAI,CAACC,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC;EAC7B,MAAMQ,EAAE,GAAGL,IAAI,CAACG,GAAG,CAACN,IAAI,GAAG,CAAC,CAAC;EAC7B,MAAMS,EAAE,GAAGN,IAAI,CAACC,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC;EAC5B,MAAMS,EAAE,GAAGP,IAAI,CAACG,GAAG,CAACL,GAAG,GAAG,CAAC,CAAC;EAE5B,OAAO,CACLI,EAAE,GAAGE,EAAE,GAAGE,EAAE,GAAGP,EAAE,GAAGM,EAAE,GAAGE,EAAE,EAC3BR,EAAE,GAAGM,EAAE,GAAGC,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGG,EAAE,EAC3BR,EAAE,GAAGK,EAAE,GAAGG,EAAE,GAAGL,EAAE,GAAGG,EAAE,GAAGC,EAAE,EAC3BP,EAAE,GAAGK,EAAE,GAAGE,EAAE,GAAGJ,EAAE,GAAGG,EAAE,GAAGE,EAAE,CAC5B;AACH;AAEA,SAASC,oCAAoCA,CAACC,IAAmB,EAAE;EACjE,SAAS;;EACT,MAAM;IAAEC,oBAAoB;IAAEd,KAAK;IAAEC,IAAI;IAAEC;EAAI,CAAC,GAAGW,IAAI;EACvD,IAAIC,oBAAoB,KAAKjB,oBAAoB,CAACkB,WAAW,EAAE;IAC7DF,IAAI,CAACb,KAAK,GAAGC,IAAI;IACjBY,IAAI,CAACZ,IAAI,GAAG,CAACD,KAAK;IAClBa,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACY,EAAE,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIF,oBAAoB,KAAKjB,oBAAoB,CAACoB,YAAY,EAAE;IACrEJ,IAAI,CAACb,KAAK,GAAG,CAACC,IAAI;IAClBY,IAAI,CAACZ,IAAI,GAAGD,KAAK;IACjBa,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACY,EAAE,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIF,oBAAoB,KAAKjB,oBAAoB,CAACqB,YAAY,EAAE;IACrEL,IAAI,CAACb,KAAK,IAAI,CAAC,CAAC;IAChBa,IAAI,CAACZ,IAAI,IAAI,CAAC,CAAC;IACfY,IAAI,CAACX,GAAG,IAAI,CAAC,CAAC;EAChB;EAEA,MAAMiB,CAAC,GAAGpB,iBAAiB,CAACc,IAAI,CAACb,KAAK,EAAEa,IAAI,CAACZ,IAAI,EAAEY,IAAI,CAACX,GAAG,CAAC;EAC5DW,IAAI,CAACO,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;EACdN,IAAI,CAACQ,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;EACdN,IAAI,CAACS,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;EACdN,IAAI,CAACU,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACd,OAAON,IAAI;AACb;AAEA,SAASW,kCAAkCA,CAACX,IAAa,EAAE;EACzD,SAAS;;EACT,MAAM;IAAEC,oBAAoB;IAAEW,CAAC;IAAEC;EAAE,CAAC,GAAGb,IAAI;EAC3C,IAAIC,oBAAoB,KAAKjB,oBAAoB,CAACkB,WAAW,EAAE;IAC7DF,IAAI,CAACY,CAAC,GAAG,CAACC,CAAC;IACXb,IAAI,CAACa,CAAC,GAAGD,CAAC;EACZ,CAAC,MAAM,IAAIX,oBAAoB,KAAKjB,oBAAoB,CAACoB,YAAY,EAAE;IACrEJ,IAAI,CAACY,CAAC,GAAGC,CAAC;IACVb,IAAI,CAACa,CAAC,GAAG,CAACD,CAAC;EACb,CAAC,MAAM,IAAIX,oBAAoB,KAAKjB,oBAAoB,CAACqB,YAAY,EAAE;IACrEL,IAAI,CAACY,CAAC,IAAI,CAAC,CAAC;IACZZ,IAAI,CAACa,CAAC,IAAI,CAAC,CAAC;EACd;EACA,OAAOb,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,OAAO,SAASc,iBAAiBA,CAC/BC,UAAsB,EACtBC,UAAkC,EACuB;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzD,MAAMC,aAAa,GAAG1C,MAAM,CAACsC,UAAU,CAAC;EAExC,MAAMK,gBAAgB,GACpB,EAAAJ,qBAAA,GAAAG,aAAa,CAACE,OAAO,cAAAL,qBAAA,uBAArBA,qBAAA,CAAuBM,4BAA4B,OACjDP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEO,4BAA4B,KAC1C,EAAAL,sBAAA,GAAAE,aAAa,CAACE,OAAO,cAAAJ,sBAAA,uBAArBA,sBAAA,CAAuBM,QAAQ,OAAKR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,QAAQ,KACxD,EAAAL,sBAAA,GAAAC,aAAa,CAACE,OAAO,cAAAH,sBAAA,uBAArBA,sBAAA,CAAuBM,iBAAiB,OAAKT,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,iBAAiB;EAE5E,IAAIJ,gBAAgB,EAAE;IACpBD,aAAa,CAACE,OAAO,GAAG;MAAE,GAAGN;IAAW,CAAC;EAC3C;EAEA,MAAMU,MAAoB,GAAGjD,OAAO,CAClC,OAAO;IACL+C,QAAQ,EAAE,MAAM;IAChBD,4BAA4B,EAAE,IAAI;IAClCE,iBAAiB,EAAE1C,iBAAiB,CAAC4C,IAAI;IACzC,GAAGP,aAAa,CAACE;EACnB,CAAC,CAAC,EACF,CAACF,aAAa,CAACE,OAAO,CACxB,CAAC;EAED,MAAMM,GAAG,GAAGlD,MAAM,CAA0C;IAC1DmD,MAAM,EAAElD,gBAAgB,CAACoC,UAAU,EAAEW,MAAM,CAAC;IAC5CI,UAAU,EAAEA,CAAA,KAAM;MAChB;IAAA,CACD;IACDC,WAAW,EAAE,KAAK;IAClBL;EACF,CAAC,CAAC;EAEFlD,SAAS,CAAC,MAAM;IACdoD,GAAG,CAACN,OAAO,GAAG;MACZO,MAAM,EAAElD,gBAAgB,CAACoC,UAAU,EAAEW,MAAM,CAAC;MAC5CI,UAAU,EAAEA,CAAA,KAAM;QAChB;MAAA,CACD;MACDC,WAAW,EAAE,KAAK;MAClBL;IACF,CAAC;IAED,MAAMM,UAAU,GAAGJ,GAAG,CAACN,OAAO,CAACO,MAAM;IACrC,MAAMN,4BAA4B,GAChCK,GAAG,CAACN,OAAO,CAACI,MAAM,CAACH,4BAA4B;IAEjD,MAAMU,EAAE,GAAGrD,cAAc,CAACmC,UAAU,EAAEW,MAAM,EAAG1B,IAAI,IAAK;MACtD,SAAS;;MACT,IAAIuB,4BAA4B,EAAE;QAChC,IAAIR,UAAU,KAAKjC,UAAU,CAACoD,QAAQ,EAAE;UACtClC,IAAI,GAAGD,oCAAoC,CAACC,IAAqB,CAAC;QACpE,CAAC,MAAM;UACLA,IAAI,GAAGW,kCAAkC,CAACX,IAAe,CAAC;QAC5D;MACF;MACAgC,UAAU,CAACG,KAAK,GAAGnC,IAAI;MACvBf,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAIgD,EAAE,KAAK,CAAC,CAAC,EAAE;MACb;MACAL,GAAG,CAACN,OAAO,CAACQ,UAAU,GAAG,MAAMjD,gBAAgB,CAACoD,EAAE,CAAC;MACnDL,GAAG,CAACN,OAAO,CAACS,WAAW,GAAG,IAAI;IAChC,CAAC,MAAM;MACL;MACAH,GAAG,CAACN,OAAO,CAACQ,UAAU,GAAG,MAAM;QAC7B;MAAA,CACD;MACDF,GAAG,CAACN,OAAO,CAACS,WAAW,GAAG,KAAK;IACjC;IAEA,OAAO,MAAM;MACXH,GAAG,CAACN,OAAO,CAACQ,UAAU,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACf,UAAU,EAAEW,MAAM,CAAC,CAAC;EAExB,OAAOE,GAAG,CAACN,OAAO;AACpB", "ignoreList": []}