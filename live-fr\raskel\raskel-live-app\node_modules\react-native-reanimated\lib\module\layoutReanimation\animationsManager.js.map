{"version": 3, "names": ["withStyleAnimation", "makeMutableUI", "LayoutAnimationType", "runOnUIImmediately", "TAG_OFFSET", "startObservingProgress", "tag", "sharedValue", "animationType", "isSharedTransition", "SHARED_ELEMENT_TRANSITION", "addListener", "global", "_notifyAboutProgress", "value", "stopObservingProgress", "<PERSON><PERSON><PERSON><PERSON>", "removeListener", "_notifyAboutEnd", "createLayoutAnimationManager", "currentAnimationForTag", "Map", "mutableValuesForTag", "start", "type", "yoga<PERSON><PERSON><PERSON>", "config", "SHARED_ELEMENT_TRANSITION_PROGRESS", "ProgressTransitionRegister", "onTransitionStart", "style", "currentAnimation", "animations", "previousAnimation", "get", "set", "undefined", "initialValues", "_value", "animation", "callback", "finished", "delete", "shouldRemoveView", "EXITING", "stop", "LayoutAnimationsManager"], "sources": ["animationsManager.ts"], "sourcesContent": ["'use strict';\nimport { withStyleAnimation } from '../animation/styleAnimation';\nimport type { SharedValue } from '../commonTypes';\nimport { makeMutableUI } from '../mutables';\nimport { LayoutAnimationType } from './animationBuilder';\nimport { runOnUIImmediately } from '../threads';\nimport type {\n  SharedTransitionAnimationsValues,\n  LayoutAnimation,\n  LayoutAnimationStartFunction,\n} from './animationBuilder/commonTypes';\n\nconst TAG_OFFSET = 1e9;\n\nfunction startObservingProgress(\n  tag: number,\n  sharedValue: SharedValue<Record<string, unknown>>,\n  animationType: LayoutAnimationType\n): void {\n  'worklet';\n  const isSharedTransition =\n    animationType === LayoutAnimationType.SHARED_ELEMENT_TRANSITION;\n  sharedValue.addListener(tag + TAG_OFFSET, () => {\n    global._notifyAboutProgress(tag, sharedValue.value, isSharedTransition);\n  });\n}\n\nfunction stopObservingProgress(\n  tag: number,\n  sharedValue: SharedValue<number>,\n  removeView = false\n): void {\n  'worklet';\n  sharedValue.removeListener(tag + TAG_OFFSET);\n  global._notifyAboutEnd(tag, removeView);\n}\n\nfunction createLayoutAnimationManager(): {\n  start: LayoutAnimationStartFunction;\n  stop: (tag: number) => void;\n} {\n  'worklet';\n  const currentAnimationForTag = new Map();\n  const mutableValuesForTag = new Map();\n\n  return {\n    start(\n      tag: number,\n      type: LayoutAnimationType,\n      /**\n       * createLayoutAnimationManager creates an animation manager for both Layout animations and Shared Transition Elements animations.\n       */\n      yogaValues: Partial<SharedTransitionAnimationsValues>,\n      config: (\n        arg: Partial<SharedTransitionAnimationsValues>\n      ) => LayoutAnimation\n    ) {\n      if (type === LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS) {\n        global.ProgressTransitionRegister.onTransitionStart(tag, yogaValues);\n        return;\n      }\n\n      const style = config(yogaValues);\n      let currentAnimation = style.animations;\n\n      // When layout animation is requested, but a previous one is still running, we merge\n      // new layout animation targets into the ongoing animation\n      const previousAnimation = currentAnimationForTag.get(tag);\n      if (previousAnimation) {\n        currentAnimation = { ...previousAnimation, ...style.animations };\n      }\n      currentAnimationForTag.set(tag, currentAnimation);\n\n      let value = mutableValuesForTag.get(tag);\n      if (value === undefined) {\n        value = makeMutableUI(style.initialValues);\n        mutableValuesForTag.set(tag, value);\n      } else {\n        stopObservingProgress(tag, value);\n        value._value = style.initialValues;\n      }\n\n      // @ts-ignore The line below started failing because I added types to the method – don't have time to fix it right now\n      const animation = withStyleAnimation(currentAnimation);\n\n      animation.callback = (finished?: boolean) => {\n        if (finished) {\n          currentAnimationForTag.delete(tag);\n          mutableValuesForTag.delete(tag);\n          const shouldRemoveView = type === LayoutAnimationType.EXITING;\n          stopObservingProgress(tag, value, shouldRemoveView);\n        }\n        style.callback &&\n          style.callback(finished === undefined ? false : finished);\n      };\n\n      startObservingProgress(tag, value, type);\n      value.value = animation;\n    },\n    stop(tag: number) {\n      const value = mutableValuesForTag.get(tag);\n      if (!value) {\n        return;\n      }\n      stopObservingProgress(tag, value);\n    },\n  };\n}\n\nrunOnUIImmediately(() => {\n  'worklet';\n  global.LayoutAnimationsManager = createLayoutAnimationManager();\n})();\n\nexport type LayoutAnimationsManager = ReturnType<\n  typeof createLayoutAnimationManager\n>;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,kBAAkB,QAAQ,6BAA6B;AAEhE,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,kBAAkB,QAAQ,YAAY;AAO/C,MAAMC,UAAU,GAAG,GAAG;AAEtB,SAASC,sBAAsBA,CAC7BC,GAAW,EACXC,WAAiD,EACjDC,aAAkC,EAC5B;EACN,SAAS;;EACT,MAAMC,kBAAkB,GACtBD,aAAa,KAAKN,mBAAmB,CAACQ,yBAAyB;EACjEH,WAAW,CAACI,WAAW,CAACL,GAAG,GAAGF,UAAU,EAAE,MAAM;IAC9CQ,MAAM,CAACC,oBAAoB,CAACP,GAAG,EAAEC,WAAW,CAACO,KAAK,EAAEL,kBAAkB,CAAC;EACzE,CAAC,CAAC;AACJ;AAEA,SAASM,qBAAqBA,CAC5BT,GAAW,EACXC,WAAgC,EAChCS,UAAU,GAAG,KAAK,EACZ;EACN,SAAS;;EACTT,WAAW,CAACU,cAAc,CAACX,GAAG,GAAGF,UAAU,CAAC;EAC5CQ,MAAM,CAACM,eAAe,CAACZ,GAAG,EAAEU,UAAU,CAAC;AACzC;AAEA,SAASG,4BAA4BA,CAAA,EAGnC;EACA,SAAS;;EACT,MAAMC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxC,MAAMC,mBAAmB,GAAG,IAAID,GAAG,CAAC,CAAC;EAErC,OAAO;IACLE,KAAKA,CACHjB,GAAW,EACXkB,IAAyB;IACzB;AACN;AACA;IACMC,UAAqD,EACrDC,MAEoB,EACpB;MACA,IAAIF,IAAI,KAAKtB,mBAAmB,CAACyB,kCAAkC,EAAE;QACnEf,MAAM,CAACgB,0BAA0B,CAACC,iBAAiB,CAACvB,GAAG,EAAEmB,UAAU,CAAC;QACpE;MACF;MAEA,MAAMK,KAAK,GAAGJ,MAAM,CAACD,UAAU,CAAC;MAChC,IAAIM,gBAAgB,GAAGD,KAAK,CAACE,UAAU;;MAEvC;MACA;MACA,MAAMC,iBAAiB,GAAGb,sBAAsB,CAACc,GAAG,CAAC5B,GAAG,CAAC;MACzD,IAAI2B,iBAAiB,EAAE;QACrBF,gBAAgB,GAAG;UAAE,GAAGE,iBAAiB;UAAE,GAAGH,KAAK,CAACE;QAAW,CAAC;MAClE;MACAZ,sBAAsB,CAACe,GAAG,CAAC7B,GAAG,EAAEyB,gBAAgB,CAAC;MAEjD,IAAIjB,KAAK,GAAGQ,mBAAmB,CAACY,GAAG,CAAC5B,GAAG,CAAC;MACxC,IAAIQ,KAAK,KAAKsB,SAAS,EAAE;QACvBtB,KAAK,GAAGb,aAAa,CAAC6B,KAAK,CAACO,aAAa,CAAC;QAC1Cf,mBAAmB,CAACa,GAAG,CAAC7B,GAAG,EAAEQ,KAAK,CAAC;MACrC,CAAC,MAAM;QACLC,qBAAqB,CAACT,GAAG,EAAEQ,KAAK,CAAC;QACjCA,KAAK,CAACwB,MAAM,GAAGR,KAAK,CAACO,aAAa;MACpC;;MAEA;MACA,MAAME,SAAS,GAAGvC,kBAAkB,CAAC+B,gBAAgB,CAAC;MAEtDQ,SAAS,CAACC,QAAQ,GAAIC,QAAkB,IAAK;QAC3C,IAAIA,QAAQ,EAAE;UACZrB,sBAAsB,CAACsB,MAAM,CAACpC,GAAG,CAAC;UAClCgB,mBAAmB,CAACoB,MAAM,CAACpC,GAAG,CAAC;UAC/B,MAAMqC,gBAAgB,GAAGnB,IAAI,KAAKtB,mBAAmB,CAAC0C,OAAO;UAC7D7B,qBAAqB,CAACT,GAAG,EAAEQ,KAAK,EAAE6B,gBAAgB,CAAC;QACrD;QACAb,KAAK,CAACU,QAAQ,IACZV,KAAK,CAACU,QAAQ,CAACC,QAAQ,KAAKL,SAAS,GAAG,KAAK,GAAGK,QAAQ,CAAC;MAC7D,CAAC;MAEDpC,sBAAsB,CAACC,GAAG,EAAEQ,KAAK,EAAEU,IAAI,CAAC;MACxCV,KAAK,CAACA,KAAK,GAAGyB,SAAS;IACzB,CAAC;IACDM,IAAIA,CAACvC,GAAW,EAAE;MAChB,MAAMQ,KAAK,GAAGQ,mBAAmB,CAACY,GAAG,CAAC5B,GAAG,CAAC;MAC1C,IAAI,CAACQ,KAAK,EAAE;QACV;MACF;MACAC,qBAAqB,CAACT,GAAG,EAAEQ,KAAK,CAAC;IACnC;EACF,CAAC;AACH;AAEAX,kBAAkB,CAAC,MAAM;EACvB,SAAS;;EACTS,MAAM,CAACkC,uBAAuB,GAAG3B,4BAA4B,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}