{"name": "msrfi-live-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:dev-client": "expo start --dev-client", "android": "expo run:android", "android:dev": "expo run:android --device", "ios": "expo run:ios", "ios:dev": "expo run:ios --device", "web": "expo start --web", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@supabase/supabase-js": "^2.50.4", "expo": "^54.0.0", "expo-av": "^15.1.7", "expo-build-properties": "^0.14.8", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-document-picker": "^13.1.6", "expo-image-picker": "^16.1.4", "expo-location": "^18.1.6", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "react": "19.0.0", "react-native": "0.79.5", "react-native-agora": "^4.5.3", "react-native-reanimated": "^3.16.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-flow-strip-types": "^7.25.9", "@babel/preset-typescript": "^7.27.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "babel-preset-expo": "^12.0.9", "jest": "^29.7.0", "jest-expo": "^53.0.0", "react-native": "0.79.5", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}