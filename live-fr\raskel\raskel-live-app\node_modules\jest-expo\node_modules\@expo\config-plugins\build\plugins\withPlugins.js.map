{"version": 3, "file": "withPlugins.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_withStaticPlugin", "e", "__esModule", "default", "with<PERSON><PERSON><PERSON>", "config", "plugins", "assert", "Array", "isArray", "reduce", "prev", "plugin", "withStaticPlugin", "exports"], "sources": ["../../src/plugins/withPlugins.ts"], "sourcesContent": ["import assert from 'assert';\n\nimport { withStaticPlugin } from './withStaticPlugin';\nimport { ConfigPlugin, StaticPlugin } from '../Plugin.types';\n\n/**\n * Resolves a list of plugins.\n *\n * @param config exported config\n * @param plugins list of config plugins to apply to the exported config\n */\nexport const withPlugins: ConfigPlugin<(StaticPlugin | ConfigPlugin | string)[]> = (\n  config,\n  plugins\n) => {\n  assert(\n    Array.isArray(plugins),\n    'withPlugins expected a valid array of plugins or plugin module paths'\n  );\n  return plugins.reduce((prev, plugin) => withStaticPlugin(prev, { plugin }), config);\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,kBAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,iBAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsD,SAAAC,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAGtD;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,WAAmE,GAAGA,CACjFC,MAAM,EACNC,OAAO,KACJ;EACH,IAAAC,iBAAM,EACJC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EACtB,sEACF,CAAC;EACD,OAAOA,OAAO,CAACI,MAAM,CAAC,CAACC,IAAI,EAAEC,MAAM,KAAK,IAAAC,oCAAgB,EAACF,IAAI,EAAE;IAAEC;EAAO,CAAC,CAAC,EAAEP,MAAM,CAAC;AACrF,CAAC;AAACS,OAAA,CAAAV,WAAA,GAAAA,WAAA", "ignoreList": []}