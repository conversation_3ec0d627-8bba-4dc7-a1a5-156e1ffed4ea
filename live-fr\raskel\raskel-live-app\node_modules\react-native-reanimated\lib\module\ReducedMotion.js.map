{"version": 3, "names": ["isWeb", "isWindowAvailable", "makeMutable", "isReducedMotionEnabledInSystem", "window", "matchMedia", "matches", "global", "_REANIMATED_IS_REDUCED_MOTION", "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM", "ReducedMotionManager", "jsValue", "uiValue", "setEnabled", "value"], "sources": ["ReducedMotion.ts"], "sourcesContent": ["'use strict';\nimport { isWeb, isWindowAvailable } from './PlatformChecker';\nimport { makeMutable } from './mutables';\n\ntype localGlobal = typeof global & Record<string, unknown>;\n\nexport function isReducedMotionEnabledInSystem() {\n  return isWeb()\n    ? isWindowAvailable()\n      ? // @ts-ignore Fallback if `window` is undefined.\n        window.matchMedia('(prefers-reduced-motion: reduce)').matches\n      : false\n    : !!(global as localGlobal)._REANIMATED_IS_REDUCED_MOTION;\n}\n\nconst IS_REDUCED_MOTION_ENABLED_IN_SYSTEM = isReducedMotionEnabledInSystem();\n\nexport const ReducedMotionManager = {\n  jsValue: IS_REDUCED_MOTION_ENABLED_IN_SYSTEM,\n  uiValue: makeMutable(IS_REDUCED_MOTION_ENABLED_IN_SYSTEM),\n  setEnabled(value: boolean) {\n    ReducedMotionManager.jsValue = value;\n    ReducedMotionManager.uiValue.value = value;\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,KAAK,EAAEC,iBAAiB,QAAQ,mBAAmB;AAC5D,SAASC,WAAW,QAAQ,YAAY;AAIxC,OAAO,SAASC,8BAA8BA,CAAA,EAAG;EAC/C,OAAOH,KAAK,CAAC,CAAC,GACVC,iBAAiB,CAAC,CAAC;EACjB;EACAG,MAAM,CAACC,UAAU,CAAC,kCAAkC,CAAC,CAACC,OAAO,GAC7D,KAAK,GACP,CAAC,CAAEC,MAAM,CAAiBC,6BAA6B;AAC7D;AAEA,MAAMC,mCAAmC,GAAGN,8BAA8B,CAAC,CAAC;AAE5E,OAAO,MAAMO,oBAAoB,GAAG;EAClCC,OAAO,EAAEF,mCAAmC;EAC5CG,OAAO,EAAEV,WAAW,CAACO,mCAAmC,CAAC;EACzDI,UAAUA,CAACC,KAAc,EAAE;IACzBJ,oBAAoB,CAACC,OAAO,GAAGG,KAAK;IACpCJ,oBAAoB,CAACE,OAAO,CAACE,KAAK,GAAGA,KAAK;EAC5C;AACF,CAAC", "ignoreList": []}