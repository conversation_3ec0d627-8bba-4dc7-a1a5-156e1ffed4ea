{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "withSequence", "withTiming", "BaseAnimationBuilder", "SequencedTransition", "constructor", "args", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "halfDuration", "durationV", "config", "duration", "reverse", "reversed", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "createInstance", "instance"], "sources": ["SequencedTransition.ts"], "sourcesContent": ["'use strict';\nimport { withSequence, withTiming } from '../../animation';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationFunction,\n} from '../animationBuilder/commonTypes';\nimport { BaseAnimationBuilder } from '../animationBuilder';\n\n/**\n * Transforms layout starting from the X-axis and width first, followed by the Y-axis and height. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `layout` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions#sequenced-transition\n */\nexport class SequencedTransition\n  extends BaseAnimationBuilder\n  implements ILayoutAnimationBuilder\n{\n  static presetName = 'SequencedTransition';\n\n  reversed = false;\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new SequencedTransition() as InstanceType<T>;\n  }\n\n  static reverse(): SequencedTransition {\n    const instance = SequencedTransition.createInstance();\n    return instance.reverse();\n  }\n\n  reverse(): SequencedTransition {\n    this.reversed = !this.reversed;\n    return this;\n  }\n\n  build = (): LayoutAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const callback = this.callbackV;\n    const delay = this.getDelay();\n    const halfDuration = (this.durationV ?? 500) / 2;\n    const config = { duration: halfDuration };\n    const reverse = this.reversed;\n\n    return (values) => {\n      'worklet';\n      return {\n        initialValues: {\n          originX: values.currentOriginX,\n          originY: values.currentOriginY,\n          width: values.currentWidth,\n          height: values.currentHeight,\n        },\n        animations: {\n          originX: delayFunction(\n            delay,\n            withSequence(\n              withTiming(\n                reverse ? values.currentOriginX : values.targetOriginX,\n                config\n              ),\n              withTiming(values.targetOriginX, config)\n            )\n          ),\n          originY: delayFunction(\n            delay,\n            withSequence(\n              withTiming(\n                reverse ? values.targetOriginY : values.currentOriginY,\n                config\n              ),\n              withTiming(values.targetOriginY, config)\n            )\n          ),\n          width: delayFunction(\n            delay,\n            withSequence(\n              withTiming(\n                reverse ? values.currentWidth : values.targetWidth,\n                config\n              ),\n              withTiming(values.targetWidth, config)\n            )\n          ),\n          height: delayFunction(\n            delay,\n            withSequence(\n              withTiming(\n                reverse ? values.targetHeight : values.currentHeight,\n                config\n              ),\n              withTiming(values.targetHeight, config)\n            )\n          ),\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAK1D,SAASC,oBAAoB,QAAQ,qBAAqB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,SACtBD,oBAAoB,CAE9B;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA1B,eAAA,mBAGa,KAAK;IAAAA,eAAA,gBAkBR,MAA+B;MACrC,MAAM2B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACC,SAAS,IAAI,GAAG,IAAI,CAAC;MAChD,MAAMC,MAAM,GAAG;QAAEC,QAAQ,EAAEH;MAAa,CAAC;MACzC,MAAMI,OAAO,GAAG,IAAI,CAACC,QAAQ;MAE7B,OAAQC,MAAM,IAAK;QACjB,SAAS;;QACT,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;YAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;YAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;YAC1BC,MAAM,EAAER,MAAM,CAACS;UACjB,CAAC;UACDC,UAAU,EAAE;YACVR,OAAO,EAAEd,aAAa,CACpBI,KAAK,EACLV,YAAY,CACVC,UAAU,CACRe,OAAO,GAAGE,MAAM,CAACG,cAAc,GAAGH,MAAM,CAACW,aAAa,EACtDf,MACF,CAAC,EACDb,UAAU,CAACiB,MAAM,CAACW,aAAa,EAAEf,MAAM,CACzC,CACF,CAAC;YACDQ,OAAO,EAAEhB,aAAa,CACpBI,KAAK,EACLV,YAAY,CACVC,UAAU,CACRe,OAAO,GAAGE,MAAM,CAACY,aAAa,GAAGZ,MAAM,CAACK,cAAc,EACtDT,MACF,CAAC,EACDb,UAAU,CAACiB,MAAM,CAACY,aAAa,EAAEhB,MAAM,CACzC,CACF,CAAC;YACDU,KAAK,EAAElB,aAAa,CAClBI,KAAK,EACLV,YAAY,CACVC,UAAU,CACRe,OAAO,GAAGE,MAAM,CAACO,YAAY,GAAGP,MAAM,CAACa,WAAW,EAClDjB,MACF,CAAC,EACDb,UAAU,CAACiB,MAAM,CAACa,WAAW,EAAEjB,MAAM,CACvC,CACF,CAAC;YACDY,MAAM,EAAEpB,aAAa,CACnBI,KAAK,EACLV,YAAY,CACVC,UAAU,CACRe,OAAO,GAAGE,MAAM,CAACc,YAAY,GAAGd,MAAM,CAACS,aAAa,EACpDb,MACF,CAAC,EACDb,UAAU,CAACiB,MAAM,CAACc,YAAY,EAAElB,MAAM,CACxC,CACF;UACF,CAAC;UACDN;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA9ED,OAAOyB,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI9B,mBAAmB,CAAC,CAAC;EAClC;EAEA,OAAOa,OAAOA,CAAA,EAAwB;IACpC,MAAMkB,QAAQ,GAAG/B,mBAAmB,CAAC8B,cAAc,CAAC,CAAC;IACrD,OAAOC,QAAQ,CAAClB,OAAO,CAAC,CAAC;EAC3B;EAEAA,OAAOA,CAAA,EAAwB;IAC7B,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAC9B,OAAO,IAAI;EACb;AAiEF;AAACtC,eAAA,CAvFYwB,mBAAmB,gBAIV,qBAAqB", "ignoreList": []}