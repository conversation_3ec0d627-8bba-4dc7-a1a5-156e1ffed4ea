ninja: Entering directory `C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-reanimated\android\.cxx\Debug\6l266c2n\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/531088e49f724540a3de4908444ccf3e/cpp/reanimated/Fabric/PropsRegistry.cpp.o
[2/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/47318364f491494f62bfd9c045c40bfe/LayoutAnimationsProxy.cpp.o
[3/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/c7fe51702da5a426d6e131de11aca324/Fabric/ReanimatedCommitHook.cpp.o
[4/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/c7fe51702da5a426d6e131de11aca324/Fabric/ReanimatedMountHook.cpp.o
[5/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e6e4e4c6b363c43de3c7dfb2308f9ebc/reanimated/Fabric/ShadowTreeCloner.cpp.o
[6/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/47318364f491494f62bfd9c045c40bfe/LayoutAnimationsUtils.cpp.o
[7/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o
[8/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/531088e49f724540a3de4908444ccf3e/cpp/reanimated/Tools/FeaturesConfig.cpp.o
[9/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/Registries/WorkletRuntimeRegistry.cpp.o
[10/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/531088e49f724540a3de4908444ccf3e/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o
[11/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/62eb4faf80be678832497bdc309ad6c1/Common/cpp/worklets/Tools/AsyncQueue.cpp.o
[12/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/62eb4faf80be678832497bdc309ad6c1/Common/cpp/worklets/Tools/JSLogger.cpp.o
[13/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/62eb4faf80be678832497bdc309ad6c1/Common/cpp/worklets/Tools/JSISerializer.cpp.o
[14/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/WorkletRuntime/ReanimatedRuntime.cpp.o
[15/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/531088e49f724540a3de4908444ccf3e/cpp/worklets/Tools/WorkletEventHandler.cpp.o
[16/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/62eb4faf80be678832497bdc309ad6c1/Common/cpp/worklets/Tools/JSScheduler.cpp.o
[17/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/62eb4faf80be678832497bdc309ad6c1/Common/cpp/worklets/Tools/UIScheduler.cpp.o
[18/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/c2b606f5eddaa341ea8d302000a23292/RNRuntimeWorkletDecorator.cpp.o
[19/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/c7fe51702da5a426d6e131de11aca324/AnimatedSensor/AnimatedSensorModule.cpp.o
[20/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/WorkletRuntime/ReanimatedHermesRuntime.cpp.o
[21/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/NativeModules/WorkletsModuleProxySpec.cpp.o
[22/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/aa3fba4e5269031a0af19389392d252e/ReanimatedModuleProxySpec.cpp.o
[23/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/531088e49f724540a3de4908444ccf3e/cpp/worklets/SharedItems/Shareables.cpp.o
[24/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/142ed241ad7a71a5b697e67df6286af0/RNRuntimeDecorator.cpp.o
[25/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/Registries/EventHandlerRegistry.cpp.o
[26/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/NativeModules/WorkletsModuleProxy.cpp.o
[27/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/e6e4e4c6b363c43de3c7dfb2308f9ebc/worklets/WorkletRuntime/WorkletRuntime.cpp.o
[28/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/47318364f491494f62bfd9c045c40bfe/LayoutAnimationsManager.cpp.o
[29/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o
[30/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o
[31/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o
[32/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/531088e49f724540a3de4908444ccf3e/cpp/worklets/Tools/ReanimatedVersion.cpp.o
[33/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/142ed241ad7a71a5b697e67df6286af0/UIRuntimeDecorator.cpp.o
[34/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o
[35/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/798096757390bf2e9be829c88a95a403/WorkletRuntime/WorkletRuntimeDecorator.cpp.o
[36/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o
[37/41] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\6l266c2n\obj\arm64-v8a\libworklets.so
[38/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o
[39/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/c7fe51702da5a426d6e131de11aca324/NativeModules/ReanimatedModuleProxy.cpp.o
[40/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o
[41/41] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\6l266c2n\obj\arm64-v8a\libreanimated.so
