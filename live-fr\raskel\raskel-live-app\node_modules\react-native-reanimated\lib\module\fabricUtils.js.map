{"version": 3, "names": ["findHostInstance_DEPRECATED", "getInternalInstanceHandleFromPublicInstance", "getShadowNodeWrapperFromRef", "ref", "_ref$getScrollRespond", "_ref$getScrollRespond2", "_ref$getNativeScrollR", "_ref$__internalInstan", "undefined", "require", "e", "_ref", "_internalInstanceHandle", "scrollViewRef", "getScrollResponder", "call", "getNativeScrollRef", "otherScrollViewRef", "textInputRef", "__internalInstanceHandle", "stateNode", "node", "resolvedRef"], "sources": ["fabricUtils.ts"], "sourcesContent": ["'use strict';\n/* eslint-disable */\n\nimport type { ShadowNodeWrapper } from './commonTypes';\n\nlet findHostInstance_DEPRECATED: (ref: unknown) => void;\nlet getInternalInstanceHandleFromPublicInstance: (ref: unknown) => {\n  stateNode: { node: unknown };\n};\n\nexport function getShadowNodeWrapperFromRef(\n  ref: React.Component\n): ShadowNodeWrapper {\n  // load findHostInstance_DEPRECATED lazily because it may not be available before render\n  if (findHostInstance_DEPRECATED === undefined) {\n    try {\n      findHostInstance_DEPRECATED =\n        require('react-native/Libraries/Renderer/shims/ReactFabric').findHostInstance_DEPRECATED;\n    } catch (e) {\n      findHostInstance_DEPRECATED = (_ref: unknown) => null;\n    }\n  }\n\n  if (getInternalInstanceHandleFromPublicInstance === undefined) {\n    try {\n      getInternalInstanceHandleFromPublicInstance =\n        require('react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n          .getInternalInstanceHandleFromPublicInstance ??\n        ((_ref: any) => _ref._internalInstanceHandle);\n    } catch (e) {\n      getInternalInstanceHandleFromPublicInstance = (_ref: any) =>\n        _ref._internalInstanceHandle;\n    }\n  }\n\n  // taken from https://github.com/facebook/react-native/commit/803bb16531697233686efd475f004c1643e03617#diff-d8172256c6d63b5d32db10e54d7b10f37a26b337d5280d89f5bfd7bcea778292R196\n  // @ts-ignore some weird stuff on RN 0.74 - see examples with scrollView\n  const scrollViewRef = ref?.getScrollResponder?.()?.getNativeScrollRef?.();\n  // @ts-ignore some weird stuff on RN 0.74  - see examples with scrollView\n  const otherScrollViewRef = ref?.getNativeScrollRef?.();\n  // @ts-ignore some weird stuff on RN 0.74 - see setNativeProps example\n  const textInputRef = ref?.__internalInstanceHandle?.stateNode?.node;\n\n  let resolvedRef;\n  if (scrollViewRef) {\n    resolvedRef = scrollViewRef.__internalInstanceHandle.stateNode.node;\n  } else if (otherScrollViewRef) {\n    resolvedRef = otherScrollViewRef.__internalInstanceHandle.stateNode.node;\n  } else if (textInputRef) {\n    resolvedRef = textInputRef;\n  } else {\n    resolvedRef = getInternalInstanceHandleFromPublicInstance(\n      findHostInstance_DEPRECATED(ref)\n    ).stateNode.node;\n  }\n\n  return resolvedRef;\n}\n"], "mappings": "AAAA,YAAY;;AACZ;AAIA,IAAIA,2BAAmD;AACvD,IAAIC,2CAEH;AAED,OAAO,SAASC,2BAA2BA,CACzCC,GAAoB,EACD;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACnB;EACA,IAAIP,2BAA2B,KAAKQ,SAAS,EAAE;IAC7C,IAAI;MACFR,2BAA2B,GACzBS,OAAO,CAAC,mDAAmD,CAAC,CAACT,2BAA2B;IAC5F,CAAC,CAAC,OAAOU,CAAC,EAAE;MACVV,2BAA2B,GAAIW,IAAa,IAAK,IAAI;IACvD;EACF;EAEA,IAAIV,2CAA2C,KAAKO,SAAS,EAAE;IAC7D,IAAI;MACFP,2CAA2C,GACzCQ,OAAO,CAAC,wFAAwF,CAAC,CAC9FR,2CAA2C,KAC5CU,IAAS,IAAKA,IAAI,CAACC,uBAAuB,CAAC;IACjD,CAAC,CAAC,OAAOF,CAAC,EAAE;MACVT,2CAA2C,GAAIU,IAAS,IACtDA,IAAI,CAACC,uBAAuB;IAChC;EACF;;EAEA;EACA;EACA,MAAMC,aAAa,GAAGV,GAAG,aAAHA,GAAG,gBAAAC,qBAAA,GAAHD,GAAG,CAAEW,kBAAkB,cAAAV,qBAAA,gBAAAA,qBAAA,GAAvBA,qBAAA,CAAAW,IAAA,CAAAZ,GAA0B,CAAC,cAAAC,qBAAA,gBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BY,kBAAkB,cAAAX,sBAAA,uBAA/CA,sBAAA,CAAAU,IAAA,CAAAX,qBAAkD,CAAC;EACzE;EACA,MAAMa,kBAAkB,GAAGd,GAAG,aAAHA,GAAG,gBAAAG,qBAAA,GAAHH,GAAG,CAAEa,kBAAkB,cAAAV,qBAAA,uBAAvBA,qBAAA,CAAAS,IAAA,CAAAZ,GAA0B,CAAC;EACtD;EACA,MAAMe,YAAY,GAAGf,GAAG,aAAHA,GAAG,gBAAAI,qBAAA,GAAHJ,GAAG,CAAEgB,wBAAwB,cAAAZ,qBAAA,gBAAAA,qBAAA,GAA7BA,qBAAA,CAA+Ba,SAAS,cAAAb,qBAAA,uBAAxCA,qBAAA,CAA0Cc,IAAI;EAEnE,IAAIC,WAAW;EACf,IAAIT,aAAa,EAAE;IACjBS,WAAW,GAAGT,aAAa,CAACM,wBAAwB,CAACC,SAAS,CAACC,IAAI;EACrE,CAAC,MAAM,IAAIJ,kBAAkB,EAAE;IAC7BK,WAAW,GAAGL,kBAAkB,CAACE,wBAAwB,CAACC,SAAS,CAACC,IAAI;EAC1E,CAAC,MAAM,IAAIH,YAAY,EAAE;IACvBI,WAAW,GAAGJ,YAAY;EAC5B,CAAC,MAAM;IACLI,WAAW,GAAGrB,2CAA2C,CACvDD,2BAA2B,CAACG,GAAG,CACjC,CAAC,CAACiB,SAAS,CAACC,IAAI;EAClB;EAEA,OAAOC,WAAW;AACpB", "ignoreList": []}