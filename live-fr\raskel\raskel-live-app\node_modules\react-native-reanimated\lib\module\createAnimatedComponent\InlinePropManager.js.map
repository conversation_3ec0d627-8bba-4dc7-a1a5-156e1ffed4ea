{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "flattenArray", "makeViewDescriptorsSet", "adaptViewConfig", "updateProps", "stopMapper", "startMapper", "isSharedValue", "isInlineStyleTransform", "transform", "Array", "isArray", "some", "hasInlineStyles", "inlinePropsHasChanged", "styles1", "styles2", "keys", "length", "getInlinePropsUpdate", "inlineProps", "update", "styleValue", "entries", "map", "item", "extractSharedValuesMapFromProps", "props", "styles", "style", "for<PERSON>ach", "styleKey", "getInlineStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newStyle", "InlinePropManager", "constructor", "attachInlineProps", "animatedComponent", "viewInfo", "newInlineProps", "has<PERSON><PERSON>ed", "_inlineProps", "_inlinePropsViewDescriptors", "viewTag", "viewName", "shadowNodeWrapper", "viewConfig", "add", "tag", "name", "shareableViewDescriptors", "updaterFunction", "_inlinePropsMapperId", "values", "detachInlineProps"], "sources": ["InlinePropManager.ts"], "sourcesContent": ["'use strict';\nimport type { StyleProps } from '../commonTypes';\nimport type {\n  IAnimatedComponentInternal,\n  AnimatedComponentProps,\n  IInlinePropManager,\n  ViewInfo,\n} from './commonTypes';\nimport { flattenArray } from './utils';\nimport { makeViewDescriptorsSet } from '../ViewDescriptorsSet';\nimport type { ViewDescriptorsSet } from '../ViewDescriptorsSet';\nimport { adaptViewConfig } from '../ConfigHelper';\nimport updateProps from '../UpdateProps';\nimport { stopMapper, startMapper } from '../mappers';\nimport { isSharedValue } from '../isSharedValue';\n\nfunction isInlineStyleTransform(transform: unknown): boolean {\n  if (!Array.isArray(transform)) {\n    return false;\n  }\n\n  return transform.some((t: Record<string, unknown>) => hasInlineStyles(t));\n}\n\nfunction inlinePropsHasChanged(\n  styles1: StyleProps,\n  styles2: StyleProps\n): boolean {\n  if (Object.keys(styles1).length !== Object.keys(styles2).length) {\n    return true;\n  }\n\n  for (const key of Object.keys(styles1)) {\n    if (styles1[key] !== styles2[key]) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getInlinePropsUpdate(inlineProps: Record<string, unknown>) {\n  'worklet';\n  const update: Record<string, unknown> = {};\n  for (const [key, styleValue] of Object.entries(inlineProps)) {\n    if (isSharedValue(styleValue)) {\n      update[key] = styleValue.value;\n    } else if (Array.isArray(styleValue)) {\n      update[key] = styleValue.map((item) => {\n        return getInlinePropsUpdate(item);\n      });\n    } else if (typeof styleValue === 'object') {\n      update[key] = getInlinePropsUpdate(styleValue as Record<string, unknown>);\n    } else {\n      update[key] = styleValue;\n    }\n  }\n  return update;\n}\n\nfunction extractSharedValuesMapFromProps(\n  props: AnimatedComponentProps<\n    Record<string, unknown> /* Initial component props */\n  >\n): Record<string, unknown> {\n  const inlineProps: Record<string, unknown> = {};\n\n  for (const key in props) {\n    const value = props[key];\n    if (key === 'style') {\n      const styles = flattenArray<StyleProps>(props.style ?? []);\n      styles.forEach((style) => {\n        if (!style) {\n          return;\n        }\n        for (const [styleKey, styleValue] of Object.entries(style)) {\n          if (isSharedValue(styleValue)) {\n            inlineProps[styleKey] = styleValue;\n          } else if (\n            styleKey === 'transform' &&\n            isInlineStyleTransform(styleValue)\n          ) {\n            inlineProps[styleKey] = styleValue;\n          }\n        }\n      });\n    } else if (isSharedValue(value)) {\n      inlineProps[key] = value;\n    }\n  }\n\n  return inlineProps;\n}\n\nexport function hasInlineStyles(style: StyleProps): boolean {\n  if (!style) {\n    return false;\n  }\n  return Object.keys(style).some((key) => {\n    const styleValue = style[key];\n    return (\n      isSharedValue(styleValue) ||\n      (key === 'transform' && isInlineStyleTransform(styleValue))\n    );\n  });\n}\n\nexport function getInlineStyle(\n  style: Record<string, unknown>,\n  isFirstRender: boolean\n) {\n  if (isFirstRender) {\n    return getInlinePropsUpdate(style);\n  }\n  const newStyle: StyleProps = {};\n  for (const [key, styleValue] of Object.entries(style)) {\n    if (\n      !isSharedValue(styleValue) &&\n      !(key === 'transform' && isInlineStyleTransform(styleValue))\n    ) {\n      newStyle[key] = styleValue;\n    }\n  }\n  return newStyle;\n}\n\nexport class InlinePropManager implements IInlinePropManager {\n  _inlinePropsViewDescriptors: ViewDescriptorsSet | null = null;\n  _inlinePropsMapperId: number | null = null;\n  _inlineProps: StyleProps = {};\n\n  public attachInlineProps(\n    animatedComponent: React.Component<unknown, unknown> &\n      IAnimatedComponentInternal,\n    viewInfo: ViewInfo\n  ) {\n    const newInlineProps: Record<string, unknown> =\n      extractSharedValuesMapFromProps(animatedComponent.props);\n    const hasChanged = inlinePropsHasChanged(newInlineProps, this._inlineProps);\n\n    if (hasChanged) {\n      if (!this._inlinePropsViewDescriptors) {\n        this._inlinePropsViewDescriptors = makeViewDescriptorsSet();\n\n        const { viewTag, viewName, shadowNodeWrapper, viewConfig } = viewInfo;\n\n        if (Object.keys(newInlineProps).length && viewConfig) {\n          adaptViewConfig(viewConfig);\n        }\n\n        this._inlinePropsViewDescriptors.add({\n          tag: viewTag as number,\n          name: viewName!,\n          shadowNodeWrapper: shadowNodeWrapper!,\n        });\n      }\n      const shareableViewDescriptors =\n        this._inlinePropsViewDescriptors.shareableViewDescriptors;\n\n      const updaterFunction = () => {\n        'worklet';\n        const update = getInlinePropsUpdate(newInlineProps);\n        updateProps(shareableViewDescriptors, update);\n      };\n      this._inlineProps = newInlineProps;\n      if (this._inlinePropsMapperId) {\n        stopMapper(this._inlinePropsMapperId);\n      }\n      this._inlinePropsMapperId = null;\n      if (Object.keys(newInlineProps).length) {\n        this._inlinePropsMapperId = startMapper(\n          updaterFunction,\n          Object.values(newInlineProps)\n        );\n      }\n    }\n  }\n\n  public detachInlineProps() {\n    if (this._inlinePropsMapperId) {\n      stopMapper(this._inlinePropsMapperId);\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAQb,SAASW,YAAY,QAAQ,SAAS;AACtC,SAASC,sBAAsB,QAAQ,uBAAuB;AAE9D,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,UAAU,EAAEC,WAAW,QAAQ,YAAY;AACpD,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,SAASC,sBAAsBA,CAACC,SAAkB,EAAW;EAC3D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOA,SAAS,CAACG,IAAI,CAAEtB,CAA0B,IAAKuB,eAAe,CAACvB,CAAC,CAAC,CAAC;AAC3E;AAEA,SAASwB,qBAAqBA,CAC5BC,OAAmB,EACnBC,OAAmB,EACV;EACT,IAAI/B,MAAM,CAACgC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,KAAKjC,MAAM,CAACgC,IAAI,CAACD,OAAO,CAAC,CAACE,MAAM,EAAE;IAC/D,OAAO,IAAI;EACb;EAEA,KAAK,MAAMpC,GAAG,IAAIG,MAAM,CAACgC,IAAI,CAACF,OAAO,CAAC,EAAE;IACtC,IAAIA,OAAO,CAACjC,GAAG,CAAC,KAAKkC,OAAO,CAAClC,GAAG,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,SAASqC,oBAAoBA,CAACC,WAAoC,EAAE;EAClE,SAAS;;EACT,MAAMC,MAA+B,GAAG,CAAC,CAAC;EAC1C,KAAK,MAAM,CAACvC,GAAG,EAAEwC,UAAU,CAAC,IAAIrC,MAAM,CAACsC,OAAO,CAACH,WAAW,CAAC,EAAE;IAC3D,IAAIb,aAAa,CAACe,UAAU,CAAC,EAAE;MAC7BD,MAAM,CAACvC,GAAG,CAAC,GAAGwC,UAAU,CAACvC,KAAK;IAChC,CAAC,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACW,UAAU,CAAC,EAAE;MACpCD,MAAM,CAACvC,GAAG,CAAC,GAAGwC,UAAU,CAACE,GAAG,CAAEC,IAAI,IAAK;QACrC,OAAON,oBAAoB,CAACM,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;MACzCD,MAAM,CAACvC,GAAG,CAAC,GAAGqC,oBAAoB,CAACG,UAAqC,CAAC;IAC3E,CAAC,MAAM;MACLD,MAAM,CAACvC,GAAG,CAAC,GAAGwC,UAAU;IAC1B;EACF;EACA,OAAOD,MAAM;AACf;AAEA,SAASK,+BAA+BA,CACtCC,KAEC,EACwB;EACzB,MAAMP,WAAoC,GAAG,CAAC,CAAC;EAE/C,KAAK,MAAMtC,GAAG,IAAI6C,KAAK,EAAE;IACvB,MAAM5C,KAAK,GAAG4C,KAAK,CAAC7C,GAAG,CAAC;IACxB,IAAIA,GAAG,KAAK,OAAO,EAAE;MACnB,MAAM8C,MAAM,GAAG3B,YAAY,CAAa0B,KAAK,CAACE,KAAK,IAAI,EAAE,CAAC;MAC1DD,MAAM,CAACE,OAAO,CAAED,KAAK,IAAK;QACxB,IAAI,CAACA,KAAK,EAAE;UACV;QACF;QACA,KAAK,MAAM,CAACE,QAAQ,EAAET,UAAU,CAAC,IAAIrC,MAAM,CAACsC,OAAO,CAACM,KAAK,CAAC,EAAE;UAC1D,IAAItB,aAAa,CAACe,UAAU,CAAC,EAAE;YAC7BF,WAAW,CAACW,QAAQ,CAAC,GAAGT,UAAU;UACpC,CAAC,MAAM,IACLS,QAAQ,KAAK,WAAW,IACxBvB,sBAAsB,CAACc,UAAU,CAAC,EAClC;YACAF,WAAW,CAACW,QAAQ,CAAC,GAAGT,UAAU;UACpC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIf,aAAa,CAACxB,KAAK,CAAC,EAAE;MAC/BqC,WAAW,CAACtC,GAAG,CAAC,GAAGC,KAAK;IAC1B;EACF;EAEA,OAAOqC,WAAW;AACpB;AAEA,OAAO,SAASP,eAAeA,CAACgB,KAAiB,EAAW;EAC1D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAO5C,MAAM,CAACgC,IAAI,CAACY,KAAK,CAAC,CAACjB,IAAI,CAAE9B,GAAG,IAAK;IACtC,MAAMwC,UAAU,GAAGO,KAAK,CAAC/C,GAAG,CAAC;IAC7B,OACEyB,aAAa,CAACe,UAAU,CAAC,IACxBxC,GAAG,KAAK,WAAW,IAAI0B,sBAAsB,CAACc,UAAU,CAAE;EAE/D,CAAC,CAAC;AACJ;AAEA,OAAO,SAASU,cAAcA,CAC5BH,KAA8B,EAC9BI,aAAsB,EACtB;EACA,IAAIA,aAAa,EAAE;IACjB,OAAOd,oBAAoB,CAACU,KAAK,CAAC;EACpC;EACA,MAAMK,QAAoB,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAM,CAACpD,GAAG,EAAEwC,UAAU,CAAC,IAAIrC,MAAM,CAACsC,OAAO,CAACM,KAAK,CAAC,EAAE;IACrD,IACE,CAACtB,aAAa,CAACe,UAAU,CAAC,IAC1B,EAAExC,GAAG,KAAK,WAAW,IAAI0B,sBAAsB,CAACc,UAAU,CAAC,CAAC,EAC5D;MACAY,QAAQ,CAACpD,GAAG,CAAC,GAAGwC,UAAU;IAC5B;EACF;EACA,OAAOY,QAAQ;AACjB;AAEA,OAAO,MAAMC,iBAAiB,CAA+B;EAAAC,YAAA;IAAAxD,eAAA,sCACF,IAAI;IAAAA,eAAA,+BACvB,IAAI;IAAAA,eAAA,uBACf,CAAC,CAAC;EAAA;EAEtByD,iBAAiBA,CACtBC,iBAC4B,EAC5BC,QAAkB,EAClB;IACA,MAAMC,cAAuC,GAC3Cd,+BAA+B,CAACY,iBAAiB,CAACX,KAAK,CAAC;IAC1D,MAAMc,UAAU,GAAG3B,qBAAqB,CAAC0B,cAAc,EAAE,IAAI,CAACE,YAAY,CAAC;IAE3E,IAAID,UAAU,EAAE;MACd,IAAI,CAAC,IAAI,CAACE,2BAA2B,EAAE;QACrC,IAAI,CAACA,2BAA2B,GAAGzC,sBAAsB,CAAC,CAAC;QAE3D,MAAM;UAAE0C,OAAO;UAAEC,QAAQ;UAAEC,iBAAiB;UAAEC;QAAW,CAAC,GAAGR,QAAQ;QAErE,IAAItD,MAAM,CAACgC,IAAI,CAACuB,cAAc,CAAC,CAACtB,MAAM,IAAI6B,UAAU,EAAE;UACpD5C,eAAe,CAAC4C,UAAU,CAAC;QAC7B;QAEA,IAAI,CAACJ,2BAA2B,CAACK,GAAG,CAAC;UACnCC,GAAG,EAAEL,OAAiB;UACtBM,IAAI,EAAEL,QAAS;UACfC,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;MACA,MAAMK,wBAAwB,GAC5B,IAAI,CAACR,2BAA2B,CAACQ,wBAAwB;MAE3D,MAAMC,eAAe,GAAGA,CAAA,KAAM;QAC5B,SAAS;;QACT,MAAM/B,MAAM,GAAGF,oBAAoB,CAACqB,cAAc,CAAC;QACnDpC,WAAW,CAAC+C,wBAAwB,EAAE9B,MAAM,CAAC;MAC/C,CAAC;MACD,IAAI,CAACqB,YAAY,GAAGF,cAAc;MAClC,IAAI,IAAI,CAACa,oBAAoB,EAAE;QAC7BhD,UAAU,CAAC,IAAI,CAACgD,oBAAoB,CAAC;MACvC;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAIpE,MAAM,CAACgC,IAAI,CAACuB,cAAc,CAAC,CAACtB,MAAM,EAAE;QACtC,IAAI,CAACmC,oBAAoB,GAAG/C,WAAW,CACrC8C,eAAe,EACfnE,MAAM,CAACqE,MAAM,CAACd,cAAc,CAC9B,CAAC;MACH;IACF;EACF;EAEOe,iBAAiBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACF,oBAAoB,EAAE;MAC7BhD,UAAU,CAAC,IAAI,CAACgD,oBAAoB,CAAC;IACvC;EACF;AACF", "ignoreList": []}