{"version": 3, "names": ["isAffineMatrixFlat", "x", "Array", "isArray", "length", "every", "element", "isNaN", "isAffineMatrix", "row", "flatten", "matrix", "flat", "unflatten", "m", "maybeFlattenMatrix", "multiplyMatrices", "a", "b", "subtractMatrices", "maybeFlatA", "maybeFlatB", "isFlatOnStart", "c", "map", "_", "i", "addMatrices", "scaleMatrix", "scalar", "getRotationMatrix", "angle", "axis", "cos", "Math", "sin", "norm3d", "y", "z", "sqrt", "transposeMatrix", "assertVectorsHaveEqualLengths", "__DEV__", "Error", "toString", "innerProduct", "reduce", "acc", "projection", "u", "s", "e", "subtractVectors", "scaleVector", "gramSchmidtAlgorithm", "a0", "a1", "a2", "a3", "u0", "u1", "u2", "u3", "e0", "e1", "e2", "e3", "rotationMatrix", "skewMatrix", "decomposeMatrix", "unknownTypeMatrix", "for<PERSON>ach", "translationMatrix", "sx", "sy", "sz", "rotationAndSkewMatrix", "decomposeMatrixIntoMatricesAndAngles", "sinRy", "ry", "asin", "rx", "rz", "atan2"], "sources": ["matrixUtils.tsx"], "sourcesContent": ["'use strict';\ntype FixedLengthArray<\n  T,\n  L extends number,\n  PassedObject = [T, ...Array<T>]\n> = PassedObject & {\n  readonly length: L;\n  [I: number]: T;\n};\n\nexport type AffineMatrix = FixedLengthArray<FixedLengthArray<number, 4>, 4>;\n\nexport type AffineMatrixFlat = FixedLengthArray<number, 16>;\n\ntype TransformMatrixDecomposition = Record<\n  'translationMatrix' | 'scaleMatrix' | 'rotationMatrix' | 'skewMatrix',\n  AffineMatrix\n>;\n\ntype Axis = 'x' | 'y' | 'z';\n\ninterface TansformMatrixDecompositionWithAngles\n  extends TransformMatrixDecomposition {\n  rx: number;\n  ry: number;\n  rz: number;\n}\n\nexport function isAffineMatrixFlat(x: unknown): x is AffineMatrixFlat {\n  'worklet';\n  return (\n    Array.isArray(x) &&\n    x.length === 16 &&\n    x.every((element) => typeof element === 'number' && !isNaN(element))\n  );\n}\n\n// ts-prune-ignore-next This function is exported to be tested\nexport function isAffineMatrix(x: unknown): x is AffineMatrix {\n  'worklet';\n  return (\n    Array.isArray(x) &&\n    x.length === 4 &&\n    x.every(\n      (row) =>\n        Array.isArray(row) &&\n        row.length === 4 &&\n        row.every((element) => typeof element === 'number' && !isNaN(element))\n    )\n  );\n}\n\nexport function flatten(matrix: AffineMatrix): AffineMatrixFlat {\n  'worklet';\n  return matrix.flat() as AffineMatrixFlat;\n}\n\n// ts-prune-ignore-next This function is exported to be tested\nexport function unflatten(m: AffineMatrixFlat): AffineMatrix {\n  'worklet';\n  return [\n    [m[0], m[1], m[2], m[3]],\n    [m[4], m[5], m[6], m[7]],\n    [m[8], m[9], m[10], m[11]],\n    [m[12], m[13], m[14], m[15]],\n  ] as AffineMatrix;\n}\n\nfunction maybeFlattenMatrix(\n  matrix: AffineMatrix | AffineMatrixFlat\n): AffineMatrixFlat {\n  'worklet';\n  return isAffineMatrix(matrix) ? flatten(matrix) : matrix;\n}\n\nexport function multiplyMatrices(\n  a: AffineMatrix,\n  b: AffineMatrix\n): AffineMatrix {\n  'worklet';\n  return [\n    [\n      a[0][0] * b[0][0] +\n        a[0][1] * b[1][0] +\n        a[0][2] * b[2][0] +\n        a[0][3] * b[3][0],\n\n      a[0][0] * b[0][1] +\n        a[0][1] * b[1][1] +\n        a[0][2] * b[2][1] +\n        a[0][3] * b[3][1],\n\n      a[0][0] * b[0][2] +\n        a[0][1] * b[1][2] +\n        a[0][2] * b[2][2] +\n        a[0][3] * b[3][2],\n\n      a[0][0] * b[0][3] +\n        a[0][1] * b[1][3] +\n        a[0][2] * b[2][3] +\n        a[0][3] * b[3][3],\n    ],\n    [\n      a[1][0] * b[0][0] +\n        a[1][1] * b[1][0] +\n        a[1][2] * b[2][0] +\n        a[1][3] * b[3][0],\n\n      a[1][0] * b[0][1] +\n        a[1][1] * b[1][1] +\n        a[1][2] * b[2][1] +\n        a[1][3] * b[3][1],\n\n      a[1][0] * b[0][2] +\n        a[1][1] * b[1][2] +\n        a[1][2] * b[2][2] +\n        a[1][3] * b[3][2],\n\n      a[1][0] * b[0][3] +\n        a[1][1] * b[1][3] +\n        a[1][2] * b[2][3] +\n        a[1][3] * b[3][3],\n    ],\n    [\n      a[2][0] * b[0][0] +\n        a[2][1] * b[1][0] +\n        a[2][2] * b[2][0] +\n        a[2][3] * b[3][0],\n\n      a[2][0] * b[0][1] +\n        a[2][1] * b[1][1] +\n        a[2][2] * b[2][1] +\n        a[2][3] * b[3][1],\n\n      a[2][0] * b[0][2] +\n        a[2][1] * b[1][2] +\n        a[2][2] * b[2][2] +\n        a[2][3] * b[3][2],\n\n      a[2][0] * b[0][3] +\n        a[2][1] * b[1][3] +\n        a[2][2] * b[2][3] +\n        a[2][3] * b[3][3],\n    ],\n    [\n      a[3][0] * b[0][0] +\n        a[3][1] * b[1][0] +\n        a[3][2] * b[2][0] +\n        a[3][3] * b[3][0],\n\n      a[3][0] * b[0][1] +\n        a[3][1] * b[1][1] +\n        a[3][2] * b[2][1] +\n        a[3][3] * b[3][1],\n\n      a[3][0] * b[0][2] +\n        a[3][1] * b[1][2] +\n        a[3][2] * b[2][2] +\n        a[3][3] * b[3][2],\n\n      a[3][0] * b[0][3] +\n        a[3][1] * b[1][3] +\n        a[3][2] * b[2][3] +\n        a[3][3] * b[3][3],\n    ],\n  ];\n}\n\nexport function subtractMatrices<T extends AffineMatrixFlat | AffineMatrix>(\n  maybeFlatA: T,\n  maybeFlatB: T\n): T {\n  'worklet';\n  const isFlatOnStart = isAffineMatrixFlat(maybeFlatA);\n  const a: AffineMatrixFlat = maybeFlattenMatrix(maybeFlatA);\n  const b: AffineMatrixFlat = maybeFlattenMatrix(maybeFlatB);\n\n  const c = a.map((_, i) => a[i] - b[i]) as AffineMatrixFlat;\n  return isFlatOnStart ? (c as T) : (unflatten(c) as T);\n}\n\nexport function addMatrices<T extends AffineMatrixFlat | AffineMatrix>(\n  maybeFlatA: T,\n  maybeFlatB: T\n): T {\n  'worklet';\n  const isFlatOnStart = isAffineMatrixFlat(maybeFlatA);\n  const a = maybeFlattenMatrix(maybeFlatA);\n  const b = maybeFlattenMatrix(maybeFlatB);\n\n  const c = a.map((_, i) => a[i] + b[i]) as AffineMatrixFlat;\n  return isFlatOnStart ? (c as T) : (unflatten(c) as T);\n}\n\nexport function scaleMatrix<T extends AffineMatrixFlat | AffineMatrix>(\n  maybeFlatA: T,\n  scalar: number\n): T {\n  'worklet';\n  const isFlatOnStart = isAffineMatrixFlat(maybeFlatA);\n  const a = maybeFlattenMatrix(maybeFlatA);\n\n  const b = a.map((x) => x * scalar) as AffineMatrixFlat;\n  return isFlatOnStart ? (b as T) : (unflatten(b) as T);\n}\n\nexport function getRotationMatrix(\n  angle: number,\n  axis: Axis = 'z'\n): AffineMatrix {\n  'worklet';\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  switch (axis) {\n    case 'z':\n      return [\n        [cos, sin, 0, 0],\n        [-sin, cos, 0, 0],\n        [0, 0, 1, 0],\n        [0, 0, 0, 1],\n      ];\n    case 'y':\n      return [\n        [cos, 0, -sin, 0],\n        [0, 1, 0, 0],\n        [sin, 0, cos, 0],\n        [0, 0, 0, 1],\n      ];\n    case 'x':\n      return [\n        [1, 0, 0, 0],\n        [0, cos, sin, 0],\n        [0, -sin, cos, 0],\n        [0, 0, 0, 1],\n      ];\n  }\n}\n\nfunction norm3d(x: number, y: number, z: number) {\n  'worklet';\n  return Math.sqrt(x * x + y * y + z * z);\n}\n\nfunction transposeMatrix(matrix: AffineMatrix): AffineMatrix {\n  'worklet';\n  const m = flatten(matrix);\n  return [\n    [m[0], m[4], m[8], m[12]],\n    [m[1], m[5], m[9], m[13]],\n    [m[2], m[6], m[10], m[14]],\n    [m[3], m[7], m[11], m[15]],\n  ];\n}\n\nfunction assertVectorsHaveEqualLengths(a: number[], b: number[]) {\n  'worklet';\n  if (__DEV__ && a.length !== b.length) {\n    throw new Error(\n      `[Reanimated] Cannot calculate inner product of two vectors of different lengths. Length of ${a.toString()} is ${\n        a.length\n      } and length of ${b.toString()} is ${b.length}.`\n    );\n  }\n}\n\nfunction innerProduct(a: number[], b: number[]) {\n  'worklet';\n  assertVectorsHaveEqualLengths(a, b);\n  return a.reduce((acc, _, i) => acc + a[i] * b[i], 0);\n}\n\nfunction projection(u: number[], a: number[]) {\n  'worklet';\n  assertVectorsHaveEqualLengths(u, a);\n  const s = innerProduct(u, a) / innerProduct(u, u);\n  return u.map((e) => e * s);\n}\n\nfunction subtractVectors(a: number[], b: number[]) {\n  'worklet';\n  assertVectorsHaveEqualLengths(a, b);\n  return a.map((_, i) => a[i] - b[i]);\n}\n\nfunction scaleVector(u: number[], a: number) {\n  'worklet';\n  return u.map((e) => e * a);\n}\n\nfunction gramSchmidtAlgorithm(matrix: AffineMatrix): {\n  rotationMatrix: AffineMatrix;\n  skewMatrix: AffineMatrix;\n} {\n  // Gram-Schmidt orthogonalization decomposes any matrix with non-zero determinant into an orthogonal and a triangular matrix\n  // These matrices are equal to rotation and skew matrices respectively, because we apply it to transformation matrix\n  // That is expected to already have extracted the remaining transforms (scale & translation)\n  'worklet';\n  const [a0, a1, a2, a3] = matrix;\n\n  const u0 = a0;\n  const u1 = subtractVectors(a1, projection(u0, a1));\n  const u2 = subtractVectors(\n    subtractVectors(a2, projection(u0, a2)),\n    projection(u1, a2)\n  );\n  const u3 = subtractVectors(\n    subtractVectors(\n      subtractVectors(a3, projection(u0, a3)),\n      projection(u1, a3)\n    ),\n    projection(u2, a3)\n  );\n\n  const [e0, e1, e2, e3] = [u0, u1, u2, u3].map((u) =>\n    scaleVector(u, 1 / Math.sqrt(innerProduct(u, u)))\n  );\n\n  const rotationMatrix: AffineMatrix = [\n    [e0[0], e1[0], e2[0], e3[0]],\n    [e0[1], e1[1], e2[1], e3[1]],\n    [e0[2], e1[2], e2[2], e3[2]],\n    [e0[3], e1[3], e2[3], e3[3]],\n  ];\n\n  const skewMatrix: AffineMatrix = [\n    [\n      innerProduct(e0, a0),\n      innerProduct(e0, a1),\n      innerProduct(e0, a2),\n      innerProduct(e0, a3),\n    ],\n    [0, innerProduct(e1, a1), innerProduct(e1, a2), innerProduct(e1, a3)],\n    [0, 0, innerProduct(e2, a2), innerProduct(e2, a3)],\n    [0, 0, 0, innerProduct(e3, a3)],\n  ];\n  return {\n    rotationMatrix: transposeMatrix(rotationMatrix),\n    skewMatrix: transposeMatrix(skewMatrix),\n  };\n}\n\n// ts-prune-ignore-next This function is exported to be tested\nexport function decomposeMatrix(\n  unknownTypeMatrix: AffineMatrixFlat | AffineMatrix\n): TransformMatrixDecomposition {\n  'worklet';\n  const matrix = maybeFlattenMatrix(unknownTypeMatrix);\n\n  // normalize matrix\n  if (matrix[15] === 0) {\n    throw new Error('[Reanimated] Invalid transform matrix.');\n  }\n  matrix.forEach((_, i) => (matrix[i] /= matrix[15]));\n\n  const translationMatrix: AffineMatrix = [\n    [1, 0, 0, 0],\n    [0, 1, 0, 0],\n    [0, 0, 1, 0],\n    [matrix[12], matrix[13], matrix[14], 1],\n  ];\n  const sx = matrix[15] * norm3d(matrix[0], matrix[4], matrix[8]);\n  const sy = matrix[15] * norm3d(matrix[1], matrix[5], matrix[9]);\n  const sz = matrix[15] * norm3d(matrix[2], matrix[6], matrix[10]);\n\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  const scaleMatrix: AffineMatrix = [\n    [sx, 0, 0, 0],\n    [0, sy, 0, 0],\n    [0, 0, sz, 0],\n    [0, 0, 0, 1],\n  ];\n\n  const rotationAndSkewMatrix: AffineMatrix = [\n    [matrix[0] / sx, matrix[1] / sx, matrix[2] / sx, 0],\n    [matrix[4] / sy, matrix[5] / sy, matrix[6] / sy, 0],\n    [matrix[8] / sz, matrix[9] / sz, matrix[10] / sz, 0],\n    [0, 0, 0, 1],\n  ];\n\n  const { rotationMatrix, skewMatrix } = gramSchmidtAlgorithm(\n    rotationAndSkewMatrix\n  );\n\n  return {\n    translationMatrix,\n    scaleMatrix,\n    rotationMatrix,\n    skewMatrix,\n  };\n}\n\nexport function decomposeMatrixIntoMatricesAndAngles(\n  matrix: AffineMatrixFlat | AffineMatrix\n): TansformMatrixDecompositionWithAngles {\n  'worklet';\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  const { scaleMatrix, rotationMatrix, translationMatrix, skewMatrix } =\n    decomposeMatrix(matrix);\n\n  const sinRy = -rotationMatrix[0][2];\n\n  const ry = Math.asin(sinRy);\n  let rx;\n  let rz;\n  if (sinRy === 1 || sinRy === -1) {\n    rz = 0;\n    rx = Math.atan2(sinRy * rotationMatrix[0][1], sinRy * rotationMatrix[0][2]);\n  } else {\n    rz = Math.atan2(rotationMatrix[0][1], rotationMatrix[0][0]);\n    rx = Math.atan2(rotationMatrix[1][2], rotationMatrix[2][2]);\n  }\n\n  return {\n    scaleMatrix,\n    rotationMatrix,\n    translationMatrix,\n    skewMatrix,\n    rx: rx || 0,\n    ry: ry || 0,\n    rz: rz || 0,\n  };\n}\n"], "mappings": "AAAA,YAAY;;AA4BZ,OAAO,SAASA,kBAAkBA,CAACC,CAAU,EAAyB;EACpE,SAAS;;EACT,OACEC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAChBA,CAAC,CAACG,MAAM,KAAK,EAAE,IACfH,CAAC,CAACI,KAAK,CAAEC,OAAO,IAAK,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACD,OAAO,CAAC,CAAC;AAExE;;AAEA;AACA,OAAO,SAASE,cAAcA,CAACP,CAAU,EAAqB;EAC5D,SAAS;;EACT,OACEC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAChBA,CAAC,CAACG,MAAM,KAAK,CAAC,IACdH,CAAC,CAACI,KAAK,CACJI,GAAG,IACFP,KAAK,CAACC,OAAO,CAACM,GAAG,CAAC,IAClBA,GAAG,CAACL,MAAM,KAAK,CAAC,IAChBK,GAAG,CAACJ,KAAK,CAAEC,OAAO,IAAK,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACD,OAAO,CAAC,CACzE,CAAC;AAEL;AAEA,OAAO,SAASI,OAAOA,CAACC,MAAoB,EAAoB;EAC9D,SAAS;;EACT,OAAOA,MAAM,CAACC,IAAI,CAAC,CAAC;AACtB;;AAEA;AACA,OAAO,SAASC,SAASA,CAACC,CAAmB,EAAgB;EAC3D,SAAS;;EACT,OAAO,CACL,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EACxB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EACxB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1B,CAACA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAC7B;AACH;AAEA,SAASC,kBAAkBA,CACzBJ,MAAuC,EACrB;EAClB,SAAS;;EACT,OAAOH,cAAc,CAACG,MAAM,CAAC,GAAGD,OAAO,CAACC,MAAM,CAAC,GAAGA,MAAM;AAC1D;AAEA,OAAO,SAASK,gBAAgBA,CAC9BC,CAAe,EACfC,CAAe,EACD;EACd,SAAS;;EACT,OAAO,CACL,CACED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpB,EACD,CACED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpB,EACD,CACED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpB,EACD,CACED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAEnBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjBD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpB,CACF;AACH;AAEA,OAAO,SAASC,gBAAgBA,CAC9BC,UAAa,EACbC,UAAa,EACV;EACH,SAAS;;EACT,MAAMC,aAAa,GAAGtB,kBAAkB,CAACoB,UAAU,CAAC;EACpD,MAAMH,CAAmB,GAAGF,kBAAkB,CAACK,UAAU,CAAC;EAC1D,MAAMF,CAAmB,GAAGH,kBAAkB,CAACM,UAAU,CAAC;EAE1D,MAAME,CAAC,GAAGN,CAAC,CAACO,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKT,CAAC,CAACS,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,CAAqB;EAC1D,OAAOJ,aAAa,GAAIC,CAAC,GAAUV,SAAS,CAACU,CAAC,CAAO;AACvD;AAEA,OAAO,SAASI,WAAWA,CACzBP,UAAa,EACbC,UAAa,EACV;EACH,SAAS;;EACT,MAAMC,aAAa,GAAGtB,kBAAkB,CAACoB,UAAU,CAAC;EACpD,MAAMH,CAAC,GAAGF,kBAAkB,CAACK,UAAU,CAAC;EACxC,MAAMF,CAAC,GAAGH,kBAAkB,CAACM,UAAU,CAAC;EAExC,MAAME,CAAC,GAAGN,CAAC,CAACO,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKT,CAAC,CAACS,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,CAAqB;EAC1D,OAAOJ,aAAa,GAAIC,CAAC,GAAUV,SAAS,CAACU,CAAC,CAAO;AACvD;AAEA,OAAO,SAASK,WAAWA,CACzBR,UAAa,EACbS,MAAc,EACX;EACH,SAAS;;EACT,MAAMP,aAAa,GAAGtB,kBAAkB,CAACoB,UAAU,CAAC;EACpD,MAAMH,CAAC,GAAGF,kBAAkB,CAACK,UAAU,CAAC;EAExC,MAAMF,CAAC,GAAGD,CAAC,CAACO,GAAG,CAAEvB,CAAC,IAAKA,CAAC,GAAG4B,MAAM,CAAqB;EACtD,OAAOP,aAAa,GAAIJ,CAAC,GAAUL,SAAS,CAACK,CAAC,CAAO;AACvD;AAEA,OAAO,SAASY,iBAAiBA,CAC/BC,KAAa,EACbC,IAAU,GAAG,GAAG,EACF;EACd,SAAS;;EACT,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACF,KAAK,CAAC;EAC3B,MAAMI,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC;EAC3B,QAAQC,IAAI;IACV,KAAK,GAAG;MACN,OAAO,CACL,CAACC,GAAG,EAAEE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAChB,CAAC,CAACA,GAAG,EAAEF,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EACjB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb;IACH,KAAK,GAAG;MACN,OAAO,CACL,CAACA,GAAG,EAAE,CAAC,EAAE,CAACE,GAAG,EAAE,CAAC,CAAC,EACjB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACZ,CAACA,GAAG,EAAE,CAAC,EAAEF,GAAG,EAAE,CAAC,CAAC,EAChB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb;IACH,KAAK,GAAG;MACN,OAAO,CACL,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACZ,CAAC,CAAC,EAAEA,GAAG,EAAEE,GAAG,EAAE,CAAC,CAAC,EAChB,CAAC,CAAC,EAAE,CAACA,GAAG,EAAEF,GAAG,EAAE,CAAC,CAAC,EACjB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb;EACL;AACF;AAEA,SAASG,MAAMA,CAACnC,CAAS,EAAEoC,CAAS,EAAEC,CAAS,EAAE;EAC/C,SAAS;;EACT,OAAOJ,IAAI,CAACK,IAAI,CAACtC,CAAC,GAAGA,CAAC,GAAGoC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;AACzC;AAEA,SAASE,eAAeA,CAAC7B,MAAoB,EAAgB;EAC3D,SAAS;;EACT,MAAMG,CAAC,GAAGJ,OAAO,CAACC,MAAM,CAAC;EACzB,OAAO,CACL,CAACG,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,EACzB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,EACzB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1B,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAC3B;AACH;AAEA,SAAS2B,6BAA6BA,CAACxB,CAAW,EAAEC,CAAW,EAAE;EAC/D,SAAS;;EACT,IAAIwB,OAAO,IAAIzB,CAAC,CAACb,MAAM,KAAKc,CAAC,CAACd,MAAM,EAAE;IACpC,MAAM,IAAIuC,KAAK,CACZ,8FAA6F1B,CAAC,CAAC2B,QAAQ,CAAC,CAAE,OACzG3B,CAAC,CAACb,MACH,kBAAiBc,CAAC,CAAC0B,QAAQ,CAAC,CAAE,OAAM1B,CAAC,CAACd,MAAO,GAChD,CAAC;EACH;AACF;AAEA,SAASyC,YAAYA,CAAC5B,CAAW,EAAEC,CAAW,EAAE;EAC9C,SAAS;;EACTuB,6BAA6B,CAACxB,CAAC,EAAEC,CAAC,CAAC;EACnC,OAAOD,CAAC,CAAC6B,MAAM,CAAC,CAACC,GAAG,EAAEtB,CAAC,EAAEC,CAAC,KAAKqB,GAAG,GAAG9B,CAAC,CAACS,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,EAAE,CAAC,CAAC;AACtD;AAEA,SAASsB,UAAUA,CAACC,CAAW,EAAEhC,CAAW,EAAE;EAC5C,SAAS;;EACTwB,6BAA6B,CAACQ,CAAC,EAAEhC,CAAC,CAAC;EACnC,MAAMiC,CAAC,GAAGL,YAAY,CAACI,CAAC,EAAEhC,CAAC,CAAC,GAAG4B,YAAY,CAACI,CAAC,EAAEA,CAAC,CAAC;EACjD,OAAOA,CAAC,CAACzB,GAAG,CAAE2B,CAAC,IAAKA,CAAC,GAAGD,CAAC,CAAC;AAC5B;AAEA,SAASE,eAAeA,CAACnC,CAAW,EAAEC,CAAW,EAAE;EACjD,SAAS;;EACTuB,6BAA6B,CAACxB,CAAC,EAAEC,CAAC,CAAC;EACnC,OAAOD,CAAC,CAACO,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKT,CAAC,CAACS,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,CAAC;AACrC;AAEA,SAAS2B,WAAWA,CAACJ,CAAW,EAAEhC,CAAS,EAAE;EAC3C,SAAS;;EACT,OAAOgC,CAAC,CAACzB,GAAG,CAAE2B,CAAC,IAAKA,CAAC,GAAGlC,CAAC,CAAC;AAC5B;AAEA,SAASqC,oBAAoBA,CAAC3C,MAAoB,EAGhD;EACA;EACA;EACA;EACA,SAAS;;EACT,MAAM,CAAC4C,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG/C,MAAM;EAE/B,MAAMgD,EAAE,GAAGJ,EAAE;EACb,MAAMK,EAAE,GAAGR,eAAe,CAACI,EAAE,EAAER,UAAU,CAACW,EAAE,EAAEH,EAAE,CAAC,CAAC;EAClD,MAAMK,EAAE,GAAGT,eAAe,CACxBA,eAAe,CAACK,EAAE,EAAET,UAAU,CAACW,EAAE,EAAEF,EAAE,CAAC,CAAC,EACvCT,UAAU,CAACY,EAAE,EAAEH,EAAE,CACnB,CAAC;EACD,MAAMK,EAAE,GAAGV,eAAe,CACxBA,eAAe,CACbA,eAAe,CAACM,EAAE,EAAEV,UAAU,CAACW,EAAE,EAAED,EAAE,CAAC,CAAC,EACvCV,UAAU,CAACY,EAAE,EAAEF,EAAE,CACnB,CAAC,EACDV,UAAU,CAACa,EAAE,EAAEH,EAAE,CACnB,CAAC;EAED,MAAM,CAACK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACP,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACtC,GAAG,CAAEyB,CAAC,IAC9CI,WAAW,CAACJ,CAAC,EAAE,CAAC,GAAGf,IAAI,CAACK,IAAI,CAACM,YAAY,CAACI,CAAC,EAAEA,CAAC,CAAC,CAAC,CAClD,CAAC;EAED,MAAMkB,cAA4B,GAAG,CACnC,CAACJ,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5B,CAACH,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5B,CAACH,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5B,CAACH,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,CAC7B;EAED,MAAME,UAAwB,GAAG,CAC/B,CACEvB,YAAY,CAACkB,EAAE,EAAER,EAAE,CAAC,EACpBV,YAAY,CAACkB,EAAE,EAAEP,EAAE,CAAC,EACpBX,YAAY,CAACkB,EAAE,EAAEN,EAAE,CAAC,EACpBZ,YAAY,CAACkB,EAAE,EAAEL,EAAE,CAAC,CACrB,EACD,CAAC,CAAC,EAAEb,YAAY,CAACmB,EAAE,EAAER,EAAE,CAAC,EAAEX,YAAY,CAACmB,EAAE,EAAEP,EAAE,CAAC,EAAEZ,YAAY,CAACmB,EAAE,EAAEN,EAAE,CAAC,CAAC,EACrE,CAAC,CAAC,EAAE,CAAC,EAAEb,YAAY,CAACoB,EAAE,EAAER,EAAE,CAAC,EAAEZ,YAAY,CAACoB,EAAE,EAAEP,EAAE,CAAC,CAAC,EAClD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEb,YAAY,CAACqB,EAAE,EAAER,EAAE,CAAC,CAAC,CAChC;EACD,OAAO;IACLS,cAAc,EAAE3B,eAAe,CAAC2B,cAAc,CAAC;IAC/CC,UAAU,EAAE5B,eAAe,CAAC4B,UAAU;EACxC,CAAC;AACH;;AAEA;AACA,OAAO,SAASC,eAAeA,CAC7BC,iBAAkD,EACpB;EAC9B,SAAS;;EACT,MAAM3D,MAAM,GAAGI,kBAAkB,CAACuD,iBAAiB,CAAC;;EAEpD;EACA,IAAI3D,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;IACpB,MAAM,IAAIgC,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EACAhC,MAAM,CAAC4D,OAAO,CAAC,CAAC9C,CAAC,EAAEC,CAAC,KAAMf,MAAM,CAACe,CAAC,CAAC,IAAIf,MAAM,CAAC,EAAE,CAAE,CAAC;EAEnD,MAAM6D,iBAA+B,GAAG,CACtC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACZ,CAAC7D,MAAM,CAAC,EAAE,CAAC,EAAEA,MAAM,CAAC,EAAE,CAAC,EAAEA,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACxC;EACD,MAAM8D,EAAE,GAAG9D,MAAM,CAAC,EAAE,CAAC,GAAGyB,MAAM,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM+D,EAAE,GAAG/D,MAAM,CAAC,EAAE,CAAC,GAAGyB,MAAM,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAMgE,EAAE,GAAGhE,MAAM,CAAC,EAAE,CAAC,GAAGyB,MAAM,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,EAAE,CAAC,CAAC;;EAEhE;EACA,MAAMiB,WAAyB,GAAG,CAChC,CAAC6C,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACb,CAAC,CAAC,EAAEC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EACb,CAAC,CAAC,EAAE,CAAC,EAAEC,EAAE,EAAE,CAAC,CAAC,EACb,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb;EAED,MAAMC,qBAAmC,GAAG,CAC1C,CAACjE,MAAM,CAAC,CAAC,CAAC,GAAG8D,EAAE,EAAE9D,MAAM,CAAC,CAAC,CAAC,GAAG8D,EAAE,EAAE9D,MAAM,CAAC,CAAC,CAAC,GAAG8D,EAAE,EAAE,CAAC,CAAC,EACnD,CAAC9D,MAAM,CAAC,CAAC,CAAC,GAAG+D,EAAE,EAAE/D,MAAM,CAAC,CAAC,CAAC,GAAG+D,EAAE,EAAE/D,MAAM,CAAC,CAAC,CAAC,GAAG+D,EAAE,EAAE,CAAC,CAAC,EACnD,CAAC/D,MAAM,CAAC,CAAC,CAAC,GAAGgE,EAAE,EAAEhE,MAAM,CAAC,CAAC,CAAC,GAAGgE,EAAE,EAAEhE,MAAM,CAAC,EAAE,CAAC,GAAGgE,EAAE,EAAE,CAAC,CAAC,EACpD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACb;EAED,MAAM;IAAER,cAAc;IAAEC;EAAW,CAAC,GAAGd,oBAAoB,CACzDsB,qBACF,CAAC;EAED,OAAO;IACLJ,iBAAiB;IACjB5C,WAAW;IACXuC,cAAc;IACdC;EACF,CAAC;AACH;AAEA,OAAO,SAASS,oCAAoCA,CAClDlE,MAAuC,EACA;EACvC,SAAS;;EACT;EACA,MAAM;IAAEiB,WAAW;IAAEuC,cAAc;IAAEK,iBAAiB;IAAEJ;EAAW,CAAC,GAClEC,eAAe,CAAC1D,MAAM,CAAC;EAEzB,MAAMmE,KAAK,GAAG,CAACX,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAEnC,MAAMY,EAAE,GAAG7C,IAAI,CAAC8C,IAAI,CAACF,KAAK,CAAC;EAC3B,IAAIG,EAAE;EACN,IAAIC,EAAE;EACN,IAAIJ,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;IAC/BI,EAAE,GAAG,CAAC;IACND,EAAE,GAAG/C,IAAI,CAACiD,KAAK,CAACL,KAAK,GAAGX,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEW,KAAK,GAAGX,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7E,CAAC,MAAM;IACLe,EAAE,GAAGhD,IAAI,CAACiD,KAAK,CAAChB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3Dc,EAAE,GAAG/C,IAAI,CAACiD,KAAK,CAAChB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D;EAEA,OAAO;IACLvC,WAAW;IACXuC,cAAc;IACdK,iBAAiB;IACjBJ,UAAU;IACVa,EAAE,EAAEA,EAAE,IAAI,CAAC;IACXF,EAAE,EAAEA,EAAE,IAAI,CAAC;IACXG,EAAE,EAAEA,EAAE,IAAI;EACZ,CAAC;AACH", "ignoreList": []}