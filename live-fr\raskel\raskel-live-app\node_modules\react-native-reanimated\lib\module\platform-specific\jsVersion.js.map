{"version": 3, "names": ["jsVersion"], "sources": ["jsVersion.ts"], "sourcesContent": ["'use strict';\n/**\n * We hardcode the version of Reanimated here in order to compare it\n * with the version used to build the native part of the library in runtime.\n * Remember to keep this in sync with the version declared in `package.json`\n */\nexport const jsVersion = '3.15.4';\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,GAAG,QAAQ", "ignoreList": []}