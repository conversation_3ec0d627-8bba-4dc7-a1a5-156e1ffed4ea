{"version": 3, "names": ["jsVersion", "checkCppVersion", "cppVersion", "global", "_REANIMATED_VERSION_CPP", "undefined", "console", "warn", "ok", "matchVersion", "Error", "version1", "version2", "match", "major1", "minor1", "split", "major2", "minor2"], "sources": ["checkCppVersion.ts"], "sourcesContent": ["'use strict';\nimport { jsVersion } from './jsVersion';\n\nexport function checkCppVersion() {\n  const cppVersion = global._REANIMATED_VERSION_CPP;\n  if (cppVersion === undefined) {\n    console.warn(\n      `[Reanimated] Couldn't determine the version of the native part of Reanimated.\n    See \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#couldnt-determine-the-version-of-the-native-part-of-reanimated\\` for more details.`\n    );\n    return;\n  }\n  const ok = matchVersion(jsVersion, cppVersion);\n  if (!ok) {\n    throw new Error(\n      `[Reanimated] Mismatch between JavaScript part and native part of Reanimated (${jsVersion} vs ${cppVersion}).\n    See \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#mismatch-between-javascript-part-and-native-part-of-reanimated\\` for more details.`\n    );\n  }\n}\n\n// This is used only in test files, therefore it is reported by ts-prune (which is desired)\n// ts-prune-ignore-next\nexport function matchVersion(version1: string, version2: string) {\n  if (version1.match(/^\\d+\\.\\d+\\.\\d+$/) && version2.match(/^\\d+\\.\\d+\\.\\d+$/)) {\n    // x.y.z, compare only major and minor, skip patch\n    const [major1, minor1] = version1.split('.');\n    const [major2, minor2] = version2.split('.');\n    return major1 === major2 && minor1 === minor2;\n  } else {\n    // alpha, beta or rc, compare everything\n    return version1 === version2;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,aAAa;AAEvC,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,UAAU,GAAGC,MAAM,CAACC,uBAAuB;EACjD,IAAIF,UAAU,KAAKG,SAAS,EAAE;IAC5BC,OAAO,CAACC,IAAI,CACT;AACP,4KACI,CAAC;IACD;EACF;EACA,MAAMC,EAAE,GAAGC,YAAY,CAACT,SAAS,EAAEE,UAAU,CAAC;EAC9C,IAAI,CAACM,EAAE,EAAE;IACP,MAAM,IAAIE,KAAK,CACZ,gFAA+EV,SAAU,OAAME,UAAW;AACjH,4KACI,CAAC;EACH;AACF;;AAEA;AACA;AACA,OAAO,SAASO,YAAYA,CAACE,QAAgB,EAAEC,QAAgB,EAAE;EAC/D,IAAID,QAAQ,CAACE,KAAK,CAAC,iBAAiB,CAAC,IAAID,QAAQ,CAACC,KAAK,CAAC,iBAAiB,CAAC,EAAE;IAC1E;IACA,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGJ,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC;IAC5C,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGN,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC;IAC5C,OAAOF,MAAM,KAAKG,MAAM,IAAIF,MAAM,KAAKG,MAAM;EAC/C,CAAC,MAAM;IACL;IACA,OAAOP,QAAQ,KAAKC,QAAQ;EAC9B;AACF", "ignoreList": []}