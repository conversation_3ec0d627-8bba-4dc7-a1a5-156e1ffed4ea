{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "ComplexAnimationBuilder", "LinearTransition", "constructor", "args", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "callback", "callbackV", "delay", "get<PERSON>elay", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "createInstance", "Layout"], "sources": ["LinearTransition.ts"], "sourcesContent": ["'use strict';\nimport type { BaseAnimationBuilder } from '../animationBuilder';\nimport { ComplexAnimationBuilder } from '../animationBuilder';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationFunction,\n} from '../animationBuilder/commonTypes';\n\n/**\n * Linearly transforms the layout from one position to another. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `layout` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions#linear-transition\n */\nexport class LinearTransition\n  extends ComplexAnimationBuilder\n  implements ILayoutAnimationBuilder\n{\n  static presetName = 'LinearTransition';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new LinearTransition() as InstanceType<T>;\n  }\n\n  build = (): LayoutAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const callback = this.callbackV;\n    const delay = this.getDelay();\n\n    return (values) => {\n      'worklet';\n      return {\n        initialValues: {\n          originX: values.currentOriginX,\n          originY: values.currentOriginY,\n          width: values.currentWidth,\n          height: values.currentHeight,\n        },\n        animations: {\n          originX: delayFunction(\n            delay,\n            animation(values.targetOriginX, config)\n          ),\n          originY: delayFunction(\n            delay,\n            animation(values.targetOriginY, config)\n          ),\n          width: delayFunction(delay, animation(values.targetWidth, config)),\n          height: delayFunction(delay, animation(values.targetHeight, config)),\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * @deprecated Please use {@link LinearTransition} instead.\n */\nexport const Layout = LinearTransition;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAEb,SAASW,uBAAuB,QAAQ,qBAAqB;AAM7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SACnBD,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAA+B;MACrC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAE7B,OAAQC,MAAM,IAAK;QACjB,SAAS;;QACT,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;YAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;YAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;YAC1BC,MAAM,EAAER,MAAM,CAACS;UACjB,CAAC;UACDC,UAAU,EAAE;YACVR,OAAO,EAAEX,aAAa,CACpBO,KAAK,EACLL,SAAS,CAACO,MAAM,CAACW,aAAa,EAAEjB,MAAM,CACxC,CAAC;YACDU,OAAO,EAAEb,aAAa,CACpBO,KAAK,EACLL,SAAS,CAACO,MAAM,CAACY,aAAa,EAAElB,MAAM,CACxC,CAAC;YACDY,KAAK,EAAEf,aAAa,CAACO,KAAK,EAAEL,SAAS,CAACO,MAAM,CAACa,WAAW,EAAEnB,MAAM,CAAC,CAAC;YAClEc,MAAM,EAAEjB,aAAa,CAACO,KAAK,EAAEL,SAAS,CAACO,MAAM,CAACc,YAAY,EAAEpB,MAAM,CAAC;UACrE,CAAC;UACDE;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EApCD,OAAOmB,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI3B,gBAAgB,CAAC,CAAC;EAC/B;AAiCF;;AAEA;AACA;AACA;AAFAtB,eAAA,CA7CasB,gBAAgB,gBAIP,kBAAkB;AA4CxC,OAAO,MAAM4B,MAAM,GAAG5B,gBAAgB", "ignoreList": []}