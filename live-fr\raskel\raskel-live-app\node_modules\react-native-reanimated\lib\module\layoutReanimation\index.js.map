{"version": 3, "names": [], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nimport './animationsManager';\nexport * from './animationBuilder';\nexport * from './defaultAnimations';\nexport * from './defaultTransitions';\nexport * from './sharedTransitions';\nexport type { KeyframeProps } from './animationBuilder/commonTypes';\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,qBAAqB;AAC5B,cAAc,oBAAoB;AAClC,cAAc,qBAAqB;AACnC,cAAc,sBAAsB;AACpC,cAAc,qBAAqB", "ignoreList": []}