{"version": 3, "names": ["processColorsInProps", "_updatePropsJS", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "runOnUIImmediately", "updateProps", "viewDescriptors", "updates", "isAnimatedProps", "_viewDescriptors$valu", "value", "for<PERSON>ach", "viewDescriptor", "component", "tag", "global", "UpdatePropsManager", "update", "updatePropsJestWrapper", "animatedStyle", "adapters", "adapter", "current", "createUpdatePropsManager", "operations", "push", "shadowNodeWrapper", "length", "queueMicrotask", "flush", "_updatePropsFabric", "name", "_updatePropsPaper", "maybeThrowError", "Error", "Proxy", "get", "set"], "sources": ["UpdateProps.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n'use strict';\nimport type { MutableRefObject } from 'react';\nimport { processColorsInProps } from './Colors';\nimport type {\n  ShadowNodeWrapper,\n  SharedValue,\n  StyleProps,\n  AnimatedStyle,\n} from './commonTypes';\nimport type { Descriptor } from './hook/commonTypes';\nimport type { ReanimatedHTMLElement } from './js-reanimated';\nimport { _updatePropsJS } from './js-reanimated';\nimport { isFabric, isJest, shouldBeUseWeb } from './PlatformChecker';\nimport { runOnUIImmediately } from './threads';\n\nlet updateProps: (\n  viewDescriptor: SharedValue<Descriptor[]>,\n  updates: StyleProps | AnimatedStyle<any>,\n  isAnimatedProps?: boolean\n) => void;\n\nif (shouldBeUseWeb()) {\n  updateProps = (viewDescriptors, updates, isAnimatedProps) => {\n    'worklet';\n    viewDescriptors.value?.forEach((viewDescriptor) => {\n      const component = viewDescriptor.tag as ReanimatedHTMLElement;\n      _updatePropsJS(updates, component, isAnimatedProps);\n    });\n  };\n} else {\n  updateProps = (viewDescriptors, updates) => {\n    'worklet';\n    processColorsInProps(updates);\n    global.UpdatePropsManager.update(viewDescriptors, updates);\n  };\n}\n\nexport const updatePropsJestWrapper = (\n  viewDescriptors: SharedValue<Descriptor[]>,\n  updates: AnimatedStyle<any>,\n  animatedStyle: MutableRefObject<AnimatedStyle<any>>,\n  adapters: ((updates: AnimatedStyle<any>) => void)[]\n): void => {\n  adapters.forEach((adapter) => {\n    adapter(updates);\n  });\n  animatedStyle.current.value = {\n    ...animatedStyle.current.value,\n    ...updates,\n  };\n\n  updateProps(viewDescriptors, updates);\n};\n\nexport default updateProps;\n\nconst createUpdatePropsManager = isFabric()\n  ? () => {\n      'worklet';\n      // Fabric\n      const operations: {\n        shadowNodeWrapper: ShadowNodeWrapper;\n        updates: StyleProps | AnimatedStyle<any>;\n      }[] = [];\n      return {\n        update(\n          viewDescriptors: SharedValue<Descriptor[]>,\n          updates: StyleProps | AnimatedStyle<any>\n        ) {\n          viewDescriptors.value.forEach((viewDescriptor) => {\n            operations.push({\n              shadowNodeWrapper: viewDescriptor.shadowNodeWrapper,\n              updates,\n            });\n            if (operations.length === 1) {\n              queueMicrotask(this.flush);\n            }\n          });\n        },\n        flush(this: void) {\n          global._updatePropsFabric!(operations);\n          operations.length = 0;\n        },\n      };\n    }\n  : () => {\n      'worklet';\n      // Paper\n      const operations: {\n        tag: number;\n        name: string;\n        updates: StyleProps | AnimatedStyle<any>;\n      }[] = [];\n      return {\n        update(\n          viewDescriptors: SharedValue<Descriptor[]>,\n          updates: StyleProps | AnimatedStyle<any>\n        ) {\n          viewDescriptors.value.forEach((viewDescriptor) => {\n            operations.push({\n              tag: viewDescriptor.tag as number,\n              name: viewDescriptor.name || 'RCTView',\n              updates,\n            });\n            if (operations.length === 1) {\n              queueMicrotask(this.flush);\n            }\n          });\n        },\n        flush(this: void) {\n          global._updatePropsPaper!(operations);\n          operations.length = 0;\n        },\n      };\n    };\n\nif (shouldBeUseWeb()) {\n  const maybeThrowError = () => {\n    // Jest attempts to access a property of this object to check if it is a Jest mock\n    // so we can't throw an error in the getter.\n    if (!isJest()) {\n      throw new Error(\n        '[Reanimated] `UpdatePropsManager` is not available on non-native platform.'\n      );\n    }\n  };\n  global.UpdatePropsManager = new Proxy({} as UpdatePropsManager, {\n    get: maybeThrowError,\n    set: () => {\n      maybeThrowError();\n      return false;\n    },\n  });\n} else {\n  runOnUIImmediately(() => {\n    'worklet';\n    global.UpdatePropsManager = createUpdatePropsManager();\n  })();\n}\n\nexport interface UpdatePropsManager {\n  update(\n    viewDescriptors: SharedValue<Descriptor[]>,\n    updates: StyleProps | AnimatedStyle<any>\n  ): void;\n  flush(): void;\n}\n"], "mappings": "AAAA;AACA,YAAY;;AAEZ,SAASA,oBAAoB,QAAQ,UAAU;AAS/C,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,QAAQ,mBAAmB;AACpE,SAASC,kBAAkB,QAAQ,WAAW;AAE9C,IAAIC,WAIK;AAET,IAAIF,cAAc,CAAC,CAAC,EAAE;EACpBE,WAAW,GAAGA,CAACC,eAAe,EAAEC,OAAO,EAAEC,eAAe,KAAK;IAC3D,SAAS;;IAAC,IAAAC,qBAAA;IACV,CAAAA,qBAAA,GAAAH,eAAe,CAACI,KAAK,cAAAD,qBAAA,eAArBA,qBAAA,CAAuBE,OAAO,CAAEC,cAAc,IAAK;MACjD,MAAMC,SAAS,GAAGD,cAAc,CAACE,GAA4B;MAC7Dd,cAAc,CAACO,OAAO,EAAEM,SAAS,EAAEL,eAAe,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,MAAM;EACLH,WAAW,GAAGA,CAACC,eAAe,EAAEC,OAAO,KAAK;IAC1C,SAAS;;IACTR,oBAAoB,CAACQ,OAAO,CAAC;IAC7BQ,MAAM,CAACC,kBAAkB,CAACC,MAAM,CAACX,eAAe,EAAEC,OAAO,CAAC;EAC5D,CAAC;AACH;AAEA,OAAO,MAAMW,sBAAsB,GAAGA,CACpCZ,eAA0C,EAC1CC,OAA2B,EAC3BY,aAAmD,EACnDC,QAAmD,KAC1C;EACTA,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;IAC5BA,OAAO,CAACd,OAAO,CAAC;EAClB,CAAC,CAAC;EACFY,aAAa,CAACG,OAAO,CAACZ,KAAK,GAAG;IAC5B,GAAGS,aAAa,CAACG,OAAO,CAACZ,KAAK;IAC9B,GAAGH;EACL,CAAC;EAEDF,WAAW,CAACC,eAAe,EAAEC,OAAO,CAAC;AACvC,CAAC;AAED,eAAeF,WAAW;AAE1B,MAAMkB,wBAAwB,GAAGtB,QAAQ,CAAC,CAAC,GACvC,MAAM;EACJ,SAAS;;EACT;EACA,MAAMuB,UAGH,GAAG,EAAE;EACR,OAAO;IACLP,MAAMA,CACJX,eAA0C,EAC1CC,OAAwC,EACxC;MACAD,eAAe,CAACI,KAAK,CAACC,OAAO,CAAEC,cAAc,IAAK;QAChDY,UAAU,CAACC,IAAI,CAAC;UACdC,iBAAiB,EAAEd,cAAc,CAACc,iBAAiB;UACnDnB;QACF,CAAC,CAAC;QACF,IAAIiB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBd,MAAM,CAACe,kBAAkB,CAAEN,UAAU,CAAC;MACtCA,UAAU,CAACG,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC,GACD,MAAM;EACJ,SAAS;;EACT;EACA,MAAMH,UAIH,GAAG,EAAE;EACR,OAAO;IACLP,MAAMA,CACJX,eAA0C,EAC1CC,OAAwC,EACxC;MACAD,eAAe,CAACI,KAAK,CAACC,OAAO,CAAEC,cAAc,IAAK;QAChDY,UAAU,CAACC,IAAI,CAAC;UACdX,GAAG,EAAEF,cAAc,CAACE,GAAa;UACjCiB,IAAI,EAAEnB,cAAc,CAACmB,IAAI,IAAI,SAAS;UACtCxB;QACF,CAAC,CAAC;QACF,IAAIiB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBd,MAAM,CAACiB,iBAAiB,CAAER,UAAU,CAAC;MACrCA,UAAU,CAACG,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC;AAEL,IAAIxB,cAAc,CAAC,CAAC,EAAE;EACpB,MAAM8B,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAAC/B,MAAM,CAAC,CAAC,EAAE;MACb,MAAM,IAAIgC,KAAK,CACb,4EACF,CAAC;IACH;EACF,CAAC;EACDnB,MAAM,CAACC,kBAAkB,GAAG,IAAImB,KAAK,CAAC,CAAC,CAAC,EAAwB;IAC9DC,GAAG,EAAEH,eAAe;IACpBI,GAAG,EAAEA,CAAA,KAAM;MACTJ,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACL7B,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTW,MAAM,CAACC,kBAAkB,GAAGO,wBAAwB,CAAC,CAAC;EACxD,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}