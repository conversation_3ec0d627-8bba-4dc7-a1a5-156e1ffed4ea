{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_LIGHTSPEED_TIME", "LightSpeedInData", "LightSpeedInRight", "name", "style", "transform", "translateX", "skewX", "opacity", "duration", "LightSpeedInLeft", "LightSpeedOutData", "LightSpeedOutRight", "LightSpeedOutLeft", "skew", "LightSpeedIn", "LightSpeedOut"], "sources": ["Lightspeed.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_LIGHTSPEED_TIME = 0.3;\n\nexport const LightSpeedInData = {\n  LightSpeedInRight: {\n    name: 'LightSpeedInRight',\n    style: {\n      0: {\n        transform: [{ translateX: '100vw', skewX: '-45deg' }],\n        opacity: 0,\n      },\n      70: { transform: [{ skewX: '10deg' }] },\n      85: { transform: [{ skewX: '-5deg' }] },\n      100: { transform: [{ skewX: '0deg' }] },\n    },\n    duration: DEFAULT_LIGHTSPEED_TIME,\n  },\n\n  LightSpeedInLeft: {\n    name: 'LightSpeedInLeft',\n    style: {\n      0: {\n        transform: [{ translateX: '-100vw', skewX: '45deg' }],\n        opacity: 0,\n      },\n      70: { transform: [{ skewX: '-10deg' }] },\n      85: { transform: [{ skewX: '5deg' }] },\n      100: { transform: [{ skewX: '0deg' }] },\n    },\n    duration: DEFAULT_LIGHTSPEED_TIME,\n  },\n};\n\nexport const LightSpeedOutData = {\n  LightSpeedOutRight: {\n    name: 'LightSpeedOutRight',\n    style: {\n      0: {\n        transform: [{ translateX: '0vw', skewX: '0deg' }],\n        opacity: 1,\n      },\n      100: {\n        transform: [{ translateX: '100vw', skewX: '-45deg' }],\n        opacity: 0,\n      },\n    },\n    duration: DEFAULT_LIGHTSPEED_TIME,\n  },\n\n  LightSpeedOutLeft: {\n    name: 'LightSpeedOutLeft',\n    style: {\n      0: {\n        transform: [{ translateX: '0vw', skew: '0deg' }],\n        opacity: 1,\n      },\n      100: {\n        transform: [{ translateX: '-100vw', skew: '45deg' }],\n        opacity: 0,\n      },\n    },\n    duration: DEFAULT_LIGHTSPEED_TIME,\n  },\n};\n\nexport const LightSpeedIn = {\n  LightSpeedInRight: {\n    style: convertAnimationObjectToKeyframes(\n      LightSpeedInData.LightSpeedInRight\n    ),\n    duration: LightSpeedInData.LightSpeedInRight.duration,\n  },\n  LightSpeedInLeft: {\n    style: convertAnimationObjectToKeyframes(LightSpeedInData.LightSpeedInLeft),\n    duration: LightSpeedInData.LightSpeedInLeft.duration,\n  },\n};\n\nexport const LightSpeedOut = {\n  LightSpeedOutRight: {\n    style: convertAnimationObjectToKeyframes(\n      LightSpeedOutData.LightSpeedOutRight\n    ),\n    duration: LightSpeedOutData.LightSpeedOutRight.duration,\n  },\n  LightSpeedOutLeft: {\n    style: convertAnimationObjectToKeyframes(\n      LightSpeedOutData.LightSpeedOutLeft\n    ),\n    duration: LightSpeedOutData.LightSpeedOutLeft.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,uBAAuB,GAAG,GAAG;AAEnC,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,iBAAiB,EAAE;IACjBC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAQ,CAAC;MAAE,CAAC;MACvC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAQ,CAAC;MAAE,CAAC;MACvC,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE;IACxC,CAAC;IACDE,QAAQ,EAAET;EACZ,CAAC;EAEDU,gBAAgB,EAAE;IAChBP,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAS,CAAC;MAAE,CAAC;MACxC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE,CAAC;MACtC,GAAG,EAAE;QAAEF,SAAS,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAO,CAAC;MAAE;IACxC,CAAC;IACDE,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMW,iBAAiB,GAAG;EAC/BC,kBAAkB,EAAE;IAClBT,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAO,CAAC,CAAC;QACjDC,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACrDC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDa,iBAAiB,EAAE;IACjBV,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,KAAK;UAAEQ,IAAI,EAAE;QAAO,CAAC,CAAC;QAChDN,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAE,QAAQ;UAAEQ,IAAI,EAAE;QAAQ,CAAC,CAAC;QACpDN,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMe,YAAY,GAAG;EAC1Bb,iBAAiB,EAAE;IACjBE,KAAK,EAAEL,iCAAiC,CACtCE,gBAAgB,CAACC,iBACnB,CAAC;IACDO,QAAQ,EAAER,gBAAgB,CAACC,iBAAiB,CAACO;EAC/C,CAAC;EACDC,gBAAgB,EAAE;IAChBN,KAAK,EAAEL,iCAAiC,CAACE,gBAAgB,CAACS,gBAAgB,CAAC;IAC3ED,QAAQ,EAAER,gBAAgB,CAACS,gBAAgB,CAACD;EAC9C;AACF,CAAC;AAED,OAAO,MAAMO,aAAa,GAAG;EAC3BJ,kBAAkB,EAAE;IAClBR,KAAK,EAAEL,iCAAiC,CACtCY,iBAAiB,CAACC,kBACpB,CAAC;IACDH,QAAQ,EAAEE,iBAAiB,CAACC,kBAAkB,CAACH;EACjD,CAAC;EACDI,iBAAiB,EAAE;IACjBT,KAAK,EAAEL,iCAAiC,CACtCY,iBAAiB,CAACE,iBACpB,CAAC;IACDJ,QAAQ,EAAEE,iBAAiB,CAACE,iBAAiB,CAACJ;EAChD;AACF,CAAC", "ignoreList": []}