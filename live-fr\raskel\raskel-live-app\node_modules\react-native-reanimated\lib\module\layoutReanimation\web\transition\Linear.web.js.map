{"version": 3, "names": ["LinearTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "linearTransition", "style", "transform", "scale", "duration"], "sources": ["Linear.web.ts"], "sourcesContent": ["'use strict';\nimport type { TransitionData } from '../animationParser';\n\nexport function LinearTransition(name: string, transitionData: TransitionData) {\n  const { translateX, translateY, scaleX, scaleY } = transitionData;\n\n  const linearTransition = {\n    name,\n    style: {\n      0: {\n        transform: [\n          {\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`,\n          },\n        ],\n      },\n    },\n    duration: 300,\n  };\n\n  return linearTransition;\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,gBAAgBA,CAACC,IAAY,EAAEC,cAA8B,EAAE;EAC7E,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGJ,cAAc;EAEjE,MAAMK,gBAAgB,GAAG;IACvBN,IAAI;IACJO,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEN,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BC,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BM,KAAK,EAAG,GAAEL,MAAO,IAAGC,MAAO;QAC7B,CAAC;MAEL;IACF,CAAC;IACDK,QAAQ,EAAE;EACZ,CAAC;EAED,OAAOJ,gBAAgB;AACzB", "ignoreList": []}