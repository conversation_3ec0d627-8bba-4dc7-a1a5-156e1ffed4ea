{"version": 3, "names": ["scrollTo", "animatedRef", "x", "y", "animated", "element", "scrollView"], "sources": ["scrollTo.web.ts"], "sourcesContent": ["'use strict';\nimport type { AnimatedRef } from '../hook/commonTypes';\nimport type { Component } from 'react';\nimport type { ScrollView } from 'react-native';\n\nexport function scrollTo<T extends Component>(\n  animatedRef: AnimatedRef<T>,\n  x: number,\n  y: number,\n  animated: boolean\n) {\n  const element = animatedRef();\n\n  // This prevents crashes if ref has not been set yet\n  if (element !== -1) {\n    // By ScrollView we mean any scrollable component\n    const scrollView = element as HTMLElement as unknown as ScrollView;\n    scrollView?.scrollTo({ x, y, animated });\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAKZ,OAAO,SAASA,QAAQA,CACtBC,WAA2B,EAC3BC,CAAS,EACTC,CAAS,EACTC,QAAiB,EACjB;EACA,MAAMC,OAAO,GAAGJ,WAAW,CAAC,CAAC;;EAE7B;EACA,IAAII,OAAO,KAAK,CAAC,CAAC,EAAE;IAClB;IACA,MAAMC,UAAU,GAAGD,OAA+C;IAClEC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEN,QAAQ,CAAC;MAAEE,CAAC;MAAEC,CAAC;MAAEC;IAAS,CAAC,CAAC;EAC1C;AACF", "ignoreList": []}