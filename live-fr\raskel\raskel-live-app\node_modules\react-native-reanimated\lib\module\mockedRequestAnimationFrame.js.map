{"version": 3, "names": ["mockedRequestAnimationFrame", "callback", "setTimeout", "performance", "now"], "sources": ["mockedRequestAnimationFrame.ts"], "sourcesContent": ["'use strict';\n\n// This is Jest implementation of `requestAnimationFrame` that is required\n// by React Native for test purposes.\nexport function mockedRequestAnimationFrame(\n  callback: (timestamp: number) => void\n): ReturnType<typeof setTimeout> {\n  return setTimeout(() => callback(performance.now()), 0);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA,OAAO,SAASA,2BAA2BA,CACzCC,QAAqC,EACN;EAC/B,OAAOC,UAAU,CAAC,MAAMD,QAAQ,CAACE,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACzD", "ignoreList": []}