{"buildFiles": ["C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\43k1a3c3\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\43k1a3c3\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\43k1a3c3\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\43k1a3c3\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\43k1a3c3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\43k1a3c3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"expo-modules-core::@6890427a1f51a3e7e1df": {"artifactName": "expo-modules-core", "abi": "x86_64", "output": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\43k1a3c3\\obj\\x86_64\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9595d0aeab151a9804a8af82bcc909\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9371b04a7bfb42fb4dbc04cf90f62c75\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}}}