{"version": 3, "names": ["Easing", "JumpingTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "d", "Math", "max", "abs", "peakTranslateY", "jumpingTransition", "style", "transform", "scale", "easing", "exp", "duration"], "sources": ["Jumping.web.ts"], "sourcesContent": ["'use strict';\nimport type { TransitionData } from '../animationParser';\nimport { Easing } from '../../../Easing';\n\nexport function JumpingTransition(\n  name: string,\n  transitionData: TransitionData\n) {\n  const { translateX, translateY, scaleX, scaleY } = transitionData;\n\n  const d = Math.max(Math.abs(translateX), Math.abs(translateY)) / 2;\n  const peakTranslateY = translateY <= 0 ? translateY - d : -translateY + d;\n\n  const jumpingTransition = {\n    name,\n    style: {\n      0: {\n        transform: [\n          {\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`,\n          },\n        ],\n        easing: Easing.exp,\n      },\n      50: {\n        transform: [\n          {\n            translateX: `${translateX / 2}px`,\n            translateY: `${peakTranslateY}px`,\n            scale: `${scaleX},${scaleY}`,\n          },\n        ],\n      },\n      100: {\n        transform: [{ translateX: '0px', translateY: '0px', scale: '1,1' }],\n      },\n    },\n    duration: 300,\n  };\n\n  return jumpingTransition;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,iBAAiB;AAExC,OAAO,SAASC,iBAAiBA,CAC/BC,IAAY,EACZC,cAA8B,EAC9B;EACA,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGJ,cAAc;EAEjE,MAAMK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACP,UAAU,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACN,UAAU,CAAC,CAAC,GAAG,CAAC;EAClE,MAAMO,cAAc,GAAGP,UAAU,IAAI,CAAC,GAAGA,UAAU,GAAGG,CAAC,GAAG,CAACH,UAAU,GAAGG,CAAC;EAEzE,MAAMK,iBAAiB,GAAG;IACxBX,IAAI;IACJY,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEX,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BC,UAAU,EAAG,GAAEA,UAAW,IAAG;UAC7BW,KAAK,EAAG,GAAEV,MAAO,IAAGC,MAAO;QAC7B,CAAC,CACF;QACDU,MAAM,EAAEjB,MAAM,CAACkB;MACjB,CAAC;MACD,EAAE,EAAE;QACFH,SAAS,EAAE,CACT;UACEX,UAAU,EAAG,GAAEA,UAAU,GAAG,CAAE,IAAG;UACjCC,UAAU,EAAG,GAAEO,cAAe,IAAG;UACjCI,KAAK,EAAG,GAAEV,MAAO,IAAGC,MAAO;QAC7B,CAAC;MAEL,CAAC;MACD,GAAG,EAAE;QACHQ,SAAS,EAAE,CAAC;UAAEX,UAAU,EAAE,KAAK;UAAEC,UAAU,EAAE,KAAK;UAAEW,KAAK,EAAE;QAAM,CAAC;MACpE;IACF,CAAC;IACDG,QAAQ,EAAE;EACZ,CAAC;EAED,OAAON,iBAAiB;AAC1B", "ignoreList": []}