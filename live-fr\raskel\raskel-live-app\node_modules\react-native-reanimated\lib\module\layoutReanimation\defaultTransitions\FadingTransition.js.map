{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON>", "withSequence", "withTiming", "BaseAnimationBuilder", "FadingTransition", "constructor", "args", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "halfDuration", "durationV", "values", "initialValues", "opacity", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "duration", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "createInstance"], "sources": ["FadingTransition.ts"], "sourcesContent": ["'use strict';\nimport { withDelay, withSequence, withTiming } from '../../animation';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationFunction,\n} from '../animationBuilder/commonTypes';\nimport { BaseAnimationBuilder } from '../animationBuilder';\n\n/**\n * Fades out components from one position and shows them in another. You can modify the behavior by chaining methods like `.duration(500)` or `.delay(500)`.\n *\n * You pass it to the `layout` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions#fading-transition\n */\nexport class FadingTransition\n  extends BaseAnimationBuilder\n  implements ILayoutAnimationBuilder\n{\n  static presetName = 'FadingTransition';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new FadingTransition() as InstanceType<T>;\n  }\n\n  build = (): LayoutAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const callback = this.callbackV;\n    const delay = this.getDelay();\n    const halfDuration = (this.durationV ?? 500) / 2;\n\n    return (values) => {\n      'worklet';\n      return {\n        initialValues: {\n          opacity: 1,\n          originX: values.currentOriginX,\n          originY: values.currentOriginY,\n          width: values.currentWidth,\n          height: values.currentHeight,\n        },\n        animations: {\n          opacity: delayFunction(\n            delay,\n            withSequence(\n              withTiming(0, { duration: halfDuration }),\n              withTiming(1, { duration: halfDuration })\n            )\n          ),\n          originX: withDelay(\n            delay + halfDuration,\n            withTiming(values.targetOriginX, { duration: 0 })\n          ),\n          originY: withDelay(\n            delay + halfDuration,\n            withTiming(values.targetOriginY, { duration: 0 })\n          ),\n          width: withDelay(\n            delay + halfDuration,\n            withTiming(values.targetWidth, { duration: 0 })\n          ),\n          height: withDelay(\n            delay + halfDuration,\n            withTiming(values.targetHeight, { duration: 0 })\n          ),\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAKrE,SAASC,oBAAoB,QAAQ,qBAAqB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SACnBD,oBAAoB,CAE9B;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA3B,eAAA,gBASU,MAA+B;MACrC,MAAM4B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACC,SAAS,IAAI,GAAG,IAAI,CAAC;MAEhD,OAAQC,MAAM,IAAK;QACjB,SAAS;;QACT,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAE,CAAC;YACVC,OAAO,EAAEH,MAAM,CAACI,cAAc;YAC9BC,OAAO,EAAEL,MAAM,CAACM,cAAc;YAC9BC,KAAK,EAAEP,MAAM,CAACQ,YAAY;YAC1BC,MAAM,EAAET,MAAM,CAACU;UACjB,CAAC;UACDC,UAAU,EAAE;YACVT,OAAO,EAAEV,aAAa,CACpBI,KAAK,EACLV,YAAY,CACVC,UAAU,CAAC,CAAC,EAAE;cAAEyB,QAAQ,EAAEd;YAAa,CAAC,CAAC,EACzCX,UAAU,CAAC,CAAC,EAAE;cAAEyB,QAAQ,EAAEd;YAAa,CAAC,CAC1C,CACF,CAAC;YACDK,OAAO,EAAElB,SAAS,CAChBW,KAAK,GAAGE,YAAY,EACpBX,UAAU,CAACa,MAAM,CAACa,aAAa,EAAE;cAAED,QAAQ,EAAE;YAAE,CAAC,CAClD,CAAC;YACDP,OAAO,EAAEpB,SAAS,CAChBW,KAAK,GAAGE,YAAY,EACpBX,UAAU,CAACa,MAAM,CAACc,aAAa,EAAE;cAAEF,QAAQ,EAAE;YAAE,CAAC,CAClD,CAAC;YACDL,KAAK,EAAEtB,SAAS,CACdW,KAAK,GAAGE,YAAY,EACpBX,UAAU,CAACa,MAAM,CAACe,WAAW,EAAE;cAAEH,QAAQ,EAAE;YAAE,CAAC,CAChD,CAAC;YACDH,MAAM,EAAExB,SAAS,CACfW,KAAK,GAAGE,YAAY,EACpBX,UAAU,CAACa,MAAM,CAACgB,YAAY,EAAE;cAAEJ,QAAQ,EAAE;YAAE,CAAC,CACjD;UACF,CAAC;UACDlB;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAlDD,OAAOuB,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI5B,gBAAgB,CAAC,CAAC;EAC/B;AA+CF;AAACzB,eAAA,CAzDYyB,gBAAgB,gBAIP,kBAAkB", "ignoreList": []}