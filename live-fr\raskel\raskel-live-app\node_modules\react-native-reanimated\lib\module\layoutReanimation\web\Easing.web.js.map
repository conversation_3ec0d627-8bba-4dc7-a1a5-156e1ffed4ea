{"version": 3, "names": ["WebEasings", "linear", "ease", "quad", "cubic", "sin", "circle", "exp", "getEasingByName", "easingName", "toString"], "sources": ["Easing.web.ts"], "sourcesContent": ["'use strict';\n\n// Those are the easings that can be implemented using Bezier curves.\n// Others should be done as CSS animations\nexport const WebEasings = {\n  linear: [0, 0, 1, 1],\n  ease: [0.42, 0, 1, 1],\n  quad: [0.11, 0, 0.5, 0],\n  cubic: [0.32, 0, 0.67, 0],\n  sin: [0.12, 0, 0.39, 0],\n  circle: [0.55, 0, 1, 0.45],\n  exp: [0.7, 0, 0.84, 0],\n};\n\nexport function getEasingByName(easingName: WebEasingsNames) {\n  return `cubic-bezier(${WebEasings[easingName].toString()})`;\n}\n\nexport type WebEasingsNames = keyof typeof WebEasings;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA,OAAO,MAAMA,UAAU,GAAG;EACxBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrBC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EACvBC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACzBC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACvBC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC1BC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AACvB,CAAC;AAED,OAAO,SAASC,eAAeA,CAACC,UAA2B,EAAE;EAC3D,OAAQ,gBAAeT,UAAU,CAACS,UAAU,CAAC,CAACC,QAAQ,CAAC,CAAE,GAAE;AAC7D", "ignoreList": []}