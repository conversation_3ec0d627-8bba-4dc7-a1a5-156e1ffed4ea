module.exports = {
  AIRMapCalloutManager: {},
  AIRMapCalloutSubviewManager: {},
  AIRMapCircleManager: {},
  AIRMapLocalTileManager: {},
  AIRMapManager: {
    animateCamera: { type: 'function', functionType: 'async' },
    animateToRegion: { type: 'function', functionType: 'async' },
    coordinateForPoint: { type: 'function', functionType: 'promise' },
    fitToCoordinates: { type: 'function', functionType: 'async' },
    fitToElements: { type: 'function', functionType: 'async' },
    fitToSuppliedMarkers: { type: 'function', functionType: 'async' },
    getAddressFromCoordinates: { type: 'function', functionType: 'promise' },
    getCamera: { type: 'function', functionType: 'promise' },
    getConstants: { type: 'function' },
    getMapBoundaries: { type: 'function', functionType: 'promise' },
    getMarkersFrames: { type: 'function', functionType: 'promise' },
    pointForCoordinate: { type: 'function', functionType: 'promise' },
    setCamera: { type: 'function', functionType: 'async' },
    takeSnapshot: { type: 'function', functionType: 'async' },
  },
  AIRMapMarkerManager: {
    getConstants: { type: 'function' },
    hideCallout: { type: 'function', functionType: 'async' },
    redrawCallout: { type: 'function', functionType: 'async' },
    showCallout: { type: 'function', functionType: 'async' },
  },
  AIRMapOverlayManager: {},
  AIRMapPolygonManager: {},
  AIRMapPolylineManager: {},
  AIRMapUrlTileManager: {},
  AIRMapWMSTileManager: {},
  ExpoModulesCore: {
    getConstants: { type: 'function' },
    installModules: { type: 'function', functionType: 'sync' },
  },
  'ExpoModulesCore.ViewModuleWrapper': {},
  ExponentScopedModuleRegistry: {},
  ExponentTest: {
    action: { type: 'function', functionType: 'promise' },
    completed: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    isInCI: { type: 'number', mock: 0 },
    log: { type: 'function', functionType: 'async' },
    shouldSkipTestsRequiringPermissionsAsync: { type: 'function', functionType: 'promise' },
  },
  ExponentUtil: {},
  LottieAnimationView: {
    getConstants: { type: 'function' },
    pause: { type: 'function', functionType: 'async' },
    play: { type: 'function', functionType: 'async' },
    reset: { type: 'function', functionType: 'async' },
    resume: { type: 'function', functionType: 'async' },
    VERSION: { type: 'number', mock: 1 },
  },
  ReanimatedModule: {
    addListener: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    installTurboModule: { type: 'function', functionType: 'sync' },
    removeListeners: { type: 'function', functionType: 'async' },
  },
  RNAWSCognito: {
    computeModPow: { type: 'function', functionType: 'async' },
    computeS: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    getRandomBase64: { type: 'function', functionType: 'sync' },
  },
  RNCMaskedViewManager: {},
  RNCNetInfo: {
    addListener: { type: 'function', functionType: 'async' },
    configure: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    getCurrentState: { type: 'function', functionType: 'promise' },
    removeListeners: { type: 'function', functionType: 'async' },
  },
  RNCPickerManager: {},
  RNCSafeAreaContext: {
    getConstants: { type: 'function' },
    initialWindowMetrics: { type: 'object' },
  },
  RNCSafeAreaProvider: {},
  RNCSafeAreaView: {},
  RNCSegmentedControlManager: {},
  RNCSliderManager: {},
  RNCViewPager: {
    getConstants: { type: 'function' },
    setPage: { type: 'function', functionType: 'async' },
    setPageWithoutAnimation: { type: 'function', functionType: 'async' },
    setScrollEnabledImperatively: { type: 'function', functionType: 'async' },
  },
  RNCWebView: {
    clearCache: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    goBack: { type: 'function', functionType: 'async' },
    goForward: { type: 'function', functionType: 'async' },
    injectJavaScript: { type: 'function', functionType: 'async' },
    postMessage: { type: 'function', functionType: 'async' },
    reload: { type: 'function', functionType: 'async' },
    requestFocus: { type: 'function', functionType: 'async' },
    shouldStartLoadWithLockIdentifier: { type: 'function', functionType: 'async' },
    stopLoading: { type: 'function', functionType: 'async' },
  },
  RNDateTimePickerManager: {},
  RNEdgeToEdge: {
    onColorSchemeChange: { type: 'function', functionType: 'sync' },
    setStatusBarStyle: { type: 'function', functionType: 'sync' },
    setNavigationBarStyle: { type: 'function', functionType: 'sync' },
    setStatusBarHidden: { type: 'function', functionType: 'sync' },
    setNavigationBarHidden: { type: 'function', functionType: 'sync' },
  },
  RNGestureHandlerButton: {},
  RNGestureHandlerModule: {
    addListener: { type: 'function', functionType: 'async' },
    attachGestureHandler: { type: 'function', functionType: 'async' },
    createGestureHandler: { type: 'function', functionType: 'async' },
    Direction: { type: 'object' },
    dropGestureHandler: { type: 'function', functionType: 'async' },
    flushOperations: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    handleClearJSResponder: { type: 'function', functionType: 'async' },
    handleSetJSResponder: { type: 'function', functionType: 'async' },
    removeListeners: { type: 'function', functionType: 'async' },
    State: { type: 'object' },
    updateGestureHandler: { type: 'function', functionType: 'async' },
  },
  RNSFullWindowOverlayManager: {},
  RNSkiaModule: {
    getConstants: { type: 'function' },
    install: { type: 'function', functionType: 'sync' },
  },
  RNSModalScreenManager: {},
  RNSModule: {},
  RNSScreenContainerManager: {},
  RNSScreenManager: {},
  RNSScreenNavigationContainerManager: {},
  RNSScreenStackHeaderConfigManager: {},
  RNSScreenStackHeaderSubviewManager: {},
  RNSScreenStackManager: {},
  RNSSearchBarManager: {
    blur: { type: 'function', functionType: 'async' },
    cancelSearch: { type: 'function', functionType: 'async' },
    clearText: { type: 'function', functionType: 'async' },
    focus: { type: 'function', functionType: 'async' },
    getConstants: { type: 'function' },
    setText: { type: 'function', functionType: 'async' },
    toggleCancelButton: { type: 'function', functionType: 'async' },
  },
  RNSVGCircleManager: {},
  RNSVGClipPathManager: {},
  RNSVGDefsManager: {},
  RNSVGEllipseManager: {},
  RNSVGForeignObjectManager: {},
  RNSVGGroupManager: {},
  RNSVGImageManager: {},
  RNSVGLinearGradientManager: {},
  RNSVGLineManager: {},
  RNSVGMarkerManager: {},
  RNSVGMaskManager: {},
  RNSVGNodeManager: {},
  RNSVGPathManager: {},
  RNSVGPatternManager: {},
  RNSVGRadialGradientManager: {},
  RNSVGRectManager: {},
  RNSVGRenderableManager: {},
  RNSVGRenderableModule: {
    getBBox: { type: 'function', functionType: 'sync' },
    getConstants: { type: 'function' },
    getCTM: { type: 'function', functionType: 'sync' },
    getPointAtLength: { type: 'function', functionType: 'sync' },
    getScreenCTM: { type: 'function', functionType: 'sync' },
    getTotalLength: { type: 'function', functionType: 'sync' },
    isPointInFill: { type: 'function', functionType: 'sync' },
    isPointInStroke: { type: 'function', functionType: 'sync' },
  },
  RNSVGSvgViewManager: {},
  RNSVGSvgViewModule: {
    getConstants: { type: 'function' },
    toDataURL: { type: 'function', functionType: 'async' },
  },
  RNSVGSymbolManager: {},
  RNSVGTextManager: {},
  RNSVGTextPathManager: {},
  RNSVGTSpanManager: {},
  RNSVGUseManager: {},
  RNViewShot: {
    captureRef: { type: 'function', functionType: 'promise' },
    captureScreen: { type: 'function', functionType: 'promise' },
    getConstants: { type: 'function' },
    releaseCapture: { type: 'function', functionType: 'async' },
  },
};
