{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "BaseAnimationBuilder", "Easing", "withTiming", "assertEasingIsWorklet", "CurvedTransition", "constructor", "args", "in", "ease", "out", "exp", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "duration", "durationV", "easing", "easingX", "easingXV", "easingY", "easingYV", "easingWidth", "easingWidthV", "easingHeight", "easingHeightV", "values", "initialValues", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "animations", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "createInstance", "instance", "__DEV__"], "sources": ["CurvedTransition.ts"], "sourcesContent": ["'use strict';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationFunction,\n} from '../animationBuilder/commonTypes';\nimport { BaseAnimationBuilder } from '../animationBuilder';\nimport type { EasingFunction } from '../../commonTypes';\nimport { Easing } from '../../Easing';\nimport { withTiming } from '../../animation';\nimport { assertEasingIsWorklet } from '../../animation/util';\n\n/**\n * Layout transitions with a curved animation. You can modify the behavior by chaining methods like `.duration(500)` or `.delay(500)`.\n *\n * You pass it to the `layout` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions#fading-transition\n */\nexport class CurvedTransition\n  extends BaseAnimationBuilder\n  implements ILayoutAnimationBuilder\n{\n  static presetName = 'CurvedTransition';\n\n  easingXV: EasingFunction = Easing.in(Easing.ease);\n  easingYV: EasingFunction = Easing.out(Easing.ease);\n  easingWidthV: EasingFunction = Easing.in(Easing.exp);\n  easingHeightV: EasingFunction = Easing.out(Easing.exp);\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new CurvedTransition() as InstanceType<T>;\n  }\n\n  static easingX(easing: EasingFunction): CurvedTransition {\n    const instance = this.createInstance();\n    return instance.easingX(easing);\n  }\n\n  easingX(easing: EasingFunction): CurvedTransition {\n    if (__DEV__) {\n      assertEasingIsWorklet(easing);\n    }\n    this.easingXV = easing;\n    return this;\n  }\n\n  static easingY(easing: EasingFunction): CurvedTransition {\n    const instance = this.createInstance();\n    return instance.easingY(easing);\n  }\n\n  easingY(easing: EasingFunction): CurvedTransition {\n    if (__DEV__) {\n      assertEasingIsWorklet(easing);\n    }\n    this.easingYV = easing;\n    return this;\n  }\n\n  static easingWidth(easing: EasingFunction): CurvedTransition {\n    const instance = this.createInstance();\n    return instance.easingWidth(easing);\n  }\n\n  easingWidth(easing: EasingFunction): CurvedTransition {\n    if (__DEV__) {\n      assertEasingIsWorklet(easing);\n    }\n    this.easingWidthV = easing;\n    return this;\n  }\n\n  static easingHeight(easing: EasingFunction): CurvedTransition {\n    const instance = this.createInstance();\n    return instance.easingHeight(easing);\n  }\n\n  easingHeight(easing: EasingFunction): CurvedTransition {\n    if (__DEV__) {\n      assertEasingIsWorklet(easing);\n    }\n    this.easingHeightV = easing;\n    return this;\n  }\n\n  build = (): LayoutAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const callback = this.callbackV;\n    const delay = this.getDelay();\n    const duration = this.durationV ?? 300;\n    const easing = {\n      easingX: this.easingXV,\n      easingY: this.easingYV,\n      easingWidth: this.easingWidthV,\n      easingHeight: this.easingHeightV,\n    };\n\n    return (values) => {\n      'worklet';\n\n      return {\n        initialValues: {\n          originX: values.currentOriginX,\n          originY: values.currentOriginY,\n          width: values.currentWidth,\n          height: values.currentHeight,\n        },\n        animations: {\n          originX: delayFunction(\n            delay,\n            withTiming(values.targetOriginX, {\n              duration,\n              easing: easing.easingX,\n            })\n          ),\n          originY: delayFunction(\n            delay,\n            withTiming(values.targetOriginY, {\n              duration,\n              easing: easing.easingY,\n            })\n          ),\n          width: delayFunction(\n            delay,\n            withTiming(values.targetWidth, {\n              duration,\n              easing: easing.easingWidth,\n            })\n          ),\n          height: delayFunction(\n            delay,\n            withTiming(values.targetHeight, {\n              duration,\n              easing: easing.easingHeight,\n            })\n          ),\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAKb,SAASW,oBAAoB,QAAQ,qBAAqB;AAE1D,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,qBAAqB,QAAQ,sBAAsB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SACnBJ,oBAAoB,CAE9B;EAAAK,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA3B,eAAA,mBAG6BsB,MAAM,CAACM,EAAE,CAACN,MAAM,CAACO,IAAI,CAAC;IAAA7B,eAAA,mBACtBsB,MAAM,CAACQ,GAAG,CAACR,MAAM,CAACO,IAAI,CAAC;IAAA7B,eAAA,uBACnBsB,MAAM,CAACM,EAAE,CAACN,MAAM,CAACS,GAAG,CAAC;IAAA/B,eAAA,wBACpBsB,MAAM,CAACQ,GAAG,CAACR,MAAM,CAACS,GAAG,CAAC;IAAA/B,eAAA,gBA4D9C,MAA+B;MACrC,MAAMgC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS,IAAI,GAAG;MACtC,MAAMC,MAAM,GAAG;QACbC,OAAO,EAAE,IAAI,CAACC,QAAQ;QACtBC,OAAO,EAAE,IAAI,CAACC,QAAQ;QACtBC,WAAW,EAAE,IAAI,CAACC,YAAY;QAC9BC,YAAY,EAAE,IAAI,CAACC;MACrB,CAAC;MAED,OAAQC,MAAM,IAAK;QACjB,SAAS;;QAET,OAAO;UACLC,aAAa,EAAE;YACbC,OAAO,EAAEF,MAAM,CAACG,cAAc;YAC9BC,OAAO,EAAEJ,MAAM,CAACK,cAAc;YAC9BC,KAAK,EAAEN,MAAM,CAACO,YAAY;YAC1BC,MAAM,EAAER,MAAM,CAACS;UACjB,CAAC;UACDC,UAAU,EAAE;YACVR,OAAO,EAAEnB,aAAa,CACpBI,KAAK,EACLb,UAAU,CAAC0B,MAAM,CAACW,aAAa,EAAE;cAC/BtB,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACC;YACjB,CAAC,CACH,CAAC;YACDY,OAAO,EAAErB,aAAa,CACpBI,KAAK,EACLb,UAAU,CAAC0B,MAAM,CAACY,aAAa,EAAE;cAC/BvB,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACG;YACjB,CAAC,CACH,CAAC;YACDY,KAAK,EAAEvB,aAAa,CAClBI,KAAK,EACLb,UAAU,CAAC0B,MAAM,CAACa,WAAW,EAAE;cAC7BxB,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACK;YACjB,CAAC,CACH,CAAC;YACDY,MAAM,EAAEzB,aAAa,CACnBI,KAAK,EACLb,UAAU,CAAC0B,MAAM,CAACc,YAAY,EAAE;cAC9BzB,QAAQ;cACRE,MAAM,EAAEA,MAAM,CAACO;YACjB,CAAC,CACH;UACF,CAAC;UACDb;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EAjHD,OAAO8B,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIvC,gBAAgB,CAAC,CAAC;EAC/B;EAEA,OAAOgB,OAAOA,CAACD,MAAsB,EAAoB;IACvD,MAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACtC,OAAOC,QAAQ,CAACxB,OAAO,CAACD,MAAM,CAAC;EACjC;EAEAC,OAAOA,CAACD,MAAsB,EAAoB;IAChD,IAAI0B,OAAO,EAAE;MACX1C,qBAAqB,CAACgB,MAAM,CAAC;IAC/B;IACA,IAAI,CAACE,QAAQ,GAAGF,MAAM;IACtB,OAAO,IAAI;EACb;EAEA,OAAOG,OAAOA,CAACH,MAAsB,EAAoB;IACvD,MAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACtC,OAAOC,QAAQ,CAACtB,OAAO,CAACH,MAAM,CAAC;EACjC;EAEAG,OAAOA,CAACH,MAAsB,EAAoB;IAChD,IAAI0B,OAAO,EAAE;MACX1C,qBAAqB,CAACgB,MAAM,CAAC;IAC/B;IACA,IAAI,CAACI,QAAQ,GAAGJ,MAAM;IACtB,OAAO,IAAI;EACb;EAEA,OAAOK,WAAWA,CAACL,MAAsB,EAAoB;IAC3D,MAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACtC,OAAOC,QAAQ,CAACpB,WAAW,CAACL,MAAM,CAAC;EACrC;EAEAK,WAAWA,CAACL,MAAsB,EAAoB;IACpD,IAAI0B,OAAO,EAAE;MACX1C,qBAAqB,CAACgB,MAAM,CAAC;IAC/B;IACA,IAAI,CAACM,YAAY,GAAGN,MAAM;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOO,YAAYA,CAACP,MAAsB,EAAoB;IAC5D,MAAMyB,QAAQ,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACtC,OAAOC,QAAQ,CAAClB,YAAY,CAACP,MAAM,CAAC;EACtC;EAEAO,YAAYA,CAACP,MAAsB,EAAoB;IACrD,IAAI0B,OAAO,EAAE;MACX1C,qBAAqB,CAACgB,MAAM,CAAC;IAC/B;IACA,IAAI,CAACQ,aAAa,GAAGR,MAAM;IAC3B,OAAO,IAAI;EACb;AA0DF;AAACxC,eAAA,CA7HYyB,gBAAgB,gBAIP,kBAAkB", "ignoreList": []}