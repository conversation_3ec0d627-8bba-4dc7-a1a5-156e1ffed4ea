{"version": 3, "names": ["configureProps", "applyStyle", "getSwipeSimulator", "startScreenTransition", "screenTransitionConfig", "stackTag", "sharedEvent", "addListener", "value", "getLockAxis", "goBackGesture", "includes", "undefined", "finishScreenTransition", "removeListener", "lockAxis", "step"], "sources": ["animationManager.ts"], "sourcesContent": ["'use strict';\n\nimport type { LockAxis, ScreenTransitionConfig } from './commonTypes';\nimport { configureProps } from '../ConfigHelper';\nimport { applyStyle } from './styleUpdater';\nimport { getSwipeSimulator } from './swipeSimulator';\n\nconfigureProps();\n\nexport function startScreenTransition(\n  screenTransitionConfig: ScreenTransitionConfig\n) {\n  'worklet';\n  const { stackTag, sharedEvent } = screenTransitionConfig;\n  sharedEvent.addListener(stackTag, () => {\n    applyStyle(screenTransitionConfig, sharedEvent.value);\n  });\n}\n\nfunction getLockAxis(goBackGesture: string): LockAxis {\n  'worklet';\n  if (['swipeRight', 'swipeLeft', 'horizontalSwipe'].includes(goBackGesture)) {\n    return 'x';\n  } else if (\n    ['swipeUp', 'swipeDown', 'verticalSwipe'].includes(goBackGesture)\n  ) {\n    return 'y';\n  }\n  return undefined;\n}\n\nexport function finishScreenTransition(\n  screenTransitionConfig: ScreenTransitionConfig\n) {\n  'worklet';\n  const { stackTag, sharedEvent, goBackGesture } = screenTransitionConfig;\n  sharedEvent.removeListener(stackTag);\n  const lockAxis = getLockAxis(goBackGesture);\n  const step = getSwipeSimulator(\n    sharedEvent.value,\n    screenTransitionConfig,\n    lockAxis\n  );\n  step();\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,cAAc,QAAQ,iBAAiB;AAChD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAAQ,kBAAkB;AAEpDF,cAAc,CAAC,CAAC;AAEhB,OAAO,SAASG,qBAAqBA,CACnCC,sBAA8C,EAC9C;EACA,SAAS;;EACT,MAAM;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGF,sBAAsB;EACxDE,WAAW,CAACC,WAAW,CAACF,QAAQ,EAAE,MAAM;IACtCJ,UAAU,CAACG,sBAAsB,EAAEE,WAAW,CAACE,KAAK,CAAC;EACvD,CAAC,CAAC;AACJ;AAEA,SAASC,WAAWA,CAACC,aAAqB,EAAY;EACpD,SAAS;;EACT,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,EAAE;IAC1E,OAAO,GAAG;EACZ,CAAC,MAAM,IACL,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,aAAa,CAAC,EACjE;IACA,OAAO,GAAG;EACZ;EACA,OAAOE,SAAS;AAClB;AAEA,OAAO,SAASC,sBAAsBA,CACpCT,sBAA8C,EAC9C;EACA,SAAS;;EACT,MAAM;IAAEC,QAAQ;IAAEC,WAAW;IAAEI;EAAc,CAAC,GAAGN,sBAAsB;EACvEE,WAAW,CAACQ,cAAc,CAACT,QAAQ,CAAC;EACpC,MAAMU,QAAQ,GAAGN,WAAW,CAACC,aAAa,CAAC;EAC3C,MAAMM,IAAI,GAAGd,iBAAiB,CAC5BI,WAAW,CAACE,KAAK,EACjBJ,sBAAsB,EACtBW,QACF,CAAC;EACDC,IAAI,CAAC,CAAC;AACR", "ignoreList": []}