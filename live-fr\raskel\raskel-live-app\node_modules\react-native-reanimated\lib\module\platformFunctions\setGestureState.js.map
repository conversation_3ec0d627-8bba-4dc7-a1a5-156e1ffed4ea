{"version": 3, "names": ["isChromeDebugger", "isJest", "shouldBeUseWeb", "setGestureState", "setGestureStateNative", "handlerTag", "newState", "_WORKLET", "console", "warn", "global", "_setGestureState", "setGestureStateJest", "setGestureStateChromeDebugger", "setGestureStateDefault"], "sources": ["setGestureState.ts"], "sourcesContent": ["'use strict';\nimport { isChromeDebugger, isJest, shouldBeUseWeb } from '../PlatformChecker';\n\ntype SetGestureState = (handlerTag: number, newState: number) => void;\n\nexport let setGestureState: SetGestureState;\n\nfunction setGestureStateNative(handlerTag: number, newState: number) {\n  'worklet';\n  if (!_WORKLET) {\n    console.warn(\n      '[Reanimated] You can not use setGestureState in non-worklet function.'\n    );\n    return;\n  }\n  global._setGestureState(handlerTag, newState);\n}\n\nfunction setGestureStateJest() {\n  console.warn('[Reanimated] setGestureState() cannot be used with Jest.');\n}\n\nfunction setGestureStateChromeDebugger() {\n  console.warn(\n    '[Reanimated] setGestureState() cannot be used with Chrome Debugger.'\n  );\n}\n\nfunction setGestureStateDefault() {\n  console.warn(\n    '[Reanimated] setGestureState() is not supported on this configuration.'\n  );\n}\n\nif (!shouldBeUseWeb()) {\n  setGestureState = setGestureStateNative;\n} else if (isJest()) {\n  setGestureState = setGestureStateJest;\n} else if (isChromeDebugger()) {\n  setGestureState = setGestureStateChromeDebugger;\n} else {\n  setGestureState = setGestureStateDefault;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,gBAAgB,EAAEC,MAAM,EAAEC,cAAc,QAAQ,oBAAoB;AAI7E,OAAO,IAAIC,eAAgC;AAE3C,SAASC,qBAAqBA,CAACC,UAAkB,EAAEC,QAAgB,EAAE;EACnE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbC,OAAO,CAACC,IAAI,CACV,uEACF,CAAC;IACD;EACF;EACAC,MAAM,CAACC,gBAAgB,CAACN,UAAU,EAAEC,QAAQ,CAAC;AAC/C;AAEA,SAASM,mBAAmBA,CAAA,EAAG;EAC7BJ,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;AAC1E;AAEA,SAASI,6BAA6BA,CAAA,EAAG;EACvCL,OAAO,CAACC,IAAI,CACV,qEACF,CAAC;AACH;AAEA,SAASK,sBAAsBA,CAAA,EAAG;EAChCN,OAAO,CAACC,IAAI,CACV,wEACF,CAAC;AACH;AAEA,IAAI,CAACP,cAAc,CAAC,CAAC,EAAE;EACrBC,eAAe,GAAGC,qBAAqB;AACzC,CAAC,MAAM,IAAIH,MAAM,CAAC,CAAC,EAAE;EACnBE,eAAe,GAAGS,mBAAmB;AACvC,CAAC,MAAM,IAAIZ,gBAAgB,CAAC,CAAC,EAAE;EAC7BG,eAAe,GAAGU,6BAA6B;AACjD,CAAC,MAAM;EACLV,eAAe,GAAGW,sBAAsB;AAC1C", "ignoreList": []}