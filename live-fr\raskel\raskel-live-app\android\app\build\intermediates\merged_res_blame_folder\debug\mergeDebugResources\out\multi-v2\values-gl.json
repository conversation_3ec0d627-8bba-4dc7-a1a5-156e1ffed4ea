{"logs": [{"outputFile": "com.msrfi.liveapp-mergeDebugResources-77:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1076c57e2b6fc3bba30fdb23eb82909e\\transformed\\exoplayer-ui-2.18.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1876,1995,2121,2192,2278,2354,2431,2534,2639,2703,2767,2820,2878,2926,2987,3052,3122,3188,3260,3330,3398,3464,3529,3595,3648,3710,3786,3862", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1871,1990,2116,2187,2273,2349,2426,2529,2634,2698,2762,2815,2873,2921,2982,3047,3117,3183,3255,3325,3393,3459,3524,3590,3643,3705,3781,3857,3915"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,594,7720,7806,7894,7973,8071,8166,8243,8310,8410,8510,8576,8645,8712,8783,8914,9033,9159,9230,9316,9392,9469,9572,9677,9741,10503,10556,10614,10662,10723,10788,10858,10924,10996,11066,11134,11200,11265,11331,11384,11446,11522,11598", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "381,589,772,7801,7889,7968,8066,8161,8238,8305,8405,8505,8571,8640,8707,8778,8909,9028,9154,9225,9311,9387,9464,9567,9672,9736,9800,10551,10609,10657,10718,10783,10853,10919,10991,11061,11129,11195,11260,11326,11379,11441,11517,11593,11651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dea44b29830a29c4d2a116dadfb42f8b\\transformed\\exoplayer-core-2.18.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9805,9879,9942,10007,10086,10163,10239,10338,10434", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "9874,9937,10002,10081,10158,10234,10333,10429,10498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33d72ac5290f8957ef564231fbd82b02\\transformed\\play-services-base-18.1.0\\res\\values-gl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,454,579,683,838,966,1081,1186,1353,1458,1623,1754,1915,2063,2126,2191", "endColumns": "101,158,124,103,154,127,114,104,166,104,164,130,160,147,62,64,80", "endOffsets": "294,453,578,682,837,965,1080,1185,1352,1457,1622,1753,1914,2062,2125,2190,2271"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5192,5298,5461,5590,5698,5857,5989,6108,6351,6522,6631,6800,6935,7100,7252,7319,7388", "endColumns": "105,162,128,107,158,131,118,108,170,108,168,134,164,151,66,68,84", "endOffsets": "5293,5456,5585,5693,5852,5984,6103,6212,6517,6626,6795,6930,7095,7247,7314,7383,7468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f47581e38898051b48ece556f9194c7b\\transformed\\browser-1.6.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,376", "endColumns": "106,101,111,105", "endOffsets": "157,259,371,477"}, "to": {"startLines": "83,138,139,140", "startColumns": "4,4,4,4", "startOffsets": "7473,11752,11854,11966", "endColumns": "106,101,111,105", "endOffsets": "7575,11849,11961,12067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d21d690b6df8bd5196af38182335d85\\transformed\\material-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1099,1177,1273,1352,1415,1510,1574,1643,1706,1780,1844,1900,2021,2079,2141,2197,2274,2413,2501,2578,2674,2758,2838,2978,3058,3138,3287,3377,3458,3514,3570,3636,3715,3796,3867,3955,4034,4111,4193,4282,4383,4467,4559,4652,4753,4827,4919,5021,5073,5157,5223,5315,5403,5465,5529,5592,5662,5773,5878,5984,6083,6143,6203,6288,6371,6450", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "270,351,431,516,618,714,819,952,1032,1094,1172,1268,1347,1410,1505,1569,1638,1701,1775,1839,1895,2016,2074,2136,2192,2269,2408,2496,2573,2669,2753,2833,2973,3053,3133,3282,3372,3453,3509,3565,3631,3710,3791,3862,3950,4029,4106,4188,4277,4378,4462,4554,4647,4748,4822,4914,5016,5068,5152,5218,5310,5398,5460,5524,5587,5657,5768,5873,5979,6078,6138,6198,6283,6366,6445,6523"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,84,85,137,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "777,3702,3783,3863,3948,4050,4874,4979,5112,7580,7642,11656,12072,12151,12214,12309,12373,12442,12505,12579,12643,12699,12820,12878,12940,12996,13073,13212,13300,13377,13473,13557,13637,13777,13857,13937,14086,14176,14257,14313,14369,14435,14514,14595,14666,14754,14833,14910,14992,15081,15182,15266,15358,15451,15552,15626,15718,15820,15872,15956,16022,16114,16202,16264,16328,16391,16461,16572,16677,16783,16882,16942,17002,17170,17253,17332", "endLines": "22,50,51,52,53,54,62,63,64,84,85,137,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,203,204,205", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "947,3778,3858,3943,4045,4141,4974,5107,5187,7637,7715,11747,12146,12209,12304,12368,12437,12500,12574,12638,12694,12815,12873,12935,12991,13068,13207,13295,13372,13468,13552,13632,13772,13852,13932,14081,14171,14252,14308,14364,14430,14509,14590,14661,14749,14828,14905,14987,15076,15177,15261,15353,15446,15547,15621,15713,15815,15867,15951,16017,16109,16197,16259,16323,16386,16456,16567,16672,16778,16877,16937,16997,17082,17248,17327,17405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d6cd5f4345895639e9dea77e62a721a7\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "952,1056,1160,1268,1353,1454,1582,1668,1749,1841,1935,2032,2126,2226,2320,2416,2511,2603,2695,2776,2884,2991,3098,3207,3312,3426,3603,17087", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "1051,1155,1263,1348,1449,1577,1663,1744,1836,1930,2027,2121,2221,2315,2411,2506,2598,2690,2771,2879,2986,3093,3202,3307,3421,3598,3697,17165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b5b22b250867a7891421e198eb95eaa0\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "55,56,57,58,59,60,61,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4146,4245,4347,4447,4545,4652,4758,17410", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "4240,4342,4442,4540,4647,4753,4869,17506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5cc0c55bacd14953f77d87885006a8d9\\transformed\\play-services-basement-18.1.0\\res\\values-gl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "129", "endOffsets": "324"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6217", "endColumns": "133", "endOffsets": "6346"}}]}]}