{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_PINWHEEL_TIME", "PinwheelData", "PinwheelIn", "name", "style", "transform", "rotate", "scale", "opacity", "duration", "PinwheelOut", "Pinwheel"], "sources": ["Pinwheel.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_PINWHEEL_TIME = 0.3;\n\nexport const PinwheelData = {\n  PinwheelIn: {\n    name: 'PinwheelIn',\n    style: {\n      0: {\n        transform: [{ rotate: '5rad', scale: 0 }],\n        opacity: 0,\n      },\n\n      100: {\n        transform: [{ rotate: '0deg', scale: 1 }],\n        opacity: 1,\n      },\n    },\n    duration: DEFAULT_PINWHEEL_TIME,\n  },\n\n  PinwheelOut: {\n    name: 'PinwheelOut',\n    style: {\n      0: {\n        transform: [{ rotate: '0rad', scale: 1 }],\n        opacity: 1,\n      },\n      100: {\n        transform: [{ rotate: '5rad', scale: 0 }],\n        opacity: 0,\n      },\n    },\n    duration: DEFAULT_PINWHEEL_TIME,\n  },\n};\n\nexport const Pinwheel = {\n  PinwheelIn: {\n    style: convertAnimationObjectToKeyframes(PinwheelData.PinwheelIn),\n    duration: PinwheelData.PinwheelIn.duration,\n  },\n  PinwheelOut: {\n    style: convertAnimationObjectToKeyframes(PinwheelData.PinwheelOut),\n    duration: PinwheelData.PinwheelOut.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,qBAAqB,GAAG,GAAG;AAEjC,OAAO,MAAMC,YAAY,GAAG;EAC1BC,UAAU,EAAE;IACVC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC,CAAC;QACzCC,OAAO,EAAE;MACX,CAAC;MAED,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC,CAAC;QACzCC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ,CAAC;EAEDU,WAAW,EAAE;IACXP,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC,CAAC;QACzCC,OAAO,EAAE;MACX,CAAC;MACD,GAAG,EAAE;QACHH,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC,CAAC;QACzCC,OAAO,EAAE;MACX;IACF,CAAC;IACDC,QAAQ,EAAET;EACZ;AACF,CAAC;AAED,OAAO,MAAMW,QAAQ,GAAG;EACtBT,UAAU,EAAE;IACVE,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACC,UAAU,CAAC;IACjEO,QAAQ,EAAER,YAAY,CAACC,UAAU,CAACO;EACpC,CAAC;EACDC,WAAW,EAAE;IACXN,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACS,WAAW,CAAC;IAClED,QAAQ,EAAER,YAAY,CAACS,WAAW,CAACD;EACrC;AACF,CAAC", "ignoreList": []}