{"version": 3, "names": ["AnimationsData", "TransitionType", "convertAnimationObjectToKeyframes", "LinearTransition", "SequencedTransition", "FadingTransition", "JumpingTransition", "insertWebAnimation", "CurvedTransition", "EntryExitTransition", "addPxToTransform", "transform", "newTransform", "map", "transformProp", "newTransformProp", "key", "value", "Object", "entries", "includes", "createCustomKeyFrameAnimation", "keyframeDefinitions", "values", "animationData", "name", "style", "duration", "generateNextCustomKeyframeName", "parsedKeyframe", "createAnimationWithInitialValues", "animationName", "initialValues", "animationStyle", "structuredClone", "firstAnimationStep", "rest", "transformWithPx", "transformStyle", "Map", "rule", "property", "set", "Array", "from", "keyframeName", "animationObject", "keyframe", "customKeyframeCounter", "TransitionGenerator", "transitionType", "transitionData", "transitionKeyframeName", "dummyTransitionKeyframeName", "transitionObject", "LINEAR", "SEQUENCED", "FADING", "JUMPING", "CURVED", "firstKeyframeObj", "secondKeyframeObj", "dummy<PERSON><PERSON><PERSON>", "ENTRY_EXIT", "transitionKeyframe"], "sources": ["createAnimation.ts"], "sourcesContent": ["'use strict';\n\nimport { AnimationsData, TransitionType } from './config';\nimport type { InitialValuesStyleProps, KeyframeDefinitions } from './config';\nimport { convertAnimationObjectToKeyframes } from './animationParser';\nimport type {\n  AnimationData,\n  ReanimatedWebTransformProperties,\n  TransitionData,\n} from './animationParser';\nimport type { TransformsStyle } from 'react-native';\nimport { LinearTransition } from './transition/Linear.web';\nimport { SequencedTransition } from './transition/Sequenced.web';\nimport { FadingTransition } from './transition/Fading.web';\nimport { JumpingTransition } from './transition/Jumping.web';\nimport { insertWebAnimation } from './domUtils';\nimport { CurvedTransition } from './transition/Curved.web';\nimport { EntryExitTransition } from './transition/EntryExit.web';\n\ntype TransformType = NonNullable<TransformsStyle['transform']>;\n\n// Translate values are passed as numbers. However, if `translate` property receives number, it will not automatically\n// convert it to `px`. Therefore if we want to keep transform we have to add 'px' suffix to each of translate values\n// that are present inside transform.\n//\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction addPxToTransform(transform: TransformType) {\n  type RNTransformProp = (typeof transform)[number];\n\n  // @ts-ignore `existingTransform` cannot be string because in that case\n  // we throw error in `extractTransformFromStyle`\n  const newTransform = transform.map((transformProp: RNTransformProp) => {\n    const newTransformProp: ReanimatedWebTransformProperties = {};\n    for (const [key, value] of Object.entries(transformProp)) {\n      if (\n        (key.includes('translate') || key.includes('perspective')) &&\n        typeof value === 'number'\n      ) {\n        // @ts-ignore After many trials we decided to ignore this error - it says that we cannot use 'key' to index this object.\n        // Sadly it doesn't go away after using cast `key as keyof TransformProperties`.\n        newTransformProp[key] = `${value}px`;\n      } else {\n        // @ts-ignore same as above.\n        newTransformProp[key] = value;\n      }\n    }\n    return newTransformProp;\n  });\n\n  return newTransform;\n}\n\nexport function createCustomKeyFrameAnimation(\n  keyframeDefinitions: KeyframeDefinitions\n) {\n  for (const value of Object.values(keyframeDefinitions)) {\n    if (value.transform) {\n      value.transform = addPxToTransform(value.transform as TransformType);\n    }\n  }\n\n  const animationData: AnimationData = {\n    name: '',\n    style: keyframeDefinitions,\n    duration: -1,\n  };\n\n  animationData.name = generateNextCustomKeyframeName();\n\n  const parsedKeyframe = convertAnimationObjectToKeyframes(animationData);\n\n  insertWebAnimation(animationData.name, parsedKeyframe);\n\n  return animationData.name;\n}\n\nexport function createAnimationWithInitialValues(\n  animationName: string,\n  initialValues: InitialValuesStyleProps\n) {\n  const animationStyle = structuredClone(AnimationsData[animationName].style);\n  const firstAnimationStep = animationStyle['0'];\n\n  const { transform, ...rest } = initialValues;\n  const transformWithPx = addPxToTransform(transform as TransformType);\n\n  if (transform) {\n    // If there was no predefined transform, we can simply assign transform from `initialValues`.\n    if (!firstAnimationStep.transform) {\n      firstAnimationStep.transform = transformWithPx;\n    } else {\n      // Othwerwise we have to merge predefined transform with the one provided in `initialValues`.\n      // To do that, we create `Map` that will contain final transform.\n      const transformStyle = new Map<string, any>();\n\n      // First we assign all of the predefined rules\n      for (const rule of firstAnimationStep.transform) {\n        // In most cases there will be just one iteration\n        for (const [property, value] of Object.entries(rule)) {\n          transformStyle.set(property, value);\n        }\n      }\n\n      // Then we either add new rule, or override one that already exists.\n      for (const rule of transformWithPx) {\n        for (const [property, value] of Object.entries(rule)) {\n          transformStyle.set(property, value);\n        }\n      }\n\n      // Finally, we convert `Map` with final transform back into array of objects.\n      firstAnimationStep.transform = Array.from(\n        transformStyle,\n        ([property, value]) => ({\n          [property]: value,\n        })\n      );\n    }\n  }\n\n  animationStyle['0'] = {\n    ...animationStyle['0'],\n    ...rest,\n  };\n\n  // TODO: Maybe we can extract the logic below into separate function\n  const keyframeName = generateNextCustomKeyframeName();\n\n  const animationObject: AnimationData = {\n    name: keyframeName,\n    style: animationStyle,\n    duration: AnimationsData[animationName].duration,\n  };\n\n  const keyframe = convertAnimationObjectToKeyframes(animationObject);\n\n  insertWebAnimation(keyframeName, keyframe);\n\n  return keyframeName;\n}\n\nlet customKeyframeCounter = 0;\n\nfunction generateNextCustomKeyframeName() {\n  return `REA${customKeyframeCounter++}`;\n}\n\n/**\n * Creates transition of given type, appends it to stylesheet and returns keyframe name.\n *\n * @param transitionType - Type of transition (e.g. LINEAR).\n * @param transitionData - Object containing data for transforms (translateX, scaleX,...).\n * @returns Keyframe name that represents transition.\n */\nexport function TransitionGenerator(\n  transitionType: TransitionType,\n  transitionData: TransitionData\n) {\n  const transitionKeyframeName = generateNextCustomKeyframeName();\n  let dummyTransitionKeyframeName;\n\n  let transitionObject;\n\n  switch (transitionType) {\n    case TransitionType.LINEAR:\n      transitionObject = LinearTransition(\n        transitionKeyframeName,\n        transitionData\n      );\n      break;\n    case TransitionType.SEQUENCED:\n      transitionObject = SequencedTransition(\n        transitionKeyframeName,\n        transitionData\n      );\n      break;\n    case TransitionType.FADING:\n      transitionObject = FadingTransition(\n        transitionKeyframeName,\n        transitionData\n      );\n      break;\n    case TransitionType.JUMPING:\n      transitionObject = JumpingTransition(\n        transitionKeyframeName,\n        transitionData\n      );\n      break;\n\n    // Here code block with {} is necessary because of eslint\n    case TransitionType.CURVED: {\n      dummyTransitionKeyframeName = generateNextCustomKeyframeName();\n\n      const { firstKeyframeObj, secondKeyframeObj } = CurvedTransition(\n        transitionKeyframeName,\n        dummyTransitionKeyframeName,\n        transitionData\n      );\n\n      transitionObject = firstKeyframeObj;\n\n      const dummyKeyframe =\n        convertAnimationObjectToKeyframes(secondKeyframeObj);\n\n      insertWebAnimation(dummyTransitionKeyframeName, dummyKeyframe);\n\n      break;\n    }\n    case TransitionType.ENTRY_EXIT:\n      transitionObject = EntryExitTransition(\n        transitionKeyframeName,\n        transitionData\n      );\n      break;\n  }\n\n  const transitionKeyframe =\n    convertAnimationObjectToKeyframes(transitionObject);\n\n  insertWebAnimation(transitionKeyframeName, transitionKeyframe);\n\n  return { transitionKeyframeName, dummyTransitionKeyframeName };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,EAAEC,cAAc,QAAQ,UAAU;AAEzD,SAASC,iCAAiC,QAAQ,mBAAmB;AAOrE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,kBAAkB,QAAQ,YAAY;AAC/C,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,mBAAmB,QAAQ,4BAA4B;AAIhE;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,SAAwB,EAAE;EAGlD;EACA;EACA,MAAMC,YAAY,GAAGD,SAAS,CAACE,GAAG,CAAEC,aAA8B,IAAK;IACrE,MAAMC,gBAAkD,GAAG,CAAC,CAAC;IAC7D,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,aAAa,CAAC,EAAE;MACxD,IACE,CAACE,GAAG,CAACI,QAAQ,CAAC,WAAW,CAAC,IAAIJ,GAAG,CAACI,QAAQ,CAAC,aAAa,CAAC,KACzD,OAAOH,KAAK,KAAK,QAAQ,EACzB;QACA;QACA;QACAF,gBAAgB,CAACC,GAAG,CAAC,GAAI,GAAEC,KAAM,IAAG;MACtC,CAAC,MAAM;QACL;QACAF,gBAAgB,CAACC,GAAG,CAAC,GAAGC,KAAK;MAC/B;IACF;IACA,OAAOF,gBAAgB;EACzB,CAAC,CAAC;EAEF,OAAOH,YAAY;AACrB;AAEA,OAAO,SAASS,6BAA6BA,CAC3CC,mBAAwC,EACxC;EACA,KAAK,MAAML,KAAK,IAAIC,MAAM,CAACK,MAAM,CAACD,mBAAmB,CAAC,EAAE;IACtD,IAAIL,KAAK,CAACN,SAAS,EAAE;MACnBM,KAAK,CAACN,SAAS,GAAGD,gBAAgB,CAACO,KAAK,CAACN,SAA0B,CAAC;IACtE;EACF;EAEA,MAAMa,aAA4B,GAAG;IACnCC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAEJ,mBAAmB;IAC1BK,QAAQ,EAAE,CAAC;EACb,CAAC;EAEDH,aAAa,CAACC,IAAI,GAAGG,8BAA8B,CAAC,CAAC;EAErD,MAAMC,cAAc,GAAG3B,iCAAiC,CAACsB,aAAa,CAAC;EAEvEjB,kBAAkB,CAACiB,aAAa,CAACC,IAAI,EAAEI,cAAc,CAAC;EAEtD,OAAOL,aAAa,CAACC,IAAI;AAC3B;AAEA,OAAO,SAASK,gCAAgCA,CAC9CC,aAAqB,EACrBC,aAAsC,EACtC;EACA,MAAMC,cAAc,GAAGC,eAAe,CAAClC,cAAc,CAAC+B,aAAa,CAAC,CAACL,KAAK,CAAC;EAC3E,MAAMS,kBAAkB,GAAGF,cAAc,CAAC,GAAG,CAAC;EAE9C,MAAM;IAAEtB,SAAS;IAAE,GAAGyB;EAAK,CAAC,GAAGJ,aAAa;EAC5C,MAAMK,eAAe,GAAG3B,gBAAgB,CAACC,SAA0B,CAAC;EAEpE,IAAIA,SAAS,EAAE;IACb;IACA,IAAI,CAACwB,kBAAkB,CAACxB,SAAS,EAAE;MACjCwB,kBAAkB,CAACxB,SAAS,GAAG0B,eAAe;IAChD,CAAC,MAAM;MACL;MACA;MACA,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAc,CAAC;;MAE7C;MACA,KAAK,MAAMC,IAAI,IAAIL,kBAAkB,CAACxB,SAAS,EAAE;QAC/C;QACA,KAAK,MAAM,CAAC8B,QAAQ,EAAExB,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACqB,IAAI,CAAC,EAAE;UACpDF,cAAc,CAACI,GAAG,CAACD,QAAQ,EAAExB,KAAK,CAAC;QACrC;MACF;;MAEA;MACA,KAAK,MAAMuB,IAAI,IAAIH,eAAe,EAAE;QAClC,KAAK,MAAM,CAACI,QAAQ,EAAExB,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACqB,IAAI,CAAC,EAAE;UACpDF,cAAc,CAACI,GAAG,CAACD,QAAQ,EAAExB,KAAK,CAAC;QACrC;MACF;;MAEA;MACAkB,kBAAkB,CAACxB,SAAS,GAAGgC,KAAK,CAACC,IAAI,CACvCN,cAAc,EACd,CAAC,CAACG,QAAQ,EAAExB,KAAK,CAAC,MAAM;QACtB,CAACwB,QAAQ,GAAGxB;MACd,CAAC,CACH,CAAC;IACH;EACF;EAEAgB,cAAc,CAAC,GAAG,CAAC,GAAG;IACpB,GAAGA,cAAc,CAAC,GAAG,CAAC;IACtB,GAAGG;EACL,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGjB,8BAA8B,CAAC,CAAC;EAErD,MAAMkB,eAA8B,GAAG;IACrCrB,IAAI,EAAEoB,YAAY;IAClBnB,KAAK,EAAEO,cAAc;IACrBN,QAAQ,EAAE3B,cAAc,CAAC+B,aAAa,CAAC,CAACJ;EAC1C,CAAC;EAED,MAAMoB,QAAQ,GAAG7C,iCAAiC,CAAC4C,eAAe,CAAC;EAEnEvC,kBAAkB,CAACsC,YAAY,EAAEE,QAAQ,CAAC;EAE1C,OAAOF,YAAY;AACrB;AAEA,IAAIG,qBAAqB,GAAG,CAAC;AAE7B,SAASpB,8BAA8BA,CAAA,EAAG;EACxC,OAAQ,MAAKoB,qBAAqB,EAAG,EAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CACjCC,cAA8B,EAC9BC,cAA8B,EAC9B;EACA,MAAMC,sBAAsB,GAAGxB,8BAA8B,CAAC,CAAC;EAC/D,IAAIyB,2BAA2B;EAE/B,IAAIC,gBAAgB;EAEpB,QAAQJ,cAAc;IACpB,KAAKjD,cAAc,CAACsD,MAAM;MACxBD,gBAAgB,GAAGnD,gBAAgB,CACjCiD,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKlD,cAAc,CAACuD,SAAS;MAC3BF,gBAAgB,GAAGlD,mBAAmB,CACpCgD,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKlD,cAAc,CAACwD,MAAM;MACxBH,gBAAgB,GAAGjD,gBAAgB,CACjC+C,sBAAsB,EACtBD,cACF,CAAC;MACD;IACF,KAAKlD,cAAc,CAACyD,OAAO;MACzBJ,gBAAgB,GAAGhD,iBAAiB,CAClC8C,sBAAsB,EACtBD,cACF,CAAC;MACD;;IAEF;IACA,KAAKlD,cAAc,CAAC0D,MAAM;MAAE;QAC1BN,2BAA2B,GAAGzB,8BAA8B,CAAC,CAAC;QAE9D,MAAM;UAAEgC,gBAAgB;UAAEC;QAAkB,CAAC,GAAGrD,gBAAgB,CAC9D4C,sBAAsB,EACtBC,2BAA2B,EAC3BF,cACF,CAAC;QAEDG,gBAAgB,GAAGM,gBAAgB;QAEnC,MAAME,aAAa,GACjB5D,iCAAiC,CAAC2D,iBAAiB,CAAC;QAEtDtD,kBAAkB,CAAC8C,2BAA2B,EAAES,aAAa,CAAC;QAE9D;MACF;IACA,KAAK7D,cAAc,CAAC8D,UAAU;MAC5BT,gBAAgB,GAAG7C,mBAAmB,CACpC2C,sBAAsB,EACtBD,cACF,CAAC;MACD;EACJ;EAEA,MAAMa,kBAAkB,GACtB9D,iCAAiC,CAACoD,gBAAgB,CAAC;EAErD/C,kBAAkB,CAAC6C,sBAAsB,EAAEY,kBAAkB,CAAC;EAE9D,OAAO;IAAEZ,sBAAsB;IAAEC;EAA4B,CAAC;AAChE", "ignoreList": []}