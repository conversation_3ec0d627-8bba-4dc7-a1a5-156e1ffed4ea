{"version": 3, "file": "Manifest.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_fs", "_path", "XML", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "writeAndroidManifestAsync", "manifestPath", "androidManifest", "manifestXml", "format", "fs", "promises", "mkdir", "path", "dirname", "recursive", "writeFile", "readAndroidManifestAsync", "xml", "readXMLAsync", "isManifest", "Error", "manifest", "getMainApplication", "application", "filter", "$", "endsWith", "getMainApplicationOrThrow", "mainApplication", "assert", "getMainActivityOrThrow", "mainActivity", "getMainActivity", "getRunnableActivity", "firstApplication", "enabledActivities", "activity", "isIntentFilterRunnable", "<PERSON><PERSON><PERSON><PERSON>", "action", "some", "category", "Array", "isArray", "enabledActivityNames", "map", "aliases", "includes", "length", "alias", "matchingActivity", "find", "addMetaDataItemToMainApplication", "itemName", "itemValue", "itemType", "existingMetaDataItem", "newItem", "prefixAndroidKeys", "name", "push", "removeMetaDataItemFromMainApplication", "index", "findMetaDataItem", "splice", "findApplicationSubItem", "parent", "findIndex", "findUsesLibraryItem", "getMainApplicationMetaDataValue", "item", "addUsesLibraryItemToMainApplication", "removeUsesLibraryItemFromMainApplication", "head", "entries", "reduce", "prev", "key", "curr", "ensureToolsAvailable", "ensureManifestHasNamespace", "namespace", "url"], "sources": ["../../src/android/Manifest.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as XML from '../utils/XML';\n\nexport type StringBoolean = 'true' | 'false';\n\ntype ManifestMetaDataAttributes = AndroidManifestAttributes & {\n  'android:value'?: string;\n  'android:resource'?: string;\n};\n\ntype AndroidManifestAttributes = {\n  'android:name': string | 'android.intent.action.VIEW';\n  'tools:node'?: string | 'remove';\n};\n\ntype ManifestAction = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestCategory = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestData = {\n  $: {\n    [key: string]: string | undefined;\n    'android:host'?: string;\n    'android:pathPrefix'?: string;\n    'android:scheme'?: string;\n  };\n};\n\ntype ManifestReceiver = {\n  $: AndroidManifestAttributes & {\n    'android:exported'?: StringBoolean;\n    'android:enabled'?: StringBoolean;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n};\n\nexport type ManifestIntentFilter = {\n  $?: {\n    'android:autoVerify'?: StringBoolean;\n    'data-generated'?: StringBoolean;\n  };\n  action?: ManifestAction[];\n  data?: ManifestData[];\n  category?: ManifestCategory[];\n};\n\n// https://developer.android.com/guide/topics/manifest/activity-alias-element\nexport type ManifestActivityAlias = {\n  $?: {\n    'android:name': string;\n    'android:enabled'?: StringBoolean;\n    'android:exported'?: StringBoolean;\n    'android:label'?: string;\n    'android:permission'?: string;\n    'android:icon'?: string;\n    'android:targetActivity': string;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n  'meta-data'?: ManifestMetaData[];\n};\n\nexport type ManifestMetaData = {\n  $: ManifestMetaDataAttributes;\n};\n\ntype ManifestServiceAttributes = AndroidManifestAttributes & {\n  'android:enabled'?: StringBoolean;\n  'android:exported'?: StringBoolean;\n  'android:permission'?: string;\n  'android:foregroundServiceType'?: string;\n  // ...\n};\n\ntype ManifestService = {\n  $: ManifestServiceAttributes;\n  'intent-filter'?: ManifestIntentFilter[];\n};\n\ntype ManifestApplicationAttributes = {\n  'android:name': string | '.MainApplication';\n  'android:icon'?: string;\n  'android:roundIcon'?: string;\n  'android:label'?: string;\n  'android:allowBackup'?: StringBoolean;\n  'android:largeHeap'?: StringBoolean;\n  'android:requestLegacyExternalStorage'?: StringBoolean;\n  'android:supportsPictureInPicture'?: StringBoolean;\n  'android:usesCleartextTraffic'?: StringBoolean;\n  'android:enableOnBackInvokedCallback'?: StringBoolean;\n  [key: string]: string | undefined;\n};\n\nexport type ManifestActivity = {\n  $: ManifestApplicationAttributes & {\n    'android:exported'?: StringBoolean;\n    'android:launchMode'?: string;\n    'android:theme'?: string;\n    'android:windowSoftInputMode'?:\n      | string\n      | 'stateUnspecified'\n      | 'stateUnchanged'\n      | 'stateHidden'\n      | 'stateAlwaysHidden'\n      | 'stateVisible'\n      | 'stateAlwaysVisible'\n      | 'adjustUnspecified'\n      | 'adjustResize'\n      | 'adjustPan';\n    [key: string]: string | undefined;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n  // ...\n};\n\nexport type ManifestUsesLibrary = {\n  $: AndroidManifestAttributes & {\n    'android:required'?: StringBoolean;\n  };\n};\n\nexport type ManifestApplication = {\n  $: ManifestApplicationAttributes;\n  activity?: ManifestActivity[];\n  service?: ManifestService[];\n  receiver?: ManifestReceiver[];\n  'meta-data'?: ManifestMetaData[];\n  'uses-library'?: ManifestUsesLibrary[];\n  'activity-alias'?: ManifestActivityAlias[];\n  // ...\n};\n\ntype ManifestPermission = {\n  $: AndroidManifestAttributes & {\n    'android:protectionLevel'?: string | 'signature';\n  };\n};\n\nexport type ManifestUsesPermission = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestUsesFeature = {\n  $: AndroidManifestAttributes & {\n    'android:glEsVersion'?: string;\n    'android:required': StringBoolean;\n  };\n};\n\nexport type AndroidManifest = {\n  manifest: {\n    // Probably more, but this is currently all we'd need for most cases in Expo.\n    $: {\n      'xmlns:android': string;\n      'xmlns:tools'?: string;\n      package?: string;\n      [key: string]: string | undefined;\n    };\n    permission?: ManifestPermission[];\n    'uses-permission'?: ManifestUsesPermission[];\n    'uses-permission-sdk-23'?: ManifestUsesPermission[];\n    'uses-feature'?: ManifestUsesFeature[];\n    queries: ManifestQuery[];\n    application?: ManifestApplication[];\n  };\n};\n\ntype ManifestQueryIntent = Omit<ManifestIntentFilter, '$'>;\n\nexport type ManifestQuery = {\n  package?: {\n    $: {\n      'android:name': string;\n    };\n  }[];\n  intent?: ManifestQueryIntent[];\n  provider?: {\n    $: {\n      'android:authorities': string;\n    };\n  }[];\n};\n\nexport async function writeAndroidManifestAsync(\n  manifestPath: string,\n  androidManifest: AndroidManifest\n): Promise<void> {\n  const manifestXml = XML.format(androidManifest);\n  await fs.promises.mkdir(path.dirname(manifestPath), { recursive: true });\n  await fs.promises.writeFile(manifestPath, manifestXml);\n}\n\nexport async function readAndroidManifestAsync(manifestPath: string): Promise<AndroidManifest> {\n  const xml = await XML.readXMLAsync({ path: manifestPath });\n  if (!isManifest(xml)) {\n    throw new Error('Invalid manifest found at: ' + manifestPath);\n  }\n  return xml;\n}\n\nfunction isManifest(xml: XML.XMLObject): xml is AndroidManifest {\n  // TODO: Maybe more validation\n  return !!xml.manifest;\n}\n\n/** Returns the `manifest.application` tag ending in `.MainApplication` */\nexport function getMainApplication(androidManifest: AndroidManifest): ManifestApplication | null {\n  return (\n    androidManifest?.manifest?.application?.filter((e) =>\n      e?.$?.['android:name'].endsWith('.MainApplication')\n    )[0] ?? null\n  );\n}\n\nexport function getMainApplicationOrThrow(androidManifest: AndroidManifest): ManifestApplication {\n  const mainApplication = getMainApplication(androidManifest);\n  assert(mainApplication, 'AndroidManifest.xml is missing the required MainApplication element');\n  return mainApplication;\n}\n\nexport function getMainActivityOrThrow(androidManifest: AndroidManifest): ManifestActivity {\n  const mainActivity = getMainActivity(androidManifest);\n  assert(mainActivity, 'AndroidManifest.xml is missing the required MainActivity element');\n  return mainActivity;\n}\n\nexport function getRunnableActivity(androidManifest: AndroidManifest): ManifestActivity | null {\n  const firstApplication =\n    androidManifest?.manifest?.application?.[0] ?? getMainApplication(androidManifest);\n  if (!firstApplication) {\n    return null;\n  }\n\n  // Get enabled activities\n  const enabledActivities = firstApplication.activity?.filter?.(\n    (e: any) => e.$['android:enabled'] !== 'false' && e.$['android:enabled'] !== false\n  );\n\n  if (!enabledActivities) {\n    return null;\n  }\n\n  const isIntentFilterRunnable = (intentFilter: ManifestIntentFilter): boolean => {\n    return (\n      !!intentFilter.action?.some(\n        (action) => action.$['android:name'] === 'android.intent.action.MAIN'\n      ) &&\n      !!intentFilter.category?.some(\n        (category) => category.$['android:name'] === 'android.intent.category.LAUNCHER'\n      )\n    );\n  };\n\n  // Get the activity that has a runnable intent-filter\n  for (const activity of enabledActivities) {\n    if (Array.isArray(activity['intent-filter'])) {\n      for (const intentFilter of activity['intent-filter']) {\n        if (isIntentFilterRunnable(intentFilter)) {\n          return activity;\n        }\n      }\n    }\n  }\n\n  const enabledActivityNames = enabledActivities.map((e) => e.$['android:name']);\n  // If no runnable activity is found, check for matching activity-alias that may be runnable\n  const aliases = (firstApplication['activity-alias'] ?? []).filter(\n    // https://developer.android.com/guide/topics/manifest/activity-alias-element\n    (e: any) =>\n      e.$['android:enabled'] !== 'false' &&\n      enabledActivityNames.includes(e.$['android:targetActivity'])\n  );\n  if (aliases.length) {\n    for (const alias of aliases) {\n      if (Array.isArray(alias['intent-filter'])) {\n        for (const intentFilter of alias['intent-filter']) {\n          if (isIntentFilterRunnable(intentFilter)) {\n            const matchingActivity = enabledActivities.find(\n              (e) => e.$['android:name'] === alias.$!['android:targetActivity']\n            );\n            if (matchingActivity) {\n              return matchingActivity;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  return null;\n}\n\nexport function getMainActivity(androidManifest: AndroidManifest): ManifestActivity | null {\n  const mainActivity = androidManifest?.manifest?.application?.[0]?.activity?.filter?.(\n    (e: any) => e.$['android:name'] === '.MainActivity'\n  );\n  return mainActivity?.[0] ?? null;\n}\n\nexport function addMetaDataItemToMainApplication(\n  mainApplication: ManifestApplication,\n  itemName: string,\n  itemValue: string,\n  itemType: 'resource' | 'value' = 'value'\n): ManifestApplication {\n  let existingMetaDataItem;\n  const newItem = {\n    $: prefixAndroidKeys({ name: itemName, [itemType]: itemValue }),\n  } as ManifestMetaData;\n  if (mainApplication['meta-data']) {\n    existingMetaDataItem = mainApplication['meta-data'].filter(\n      (e: any) => e.$['android:name'] === itemName\n    );\n    if (existingMetaDataItem.length) {\n      existingMetaDataItem[0].$[`android:${itemType}` as keyof ManifestMetaDataAttributes] =\n        itemValue;\n    } else {\n      mainApplication['meta-data'].push(newItem);\n    }\n  } else {\n    mainApplication['meta-data'] = [newItem];\n  }\n  return mainApplication;\n}\n\nexport function removeMetaDataItemFromMainApplication(mainApplication: any, itemName: string) {\n  const index = findMetaDataItem(mainApplication, itemName);\n  if (mainApplication?.['meta-data'] && index > -1) {\n    mainApplication['meta-data'].splice(index, 1);\n  }\n  return mainApplication;\n}\n\nfunction findApplicationSubItem(\n  mainApplication: ManifestApplication,\n  category: keyof ManifestApplication,\n  itemName: string\n): number {\n  const parent = mainApplication[category];\n  if (Array.isArray(parent)) {\n    const index = parent.findIndex((e: any) => e.$['android:name'] === itemName);\n\n    return index;\n  }\n  return -1;\n}\n\nexport function findMetaDataItem(mainApplication: any, itemName: string): number {\n  return findApplicationSubItem(mainApplication, 'meta-data', itemName);\n}\n\nexport function findUsesLibraryItem(mainApplication: any, itemName: string): number {\n  return findApplicationSubItem(mainApplication, 'uses-library', itemName);\n}\n\nexport function getMainApplicationMetaDataValue(\n  androidManifest: AndroidManifest,\n  name: string\n): string | null {\n  const mainApplication = getMainApplication(androidManifest);\n\n  if (mainApplication?.hasOwnProperty('meta-data')) {\n    const item = mainApplication?.['meta-data']?.find((e: any) => e.$['android:name'] === name);\n    return item?.$['android:value'] ?? null;\n  }\n\n  return null;\n}\n\nexport function addUsesLibraryItemToMainApplication(\n  mainApplication: ManifestApplication,\n  item: { name: string; required?: boolean }\n): ManifestApplication {\n  let existingMetaDataItem;\n  const newItem = {\n    $: prefixAndroidKeys(item),\n  } as ManifestUsesLibrary;\n\n  if (mainApplication['uses-library']) {\n    existingMetaDataItem = mainApplication['uses-library'].filter(\n      (e) => e.$['android:name'] === item.name\n    );\n    if (existingMetaDataItem.length) {\n      existingMetaDataItem[0].$ = newItem.$;\n    } else {\n      mainApplication['uses-library'].push(newItem);\n    }\n  } else {\n    mainApplication['uses-library'] = [newItem];\n  }\n  return mainApplication;\n}\n\nexport function removeUsesLibraryItemFromMainApplication(\n  mainApplication: ManifestApplication,\n  itemName: string\n) {\n  const index = findUsesLibraryItem(mainApplication, itemName);\n  if (mainApplication?.['uses-library'] && index > -1) {\n    mainApplication['uses-library'].splice(index, 1);\n  }\n  return mainApplication;\n}\n\nexport function prefixAndroidKeys<T extends Record<string, any> = Record<string, string>>(\n  head: T\n): Record<string, any> {\n  // prefix all keys with `android:`\n  return Object.entries(head).reduce(\n    (prev, [key, curr]) => ({ ...prev, [`android:${key}`]: curr }),\n    {} as T\n  );\n}\n\n/**\n * Ensure the `tools:*` namespace is available in the manifest.\n *\n * @param manifest AndroidManifest.xml\n * @returns manifest with the `tools:*` namespace available\n */\nexport function ensureToolsAvailable(manifest: AndroidManifest) {\n  return ensureManifestHasNamespace(manifest, {\n    namespace: 'xmlns:tools',\n    url: 'http://schemas.android.com/tools',\n  });\n}\n\n/**\n * Ensure a particular namespace is available in the manifest.\n *\n * @param manifest `AndroidManifest.xml`\n * @returns manifest with the provided namespace available\n */\nfunction ensureManifestHasNamespace(\n  manifest: AndroidManifest,\n  { namespace, url }: { namespace: string; url: string }\n) {\n  if (manifest?.manifest?.$?.[namespace]) {\n    return manifest;\n  }\n  manifest.manifest.$[namespace] = url;\n  return manifest;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoC,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAiDpC;;AAwIO,eAAemB,yBAAyBA,CAC7CC,YAAoB,EACpBC,eAAgC,EACjB;EACf,MAAMC,WAAW,GAAGzB,GAAG,CAAD,CAAC,CAAC0B,MAAM,CAACF,eAAe,CAAC;EAC/C,MAAMG,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,eAAI,CAACC,OAAO,CAACR,YAAY,CAAC,EAAE;IAAES,SAAS,EAAE;EAAK,CAAC,CAAC;EACxE,MAAML,aAAE,CAACC,QAAQ,CAACK,SAAS,CAACV,YAAY,EAAEE,WAAW,CAAC;AACxD;AAEO,eAAeS,wBAAwBA,CAACX,YAAoB,EAA4B;EAC7F,MAAMY,GAAG,GAAG,MAAMnC,GAAG,CAAD,CAAC,CAACoC,YAAY,CAAC;IAAEN,IAAI,EAAEP;EAAa,CAAC,CAAC;EAC1D,IAAI,CAACc,UAAU,CAACF,GAAG,CAAC,EAAE;IACpB,MAAM,IAAIG,KAAK,CAAC,6BAA6B,GAAGf,YAAY,CAAC;EAC/D;EACA,OAAOY,GAAG;AACZ;AAEA,SAASE,UAAUA,CAACF,GAAkB,EAA0B;EAC9D;EACA,OAAO,CAAC,CAACA,GAAG,CAACI,QAAQ;AACvB;;AAEA;AACO,SAASC,kBAAkBA,CAAChB,eAAgC,EAA8B;EAC/F,OACEA,eAAe,EAAEe,QAAQ,EAAEE,WAAW,EAAEC,MAAM,CAAEvC,CAAC,IAC/CA,CAAC,EAAEwC,CAAC,GAAG,cAAc,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CACpD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAEhB;AAEO,SAASC,yBAAyBA,CAACrB,eAAgC,EAAuB;EAC/F,MAAMsB,eAAe,GAAGN,kBAAkB,CAAChB,eAAe,CAAC;EAC3D,IAAAuB,iBAAM,EAACD,eAAe,EAAE,qEAAqE,CAAC;EAC9F,OAAOA,eAAe;AACxB;AAEO,SAASE,sBAAsBA,CAACxB,eAAgC,EAAoB;EACzF,MAAMyB,YAAY,GAAGC,eAAe,CAAC1B,eAAe,CAAC;EACrD,IAAAuB,iBAAM,EAACE,YAAY,EAAE,kEAAkE,CAAC;EACxF,OAAOA,YAAY;AACrB;AAEO,SAASE,mBAAmBA,CAAC3B,eAAgC,EAA2B;EAC7F,MAAM4B,gBAAgB,GACpB5B,eAAe,EAAEe,QAAQ,EAAEE,WAAW,GAAG,CAAC,CAAC,IAAID,kBAAkB,CAAChB,eAAe,CAAC;EACpF,IAAI,CAAC4B,gBAAgB,EAAE;IACrB,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,iBAAiB,GAAGD,gBAAgB,CAACE,QAAQ,EAAEZ,MAAM,GACxDvC,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,iBAAiB,CAAC,KAAK,OAAO,IAAIxC,CAAC,CAACwC,CAAC,CAAC,iBAAiB,CAAC,KAAK,KAC/E,CAAC;EAED,IAAI,CAACU,iBAAiB,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,MAAME,sBAAsB,GAAIC,YAAkC,IAAc;IAC9E,OACE,CAAC,CAACA,YAAY,CAACC,MAAM,EAAEC,IAAI,CACxBD,MAAM,IAAKA,MAAM,CAACd,CAAC,CAAC,cAAc,CAAC,KAAK,4BAC3C,CAAC,IACD,CAAC,CAACa,YAAY,CAACG,QAAQ,EAAED,IAAI,CAC1BC,QAAQ,IAAKA,QAAQ,CAAChB,CAAC,CAAC,cAAc,CAAC,KAAK,kCAC/C,CAAC;EAEL,CAAC;;EAED;EACA,KAAK,MAAMW,QAAQ,IAAID,iBAAiB,EAAE;IACxC,IAAIO,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,eAAe,CAAC,CAAC,EAAE;MAC5C,KAAK,MAAME,YAAY,IAAIF,QAAQ,CAAC,eAAe,CAAC,EAAE;QACpD,IAAIC,sBAAsB,CAACC,YAAY,CAAC,EAAE;UACxC,OAAOF,QAAQ;QACjB;MACF;IACF;EACF;EAEA,MAAMQ,oBAAoB,GAAGT,iBAAiB,CAACU,GAAG,CAAE5D,CAAC,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,CAAC;EAC9E;EACA,MAAMqB,OAAO,GAAG,CAACZ,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAEV,MAAM;EAC/D;EACCvC,CAAM,IACLA,CAAC,CAACwC,CAAC,CAAC,iBAAiB,CAAC,KAAK,OAAO,IAClCmB,oBAAoB,CAACG,QAAQ,CAAC9D,CAAC,CAACwC,CAAC,CAAC,wBAAwB,CAAC,CAC/D,CAAC;EACD,IAAIqB,OAAO,CAACE,MAAM,EAAE;IAClB,KAAK,MAAMC,KAAK,IAAIH,OAAO,EAAE;MAC3B,IAAIJ,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,eAAe,CAAC,CAAC,EAAE;QACzC,KAAK,MAAMX,YAAY,IAAIW,KAAK,CAAC,eAAe,CAAC,EAAE;UACjD,IAAIZ,sBAAsB,CAACC,YAAY,CAAC,EAAE;YACxC,MAAMY,gBAAgB,GAAGf,iBAAiB,CAACgB,IAAI,CAC5ClE,CAAC,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAKwB,KAAK,CAACxB,CAAC,CAAE,wBAAwB,CAClE,CAAC;YACD,IAAIyB,gBAAgB,EAAE;cACpB,OAAOA,gBAAgB;YACzB;UACF;QACF;MACF;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEO,SAASlB,eAAeA,CAAC1B,eAAgC,EAA2B;EACzF,MAAMyB,YAAY,GAAGzB,eAAe,EAAEe,QAAQ,EAAEE,WAAW,GAAG,CAAC,CAAC,EAAEa,QAAQ,EAAEZ,MAAM,GAC/EvC,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAK,eACtC,CAAC;EACD,OAAOM,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI;AAClC;AAEO,SAASqB,gCAAgCA,CAC9CxB,eAAoC,EACpCyB,QAAgB,EAChBC,SAAiB,EACjBC,QAA8B,GAAG,OAAO,EACnB;EACrB,IAAIC,oBAAoB;EACxB,MAAMC,OAAO,GAAG;IACdhC,CAAC,EAAEiC,iBAAiB,CAAC;MAAEC,IAAI,EAAEN,QAAQ;MAAE,CAACE,QAAQ,GAAGD;IAAU,CAAC;EAChE,CAAqB;EACrB,IAAI1B,eAAe,CAAC,WAAW,CAAC,EAAE;IAChC4B,oBAAoB,GAAG5B,eAAe,CAAC,WAAW,CAAC,CAACJ,MAAM,CACvDvC,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAK4B,QACtC,CAAC;IACD,IAAIG,oBAAoB,CAACR,MAAM,EAAE;MAC/BQ,oBAAoB,CAAC,CAAC,CAAC,CAAC/B,CAAC,CAAC,WAAW8B,QAAQ,EAAE,CAAqC,GAClFD,SAAS;IACb,CAAC,MAAM;MACL1B,eAAe,CAAC,WAAW,CAAC,CAACgC,IAAI,CAACH,OAAO,CAAC;IAC5C;EACF,CAAC,MAAM;IACL7B,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC6B,OAAO,CAAC;EAC1C;EACA,OAAO7B,eAAe;AACxB;AAEO,SAASiC,qCAAqCA,CAACjC,eAAoB,EAAEyB,QAAgB,EAAE;EAC5F,MAAMS,KAAK,GAAGC,gBAAgB,CAACnC,eAAe,EAAEyB,QAAQ,CAAC;EACzD,IAAIzB,eAAe,GAAG,WAAW,CAAC,IAAIkC,KAAK,GAAG,CAAC,CAAC,EAAE;IAChDlC,eAAe,CAAC,WAAW,CAAC,CAACoC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC/C;EACA,OAAOlC,eAAe;AACxB;AAEA,SAASqC,sBAAsBA,CAC7BrC,eAAoC,EACpCa,QAAmC,EACnCY,QAAgB,EACR;EACR,MAAMa,MAAM,GAAGtC,eAAe,CAACa,QAAQ,CAAC;EACxC,IAAIC,KAAK,CAACC,OAAO,CAACuB,MAAM,CAAC,EAAE;IACzB,MAAMJ,KAAK,GAAGI,MAAM,CAACC,SAAS,CAAElF,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAK4B,QAAQ,CAAC;IAE5E,OAAOS,KAAK;EACd;EACA,OAAO,CAAC,CAAC;AACX;AAEO,SAASC,gBAAgBA,CAACnC,eAAoB,EAAEyB,QAAgB,EAAU;EAC/E,OAAOY,sBAAsB,CAACrC,eAAe,EAAE,WAAW,EAAEyB,QAAQ,CAAC;AACvE;AAEO,SAASe,mBAAmBA,CAACxC,eAAoB,EAAEyB,QAAgB,EAAU;EAClF,OAAOY,sBAAsB,CAACrC,eAAe,EAAE,cAAc,EAAEyB,QAAQ,CAAC;AAC1E;AAEO,SAASgB,+BAA+BA,CAC7C/D,eAAgC,EAChCqD,IAAY,EACG;EACf,MAAM/B,eAAe,GAAGN,kBAAkB,CAAChB,eAAe,CAAC;EAE3D,IAAIsB,eAAe,EAAE5B,cAAc,CAAC,WAAW,CAAC,EAAE;IAChD,MAAMsE,IAAI,GAAG1C,eAAe,GAAG,WAAW,CAAC,EAAEuB,IAAI,CAAElE,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAKkC,IAAI,CAAC;IAC3F,OAAOW,IAAI,EAAE7C,CAAC,CAAC,eAAe,CAAC,IAAI,IAAI;EACzC;EAEA,OAAO,IAAI;AACb;AAEO,SAAS8C,mCAAmCA,CACjD3C,eAAoC,EACpC0C,IAA0C,EACrB;EACrB,IAAId,oBAAoB;EACxB,MAAMC,OAAO,GAAG;IACdhC,CAAC,EAAEiC,iBAAiB,CAACY,IAAI;EAC3B,CAAwB;EAExB,IAAI1C,eAAe,CAAC,cAAc,CAAC,EAAE;IACnC4B,oBAAoB,GAAG5B,eAAe,CAAC,cAAc,CAAC,CAACJ,MAAM,CAC1DvC,CAAC,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAK6C,IAAI,CAACX,IACtC,CAAC;IACD,IAAIH,oBAAoB,CAACR,MAAM,EAAE;MAC/BQ,oBAAoB,CAAC,CAAC,CAAC,CAAC/B,CAAC,GAAGgC,OAAO,CAAChC,CAAC;IACvC,CAAC,MAAM;MACLG,eAAe,CAAC,cAAc,CAAC,CAACgC,IAAI,CAACH,OAAO,CAAC;IAC/C;EACF,CAAC,MAAM;IACL7B,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC6B,OAAO,CAAC;EAC7C;EACA,OAAO7B,eAAe;AACxB;AAEO,SAAS4C,wCAAwCA,CACtD5C,eAAoC,EACpCyB,QAAgB,EAChB;EACA,MAAMS,KAAK,GAAGM,mBAAmB,CAACxC,eAAe,EAAEyB,QAAQ,CAAC;EAC5D,IAAIzB,eAAe,GAAG,cAAc,CAAC,IAAIkC,KAAK,GAAG,CAAC,CAAC,EAAE;IACnDlC,eAAe,CAAC,cAAc,CAAC,CAACoC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAClD;EACA,OAAOlC,eAAe;AACxB;AAEO,SAAS8B,iBAAiBA,CAC/Be,IAAO,EACc;EACrB;EACA,OAAO7E,MAAM,CAAC8E,OAAO,CAACD,IAAI,CAAC,CAACE,MAAM,CAChC,CAACC,IAAI,EAAE,CAACC,GAAG,EAAEC,IAAI,CAAC,MAAM;IAAE,GAAGF,IAAI;IAAE,CAAC,WAAWC,GAAG,EAAE,GAAGC;EAAK,CAAC,CAAC,EAC9D,CAAC,CACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,oBAAoBA,CAAC1D,QAAyB,EAAE;EAC9D,OAAO2D,0BAA0B,CAAC3D,QAAQ,EAAE;IAC1C4D,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,0BAA0BA,CACjC3D,QAAyB,EACzB;EAAE4D,SAAS;EAAEC;AAAwC,CAAC,EACtD;EACA,IAAI7D,QAAQ,EAAEA,QAAQ,EAAEI,CAAC,GAAGwD,SAAS,CAAC,EAAE;IACtC,OAAO5D,QAAQ;EACjB;EACAA,QAAQ,CAACA,QAAQ,CAACI,CAAC,CAACwD,SAAS,CAAC,GAAGC,GAAG;EACpC,OAAO7D,QAAQ;AACjB", "ignoreList": []}