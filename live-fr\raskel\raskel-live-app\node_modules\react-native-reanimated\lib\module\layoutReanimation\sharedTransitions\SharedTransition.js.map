{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "withTiming", "LayoutAnimationType", "SharedTransitionType", "ReduceMotion", "ProgressTransitionManager", "updateLayoutAnimations", "getReduceMotionFromConfig", "SUPPORTED_PROPS", "SharedTransition", "constructor", "System", "undefined", "custom", "customAnimationFactory", "_customAnimationFactory", "progressAnimation", "progressAnimationCallback", "_customProgressAnimation", "viewTag", "values", "progress", "newStyles", "global", "_notifyAboutProgress", "duration", "_transitionDuration", "reduceMotion", "_reduceMotion", "defaultTransitionType", "transitionType", "_defaultTransitionType", "registerTransition", "sharedTransitionTag", "isUnmounting", "getReduceMotion", "transitionAnimation", "getTransitionAnimation", "getProgressAnimation", "ANIMATION", "PROGRESS_ANIMATION", "layoutAnimationType", "SHARED_ELEMENT_TRANSITION", "SHARED_ELEMENT_TRANSITION_PROGRESS", "_progressTransitionManager", "addProgressAnimation", "unregisterTransition", "removeProgressAnimation", "_animation", "buildAnimation", "_progressAnimation", "buildProgressAnimation", "animationFactory", "transitionDuration", "animations", "initialValues", "includes", "Error", "propName", "matrix", "targetTransformMatrix", "transformMatrix", "capitalizedPropName", "char<PERSON>t", "toUpperCase", "slice", "keyToTargetValue", "currentTransformMatrix", "keyToCurrentValue", "propertyName", "currentMatrix", "targetMatrix", "newMatrix", "Array", "PropertyName", "currentPropertyName", "targetPropertyName", "currentValue", "targetValue"], "sources": ["SharedTransition.ts"], "sourcesContent": ["'use strict';\nimport { withTiming } from '../../animation';\nimport type {\n  SharedTransitionAnimationsFunction,\n  SharedTransitionAnimationsValues,\n  CustomProgressAnimation,\n  ProgressAnimation,\n  LayoutAnimationsOptions,\n} from '../animationBuilder/commonTypes';\nimport {\n  LayoutAnimationType,\n  SharedTransitionType,\n} from '../animationBuilder/commonTypes';\nimport type { StyleProps } from '../../commonTypes';\nimport { ReduceMotion } from '../../commonTypes';\nimport { ProgressTransitionManager } from './ProgressTransitionManager';\nimport { updateLayoutAnimations } from '../../UpdateLayoutAnimations';\nimport { getReduceMotionFromConfig } from '../../animation/util';\n\nconst SUPPORTED_PROPS = [\n  'width',\n  'height',\n  'originX',\n  'originY',\n  'transform',\n  'borderRadius',\n  'borderTopLeftRadius',\n  'borderTopRightRadius',\n  'borderBottomLeftRadius',\n  'borderBottomRightRadius',\n] as const;\n\ntype AnimationFactory = (\n  values: SharedTransitionAnimationsValues\n) => StyleProps;\n\n/**\n * A SharedTransition builder class.\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n * @experimental\n */\nexport class SharedTransition {\n  private _customAnimationFactory: AnimationFactory | null = null;\n  private _animation: SharedTransitionAnimationsFunction | null = null;\n  private _transitionDuration = 500;\n  private _reduceMotion: ReduceMotion = ReduceMotion.System;\n  private _customProgressAnimation?: ProgressAnimation = undefined;\n  private _progressAnimation?: ProgressAnimation = undefined;\n  private _defaultTransitionType?: SharedTransitionType = undefined;\n  private static _progressTransitionManager = new ProgressTransitionManager();\n\n  public custom(customAnimationFactory: AnimationFactory): SharedTransition {\n    this._customAnimationFactory = customAnimationFactory;\n    return this;\n  }\n\n  public progressAnimation(\n    progressAnimationCallback: CustomProgressAnimation\n  ): SharedTransition {\n    this._customProgressAnimation = (viewTag, values, progress) => {\n      'worklet';\n      const newStyles = progressAnimationCallback(values, progress);\n      global._notifyAboutProgress(viewTag, newStyles, true);\n    };\n    return this;\n  }\n\n  public duration(duration: number): SharedTransition {\n    this._transitionDuration = duration;\n    return this;\n  }\n\n  public reduceMotion(_reduceMotion: ReduceMotion): this {\n    this._reduceMotion = _reduceMotion;\n    return this;\n  }\n\n  public defaultTransitionType(\n    transitionType: SharedTransitionType\n  ): SharedTransition {\n    this._defaultTransitionType = transitionType;\n    return this;\n  }\n\n  public registerTransition(\n    viewTag: number,\n    sharedTransitionTag: string,\n    isUnmounting = false\n  ) {\n    if (getReduceMotionFromConfig(this.getReduceMotion())) {\n      return;\n    }\n\n    const transitionAnimation = this.getTransitionAnimation();\n    const progressAnimation = this.getProgressAnimation();\n    if (!this._defaultTransitionType) {\n      if (this._customAnimationFactory && !this._customProgressAnimation) {\n        this._defaultTransitionType = SharedTransitionType.ANIMATION;\n      } else {\n        this._defaultTransitionType = SharedTransitionType.PROGRESS_ANIMATION;\n      }\n    }\n    const layoutAnimationType =\n      this._defaultTransitionType === SharedTransitionType.ANIMATION\n        ? LayoutAnimationType.SHARED_ELEMENT_TRANSITION\n        : LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS;\n    updateLayoutAnimations(\n      viewTag,\n      layoutAnimationType,\n      transitionAnimation,\n      sharedTransitionTag,\n      isUnmounting\n    );\n    SharedTransition._progressTransitionManager.addProgressAnimation(\n      viewTag,\n      progressAnimation\n    );\n  }\n\n  public unregisterTransition(viewTag: number, isUnmounting = false): void {\n    const layoutAnimationType =\n      this._defaultTransitionType === SharedTransitionType.ANIMATION\n        ? LayoutAnimationType.SHARED_ELEMENT_TRANSITION\n        : LayoutAnimationType.SHARED_ELEMENT_TRANSITION_PROGRESS;\n    updateLayoutAnimations(\n      viewTag,\n      layoutAnimationType,\n      undefined,\n      undefined,\n      isUnmounting\n    );\n    SharedTransition._progressTransitionManager.removeProgressAnimation(\n      viewTag,\n      isUnmounting\n    );\n  }\n\n  public getReduceMotion(): ReduceMotion {\n    return this._reduceMotion;\n  }\n\n  private getTransitionAnimation(): SharedTransitionAnimationsFunction {\n    if (!this._animation) {\n      this.buildAnimation();\n    }\n    return this._animation!;\n  }\n\n  private getProgressAnimation(): ProgressAnimation {\n    if (!this._progressAnimation) {\n      this.buildProgressAnimation();\n    }\n    return this._progressAnimation!;\n  }\n\n  private buildAnimation() {\n    const animationFactory = this._customAnimationFactory;\n    const transitionDuration = this._transitionDuration;\n    const reduceMotion = this._reduceMotion;\n    this._animation = (values: SharedTransitionAnimationsValues) => {\n      'worklet';\n      let animations: {\n        [key: string]: unknown;\n      } = {};\n      const initialValues: {\n        [key: string]: unknown;\n      } = {};\n\n      if (animationFactory) {\n        animations = animationFactory(values);\n        for (const key in animations) {\n          if (!(SUPPORTED_PROPS as readonly string[]).includes(key)) {\n            throw new Error(\n              `[Reanimated] The prop '${key}' is not supported yet.`\n            );\n          }\n        }\n      } else {\n        for (const propName of SUPPORTED_PROPS) {\n          if (propName === 'transform') {\n            const matrix = values.targetTransformMatrix;\n            animations.transformMatrix = withTiming(matrix, {\n              reduceMotion,\n              duration: transitionDuration,\n            });\n          } else {\n            const capitalizedPropName = `${propName\n              .charAt(0)\n              .toUpperCase()}${propName.slice(\n              1\n            )}` as Capitalize<LayoutAnimationsOptions>;\n            const keyToTargetValue = `target${capitalizedPropName}` as const;\n            animations[propName] = withTiming(values[keyToTargetValue], {\n              reduceMotion,\n              duration: transitionDuration,\n            });\n          }\n        }\n      }\n\n      for (const propName in animations) {\n        if (propName === 'transform') {\n          initialValues.transformMatrix = values.currentTransformMatrix;\n        } else {\n          const capitalizedPropName = (propName.charAt(0).toUpperCase() +\n            propName.slice(1)) as Capitalize<LayoutAnimationsOptions>;\n          const keyToCurrentValue = `current${capitalizedPropName}` as const;\n          initialValues[propName] = values[keyToCurrentValue];\n        }\n      }\n\n      return { initialValues, animations };\n    };\n  }\n\n  private buildProgressAnimation() {\n    if (this._customProgressAnimation) {\n      this._progressAnimation = this._customProgressAnimation;\n      return;\n    }\n    this._progressAnimation = (viewTag, values, progress) => {\n      'worklet';\n      const newStyles: { [key: string]: number | number[] } = {};\n      for (const propertyName of SUPPORTED_PROPS) {\n        if (propertyName === 'transform') {\n          // this is not the perfect solution, but at this moment it just interpolates the whole\n          // matrix instead of interpolating scale, translate, rotate, etc. separately\n          const currentMatrix = values.currentTransformMatrix;\n          const targetMatrix = values.targetTransformMatrix;\n          const newMatrix = new Array(9);\n          for (let i = 0; i < 9; i++) {\n            newMatrix[i] =\n              progress * (targetMatrix[i] - currentMatrix[i]) +\n              currentMatrix[i];\n          }\n          newStyles.transformMatrix = newMatrix;\n        } else {\n          // PropertyName == propertyName with capitalized fist letter, (width -> Width)\n          const PropertyName = (propertyName.charAt(0).toUpperCase() +\n            propertyName.slice(1)) as Capitalize<LayoutAnimationsOptions>;\n          const currentPropertyName = `current${PropertyName}` as const;\n          const targetPropertyName = `target${PropertyName}` as const;\n\n          const currentValue = values[currentPropertyName];\n          const targetValue = values[targetPropertyName];\n\n          newStyles[propertyName] =\n            progress * (targetValue - currentValue) + currentValue;\n        }\n      }\n      global._notifyAboutProgress(viewTag, newStyles, true);\n    };\n  }\n\n  // static builder methods i.e. shared transition modifiers\n\n  /**\n   * Lets you create a custom shared transition animation. Other shared transition modifiers can be chained alongside this modifier.\n   *\n   * @param customAnimationFactory - Callback function that have to return an object with styles for the custom shared transition.\n   * @returns A {@link SharedTransition} object. Styles returned from this function need to be to the `sharedTransitionStyle` prop.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  public static custom(\n    customAnimationFactory: AnimationFactory\n  ): SharedTransition {\n    return new SharedTransition().custom(customAnimationFactory);\n  }\n\n  /**\n   * Lets you change the duration of the shared transition. Other shared transition modifiers can be chained alongside this modifier.\n   *\n   * @param duration - The duration of the shared transition animation in milliseconds.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  public static duration(duration: number): SharedTransition {\n    return new SharedTransition().duration(duration);\n  }\n\n  /**\n   * Lets you create a shared transition animation bound to the progress between navigation screens. Other shared transition modifiers can be chained alongside this modifier.\n   *\n   * @param progressAnimationCallback - A callback called with the current progress value on every animation frame. It should return an object with styles for the shared transition.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  public static progressAnimation(\n    progressAnimationCallback: CustomProgressAnimation\n  ): SharedTransition {\n    return new SharedTransition().progressAnimation(progressAnimationCallback);\n  }\n\n  /**\n   * Whether the transition is progress-bound or not. Other shared transition modifiers can be chained alongside this modifier.\n   *\n   * @param transitionType - Type of the transition. Configured with {@link SharedTransitionType} enum.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  public static defaultTransitionType(\n    transitionType: SharedTransitionType\n  ): SharedTransition {\n    return new SharedTransition().defaultTransitionType(transitionType);\n  }\n\n  /**\n   * Lets you adjust the behavior when the device's reduced motion accessibility setting is turned on. Other shared transition modifiers can be chained alongside this modifier.\n   *\n   * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/shared-element-transitions/overview\n   * @experimental\n   */\n  public static reduceMotion(reduceMotion: ReduceMotion): SharedTransition {\n    return new SharedTransition().reduceMotion(reduceMotion);\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,UAAU,QAAQ,iBAAiB;AAQ5C,SACEC,mBAAmB,EACnBC,oBAAoB,QACf,iCAAiC;AAExC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,sBAAsB,QAAQ,8BAA8B;AACrE,SAASC,yBAAyB,QAAQ,sBAAsB;AAEhE,MAAMC,eAAe,GAAG,CACtB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,SAAS,EACT,WAAW,EACX,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,wBAAwB,EACxB,yBAAyB,CACjB;AAMV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,CAAC;EAAAC,YAAA;IAAA9B,eAAA,kCAC+B,IAAI;IAAAA,eAAA,qBACC,IAAI;IAAAA,eAAA,8BACtC,GAAG;IAAAA,eAAA,wBACKwB,YAAY,CAACO,MAAM;IAAA/B,eAAA,mCACFgC,SAAS;IAAAhC,eAAA,6BACfgC,SAAS;IAAAhC,eAAA,iCACFgC,SAAS;EAAA;EAG1DC,MAAMA,CAACC,sBAAwC,EAAoB;IACxE,IAAI,CAACC,uBAAuB,GAAGD,sBAAsB;IACrD,OAAO,IAAI;EACb;EAEOE,iBAAiBA,CACtBC,yBAAkD,EAChC;IAClB,IAAI,CAACC,wBAAwB,GAAG,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MAC7D,SAAS;;MACT,MAAMC,SAAS,GAAGL,yBAAyB,CAACG,MAAM,EAAEC,QAAQ,CAAC;MAC7DE,MAAM,CAACC,oBAAoB,CAACL,OAAO,EAAEG,SAAS,EAAE,IAAI,CAAC;IACvD,CAAC;IACD,OAAO,IAAI;EACb;EAEOG,QAAQA,CAACA,QAAgB,EAAoB;IAClD,IAAI,CAACC,mBAAmB,GAAGD,QAAQ;IACnC,OAAO,IAAI;EACb;EAEOE,YAAYA,CAACC,aAA2B,EAAQ;IACrD,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACb;EAEOC,qBAAqBA,CAC1BC,cAAoC,EAClB;IAClB,IAAI,CAACC,sBAAsB,GAAGD,cAAc;IAC5C,OAAO,IAAI;EACb;EAEOE,kBAAkBA,CACvBb,OAAe,EACfc,mBAA2B,EAC3BC,YAAY,GAAG,KAAK,EACpB;IACA,IAAI3B,yBAAyB,CAAC,IAAI,CAAC4B,eAAe,CAAC,CAAC,CAAC,EAAE;MACrD;IACF;IAEA,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;IACzD,MAAMrB,iBAAiB,GAAG,IAAI,CAACsB,oBAAoB,CAAC,CAAC;IACrD,IAAI,CAAC,IAAI,CAACP,sBAAsB,EAAE;MAChC,IAAI,IAAI,CAAChB,uBAAuB,IAAI,CAAC,IAAI,CAACG,wBAAwB,EAAE;QAClE,IAAI,CAACa,sBAAsB,GAAG5B,oBAAoB,CAACoC,SAAS;MAC9D,CAAC,MAAM;QACL,IAAI,CAACR,sBAAsB,GAAG5B,oBAAoB,CAACqC,kBAAkB;MACvE;IACF;IACA,MAAMC,mBAAmB,GACvB,IAAI,CAACV,sBAAsB,KAAK5B,oBAAoB,CAACoC,SAAS,GAC1DrC,mBAAmB,CAACwC,yBAAyB,GAC7CxC,mBAAmB,CAACyC,kCAAkC;IAC5DrC,sBAAsB,CACpBa,OAAO,EACPsB,mBAAmB,EACnBL,mBAAmB,EACnBH,mBAAmB,EACnBC,YACF,CAAC;IACDzB,gBAAgB,CAACmC,0BAA0B,CAACC,oBAAoB,CAC9D1B,OAAO,EACPH,iBACF,CAAC;EACH;EAEO8B,oBAAoBA,CAAC3B,OAAe,EAAEe,YAAY,GAAG,KAAK,EAAQ;IACvE,MAAMO,mBAAmB,GACvB,IAAI,CAACV,sBAAsB,KAAK5B,oBAAoB,CAACoC,SAAS,GAC1DrC,mBAAmB,CAACwC,yBAAyB,GAC7CxC,mBAAmB,CAACyC,kCAAkC;IAC5DrC,sBAAsB,CACpBa,OAAO,EACPsB,mBAAmB,EACnB7B,SAAS,EACTA,SAAS,EACTsB,YACF,CAAC;IACDzB,gBAAgB,CAACmC,0BAA0B,CAACG,uBAAuB,CACjE5B,OAAO,EACPe,YACF,CAAC;EACH;EAEOC,eAAeA,CAAA,EAAiB;IACrC,OAAO,IAAI,CAACP,aAAa;EAC3B;EAEQS,sBAAsBA,CAAA,EAAuC;IACnE,IAAI,CAAC,IAAI,CAACW,UAAU,EAAE;MACpB,IAAI,CAACC,cAAc,CAAC,CAAC;IACvB;IACA,OAAO,IAAI,CAACD,UAAU;EACxB;EAEQV,oBAAoBA,CAAA,EAAsB;IAChD,IAAI,CAAC,IAAI,CAACY,kBAAkB,EAAE;MAC5B,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC/B;IACA,OAAO,IAAI,CAACD,kBAAkB;EAChC;EAEQD,cAAcA,CAAA,EAAG;IACvB,MAAMG,gBAAgB,GAAG,IAAI,CAACrC,uBAAuB;IACrD,MAAMsC,kBAAkB,GAAG,IAAI,CAAC3B,mBAAmB;IACnD,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,IAAI,CAACoB,UAAU,GAAI5B,MAAwC,IAAK;MAC9D,SAAS;;MACT,IAAIkC,UAEH,GAAG,CAAC,CAAC;MACN,MAAMC,aAEL,GAAG,CAAC,CAAC;MAEN,IAAIH,gBAAgB,EAAE;QACpBE,UAAU,GAAGF,gBAAgB,CAAChC,MAAM,CAAC;QACrC,KAAK,MAAMtC,GAAG,IAAIwE,UAAU,EAAE;UAC5B,IAAI,CAAE9C,eAAe,CAAuBgD,QAAQ,CAAC1E,GAAG,CAAC,EAAE;YACzD,MAAM,IAAI2E,KAAK,CACZ,0BAAyB3E,GAAI,yBAChC,CAAC;UACH;QACF;MACF,CAAC,MAAM;QACL,KAAK,MAAM4E,QAAQ,IAAIlD,eAAe,EAAE;UACtC,IAAIkD,QAAQ,KAAK,WAAW,EAAE;YAC5B,MAAMC,MAAM,GAAGvC,MAAM,CAACwC,qBAAqB;YAC3CN,UAAU,CAACO,eAAe,GAAG5D,UAAU,CAAC0D,MAAM,EAAE;cAC9ChC,YAAY;cACZF,QAAQ,EAAE4B;YACZ,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMS,mBAAmB,GAAI,GAAEJ,QAAQ,CACpCK,MAAM,CAAC,CAAC,CAAC,CACTC,WAAW,CAAC,CAAE,GAAEN,QAAQ,CAACO,KAAK,CAC/B,CACF,CAAE,EAAwC;YAC1C,MAAMC,gBAAgB,GAAI,SAAQJ,mBAAoB,EAAU;YAChER,UAAU,CAACI,QAAQ,CAAC,GAAGzD,UAAU,CAACmB,MAAM,CAAC8C,gBAAgB,CAAC,EAAE;cAC1DvC,YAAY;cACZF,QAAQ,EAAE4B;YACZ,CAAC,CAAC;UACJ;QACF;MACF;MAEA,KAAK,MAAMK,QAAQ,IAAIJ,UAAU,EAAE;QACjC,IAAII,QAAQ,KAAK,WAAW,EAAE;UAC5BH,aAAa,CAACM,eAAe,GAAGzC,MAAM,CAAC+C,sBAAsB;QAC/D,CAAC,MAAM;UACL,MAAML,mBAAmB,GAAIJ,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC3DN,QAAQ,CAACO,KAAK,CAAC,CAAC,CAAyC;UAC3D,MAAMG,iBAAiB,GAAI,UAASN,mBAAoB,EAAU;UAClEP,aAAa,CAACG,QAAQ,CAAC,GAAGtC,MAAM,CAACgD,iBAAiB,CAAC;QACrD;MACF;MAEA,OAAO;QAAEb,aAAa;QAAED;MAAW,CAAC;IACtC,CAAC;EACH;EAEQH,sBAAsBA,CAAA,EAAG;IAC/B,IAAI,IAAI,CAACjC,wBAAwB,EAAE;MACjC,IAAI,CAACgC,kBAAkB,GAAG,IAAI,CAAChC,wBAAwB;MACvD;IACF;IACA,IAAI,CAACgC,kBAAkB,GAAG,CAAC/B,OAAO,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MACvD,SAAS;;MACT,MAAMC,SAA+C,GAAG,CAAC,CAAC;MAC1D,KAAK,MAAM+C,YAAY,IAAI7D,eAAe,EAAE;QAC1C,IAAI6D,YAAY,KAAK,WAAW,EAAE;UAChC;UACA;UACA,MAAMC,aAAa,GAAGlD,MAAM,CAAC+C,sBAAsB;UACnD,MAAMI,YAAY,GAAGnD,MAAM,CAACwC,qBAAqB;UACjD,MAAMY,SAAS,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC;UAC9B,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC1BiF,SAAS,CAACjF,CAAC,CAAC,GACV8B,QAAQ,IAAIkD,YAAY,CAAChF,CAAC,CAAC,GAAG+E,aAAa,CAAC/E,CAAC,CAAC,CAAC,GAC/C+E,aAAa,CAAC/E,CAAC,CAAC;UACpB;UACA+B,SAAS,CAACuC,eAAe,GAAGW,SAAS;QACvC,CAAC,MAAM;UACL;UACA,MAAME,YAAY,GAAIL,YAAY,CAACN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACxDK,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAyC;UAC/D,MAAMU,mBAAmB,GAAI,UAASD,YAAa,EAAU;UAC7D,MAAME,kBAAkB,GAAI,SAAQF,YAAa,EAAU;UAE3D,MAAMG,YAAY,GAAGzD,MAAM,CAACuD,mBAAmB,CAAC;UAChD,MAAMG,WAAW,GAAG1D,MAAM,CAACwD,kBAAkB,CAAC;UAE9CtD,SAAS,CAAC+C,YAAY,CAAC,GACrBhD,QAAQ,IAAIyD,WAAW,GAAGD,YAAY,CAAC,GAAGA,YAAY;QAC1D;MACF;MACAtD,MAAM,CAACC,oBAAoB,CAACL,OAAO,EAAEG,SAAS,EAAE,IAAI,CAAC;IACvD,CAAC;EACH;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAcT,MAAMA,CAClBC,sBAAwC,EACtB;IAClB,OAAO,IAAIL,gBAAgB,CAAC,CAAC,CAACI,MAAM,CAACC,sBAAsB,CAAC;EAC9D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAcW,QAAQA,CAACA,QAAgB,EAAoB;IACzD,OAAO,IAAIhB,gBAAgB,CAAC,CAAC,CAACgB,QAAQ,CAACA,QAAQ,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAcT,iBAAiBA,CAC7BC,yBAAkD,EAChC;IAClB,OAAO,IAAIR,gBAAgB,CAAC,CAAC,CAACO,iBAAiB,CAACC,yBAAyB,CAAC;EAC5E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAcY,qBAAqBA,CACjCC,cAAoC,EAClB;IAClB,OAAO,IAAIrB,gBAAgB,CAAC,CAAC,CAACoB,qBAAqB,CAACC,cAAc,CAAC;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAcH,YAAYA,CAACA,YAA0B,EAAoB;IACvE,OAAO,IAAIlB,gBAAgB,CAAC,CAAC,CAACkB,YAAY,CAACA,YAAY,CAAC;EAC1D;AACF;AAAC/C,eAAA,CApRY6B,gBAAgB,gCAQiB,IAAIJ,yBAAyB,CAAC,CAAC", "ignoreList": []}