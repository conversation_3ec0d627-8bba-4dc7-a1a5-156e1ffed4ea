[{"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-screens/android/.cxx/Debug/2t6y4vk1/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\f91fac959c0e0da1b9b9ae28e08e7938\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-screens/android/.cxx/Debug/2t6y4vk1/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\jni-adapter.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp"}]