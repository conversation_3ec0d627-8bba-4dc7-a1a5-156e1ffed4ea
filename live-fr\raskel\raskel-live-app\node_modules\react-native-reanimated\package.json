{"name": "react-native-reanimated", "version": "3.15.4", "description": "More powerful alternative to Animated library for React Native.", "scripts": {"test": "yarn format:js && yarn lint:js && yarn test:unit", "test:unit": "jest", "test:update-snapshot": "jest --updateSnapshot", "lint": "yarn lint:js && yarn lint:plugin && yarn lint:cpp && yarn lint:java && yarn lint:ios", "lint:js": "eslint --ext '.js,.ts,.tsx' src __tests__ __typetests__ && yarn prettier --check src __tests__ __typetests__", "lint:plugin": "cd plugin && yarn lint && cd ..", "lint:docs": "cd docs && yarn lint && cd ..", "lint:java": "./android/gradlew -p android spotlessCheck -q", "lint:cpp": "./scripts/cpplint.sh", "lint:ios": "./scripts/validate-ios.sh && yarn format:ios --dry-run", "format": "yarn format:js && yarn format:plugin && yarn format:java && yarn format:ios && yarn format:android && yarn format:common", "format:js": "prettier --write --list-different src __tests__ __typetests__", "format:plugin": "cd plugin && yarn format", "format:java": "node ./scripts/format-java.js", "format:ios": "find apple/ -iname \"*.h\" -o -iname \"*.m\" -o -iname \"*.mm\" -o -iname \"*.cpp\" | xargs clang-format -i --Werror", "format:android": "find android/src/ -iname \"*.h\" -o -iname \"*.cpp\" | xargs clang-format -i", "format:common": "find Common/ -iname \"*.h\" -o -iname \"*.cpp\" | xargs clang-format -i", "format:docs": "cd docs && yarn format", "find-unused-code:js": "yarn ts-prune --ignore \"index|.web.\" --error", "type:check:src": "yarn tsc --noEmit", "type:check:plugin": "cd plugin && yarn type:check:src && cd ..", "type:check:app": "./scripts/test-ts.sh ../../apps/common-app/src/App.tsx", "type:check:tests:common": "./scripts/test-ts.sh __typetests__/common", "type:check:tests:0.72+": "./scripts/test-ts.sh __typetests__/72plus", "type:check:tests:legacy": "./scripts/test-ts.sh __typetests__/legacy", "type:check:all": "yarn type:check:src && yarn type:check:plugin && ./scripts/test-ts.sh ../../apps/common-app/src/App.tsx __typetests__/common __typetests__/72plus __typetests__/legacy", "build": "yarn build:plugin && bob build", "build:plugin": "cd plugin && yarn install && yarn build", "circular-dependency-check": "yarn madge --extensions js,ts,tsx --circular src lib", "use-strict-check": "node ./scripts/validate-use-strict.js", "prepack": "cp ../../README.md ./README.md", "postpack": "rm ./README.md"}, "main": "lib/module/index", "module": "lib/module/index", "react-native": "src/index", "source": "src/index", "types": "lib/typescript/index", "files": ["Common/", "src/", "lib/", "android/src/main/AndroidManifest.xml", "android/src/main/java/", "android/build.gradle", "android/", "apple/", "RNReanimated.podspec", "scripts/reanimated_utils.rb", "mock.js", "plugin/index.js", "plugin/build/plugin.js", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!apple/build/", "!android/build/", "!android/.cxx/", "!android/.gradle/", "!__snapshots__", "!*.test.js", "!*.test.js.map", "!**/node_modules"], "repository": {"type": "git", "url": "git+https://github.com/software-mansion/react-native-reanimated.git", "directory": "packages/react-native-reanimated"}, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/software-mansion/react-native-reanimated/issues"}, "homepage": "https://docs.swmansion.com/react-native-reanimated", "dependencies": {"@babel/plugin-transform-arrow-functions": "^7.0.0-0", "@babel/plugin-transform-class-properties": "^7.0.0-0", "@babel/plugin-transform-classes": "^7.0.0-0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.0.0-0", "@babel/plugin-transform-optional-chaining": "^7.0.0-0", "@babel/plugin-transform-shorthand-properties": "^7.0.0-0", "@babel/plugin-transform-template-literals": "^7.0.0-0", "@babel/plugin-transform-unicode-regex": "^7.0.0-0", "@babel/preset-typescript": "^7.16.7", "convert-source-map": "^2.0.0", "invariant": "^2.2.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "react": "*", "react-native": "*"}, "devDependencies": {"@babel/cli": "^7.20.0", "@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/types": "^7.20.0", "@react-native/babel-preset": "0.74.81", "@react-native/eslint-config": "0.74.81", "@react-native/metro-config": "0.74.81", "@react-native/typescript-config": "0.74.81", "@testing-library/jest-native": "^4.0.4", "@testing-library/react-hooks": "^8.0.0", "@testing-library/react-native": "^7.1.0", "@types/babel__core": "^7.20.0", "@types/babel__generator": "^7.6.4", "@types/babel__traverse": "^7.14.2", "@types/convert-source-map": "^2.0.0", "@types/invariant": "^2.2.35", "@types/jest": "^29.0.0", "@types/node": "^18.0.0", "@types/react": "^18.0.26", "@types/react-test-renderer": "^17.0.0-0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@typescript-eslint/rule-tester": "^6.21.0", "axios": "^1.4.0", "babel-eslint": "^10.1.0", "babel-jest": "^27.5.1", "babel-plugin-module-resolver": "^5.0.0", "clang-format": "^1.6.0", "code-tag": "^1.1.0", "cspell": "^8.8.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-babel-module": "^5.3.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-n": "^16.4.0", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-reanimated": "workspace:*", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-tsdoc": "^0.2.17", "jest": "^29.0.0", "madge": "^5.0.1", "prettier": "2.8.8", "react": "18.3.1", "react-native": "0.75.0-rc.6", "react-native-builder-bob": "^0.18.3", "react-native-gesture-handler": "2.18.0", "react-native-web": "0.19.11", "react-test-renderer": "18.2.0", "shelljs": "^0.8.5", "ts-prune": "^0.10.3", "typescript": "~5.3.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["module", "typescript"]}, "codegenConfig": {"name": "rnreanimated", "type": "modules", "jsSrcsDir": "./src/specs", "android": {"javaPackageName": "com.swmansion.reanimated"}}, "sideEffects": ["./lib/module/reanimated2/layoutReanimation/animationsManager.js", "./lib/module/reanimated2/core.js", "./lib/module/index.js"], "packageManager": "yarn@4.1.1"}