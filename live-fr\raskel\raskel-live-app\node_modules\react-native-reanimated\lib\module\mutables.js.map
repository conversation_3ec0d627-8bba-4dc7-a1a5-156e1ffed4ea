{"version": 3, "names": ["shouldBeUseWeb", "shareableMappingCache", "makeShareableCloneRecursive", "executeOnUIRuntimeSync", "runOnUI", "valueSetter", "SHOULD_BE_USE_WEB", "makeMutableUI", "initial", "listeners", "Map", "value", "mutable", "newValue", "_value", "for<PERSON>ach", "listener", "modify", "modifier", "forceUpdate", "undefined", "addListener", "id", "set", "removeListener", "delete", "_animation", "_isReanimatedSharedValue", "makeMutableNative", "handle", "__init", "uiValueGetter", "sv", "Error", "_newValue", "makeMutableWeb", "makeMutable"], "sources": ["mutables.ts"], "sourcesContent": ["'use strict';\nimport { shouldBeUseWeb } from './PlatformChecker';\nimport type { Mutable } from './commonTypes';\nimport { shareableMappingCache } from './shareableMappingCache';\nimport { makeShareableCloneRecursive } from './shareables';\nimport { executeOnUIRuntimeSync, runOnUI } from './threads';\nimport { valueSetter } from './valueSetter';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\ntype Listener<Value> = (newValue: Value) => void;\n\nexport function makeMutableUI<Value>(initial: Value): Mutable<Value> {\n  'worklet';\n  const listeners = new Map<number, Listener<Value>>();\n  let value = initial;\n\n  const mutable: Mutable<Value> = {\n    get value() {\n      return value;\n    },\n    set value(newValue) {\n      valueSetter(mutable, newValue);\n    },\n\n    /**\n     * _value prop should only be accessed by the valueSetter implementation\n     * which may make the decision about updating the mutable value depending\n     * on the provided new value. All other places should only attempt to modify\n     * the mutable by assigning to value prop directly.\n     */\n    get _value(): Value {\n      return value;\n    },\n    set _value(newValue: Value) {\n      value = newValue;\n      listeners.forEach((listener) => {\n        listener(newValue);\n      });\n    },\n\n    modify: (modifier, forceUpdate = true) => {\n      valueSetter(\n        mutable,\n        modifier !== undefined ? modifier(value) : value,\n        forceUpdate\n      );\n    },\n    addListener: (id: number, listener: Listener<Value>) => {\n      listeners.set(id, listener);\n    },\n    removeListener: (id: number) => {\n      listeners.delete(id);\n    },\n\n    _animation: null,\n    _isReanimatedSharedValue: true,\n  };\n  return mutable;\n}\n\nfunction makeMutableNative<Value>(initial: Value): Mutable<Value> {\n  const handle = makeShareableCloneRecursive({\n    __init: () => {\n      'worklet';\n      return makeMutableUI(initial);\n    },\n  });\n\n  const mutable: Mutable<Value> = {\n    get value(): Value {\n      const uiValueGetter = executeOnUIRuntimeSync((sv: Mutable<Value>) => {\n        return sv.value;\n      });\n      return uiValueGetter(mutable);\n    },\n    set value(newValue) {\n      runOnUI(() => {\n        mutable.value = newValue;\n      })();\n    },\n\n    get _value(): Value {\n      throw new Error(\n        '[Reanimated] Reading from `_value` directly is only possible on the UI runtime. Perhaps you passed an Animated Style to a non-animated component?'\n      );\n    },\n    set _value(_newValue: Value) {\n      throw new Error(\n        '[Reanimated] Setting `_value` directly is only possible on the UI runtime. Perhaps you want to assign to `value` instead?'\n      );\n    },\n\n    modify: (modifier, forceUpdate = true) => {\n      runOnUI(() => {\n        mutable.modify(modifier, forceUpdate);\n      })();\n    },\n    addListener: () => {\n      throw new Error(\n        '[Reanimated] Adding listeners is only possible on the UI runtime.'\n      );\n    },\n    removeListener: () => {\n      throw new Error(\n        '[Reanimated] Removing listeners is only possible on the UI runtime.'\n      );\n    },\n\n    _isReanimatedSharedValue: true,\n  };\n\n  shareableMappingCache.set(mutable, handle);\n  return mutable;\n}\n\nfunction makeMutableWeb<Value>(initial: Value): Mutable<Value> {\n  let value: Value = initial;\n  const listeners = new Map<number, Listener<Value>>();\n\n  const mutable: Mutable<Value> = {\n    get value(): Value {\n      return value;\n    },\n    set value(newValue) {\n      valueSetter(mutable, newValue);\n    },\n\n    get _value(): Value {\n      return value;\n    },\n    set _value(newValue: Value) {\n      value = newValue;\n      listeners.forEach((listener) => {\n        listener(newValue);\n      });\n    },\n\n    modify: (modifier, forceUpdate = true) => {\n      valueSetter(\n        mutable,\n        modifier !== undefined ? modifier(mutable.value) : mutable.value,\n        forceUpdate\n      );\n    },\n    addListener: (id: number, listener: Listener<Value>) => {\n      listeners.set(id, listener);\n    },\n    removeListener: (id: number) => {\n      listeners.delete(id);\n    },\n\n    _isReanimatedSharedValue: true,\n  };\n\n  return mutable;\n}\n\nexport const makeMutable = SHOULD_BE_USE_WEB\n  ? makeMutableWeb\n  : makeMutableNative;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,cAAc,QAAQ,mBAAmB;AAElD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,2BAA2B,QAAQ,cAAc;AAC1D,SAASC,sBAAsB,EAAEC,OAAO,QAAQ,WAAW;AAC3D,SAASC,WAAW,QAAQ,eAAe;AAE3C,MAAMC,iBAAiB,GAAGN,cAAc,CAAC,CAAC;AAI1C,OAAO,SAASO,aAAaA,CAAQC,OAAc,EAAkB;EACnE,SAAS;;EACT,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EACpD,IAAIC,KAAK,GAAGH,OAAO;EAEnB,MAAMI,OAAuB,GAAG;IAC9B,IAAID,KAAKA,CAAA,EAAG;MACV,OAAOA,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAACE,QAAQ,EAAE;MAClBR,WAAW,CAACO,OAAO,EAAEC,QAAQ,CAAC;IAChC,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACI,IAAIC,MAAMA,CAAA,EAAU;MAClB,OAAOH,KAAK;IACd,CAAC;IACD,IAAIG,MAAMA,CAACD,QAAe,EAAE;MAC1BF,KAAK,GAAGE,QAAQ;MAChBJ,SAAS,CAACM,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACH,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IAEDI,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxCd,WAAW,CACTO,OAAO,EACPM,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,CAACP,KAAK,CAAC,GAAGA,KAAK,EAChDQ,WACF,CAAC;IACH,CAAC;IACDE,WAAW,EAAEA,CAACC,EAAU,EAAEN,QAAyB,KAAK;MACtDP,SAAS,CAACc,GAAG,CAACD,EAAE,EAAEN,QAAQ,CAAC;IAC7B,CAAC;IACDQ,cAAc,EAAGF,EAAU,IAAK;MAC9Bb,SAAS,CAACgB,MAAM,CAACH,EAAE,CAAC;IACtB,CAAC;IAEDI,UAAU,EAAE,IAAI;IAChBC,wBAAwB,EAAE;EAC5B,CAAC;EACD,OAAOf,OAAO;AAChB;AAEA,SAASgB,iBAAiBA,CAAQpB,OAAc,EAAkB;EAChE,MAAMqB,MAAM,GAAG3B,2BAA2B,CAAC;IACzC4B,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOvB,aAAa,CAACC,OAAO,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,MAAMI,OAAuB,GAAG;IAC9B,IAAID,KAAKA,CAAA,EAAU;MACjB,MAAMoB,aAAa,GAAG5B,sBAAsB,CAAE6B,EAAkB,IAAK;QACnE,OAAOA,EAAE,CAACrB,KAAK;MACjB,CAAC,CAAC;MACF,OAAOoB,aAAa,CAACnB,OAAO,CAAC;IAC/B,CAAC;IACD,IAAID,KAAKA,CAACE,QAAQ,EAAE;MAClBT,OAAO,CAAC,MAAM;QACZQ,OAAO,CAACD,KAAK,GAAGE,QAAQ;MAC1B,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAIC,MAAMA,CAAA,EAAU;MAClB,MAAM,IAAImB,KAAK,CACb,mJACF,CAAC;IACH,CAAC;IACD,IAAInB,MAAMA,CAACoB,SAAgB,EAAE;MAC3B,MAAM,IAAID,KAAK,CACb,2HACF,CAAC;IACH,CAAC;IAEDhB,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxCf,OAAO,CAAC,MAAM;QACZQ,OAAO,CAACK,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IACDE,WAAW,EAAEA,CAAA,KAAM;MACjB,MAAM,IAAIY,KAAK,CACb,mEACF,CAAC;IACH,CAAC;IACDT,cAAc,EAAEA,CAAA,KAAM;MACpB,MAAM,IAAIS,KAAK,CACb,qEACF,CAAC;IACH,CAAC;IAEDN,wBAAwB,EAAE;EAC5B,CAAC;EAED1B,qBAAqB,CAACsB,GAAG,CAACX,OAAO,EAAEiB,MAAM,CAAC;EAC1C,OAAOjB,OAAO;AAChB;AAEA,SAASuB,cAAcA,CAAQ3B,OAAc,EAAkB;EAC7D,IAAIG,KAAY,GAAGH,OAAO;EAC1B,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EAEpD,MAAME,OAAuB,GAAG;IAC9B,IAAID,KAAKA,CAAA,EAAU;MACjB,OAAOA,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAACE,QAAQ,EAAE;MAClBR,WAAW,CAACO,OAAO,EAAEC,QAAQ,CAAC;IAChC,CAAC;IAED,IAAIC,MAAMA,CAAA,EAAU;MAClB,OAAOH,KAAK;IACd,CAAC;IACD,IAAIG,MAAMA,CAACD,QAAe,EAAE;MAC1BF,KAAK,GAAGE,QAAQ;MAChBJ,SAAS,CAACM,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACH,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IAEDI,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxCd,WAAW,CACTO,OAAO,EACPM,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,CAACN,OAAO,CAACD,KAAK,CAAC,GAAGC,OAAO,CAACD,KAAK,EAChEQ,WACF,CAAC;IACH,CAAC;IACDE,WAAW,EAAEA,CAACC,EAAU,EAAEN,QAAyB,KAAK;MACtDP,SAAS,CAACc,GAAG,CAACD,EAAE,EAAEN,QAAQ,CAAC;IAC7B,CAAC;IACDQ,cAAc,EAAGF,EAAU,IAAK;MAC9Bb,SAAS,CAACgB,MAAM,CAACH,EAAE,CAAC;IACtB,CAAC;IAEDK,wBAAwB,EAAE;EAC5B,CAAC;EAED,OAAOf,OAAO;AAChB;AAEA,OAAO,MAAMwB,WAAW,GAAG9B,iBAAiB,GACxC6B,cAAc,GACdP,iBAAiB", "ignoreList": []}