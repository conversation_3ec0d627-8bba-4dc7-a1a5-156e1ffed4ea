{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_STRETCH_TIME", "StretchInData", "StretchInX", "name", "style", "transform", "scaleX", "duration", "StretchInY", "scaleY", "StretchOutData", "StretchOutX", "StretchOutY", "StretchIn", "StretchOut"], "sources": ["Stretch.web.ts"], "sourcesContent": ["'use strict';\nimport { convertAnimationObjectToKeyframes } from '../animationParser';\n\nconst DEFAULT_STRETCH_TIME = 0.3;\n\nexport const StretchInData = {\n  StretchInX: {\n    name: 'StretchInX',\n    style: {\n      0: { transform: [{ scaleX: 0 }] },\n      100: { transform: [{ scaleX: 1 }] },\n    },\n    duration: DEFAULT_STRETCH_TIME,\n  },\n\n  StretchInY: {\n    name: 'StretchInY',\n    style: {\n      0: { transform: [{ scaleY: 0 }] },\n      100: { transform: [{ scaleY: 1 }] },\n    },\n    duration: DEFAULT_STRETCH_TIME,\n  },\n};\n\nexport const StretchOutData = {\n  StretchOutX: {\n    name: 'StretchOutX',\n    style: {\n      0: { transform: [{ scaleX: 1 }] },\n      100: { transform: [{ scaleX: 0 }] },\n    },\n    duration: DEFAULT_STRETCH_TIME,\n  },\n\n  StretchOutY: {\n    name: 'StretchOutY',\n    style: {\n      0: { transform: [{ scaleY: 1 }] },\n      100: { transform: [{ scaleY: 0 }] },\n    },\n    duration: DEFAULT_STRETCH_TIME,\n  },\n};\n\nexport const StretchIn = {\n  StretchInX: {\n    style: convertAnimationObjectToKeyframes(StretchInData.StretchInX),\n    duration: StretchInData.StretchInX.duration,\n  },\n  StretchInY: {\n    style: convertAnimationObjectToKeyframes(StretchInData.StretchInY),\n    duration: StretchInData.StretchInY.duration,\n  },\n};\n\nexport const StretchOut = {\n  StretchOutX: {\n    style: convertAnimationObjectToKeyframes(StretchOutData.StretchOutX),\n    duration: StretchOutData.StretchOutX.duration,\n  },\n  StretchOutY: {\n    style: convertAnimationObjectToKeyframes(StretchOutData.StretchOutY),\n    duration: StretchOutData.StretchOutY.duration,\n  },\n};\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,oBAAoB;AAEtE,MAAMC,oBAAoB,GAAG,GAAG;AAEhC,OAAO,MAAMC,aAAa,GAAG;EAC3BC,UAAU,EAAE;IACVC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDQ,UAAU,EAAE;IACVL,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDF,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAMU,cAAc,GAAG;EAC5BC,WAAW,EAAE;IACXR,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDY,WAAW,EAAE;IACXT,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE,CAAC;MACjC,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAE,CAAC;MAAE;IACpC,CAAC;IACDF,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAMa,SAAS,GAAG;EACvBX,UAAU,EAAE;IACVE,KAAK,EAAEL,iCAAiC,CAACE,aAAa,CAACC,UAAU,CAAC;IAClEK,QAAQ,EAAEN,aAAa,CAACC,UAAU,CAACK;EACrC,CAAC;EACDC,UAAU,EAAE;IACVJ,KAAK,EAAEL,iCAAiC,CAACE,aAAa,CAACO,UAAU,CAAC;IAClED,QAAQ,EAAEN,aAAa,CAACO,UAAU,CAACD;EACrC;AACF,CAAC;AAED,OAAO,MAAMO,UAAU,GAAG;EACxBH,WAAW,EAAE;IACXP,KAAK,EAAEL,iCAAiC,CAACW,cAAc,CAACC,WAAW,CAAC;IACpEJ,QAAQ,EAAEG,cAAc,CAACC,WAAW,CAACJ;EACvC,CAAC;EACDK,WAAW,EAAE;IACXR,KAAK,EAAEL,iCAAiC,CAACW,cAAc,CAACE,WAAW,CAAC;IACpEL,QAAQ,EAAEG,cAAc,CAACE,WAAW,CAACL;EACvC;AACF,CAAC", "ignoreList": []}