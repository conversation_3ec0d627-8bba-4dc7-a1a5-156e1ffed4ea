[{"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\e6e4e4c6b363c43de3c7dfb2308f9ebc\\worklets\\NativeModules\\WorkletsModuleProxy.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\798096757390bf2e9be829c88a95a403\\NativeModules\\WorkletsModuleProxySpec.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\e6e4e4c6b363c43de3c7dfb2308f9ebc\\worklets\\Registries\\EventHandlerRegistry.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\e6e4e4c6b363c43de3c7dfb2308f9ebc\\worklets\\Registries\\WorkletRuntimeRegistry.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\62eb4faf80be678832497bdc309ad6c1\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\62eb4faf80be678832497bdc309ad6c1\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\62eb4faf80be678832497bdc309ad6c1\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\62eb4faf80be678832497bdc309ad6c1\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\62eb4faf80be678832497bdc309ad6c1\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\62eb4faf80be678832497bdc309ad6c1\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\798096757390bf2e9be829c88a95a403\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\798096757390bf2e9be829c88a95a403\\WorkletRuntime\\ReanimatedHermesRuntime.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\e6e4e4c6b363c43de3c7dfb2308f9ebc\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\798096757390bf2e9be829c88a95a403\\WorkletRuntime\\WorkletRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\AndroidUIScheduler.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\PlatformLogger.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsModule.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsOnLoad.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\c7fe51702da5a426d6e131de11aca324\\AnimatedSensor\\AnimatedSensorModule.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\e6e4e4c6b363c43de3c7dfb2308f9ebc\\reanimated\\Fabric\\ReanimatedCommitHook.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\e6e4e4c6b363c43de3c7dfb2308f9ebc\\reanimated\\Fabric\\ReanimatedMountHook.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\47318364f491494f62bfd9c045c40bfe\\LayoutAnimationsManager.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\c7fe51702da5a426d6e131de11aca324\\LayoutAnimations\\LayoutAnimationsProxy.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\c7fe51702da5a426d6e131de11aca324\\LayoutAnimations\\LayoutAnimationsUtils.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\c7fe51702da5a426d6e131de11aca324\\NativeModules\\ReanimatedModuleProxy.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\aa3fba4e5269031a0af19389392d252e\\ReanimatedModuleProxySpec.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\c7fe51702da5a426d6e131de11aca324\\RuntimeDecorators\\RNRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\c7fe51702da5a426d6e131de11aca324\\RuntimeDecorators\\UIRuntimeDecorator.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\531088e49f724540a3de4908444ccf3e\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\JNIHelper.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\LayoutAnimations.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\NativeProxy.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp"}, {"directory": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/.cxx/Debug/6l266c2n/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/../Common/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native-reanimated/android/src/main/cpp -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/callinvoker -IC:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/af9595d0aeab151a9804a8af82bcc909/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/aab9761a5bc22bd9c4bd696ca1cf1810/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/9371b04a7bfb42fb4dbc04cf90f62c75/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\OnLoad.cpp.o -c C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp", "file": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp"}]