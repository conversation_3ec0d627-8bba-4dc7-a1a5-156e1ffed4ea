{"version": 3, "names": ["useEffect", "useRef", "isWeb", "isJest", "areDependenciesEqual", "buildDependencies", "makeShareable", "useHandler", "handlers", "dependencies", "initRef", "current", "context", "savedDependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb"], "sources": ["useHandler.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useRef } from 'react';\nimport type { WorkletFunction } from '../commonTypes';\nimport { isWeb, isJest } from '../PlatformChecker';\nimport type { DependencyList, ReanimatedEvent } from './commonTypes';\nimport { areDependenciesEqual, buildDependencies } from './utils';\nimport { makeShareable } from '../shareables';\n\ninterface GeneralHandler<\n  Event extends object,\n  Context extends Record<string, unknown>\n> {\n  (event: ReanimatedEvent<Event>, context: Context): void;\n}\n\ntype GeneralWorkletHandler<\n  Event extends object,\n  Context extends Record<string, unknown>\n> = WorkletFunction<[event: ReanimatedEvent<Event>, context: Context]>;\n\ntype GeneralHandlers<\n  Event extends object,\n  Context extends Record<string, unknown>\n> = Record<string, GeneralHandler<Event, Context> | undefined>;\n\ntype GeneralWorkletHandlers<\n  Event extends object,\n  Context extends Record<string, unknown>\n> = Record<string, GeneralWorkletHandler<Event, Context> | undefined>;\n\ninterface ContextWithDependencies<Context extends Record<string, unknown>> {\n  context: Context;\n  savedDependencies: DependencyList;\n}\n\nexport interface UseHandlerContext<Context extends Record<string, unknown>> {\n  context: Context;\n  doDependenciesDiffer: boolean;\n  useWeb: boolean;\n}\n\n/**\n * Lets you find out whether the event handler dependencies have changed.\n *\n * @param handlers - An object of event handlers.\n * @param dependencies - An optional array of dependencies.\n * @returns An object containing a boolean indicating whether the dependencies have changed, and a boolean indicating whether the code is running on the web.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useHandler\n */\n// @ts-expect-error This overload is required by our API.\nexport function useHandler<\n  Event extends object,\n  Context extends Record<string, unknown>\n>(\n  handlers: GeneralHandlers<Event, Context>,\n  dependencies?: DependencyList\n): UseHandlerContext<Context>;\n\nexport function useHandler<\n  Event extends object,\n  Context extends Record<string, unknown>\n>(\n  handlers: GeneralWorkletHandlers<Event, Context>,\n  dependencies?: DependencyList\n): UseHandlerContext<Context> {\n  const initRef = useRef<ContextWithDependencies<Context> | null>(null);\n  if (initRef.current === null) {\n    const context = makeShareable({} as Context);\n    initRef.current = {\n      context,\n      savedDependencies: [],\n    };\n  }\n\n  useEffect(() => {\n    return () => {\n      initRef.current = null;\n    };\n  }, []);\n\n  const { context, savedDependencies } = initRef.current;\n\n  dependencies = buildDependencies(\n    dependencies,\n    handlers as Record<string, WorkletFunction | undefined>\n  );\n\n  const doDependenciesDiffer = !areDependenciesEqual(\n    dependencies,\n    savedDependencies\n  );\n  initRef.current.savedDependencies = dependencies;\n  const useWeb = isWeb() || isJest();\n\n  return { context, doDependenciesDiffer, useWeb };\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,SAASC,KAAK,EAAEC,MAAM,QAAQ,oBAAoB;AAElD,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,SAAS;AACjE,SAASC,aAAa,QAAQ,eAAe;;AAmC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,OAAO,SAASC,UAAUA,CAIxBC,QAAgD,EAChDC,YAA6B,EACD;EAC5B,MAAMC,OAAO,GAAGT,MAAM,CAA0C,IAAI,CAAC;EACrE,IAAIS,OAAO,CAACC,OAAO,KAAK,IAAI,EAAE;IAC5B,MAAMC,OAAO,GAAGN,aAAa,CAAC,CAAC,CAAY,CAAC;IAC5CI,OAAO,CAACC,OAAO,GAAG;MAChBC,OAAO;MACPC,iBAAiB,EAAE;IACrB,CAAC;EACH;EAEAb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXU,OAAO,CAACC,OAAO,GAAG,IAAI;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEC,OAAO;IAAEC;EAAkB,CAAC,GAAGH,OAAO,CAACC,OAAO;EAEtDF,YAAY,GAAGJ,iBAAiB,CAC9BI,YAAY,EACZD,QACF,CAAC;EAED,MAAMM,oBAAoB,GAAG,CAACV,oBAAoB,CAChDK,YAAY,EACZI,iBACF,CAAC;EACDH,OAAO,CAACC,OAAO,CAACE,iBAAiB,GAAGJ,YAAY;EAChD,MAAMM,MAAM,GAAGb,KAAK,CAAC,CAAC,IAAIC,MAAM,CAAC,CAAC;EAElC,OAAO;IAAES,OAAO;IAAEE,oBAAoB;IAAEC;EAAO,CAAC;AAClD", "ignoreList": []}