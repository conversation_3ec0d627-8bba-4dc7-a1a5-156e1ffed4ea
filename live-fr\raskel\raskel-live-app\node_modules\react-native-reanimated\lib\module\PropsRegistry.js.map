{"version": 3, "names": ["isF<PERSON><PERSON>", "runOnUI", "VIEW_TAGS", "removeFromPropsRegistry", "viewTag", "push", "length", "queueMicrotask", "flush", "__DEV__", "Error", "removeFromPropsRegistryOnUI", "viewTags", "global", "_removeFromPropsRegistry"], "sources": ["PropsRegistry.ts"], "sourcesContent": ["'use strict';\nimport { isFabric } from './PlatformChecker';\nimport { runOnUI } from './threads';\n\nlet VIEW_TAGS: number[] = [];\n\nexport function removeFromPropsRegistry(viewTag: number) {\n  VIEW_TAGS.push(viewTag);\n  if (VIEW_TAGS.length === 1) {\n    queueMicrotask(flush);\n  }\n}\n\nfunction flush() {\n  if (__DEV__ && !isFabric()) {\n    throw new Error('[Reanimated] PropsRegistry is only available on Fabric.');\n  }\n  runOnUI(removeFromPropsRegistryOnUI)(VIEW_TAGS);\n  VIEW_TAGS = [];\n}\n\nfunction removeFromPropsRegistryOnUI(viewTags: number[]) {\n  'worklet';\n  global._removeFromPropsRegistry(viewTags);\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,OAAO,QAAQ,WAAW;AAEnC,IAAIC,SAAmB,GAAG,EAAE;AAE5B,OAAO,SAASC,uBAAuBA,CAACC,OAAe,EAAE;EACvDF,SAAS,CAACG,IAAI,CAACD,OAAO,CAAC;EACvB,IAAIF,SAAS,CAACI,MAAM,KAAK,CAAC,EAAE;IAC1BC,cAAc,CAACC,KAAK,CAAC;EACvB;AACF;AAEA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAIC,OAAO,IAAI,CAACT,QAAQ,CAAC,CAAC,EAAE;IAC1B,MAAM,IAAIU,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EACAT,OAAO,CAACU,2BAA2B,CAAC,CAACT,SAAS,CAAC;EAC/CA,SAAS,GAAG,EAAE;AAChB;AAEA,SAASS,2BAA2BA,CAACC,QAAkB,EAAE;EACvD,SAAS;;EACTC,MAAM,CAACC,wBAAwB,CAACF,QAAQ,CAAC;AAC3C", "ignoreList": []}