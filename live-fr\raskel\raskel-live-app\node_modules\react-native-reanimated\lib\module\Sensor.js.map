{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "NativeReanimatedModule", "SensorType", "makeMutable", "initSensorData", "sensorType", "ROTATION", "qw", "qx", "qy", "qz", "yaw", "pitch", "roll", "interfaceOrientation", "x", "y", "z", "Sensor", "constructor", "config", "data", "register", "<PERSON><PERSON><PERSON><PERSON>", "sensorId", "registerSensor", "interval", "iosReferenceFrame", "isRunning", "isAvailable", "getSharedValue", "unregister", "unregisterSensor"], "sources": ["Sensor.ts"], "sourcesContent": ["'use strict';\nimport NativeReanimatedModule from './NativeReanimated';\nimport type {\n  SensorConfig,\n  SharedValue,\n  Value3D,\n  ValueRotation,\n  ShareableRef,\n} from './commonTypes';\nimport { SensorType } from './commonTypes';\nimport { makeMutable } from './mutables';\n\nfunction initSensorData(\n  sensorType: SensorType\n): SharedValue<Value3D | ValueRotation> {\n  if (sensorType === SensorType.ROTATION) {\n    return makeMutable<Value3D | ValueRotation>({\n      qw: 0,\n      qx: 0,\n      qy: 0,\n      qz: 0,\n      yaw: 0,\n      pitch: 0,\n      roll: 0,\n      interfaceOrientation: 0,\n    });\n  } else {\n    return makeMutable<Value3D | ValueRotation>({\n      x: 0,\n      y: 0,\n      z: 0,\n      interfaceOrientation: 0,\n    });\n  }\n}\n\nexport default class Sensor {\n  public listenersNumber = 0;\n  private sensorId: number | null = null;\n  private sensorType: SensorType;\n  private data: SharedValue<Value3D | ValueRotation>;\n  private config: SensorConfig;\n\n  constructor(sensorType: SensorType, config: SensorConfig) {\n    this.sensorType = sensorType;\n    this.config = config;\n    this.data = initSensorData(sensorType);\n  }\n\n  register(\n    eventHandler: ShareableRef<(data: Value3D | ValueRotation) => void>\n  ) {\n    const config = this.config;\n    const sensorType = this.sensorType;\n    this.sensorId = NativeReanimatedModule.registerSensor(\n      sensorType,\n      config.interval === 'auto' ? -1 : config.interval,\n      config.iosReferenceFrame,\n      eventHandler\n    );\n    return this.sensorId !== -1;\n  }\n\n  isRunning() {\n    return this.sensorId !== -1 && this.sensorId !== null;\n  }\n\n  isAvailable() {\n    return this.sensorId !== -1;\n  }\n\n  getSharedValue() {\n    return this.data;\n  }\n\n  unregister() {\n    if (this.sensorId !== null && this.sensorId !== -1) {\n      NativeReanimatedModule.unregisterSensor(this.sensorId);\n    }\n    this.sensorId = null;\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,OAAOW,sBAAsB,MAAM,oBAAoB;AAQvD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,YAAY;AAExC,SAASC,cAAcA,CACrBC,UAAsB,EACgB;EACtC,IAAIA,UAAU,KAAKH,UAAU,CAACI,QAAQ,EAAE;IACtC,OAAOH,WAAW,CAA0B;MAC1CI,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE,CAAC;MACPC,oBAAoB,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAOX,WAAW,CAA0B;MAC1CY,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJH,oBAAoB,EAAE;IACxB,CAAC,CAAC;EACJ;AACF;AAEA,eAAe,MAAMI,MAAM,CAAC;EAO1BC,WAAWA,CAACd,UAAsB,EAAEe,MAAoB,EAAE;IAAAxC,eAAA,0BANjC,CAAC;IAAAA,eAAA,mBACQ,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAMpC,IAAI,CAACyB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACe,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGjB,cAAc,CAACC,UAAU,CAAC;EACxC;EAEAiB,QAAQA,CACNC,YAAmE,EACnE;IACA,MAAMH,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMf,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAACmB,QAAQ,GAAGvB,sBAAsB,CAACwB,cAAc,CACnDpB,UAAU,EACVe,MAAM,CAACM,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC,GAAGN,MAAM,CAACM,QAAQ,EACjDN,MAAM,CAACO,iBAAiB,EACxBJ,YACF,CAAC;IACD,OAAO,IAAI,CAACC,QAAQ,KAAK,CAAC,CAAC;EAC7B;EAEAI,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACJ,QAAQ,KAAK,CAAC,CAAC,IAAI,IAAI,CAACA,QAAQ,KAAK,IAAI;EACvD;EAEAK,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACL,QAAQ,KAAK,CAAC,CAAC;EAC7B;EAEAM,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACT,IAAI;EAClB;EAEAU,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACP,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,CAAC,EAAE;MAClDvB,sBAAsB,CAAC+B,gBAAgB,CAAC,IAAI,CAACR,QAAQ,CAAC;IACxD;IACA,IAAI,CAACA,QAAQ,GAAG,IAAI;EACtB;AACF", "ignoreList": []}