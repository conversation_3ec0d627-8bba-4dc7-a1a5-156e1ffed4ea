{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "ComplexAnimationBuilder", "StretchInX", "constructor", "args", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "animations", "transform", "scaleX", "createInstance", "StretchInY", "scaleY", "StretchOutX", "StretchOutY"], "sources": ["Stretch.ts"], "sourcesContent": ["'use strict';\nimport type {\n  IEntryExitAnimationBuilder,\n  EntryExitAnimationFunction,\n} from '../animationBuilder/commonTypes';\nimport type { BaseAnimationBuilder } from '../animationBuilder';\nimport { ComplexAnimationBuilder } from '../animationBuilder';\n\n/**\n * Stretch animation on the X axis. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations/#stretch\n */\nexport class StretchInX\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'StretchInX';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new StretchInX() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return () => {\n      'worklet';\n      return {\n        animations: {\n          transform: [{ scaleX: delayFunction(delay, animation(1, config)) }],\n        },\n        initialValues: {\n          transform: [{ scaleX: 0 }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Stretch animation on the Y axis. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `entering` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations/#stretch\n */\nexport class StretchInY\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'StretchInY';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new StretchInY() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return () => {\n      'worklet';\n      return {\n        animations: {\n          transform: [{ scaleY: delayFunction(delay, animation(1, config)) }],\n        },\n        initialValues: {\n          transform: [{ scaleY: 0 }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Stretch animation on the X axis. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations/#stretch\n */\nexport class StretchOutX\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'StretchOutX';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new StretchOutX() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return () => {\n      'worklet';\n      return {\n        animations: {\n          transform: [{ scaleX: delayFunction(delay, animation(0, config)) }],\n        },\n        initialValues: {\n          transform: [{ scaleX: 1 }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * Stretch animation on the Y axis. You can modify the behavior by chaining methods like `.springify()` or `.duration(500)`.\n *\n * You pass it to the `exiting` prop on [an Animated component](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animated-component).\n *\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/entering-exiting-animations/#stretch\n */\nexport class StretchOutY\n  extends ComplexAnimationBuilder\n  implements IEntryExitAnimationBuilder\n{\n  static presetName = 'StretchOutY';\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new StretchOutY() as InstanceType<T>;\n  }\n\n  build = (): EntryExitAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const [animation, config] = this.getAnimationAndConfig();\n    const delay = this.getDelay();\n    const callback = this.callbackV;\n    const initialValues = this.initialValues;\n\n    return () => {\n      'worklet';\n      return {\n        animations: {\n          transform: [{ scaleY: delayFunction(delay, animation(0, config)) }],\n        },\n        initialValues: {\n          transform: [{ scaleY: 1 }],\n          ...initialValues,\n        },\n        callback,\n      };\n    };\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAMb,SAASW,uBAAuB,QAAQ,qBAAqB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SACbD,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAO,MAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDM,aAAa,EAAE;YACbE,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC,CAAC;YAC1B,GAAGH;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA1BD,OAAOM,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIhB,UAAU,CAAC,CAAC;EACzB;AAuBF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAtB,eAAA,CAnCasB,UAAU,gBAID,YAAY;AAsClC,OAAO,MAAMiB,UAAU,SACblB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAO,MAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEI,MAAM,EAAEf,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDM,aAAa,EAAE;YACbE,SAAS,EAAE,CAAC;cAAEI,MAAM,EAAE;YAAE,CAAC,CAAC;YAC1B,GAAGN;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA1BD,OAAOM,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIC,UAAU,CAAC,CAAC;EACzB;AAuBF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAvC,eAAA,CAnCauC,UAAU,gBAID,YAAY;AAsClC,OAAO,MAAME,WAAW,SACdpB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAO,MAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDM,aAAa,EAAE;YACbE,SAAS,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC,CAAC;YAC1B,GAAGH;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA1BD,OAAOM,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIG,WAAW,CAAC,CAAC;EAC1B;AAuBF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANAzC,eAAA,CAnCayC,WAAW,gBAIF,aAAa;AAsCnC,OAAO,MAAMC,WAAW,SACdrB,uBAAuB,CAEjC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAxB,eAAA,gBASU,MAAkC;MACxC,MAAMyB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;MAExC,OAAO,MAAM;QACX,SAAS;;QACT,OAAO;UACLC,UAAU,EAAE;YACVC,SAAS,EAAE,CAAC;cAAEI,MAAM,EAAEf,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;YAAE,CAAC;UACpE,CAAC;UACDM,aAAa,EAAE;YACbE,SAAS,EAAE,CAAC;cAAEI,MAAM,EAAE;YAAE,CAAC,CAAC;YAC1B,GAAGN;UACL,CAAC;UACDF;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA1BD,OAAOM,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAII,WAAW,CAAC,CAAC;EAC1B;AAuBF;AAAC1C,eAAA,CAjCY0C,WAAW,gBAIF,aAAa", "ignoreList": []}