{"version": 3, "names": ["isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "measure", "measureFabric", "animatedRef", "_WORKLET", "viewTag", "console", "warn", "measured", "global", "_measureFabric", "x", "isNaN", "measurePaper", "_measurePaper", "measureJest", "measureChromeDebugger", "measureDefault"], "sources": ["measure.ts"], "sourcesContent": ["'use strict';\nimport type { MeasuredDimensions, ShadowNodeWrapper } from '../commonTypes';\nimport {\n  isChromeDebugger,\n  isFabric,\n  isJest,\n  shouldBeUseWeb,\n} from '../PlatformChecker';\nimport type {\n  AnimatedRef,\n  AnimatedRefOnJS,\n  AnimatedRefOnUI,\n} from '../hook/commonTypes';\nimport type { Component } from 'react';\n\ntype Measure = <T extends Component>(\n  animatedRef: AnimatedRef<T>\n) => MeasuredDimensions | null;\n\n/**\n * Lets you synchronously get the dimensions and position of a view on the screen.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns) connected to the component you'd want to get the measurements from.\n * @returns An object containing component measurements or null when the measurement couldn't be performed- {@link MeasuredDimensions}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/measure/\n */\nexport let measure: Measure;\n\nfunction measureFabric(animatedRef: AnimatedRefOnJS | AnimatedRefOnUI) {\n  'worklet';\n  if (!_WORKLET) {\n    return null;\n  }\n\n  const viewTag = animatedRef();\n  if (viewTag === -1) {\n    console.warn(\n      `[Reanimated] The view with tag ${viewTag} is not a valid argument for measure(). This may be because the view is not currently rendered, which may not be a bug (e.g. an off-screen FlatList item).`\n    );\n    return null;\n  }\n\n  const measured = global._measureFabric!(viewTag as ShadowNodeWrapper);\n  if (measured === null) {\n    console.warn(\n      `[Reanimated] The view has some undefined, not-yet-computed or meaningless value of \\`LayoutMetrics\\` type. This may be because the view is not currently rendered, which may not be a bug (e.g. an off-screen FlatList item).`\n    );\n    return null;\n  } else if (measured.x === -1234567) {\n    console.warn(\n      `[Reanimated] The view returned an invalid measurement response. Please make sure the view is currently rendered.`\n    );\n    return null;\n  } else if (isNaN(measured.x)) {\n    console.warn(\n      `[Reanimated] The view gets view-flattened on Android. To disable view-flattening, set \\`collapsable={false}\\` on this component.`\n    );\n    return null;\n  } else {\n    return measured;\n  }\n}\n\nfunction measurePaper(animatedRef: AnimatedRefOnJS | AnimatedRefOnUI) {\n  'worklet';\n  if (!_WORKLET) {\n    return null;\n  }\n\n  const viewTag = animatedRef();\n  if (viewTag === -1) {\n    console.warn(\n      `[Reanimated] The view with tag ${viewTag} is not a valid argument for measure(). This may be because the view is not currently rendered, which may not be a bug (e.g. an off-screen FlatList item).`\n    );\n    return null;\n  }\n\n  const measured = global._measurePaper!(viewTag as number);\n  if (measured === null) {\n    console.warn(\n      `[Reanimated] The view with tag ${\n        viewTag as number\n      } has some undefined, not-yet-computed or meaningless value of \\`LayoutMetrics\\` type. This may be because the view is not currently rendered, which may not be a bug (e.g. an off-screen FlatList item).`\n    );\n    return null;\n  } else if (measured.x === -1234567) {\n    console.warn(\n      `[Reanimated] The view with tag ${\n        viewTag as number\n      } returned an invalid measurement response. Please make sure the view is currently rendered.`\n    );\n    return null;\n  } else if (isNaN(measured.x)) {\n    console.warn(\n      `[Reanimated] The view with tag ${\n        viewTag as number\n      } gets view-flattened on Android. To disable view-flattening, set \\`collapsable={false}\\` on this component.`\n    );\n    return null;\n  } else {\n    return measured;\n  }\n}\n\nfunction measureJest() {\n  console.warn('[Reanimated] measure() cannot be used with Jest.');\n  return null;\n}\n\nfunction measureChromeDebugger() {\n  console.warn('[Reanimated] measure() cannot be used with Chrome Debugger.');\n  return null;\n}\n\nfunction measureDefault() {\n  console.warn(\n    '[Reanimated] measure() is not supported on this configuration.'\n  );\n  return null;\n}\n\nif (!shouldBeUseWeb()) {\n  // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n  // mapped as a different function in `shareableMappingCache` and\n  // TypeScript is not able to infer that.\n  if (isFabric()) {\n    measure = measureFabric as unknown as Measure;\n  } else {\n    measure = measurePaper as unknown as Measure;\n  }\n} else if (isJest()) {\n  measure = measureJest;\n} else if (isChromeDebugger()) {\n  measure = measureChromeDebugger;\n} else {\n  measure = measureDefault;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SACEA,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,oBAAoB;AAY3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAgB;AAE3B,SAASC,aAAaA,CAACC,WAA8C,EAAE;EACrE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBC,OAAO,CAACC,IAAI,CACT,kCAAiCF,OAAQ,4JAC5C,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAMG,QAAQ,GAAGC,MAAM,CAACC,cAAc,CAAEL,OAA4B,CAAC;EACrE,IAAIG,QAAQ,KAAK,IAAI,EAAE;IACrBF,OAAO,CAACC,IAAI,CACT,+NACH,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIC,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCL,OAAO,CAACC,IAAI,CACT,kHACH,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5BL,OAAO,CAACC,IAAI,CACT,kIACH,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOC,QAAQ;EACjB;AACF;AAEA,SAASK,YAAYA,CAACV,WAA8C,EAAE;EACpE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBC,OAAO,CAACC,IAAI,CACT,kCAAiCF,OAAQ,4JAC5C,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAMG,QAAQ,GAAGC,MAAM,CAACK,aAAa,CAAET,OAAiB,CAAC;EACzD,IAAIG,QAAQ,KAAK,IAAI,EAAE;IACrBF,OAAO,CAACC,IAAI,CACT,kCACCF,OACD,0MACH,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIG,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCL,OAAO,CAACC,IAAI,CACT,kCACCF,OACD,6FACH,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIO,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5BL,OAAO,CAACC,IAAI,CACT,kCACCF,OACD,6GACH,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOG,QAAQ;EACjB;AACF;AAEA,SAASO,WAAWA,CAAA,EAAG;EACrBT,OAAO,CAACC,IAAI,CAAC,kDAAkD,CAAC;EAChE,OAAO,IAAI;AACb;AAEA,SAASS,qBAAqBA,CAAA,EAAG;EAC/BV,OAAO,CAACC,IAAI,CAAC,6DAA6D,CAAC;EAC3E,OAAO,IAAI;AACb;AAEA,SAASU,cAAcA,CAAA,EAAG;EACxBX,OAAO,CAACC,IAAI,CACV,gEACF,CAAC;EACD,OAAO,IAAI;AACb;AAEA,IAAI,CAACP,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdG,OAAO,GAAGC,aAAmC;EAC/C,CAAC,MAAM;IACLD,OAAO,GAAGY,YAAkC;EAC9C;AACF,CAAC,MAAM,IAAId,MAAM,CAAC,CAAC,EAAE;EACnBE,OAAO,GAAGc,WAAW;AACvB,CAAC,MAAM,IAAIlB,gBAAgB,CAAC,CAAC,EAAE;EAC7BI,OAAO,GAAGe,qBAAqB;AACjC,CAAC,MAAM;EACLf,OAAO,GAAGgB,cAAc;AAC1B", "ignoreList": []}