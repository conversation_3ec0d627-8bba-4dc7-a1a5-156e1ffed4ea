{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "expo-modules-core", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "expo-modules-core::@6890427a1f51a3e7e1df", "jsonFile": "target-expo-modules-core-Debug-0e36eb7c1cea6c42b368.json", "name": "expo-modules-core", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android/.cxx/Debug/43k1a3c3/arm64-v8a", "source": "C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/expo-modules-core/android"}, "version": {"major": 2, "minor": 3}}