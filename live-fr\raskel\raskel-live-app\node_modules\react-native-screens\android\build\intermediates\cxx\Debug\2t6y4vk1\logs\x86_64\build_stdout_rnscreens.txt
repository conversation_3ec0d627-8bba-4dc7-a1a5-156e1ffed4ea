ninja: Entering directory `C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-screens\android\.cxx\Debug\2t6y4vk1\x86_64'
[1/3] Building CXX object CMakeFiles/rnscreens.dir/f91fac959c0e0da1b9b9ae28e08e7938/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/3] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/3] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\2t6y4vk1\obj\x86_64\librnscreens.so
