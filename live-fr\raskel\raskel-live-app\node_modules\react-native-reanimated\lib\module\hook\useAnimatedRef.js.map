{"version": 3, "names": ["useRef", "useSharedValue", "getShadowNodeWrapperFromRef", "makeShareableCloneRecursive", "shareableMappingCache", "Platform", "findNodeHandle", "isF<PERSON><PERSON>", "isWeb", "IS_WEB", "getComponentOrScrollable", "component", "getNativeScrollRef", "getScrollableNode", "useAnimatedRef", "tag", "viewName", "ref", "current", "fun", "getTagValueFunction", "getTagOrShadowNodeWrapper", "value", "getTag", "OS", "_viewConfig", "viewConfig", "uiViewClassName", "animatedRefShareableHandle", "__init", "f", "set"], "sources": ["useAnimatedRef.ts"], "sourcesContent": ["'use strict';\nimport type { Component } from 'react';\nimport { useRef } from 'react';\nimport { useSharedValue } from './useSharedValue';\nimport type { AnimatedRef, AnimatedRefOnUI } from './commonTypes';\nimport type { ShadowNodeWrapper } from '../commonTypes';\nimport { getShadowNodeWrapperFromRef } from '../fabricUtils';\nimport { makeShareableCloneRecursive } from '../shareables';\nimport { shareableMappingCache } from '../shareableMappingCache';\nimport { Platform, findNodeHandle } from 'react-native';\nimport type { ScrollView, FlatList } from 'react-native';\nimport { isFabric, isWeb } from '../PlatformChecker';\n\nconst IS_WEB = isWeb();\n\ninterface MaybeScrollableComponent extends Component {\n  getNativeScrollRef?: FlatList['getNativeScrollRef'];\n  getScrollableNode?:\n    | ScrollView['getScrollableNode']\n    | FlatList['getScrollableNode'];\n  viewConfig?: {\n    uiViewClassName?: string;\n  };\n}\n\nfunction getComponentOrScrollable(component: MaybeScrollableComponent) {\n  if (isFabric() && component.getNativeScrollRef) {\n    return component.getNativeScrollRef();\n  } else if (!isFabric() && component.getScrollableNode) {\n    return component.getScrollableNode();\n  }\n  return component;\n}\n\n/**\n * Lets you get a reference of a view that you can use inside a worklet.\n *\n * @returns An object with a `.current` property which contains an instance of a component.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef\n */\nexport function useAnimatedRef<\n  TComponent extends Component\n>(): AnimatedRef<TComponent> {\n  const tag = useSharedValue<number | ShadowNodeWrapper | null>(-1);\n  const viewName = useSharedValue<string | null>(null);\n\n  const ref = useRef<AnimatedRef<TComponent>>();\n\n  if (!ref.current) {\n    const fun: AnimatedRef<TComponent> = <AnimatedRef<TComponent>>((\n      component\n    ) => {\n      // enters when ref is set by attaching to a component\n      if (component) {\n        const getTagValueFunction = isFabric()\n          ? getShadowNodeWrapperFromRef\n          : findNodeHandle;\n\n        const getTagOrShadowNodeWrapper = () => {\n          return IS_WEB\n            ? getComponentOrScrollable(component)\n            : getTagValueFunction(getComponentOrScrollable(component));\n        };\n\n        tag.value = getTagOrShadowNodeWrapper();\n\n        // On Fabric we have to unwrap the tag from the shadow node wrapper\n        fun.getTag = isFabric()\n          ? () => findNodeHandle(getComponentOrScrollable(component))\n          : getTagOrShadowNodeWrapper;\n\n        fun.current = component;\n        // viewName is required only on iOS with Paper\n        if (Platform.OS === 'ios' && !isFabric()) {\n          viewName.value =\n            (component as MaybeScrollableComponent)?.viewConfig\n              ?.uiViewClassName || 'RCTView';\n        }\n      }\n      return tag.value;\n    });\n\n    fun.current = null;\n\n    const animatedRefShareableHandle = makeShareableCloneRecursive({\n      __init: () => {\n        'worklet';\n        const f: AnimatedRefOnUI = () => tag.value;\n        f.viewName = viewName;\n        return f;\n      },\n    });\n    shareableMappingCache.set(fun, animatedRefShareableHandle);\n    ref.current = fun;\n  }\n\n  return ref.current;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,cAAc,QAAQ,kBAAkB;AAGjD,SAASC,2BAA2B,QAAQ,gBAAgB;AAC5D,SAASC,2BAA2B,QAAQ,eAAe;AAC3D,SAASC,qBAAqB,QAAQ,0BAA0B;AAChE,SAASC,QAAQ,EAAEC,cAAc,QAAQ,cAAc;AAEvD,SAASC,QAAQ,EAAEC,KAAK,QAAQ,oBAAoB;AAEpD,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC;AAYtB,SAASE,wBAAwBA,CAACC,SAAmC,EAAE;EACrE,IAAIJ,QAAQ,CAAC,CAAC,IAAII,SAAS,CAACC,kBAAkB,EAAE;IAC9C,OAAOD,SAAS,CAACC,kBAAkB,CAAC,CAAC;EACvC,CAAC,MAAM,IAAI,CAACL,QAAQ,CAAC,CAAC,IAAII,SAAS,CAACE,iBAAiB,EAAE;IACrD,OAAOF,SAAS,CAACE,iBAAiB,CAAC,CAAC;EACtC;EACA,OAAOF,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAAA,EAED;EAC3B,MAAMC,GAAG,GAAGd,cAAc,CAAoC,CAAC,CAAC,CAAC;EACjE,MAAMe,QAAQ,GAAGf,cAAc,CAAgB,IAAI,CAAC;EAEpD,MAAMgB,GAAG,GAAGjB,MAAM,CAA0B,CAAC;EAE7C,IAAI,CAACiB,GAAG,CAACC,OAAO,EAAE;IAChB,MAAMC,GAA4B,GAChCR,SAAS,IACN;MACH;MACA,IAAIA,SAAS,EAAE;QACb,MAAMS,mBAAmB,GAAGb,QAAQ,CAAC,CAAC,GAClCL,2BAA2B,GAC3BI,cAAc;QAElB,MAAMe,yBAAyB,GAAGA,CAAA,KAAM;UACtC,OAAOZ,MAAM,GACTC,wBAAwB,CAACC,SAAS,CAAC,GACnCS,mBAAmB,CAACV,wBAAwB,CAACC,SAAS,CAAC,CAAC;QAC9D,CAAC;QAEDI,GAAG,CAACO,KAAK,GAAGD,yBAAyB,CAAC,CAAC;;QAEvC;QACAF,GAAG,CAACI,MAAM,GAAGhB,QAAQ,CAAC,CAAC,GACnB,MAAMD,cAAc,CAACI,wBAAwB,CAACC,SAAS,CAAC,CAAC,GACzDU,yBAAyB;QAE7BF,GAAG,CAACD,OAAO,GAAGP,SAAS;QACvB;QACA,IAAIN,QAAQ,CAACmB,EAAE,KAAK,KAAK,IAAI,CAACjB,QAAQ,CAAC,CAAC,EAAE;UAAA,IAAAkB,WAAA;UACxCT,QAAQ,CAACM,KAAK,GACZ,CAACX,SAAS,aAATA,SAAS,gBAAAc,WAAA,GAATd,SAAS,CAA+Be,UAAU,cAAAD,WAAA,uBAAnDA,WAAA,CACIE,eAAe,KAAI,SAAS;QACpC;MACF;MACA,OAAOZ,GAAG,CAACO,KAAK;IAClB,CAAE;IAEFH,GAAG,CAACD,OAAO,GAAG,IAAI;IAElB,MAAMU,0BAA0B,GAAGzB,2BAA2B,CAAC;MAC7D0B,MAAM,EAAEA,CAAA,KAAM;QACZ,SAAS;;QACT,MAAMC,CAAkB,GAAGA,CAAA,KAAMf,GAAG,CAACO,KAAK;QAC1CQ,CAAC,CAACd,QAAQ,GAAGA,QAAQ;QACrB,OAAOc,CAAC;MACV;IACF,CAAC,CAAC;IACF1B,qBAAqB,CAAC2B,GAAG,CAACZ,GAAG,EAAES,0BAA0B,CAAC;IAC1DX,GAAG,CAACC,OAAO,GAAGC,GAAG;EACnB;EAEA,OAAOF,GAAG,CAACC,OAAO;AACpB", "ignoreList": []}