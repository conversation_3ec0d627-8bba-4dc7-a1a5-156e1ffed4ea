{"version": 3, "names": ["useRef", "WorkletEventHandler", "useEvent", "handler", "eventNames", "rebuild", "initRef", "current", "workletEventHandler", "updateEventHandler"], "sources": ["useEvent.ts"], "sourcesContent": ["'use strict';\nimport { useRef } from 'react';\nimport { WorkletEventHandler } from '../WorkletEventHandler';\nimport type { IWorkletEventHandler, ReanimatedEvent } from './commonTypes';\n\n/**\n * Worklet to provide as an argument to `useEvent` hook.\n */\nexport type EventHandler<\n  Event extends object,\n  Context extends Record<string, unknown> = never\n> = (event: ReanimatedEvent<Event>, context?: Context) => void;\n\nexport type EventHandlerProcessed<\n  Event extends object,\n  Context extends Record<string, unknown> = never\n> = (event: Event, context?: Context) => void;\n\nexport type EventHandlerInternal<Event extends object> = {\n  workletEventHandler: IWorkletEventHandler<Event>;\n};\n\n/**\n * Lets you run a function whenever a specified native event occurs.\n *\n * @param handler - A function that receives an event object with event data - {@link EventHandler}.\n * @param eventNames - An array of event names the `handler` callback will react to.\n * @param rebuild - Whether the event handler should be rebuilt. Defaults to `false`.\n * @returns A function that will be called when the event occurs - {@link EventHandlerProcessed}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent\n */\n// @ts-expect-error This overload is required by our API.\n// We don't know which properites of a component that is made into\n// an AnimatedComponent are event handlers and we don't want to force the user to define it.\n// Therefore we disguise `useEvent` return type as a simple function and we handle\n// it being a React Ref in `createAnimatedComponent`.\nexport function useEvent<\n  Event extends object,\n  Context extends Record<string, unknown> = never\n>(\n  handler: EventHandler<Event, Context>,\n  eventNames?: string[],\n  rebuild?: boolean\n): EventHandlerProcessed<Event, Context>;\n\nexport function useEvent<Event extends object, Context = never>(\n  handler: (event: ReanimatedEvent<Event>, context?: Context) => void,\n  eventNames: string[] = [],\n  rebuild = false\n): EventHandlerInternal<Event> {\n  const initRef = useRef<EventHandlerInternal<Event>>(null!);\n  if (initRef.current === null) {\n    const workletEventHandler = new WorkletEventHandler<Event>(\n      handler,\n      eventNames\n    );\n    initRef.current = { workletEventHandler };\n  } else if (rebuild) {\n    const workletEventHandler = initRef.current.workletEventHandler;\n    workletEventHandler.updateEventHandler(handler, eventNames);\n    initRef.current = { workletEventHandler };\n  }\n\n  return initRef.current;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,wBAAwB;;AAG5D;AACA;AACA;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAUA,OAAO,SAASC,QAAQA,CACtBC,OAAmE,EACnEC,UAAoB,GAAG,EAAE,EACzBC,OAAO,GAAG,KAAK,EACc;EAC7B,MAAMC,OAAO,GAAGN,MAAM,CAA8B,IAAK,CAAC;EAC1D,IAAIM,OAAO,CAACC,OAAO,KAAK,IAAI,EAAE;IAC5B,MAAMC,mBAAmB,GAAG,IAAIP,mBAAmB,CACjDE,OAAO,EACPC,UACF,CAAC;IACDE,OAAO,CAACC,OAAO,GAAG;MAAEC;IAAoB,CAAC;EAC3C,CAAC,MAAM,IAAIH,OAAO,EAAE;IAClB,MAAMG,mBAAmB,GAAGF,OAAO,CAACC,OAAO,CAACC,mBAAmB;IAC/DA,mBAAmB,CAACC,kBAAkB,CAACN,OAAO,EAAEC,UAAU,CAAC;IAC3DE,OAAO,CAACC,OAAO,GAAG;MAAEC;IAAoB,CAAC;EAC3C;EAEA,OAAOF,OAAO,CAACC,OAAO;AACxB", "ignoreList": []}