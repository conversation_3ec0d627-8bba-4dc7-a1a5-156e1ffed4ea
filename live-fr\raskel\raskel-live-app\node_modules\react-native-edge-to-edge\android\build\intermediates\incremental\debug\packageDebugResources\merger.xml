<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res"><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values\attrs.xml" qualifiers=""><attr format="boolean" name="enforceNavigationBarContrast"/><attr format="boolean" name="enforceSystemBarsLightTheme"/></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values\bools.xml" qualifiers=""><bool name="windowLightSystemBars">true</bool></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values\colors.xml" qualifiers=""><color name="navigationBarColor">#801b1b1b</color></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values\public.xml" qualifiers=""><public name="enforceNavigationBarContrast" type="attr"/><public name="Theme.EdgeToEdge" type="style"/><public name="Theme.EdgeToEdge.Material2" type="style"/><public name="Theme.EdgeToEdge.Material3" type="style"/><public name="Theme.EdgeToEdge.Material3.Dynamic" type="style"/><public name="Theme.EdgeToEdge.Light" type="style"/><public name="Theme.EdgeToEdge.Material2.Light" type="style"/><public name="Theme.EdgeToEdge.Material3.Light" type="style"/><public name="Theme.EdgeToEdge.Material3.Dynamic.Light" type="style"/></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values\styles.xml" qualifiers=""><style name="Theme.EdgeToEdge.DayNight.Common" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style><style name="Theme.EdgeToEdge.Material2.DayNight.Common" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style><style name="Theme.EdgeToEdge.Material3.DayNight.Common" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common" parent="Theme.Material3.DynamicColors.DayNight.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">false</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">@bool/windowLightSystemBars</item>
    </style><style name="Theme.EdgeToEdge.Light.Common" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style><style name="Theme.EdgeToEdge.Material2.Light.Common" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style><style name="Theme.EdgeToEdge.Material3.Light.Common" parent="Theme.Material3.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic.Light.Common" parent="Theme.Material3.DynamicColors.Light.NoActionBar">
        <item name="enforceNavigationBarContrast">true</item>
        <item name="enforceSystemBarsLightTheme">true</item>

        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
    </style><style name="Theme.EdgeToEdge" parent="Theme.EdgeToEdge.DayNight.Common"/><style name="Theme.EdgeToEdge.Material2" parent="Theme.EdgeToEdge.Material2.DayNight.Common"/><style name="Theme.EdgeToEdge.Material3" parent="Theme.EdgeToEdge.Material3.DayNight.Common"/><style name="Theme.EdgeToEdge.Material3.Dynamic" parent="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common"/><style name="Theme.EdgeToEdge.Light" parent="Theme.EdgeToEdge.Light.Common"/><style name="Theme.EdgeToEdge.Material2.Light" parent="Theme.EdgeToEdge.Material2.Light.Common"/><style name="Theme.EdgeToEdge.Material3.Light" parent="Theme.EdgeToEdge.Material3.Light.Common"/><style name="Theme.EdgeToEdge.Material3.Dynamic.Light" parent="Theme.EdgeToEdge.Material3.Dynamic.Light.Common"/></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-night\bools.xml" qualifiers="night-v8"><bool name="windowLightSystemBars">false</bool></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-night-v27\bools.xml" qualifiers="night-v27"><bool name="windowLightSystemBars">false</bool></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-v27\bools.xml" qualifiers="v27"><bool name="windowLightSystemBars">true</bool></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-v27\colors.xml" qualifiers="v27"><color name="navigationBarColor">@android:color/transparent</color></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-v27\styles.xml" qualifiers="v27"><style name="Theme.EdgeToEdge" parent="Theme.EdgeToEdge.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Material2" parent="Theme.EdgeToEdge.Material2.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Material3" parent="Theme.EdgeToEdge.Material3.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic" parent="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Light" parent="Theme.EdgeToEdge.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Material2.Light" parent="Theme.EdgeToEdge.Material2.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Material3.Light" parent="Theme.EdgeToEdge.Material3.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic.Light" parent="Theme.EdgeToEdge.Material3.Dynamic.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-v29\styles.xml" qualifiers="v29"><style name="Theme.EdgeToEdge" parent="Theme.EdgeToEdge.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material2" parent="Theme.EdgeToEdge.Material2.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3" parent="Theme.EdgeToEdge.Material3.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic" parent="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Light" parent="Theme.EdgeToEdge.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material2.Light" parent="Theme.EdgeToEdge.Material2.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3.Light" parent="Theme.EdgeToEdge.Material3.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic.Light" parent="Theme.EdgeToEdge.Material3.Dynamic.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style></file><file path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\main\res\values-v30\styles.xml" qualifiers="v30"><style name="Theme.EdgeToEdge" parent="Theme.EdgeToEdge.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material2" parent="Theme.EdgeToEdge.Material2.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3" parent="Theme.EdgeToEdge.Material3.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic" parent="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Light" parent="Theme.EdgeToEdge.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material2.Light" parent="Theme.EdgeToEdge.Material2.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3.Light" parent="Theme.EdgeToEdge.Material3.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style><style name="Theme.EdgeToEdge.Material3.Dynamic.Light" parent="Theme.EdgeToEdge.Material3.Dynamic.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-edge-to-edge\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>