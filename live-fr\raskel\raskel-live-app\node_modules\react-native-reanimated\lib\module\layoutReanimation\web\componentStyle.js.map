{"version": 3, "names": ["_updatePropsJS", "snapshots", "WeakMap", "makeElementVisible", "element", "delay", "visibility", "setTimeout", "fixElementPosition", "parent", "snapshot", "parentRect", "getBoundingClientRect", "parentBorderTopValue", "parseInt", "getComputedStyle", "borderTopWidth", "parentBorderLeftValue", "borderLeftWidth", "dummyRect", "top", "style", "left", "setElementPosition", "transform", "position", "width", "height", "margin", "parentElement"], "sources": ["componentStyle.ts"], "sourcesContent": ["'use strict';\n\nimport { _updatePropsJS } from '../../js-reanimated';\nimport type { ReanimatedHTMLElement } from '../../js-reanimated';\n\nexport interface ReanimatedSnapshot {\n  top: number;\n  left: number;\n  width: number;\n  height: number;\n  scrollOffsets: ScrollOffsets;\n}\n\nexport interface ScrollOffsets {\n  scrollTopOffset: number;\n  scrollLeftOffset: number;\n}\n\nexport const snapshots = new WeakMap<HTMLElement, ReanimatedSnapshot>();\n\nexport function makeElementVisible(element: HTMLElement, delay: number) {\n  if (delay === 0) {\n    _updatePropsJS({ visibility: 'initial' }, element as ReanimatedHTMLElement);\n  } else {\n    setTimeout(() => {\n      _updatePropsJS(\n        { visibility: 'initial' },\n        element as ReanimatedHTMLElement\n      );\n    }, delay * 1000);\n  }\n}\n\nfunction fixElementPosition(\n  element: HTMLElement,\n  parent: HTMLElement,\n  snapshot: ReanimatedSnapshot\n) {\n  const parentRect = parent.getBoundingClientRect();\n\n  const parentBorderTopValue = parseInt(\n    getComputedStyle(parent).borderTopWidth\n  );\n\n  const parentBorderLeftValue = parseInt(\n    getComputedStyle(parent).borderLeftWidth\n  );\n\n  const dummyRect = element.getBoundingClientRect();\n  // getBoundingClientRect returns DOMRect with position of the element with respect to document body.\n  // However, using position `absolute` doesn't guarantee, that the dummy will be placed relative to body element.\n  // The trick below allows us to once again get position relative to body, by comparing snapshot with new position of the dummy.\n  if (dummyRect.top !== snapshot.top) {\n    element.style.top = `${\n      snapshot.top - parentRect.top - parentBorderTopValue\n    }px`;\n  }\n\n  if (dummyRect.left !== snapshot.left) {\n    element.style.left = `${\n      snapshot.left - parentRect.left - parentBorderLeftValue\n    }px`;\n  }\n}\n\nexport function setElementPosition(\n  element: HTMLElement,\n  snapshot: ReanimatedSnapshot\n) {\n  element.style.transform = '';\n  element.style.position = 'absolute';\n  element.style.top = `${snapshot.top}px`;\n  element.style.left = `${snapshot.left}px`;\n  element.style.width = `${snapshot.width}px`;\n  element.style.height = `${snapshot.height}px`;\n  element.style.margin = '0px'; // tmpElement has absolute position, so margin is not necessary\n\n  if (element.parentElement) {\n    fixElementPosition(element, element.parentElement, snapshot);\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,qBAAqB;AAgBpD,OAAO,MAAMC,SAAS,GAAG,IAAIC,OAAO,CAAkC,CAAC;AAEvE,OAAO,SAASC,kBAAkBA,CAACC,OAAoB,EAAEC,KAAa,EAAE;EACtE,IAAIA,KAAK,KAAK,CAAC,EAAE;IACfL,cAAc,CAAC;MAAEM,UAAU,EAAE;IAAU,CAAC,EAAEF,OAAgC,CAAC;EAC7E,CAAC,MAAM;IACLG,UAAU,CAAC,MAAM;MACfP,cAAc,CACZ;QAAEM,UAAU,EAAE;MAAU,CAAC,EACzBF,OACF,CAAC;IACH,CAAC,EAAEC,KAAK,GAAG,IAAI,CAAC;EAClB;AACF;AAEA,SAASG,kBAAkBA,CACzBJ,OAAoB,EACpBK,MAAmB,EACnBC,QAA4B,EAC5B;EACA,MAAMC,UAAU,GAAGF,MAAM,CAACG,qBAAqB,CAAC,CAAC;EAEjD,MAAMC,oBAAoB,GAAGC,QAAQ,CACnCC,gBAAgB,CAACN,MAAM,CAAC,CAACO,cAC3B,CAAC;EAED,MAAMC,qBAAqB,GAAGH,QAAQ,CACpCC,gBAAgB,CAACN,MAAM,CAAC,CAACS,eAC3B,CAAC;EAED,MAAMC,SAAS,GAAGf,OAAO,CAACQ,qBAAqB,CAAC,CAAC;EACjD;EACA;EACA;EACA,IAAIO,SAAS,CAACC,GAAG,KAAKV,QAAQ,CAACU,GAAG,EAAE;IAClChB,OAAO,CAACiB,KAAK,CAACD,GAAG,GAAI,GACnBV,QAAQ,CAACU,GAAG,GAAGT,UAAU,CAACS,GAAG,GAAGP,oBACjC,IAAG;EACN;EAEA,IAAIM,SAAS,CAACG,IAAI,KAAKZ,QAAQ,CAACY,IAAI,EAAE;IACpClB,OAAO,CAACiB,KAAK,CAACC,IAAI,GAAI,GACpBZ,QAAQ,CAACY,IAAI,GAAGX,UAAU,CAACW,IAAI,GAAGL,qBACnC,IAAG;EACN;AACF;AAEA,OAAO,SAASM,kBAAkBA,CAChCnB,OAAoB,EACpBM,QAA4B,EAC5B;EACAN,OAAO,CAACiB,KAAK,CAACG,SAAS,GAAG,EAAE;EAC5BpB,OAAO,CAACiB,KAAK,CAACI,QAAQ,GAAG,UAAU;EACnCrB,OAAO,CAACiB,KAAK,CAACD,GAAG,GAAI,GAAEV,QAAQ,CAACU,GAAI,IAAG;EACvChB,OAAO,CAACiB,KAAK,CAACC,IAAI,GAAI,GAAEZ,QAAQ,CAACY,IAAK,IAAG;EACzClB,OAAO,CAACiB,KAAK,CAACK,KAAK,GAAI,GAAEhB,QAAQ,CAACgB,KAAM,IAAG;EAC3CtB,OAAO,CAACiB,KAAK,CAACM,MAAM,GAAI,GAAEjB,QAAQ,CAACiB,MAAO,IAAG;EAC7CvB,OAAO,CAACiB,KAAK,CAACO,MAAM,GAAG,KAAK,CAAC,CAAC;;EAE9B,IAAIxB,OAAO,CAACyB,aAAa,EAAE;IACzBrB,kBAAkB,CAACJ,OAAO,EAAEA,OAAO,CAACyB,aAAa,EAAEnB,QAAQ,CAAC;EAC9D;AACF", "ignoreList": []}