{"version": 3, "names": ["createReactDOMStyle", "createTransformValue", "createTextShadowValue"], "sources": ["webUtils.ts"], "sourcesContent": ["'use strict';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let createReactDOMStyle: (style: any) => any;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let createTransformValue: (transform: any) => any;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let createTextShadowValue: (style: any) => void | string;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,IAAIA,mBAAwC;AACnD;AACA,OAAO,IAAIC,oBAA6C;AACxD;AACA,OAAO,IAAIC,qBAAoD", "ignoreList": []}