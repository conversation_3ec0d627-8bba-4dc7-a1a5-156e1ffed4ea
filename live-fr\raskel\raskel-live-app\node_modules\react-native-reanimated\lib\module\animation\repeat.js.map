{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "withRepeat", "_nextAnimation", "numberOfReps", "reverse", "callback", "reduceMotion", "nextAnimation", "repeat", "animation", "now", "finished", "onFrame", "current", "reps", "startValue", "toValue", "onStart", "previousAnimation", "rep<PERSON><PERSON><PERSON>", "value", "undefined", "isHigherOrder"], "sources": ["repeat.ts"], "sourcesContent": ["'use strict';\nimport { defineAnimation, getReduceMotionForAnimation } from './util';\nimport type {\n  Animation,\n  AnimationCallback,\n  AnimatableValue,\n  Timestamp,\n  AnimationObject,\n  ReduceMotion,\n} from '../commonTypes';\nimport type { RepeatAnimation } from './commonTypes';\n\n// TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\ntype withRepeatType = <T extends AnimatableValue>(\n  animation: T,\n  numberOfReps?: number,\n  reverse?: boolean,\n  callback?: AnimationCallback,\n  reduceMotion?: ReduceMotion\n) => T;\n\n/**\n * Lets you repeat an animation given number of times or run it indefinitely.\n *\n * @param animation - An animation object you want to repeat.\n * @param numberOfReps - The number of times the animation is going to be repeated. Defaults to 2.\n * @param reverse - Whether the animation should run in reverse every other repetition. Defaults to false.\n * @param callback - A function called on animation complete.\n * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n * @returns An [animation object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object) which holds the current state of the animation.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withRepeat\n */\nexport const withRepeat = function <T extends AnimationObject>(\n  _nextAnimation: T | (() => T),\n  numberOfReps = 2,\n  reverse = false,\n  callback?: AnimationCallback,\n  reduceMotion?: ReduceMotion\n): Animation<RepeatAnimation> {\n  'worklet';\n\n  return defineAnimation<RepeatAnimation, T>(\n    _nextAnimation,\n    (): RepeatAnimation => {\n      'worklet';\n\n      const nextAnimation =\n        typeof _nextAnimation === 'function'\n          ? _nextAnimation()\n          : _nextAnimation;\n\n      function repeat(animation: RepeatAnimation, now: Timestamp): boolean {\n        const finished = nextAnimation.onFrame(nextAnimation, now);\n        animation.current = nextAnimation.current;\n        if (finished) {\n          animation.reps += 1;\n          // call inner animation's callback on every repetition\n          // as the second argument the animation's current value is passed\n          if (nextAnimation.callback) {\n            nextAnimation.callback(true /* finished */, animation.current);\n          }\n          if (\n            animation.reduceMotion ||\n            (numberOfReps > 0 && animation.reps >= numberOfReps)\n          ) {\n            return true;\n          }\n\n          const startValue = reverse\n            ? (nextAnimation.current as number)\n            : animation.startValue;\n          if (reverse) {\n            nextAnimation.toValue = animation.startValue;\n            animation.startValue = startValue;\n          }\n          nextAnimation.onStart(\n            nextAnimation,\n            startValue,\n            now,\n            nextAnimation.previousAnimation as RepeatAnimation\n          );\n          return false;\n        }\n        return false;\n      }\n\n      const repCallback = (finished?: boolean): void => {\n        if (callback) {\n          callback(finished);\n        }\n        // when cancelled call inner animation's callback\n        if (!finished && nextAnimation.callback) {\n          nextAnimation.callback(false /* finished */);\n        }\n      };\n\n      function onStart(\n        animation: RepeatAnimation,\n        value: AnimatableValue,\n        now: Timestamp,\n        previousAnimation: Animation<any> | null\n      ): void {\n        animation.startValue = value;\n        animation.reps = 0;\n\n        // child animations inherit the setting, unless they already have it defined\n        // they will have it defined only if the user used the `reduceMotion` prop\n        if (nextAnimation.reduceMotion === undefined) {\n          nextAnimation.reduceMotion = animation.reduceMotion;\n        }\n\n        // don't start the animation if reduced motion is enabled and\n        // the animation would end at its starting point\n        if (\n          animation.reduceMotion &&\n          reverse &&\n          (numberOfReps <= 0 || numberOfReps % 2 === 0)\n        ) {\n          animation.current = animation.startValue;\n          animation.onFrame = () => true;\n        } else {\n          nextAnimation.onStart(nextAnimation, value, now, previousAnimation);\n        }\n      }\n\n      return {\n        isHigherOrder: true,\n        onFrame: repeat,\n        onStart,\n        reps: 0,\n        current: nextAnimation.current,\n        callback: repCallback,\n        startValue: 0,\n        reduceMotion: getReduceMotionForAnimation(reduceMotion),\n      };\n    }\n  );\n} as withRepeatType;\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,QAAQ;;AAWrE;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG,SAAAA,CACxBC,cAA6B,EAC7BC,YAAY,GAAG,CAAC,EAChBC,OAAO,GAAG,KAAK,EACfC,QAA4B,EAC5BC,YAA2B,EACC;EAC5B,SAAS;;EAET,OAAOP,eAAe,CACpBG,cAAc,EACd,MAAuB;IACrB,SAAS;;IAET,MAAMK,aAAa,GACjB,OAAOL,cAAc,KAAK,UAAU,GAChCA,cAAc,CAAC,CAAC,GAChBA,cAAc;IAEpB,SAASM,MAAMA,CAACC,SAA0B,EAAEC,GAAc,EAAW;MACnE,MAAMC,QAAQ,GAAGJ,aAAa,CAACK,OAAO,CAACL,aAAa,EAAEG,GAAG,CAAC;MAC1DD,SAAS,CAACI,OAAO,GAAGN,aAAa,CAACM,OAAO;MACzC,IAAIF,QAAQ,EAAE;QACZF,SAAS,CAACK,IAAI,IAAI,CAAC;QACnB;QACA;QACA,IAAIP,aAAa,CAACF,QAAQ,EAAE;UAC1BE,aAAa,CAACF,QAAQ,CAAC,IAAI,CAAC,gBAAgBI,SAAS,CAACI,OAAO,CAAC;QAChE;QACA,IACEJ,SAAS,CAACH,YAAY,IACrBH,YAAY,GAAG,CAAC,IAAIM,SAAS,CAACK,IAAI,IAAIX,YAAa,EACpD;UACA,OAAO,IAAI;QACb;QAEA,MAAMY,UAAU,GAAGX,OAAO,GACrBG,aAAa,CAACM,OAAO,GACtBJ,SAAS,CAACM,UAAU;QACxB,IAAIX,OAAO,EAAE;UACXG,aAAa,CAACS,OAAO,GAAGP,SAAS,CAACM,UAAU;UAC5CN,SAAS,CAACM,UAAU,GAAGA,UAAU;QACnC;QACAR,aAAa,CAACU,OAAO,CACnBV,aAAa,EACbQ,UAAU,EACVL,GAAG,EACHH,aAAa,CAACW,iBAChB,CAAC;QACD,OAAO,KAAK;MACd;MACA,OAAO,KAAK;IACd;IAEA,MAAMC,WAAW,GAAIR,QAAkB,IAAW;MAChD,IAAIN,QAAQ,EAAE;QACZA,QAAQ,CAACM,QAAQ,CAAC;MACpB;MACA;MACA,IAAI,CAACA,QAAQ,IAAIJ,aAAa,CAACF,QAAQ,EAAE;QACvCE,aAAa,CAACF,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC;MAC9C;IACF,CAAC;IAED,SAASY,OAAOA,CACdR,SAA0B,EAC1BW,KAAsB,EACtBV,GAAc,EACdQ,iBAAwC,EAClC;MACNT,SAAS,CAACM,UAAU,GAAGK,KAAK;MAC5BX,SAAS,CAACK,IAAI,GAAG,CAAC;;MAElB;MACA;MACA,IAAIP,aAAa,CAACD,YAAY,KAAKe,SAAS,EAAE;QAC5Cd,aAAa,CAACD,YAAY,GAAGG,SAAS,CAACH,YAAY;MACrD;;MAEA;MACA;MACA,IACEG,SAAS,CAACH,YAAY,IACtBF,OAAO,KACND,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,EAC7C;QACAM,SAAS,CAACI,OAAO,GAAGJ,SAAS,CAACM,UAAU;QACxCN,SAAS,CAACG,OAAO,GAAG,MAAM,IAAI;MAChC,CAAC,MAAM;QACLL,aAAa,CAACU,OAAO,CAACV,aAAa,EAAEa,KAAK,EAAEV,GAAG,EAAEQ,iBAAiB,CAAC;MACrE;IACF;IAEA,OAAO;MACLI,aAAa,EAAE,IAAI;MACnBV,OAAO,EAAEJ,MAAM;MACfS,OAAO;MACPH,IAAI,EAAE,CAAC;MACPD,OAAO,EAAEN,aAAa,CAACM,OAAO;MAC9BR,QAAQ,EAAEc,WAAW;MACrBJ,UAAU,EAAE,CAAC;MACbT,YAAY,EAAEN,2BAA2B,CAACM,YAAY;IACxD,CAAC;EACH,CACF,CAAC;AACH,CAAmB", "ignoreList": []}