[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\jdk-17\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86_64 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging12260908650641079102\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\9371b04a7bfb42fb4dbc04cf90f62c75\\\\transformed\\\\react-android-0.79.5-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\aab9761a5bc22bd9c4bd696ca1cf1810\\\\transformed\\\\hermes-android-0.79.5-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\af9595d0aeab151a9804a8af82bcc909\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64'", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64'", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6l266c2n\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6l266c2n\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\6l266c2n\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\6l266c2n\\\\x86_64\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=79\" ^\n  \"-DANDROID_TOOLCHAIN=clang\" ^\n  \"-DREACT_NATIVE_DIR=C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native\" ^\n  \"-DJS_RUNTIME=hermes\" ^\n  \"-DJS_RUNTIME_DIR=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native\\\\sdks\\\\hermes\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=false\" ^\n  \"-DIS_REANIMATED_EXAMPLE_APP=false\" ^\n  \"-DREANIMATED_VERSION=3.17.5\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\" ^\n  \"-DHERMES_ENABLE_DEBUGGER=1\"\n", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6l266c2n\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6l266c2n\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\6l266c2n\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\6l266c2n\\\\x86_64\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=79\" ^\n  \"-DANDROID_TOOLCHAIN=clang\" ^\n  \"-DREACT_NATIVE_DIR=C:/Users/<USER>/fr/live-fr/raskel/raskel-live-app/node_modules/react-native\" ^\n  \"-DJS_RUNTIME=hermes\" ^\n  \"-DJS_RUNTIME_DIR=C:\\\\Users\\\\<USER>\\\\fr\\\\live-fr\\\\raskel\\\\raskel-live-app\\\\node_modules\\\\react-native\\\\sdks\\\\hermes\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=false\" ^\n  \"-DIS_REANIMATED_EXAMPLE_APP=false\" ^\n  \"-DREANIMATED_VERSION=3.17.5\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\" ^\n  \"-DHERMES_ENABLE_DEBUGGER=1\"\n", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6l266c2n\\x86_64\\compile_commands.json to C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\.cxx\\tools\\debug\\x86_64\\compile_commands.json", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\fr\\live-fr\\raskel\\raskel-live-app\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]