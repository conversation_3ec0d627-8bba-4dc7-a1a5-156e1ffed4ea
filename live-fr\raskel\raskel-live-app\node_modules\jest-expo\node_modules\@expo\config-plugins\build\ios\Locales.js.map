{"version": 3, "file": "Locales.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_Xcodeproj", "_iosPlugins", "_locales", "e", "__esModule", "default", "withLocales", "config", "withXcodeProject", "modResults", "setLocalesAsync", "projectRoot", "modRequest", "project", "exports", "getLocales", "locales", "localesMap", "getResolvedLocalesAsync", "projectName", "getProjectName", "supportingDirectory", "path", "join", "stringName", "lang", "localizationObj", "Object", "entries", "dir", "fs", "promises", "mkdir", "recursive", "strings", "buffer", "plist<PERSON><PERSON>", "localVersion", "push", "writeFile", "groupName", "group", "ensureGroupRecursively", "children", "some", "comment", "addResourceFileToGroup", "filepath", "relative", "isBuildFile", "verbose"], "sources": ["../../src/ios/Locales.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport fs from 'fs';\nimport path from 'path';\nimport { XcodeProject } from 'xcode';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { addResourceFileToGroup, ensureGroupRecursively, getProjectName } from './utils/Xcodeproj';\nimport { withXcodeProject } from '../plugins/ios-plugins';\nimport { getResolvedLocalesAsync, LocaleJson } from '../utils/locales';\n\nexport const withLocales: ConfigPlugin = (config) => {\n  return withXcodeProject(config, async (config) => {\n    config.modResults = await setLocalesAsync(config, {\n      projectRoot: config.modRequest.projectRoot,\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\nexport function getLocales(\n  config: Pick<ExpoConfig, 'locales'>\n): Record<string, string | LocaleJson> | null {\n  return config.locales ?? null;\n}\n\nexport async function setLocalesAsync(\n  config: Pick<ExpoConfig, 'locales'>,\n  { projectRoot, project }: { projectRoot: string; project: XcodeProject }\n): Promise<XcodeProject> {\n  const locales = getLocales(config);\n  if (!locales) {\n    return project;\n  }\n  // possibly validate CFBundleAllowMixedLocalizations is enabled\n  const localesMap = await getResolvedLocalesAsync(projectRoot, locales, 'ios');\n\n  const projectName = getProjectName(projectRoot);\n  const supportingDirectory = path.join(projectRoot, 'ios', projectName, 'Supporting');\n\n  // TODO: Should we delete all before running? Revisit after we land on a lock file.\n  const stringName = 'InfoPlist.strings';\n\n  for (const [lang, localizationObj] of Object.entries(localesMap)) {\n    const dir = path.join(supportingDirectory, `${lang}.lproj`);\n    // await fs.ensureDir(dir);\n    await fs.promises.mkdir(dir, { recursive: true });\n\n    const strings = path.join(dir, stringName);\n    const buffer = [];\n    for (const [plistKey, localVersion] of Object.entries(localizationObj)) {\n      buffer.push(`${plistKey} = \"${localVersion}\";`);\n    }\n    // Write the file to the file system.\n    await fs.promises.writeFile(strings, buffer.join('\\n'));\n\n    const groupName = `${projectName}/Supporting/${lang}.lproj`;\n    // deep find the correct folder\n    const group = ensureGroupRecursively(project, groupName);\n\n    // Ensure the file doesn't already exist\n    if (!group?.children.some(({ comment }) => comment === stringName)) {\n      // Only write the file if it doesn't already exist.\n      project = addResourceFileToGroup({\n        filepath: path.relative(supportingDirectory, strings),\n        groupName,\n        project,\n        isBuildFile: true,\n        verbose: true,\n      });\n    }\n  }\n\n  return project;\n}\n"], "mappings": ";;;;;;;;AACA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAI,WAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,YAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,WAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuE,SAAAC,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhE,MAAMG,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,8BAAgB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChDA,MAAM,CAACE,UAAU,GAAG,MAAMC,eAAe,CAACH,MAAM,EAAE;MAChDI,WAAW,EAAEJ,MAAM,CAACK,UAAU,CAACD,WAAW;MAC1CE,OAAO,EAAEN,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,WAAA,GAAAA,WAAA;AAEK,SAASS,UAAUA,CACxBR,MAAmC,EACS;EAC5C,OAAOA,MAAM,CAACS,OAAO,IAAI,IAAI;AAC/B;AAEO,eAAeN,eAAeA,CACnCH,MAAmC,EACnC;EAAEI,WAAW;EAAEE;AAAwD,CAAC,EACjD;EACvB,MAAMG,OAAO,GAAGD,UAAU,CAACR,MAAM,CAAC;EAClC,IAAI,CAACS,OAAO,EAAE;IACZ,OAAOH,OAAO;EAChB;EACA;EACA,MAAMI,UAAU,GAAG,MAAM,IAAAC,kCAAuB,EAACP,WAAW,EAAEK,OAAO,EAAE,KAAK,CAAC;EAE7E,MAAMG,WAAW,GAAG,IAAAC,2BAAc,EAACT,WAAW,CAAC;EAC/C,MAAMU,mBAAmB,GAAGC,eAAI,CAACC,IAAI,CAACZ,WAAW,EAAE,KAAK,EAAEQ,WAAW,EAAE,YAAY,CAAC;;EAEpF;EACA,MAAMK,UAAU,GAAG,mBAAmB;EAEtC,KAAK,MAAM,CAACC,IAAI,EAAEC,eAAe,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACX,UAAU,CAAC,EAAE;IAChE,MAAMY,GAAG,GAAGP,eAAI,CAACC,IAAI,CAACF,mBAAmB,EAAE,GAAGI,IAAI,QAAQ,CAAC;IAC3D;IACA,MAAMK,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACH,GAAG,EAAE;MAAEI,SAAS,EAAE;IAAK,CAAC,CAAC;IAEjD,MAAMC,OAAO,GAAGZ,eAAI,CAACC,IAAI,CAACM,GAAG,EAAEL,UAAU,CAAC;IAC1C,MAAMW,MAAM,GAAG,EAAE;IACjB,KAAK,MAAM,CAACC,QAAQ,EAAEC,YAAY,CAAC,IAAIV,MAAM,CAACC,OAAO,CAACF,eAAe,CAAC,EAAE;MACtES,MAAM,CAACG,IAAI,CAAC,GAAGF,QAAQ,OAAOC,YAAY,IAAI,CAAC;IACjD;IACA;IACA,MAAMP,aAAE,CAACC,QAAQ,CAACQ,SAAS,CAACL,OAAO,EAAEC,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;IAEvD,MAAMiB,SAAS,GAAG,GAAGrB,WAAW,eAAeM,IAAI,QAAQ;IAC3D;IACA,MAAMgB,KAAK,GAAG,IAAAC,mCAAsB,EAAC7B,OAAO,EAAE2B,SAAS,CAAC;;IAExD;IACA,IAAI,CAACC,KAAK,EAAEE,QAAQ,CAACC,IAAI,CAAC,CAAC;MAAEC;IAAQ,CAAC,KAAKA,OAAO,KAAKrB,UAAU,CAAC,EAAE;MAClE;MACAX,OAAO,GAAG,IAAAiC,mCAAsB,EAAC;QAC/BC,QAAQ,EAAEzB,eAAI,CAAC0B,QAAQ,CAAC3B,mBAAmB,EAAEa,OAAO,CAAC;QACrDM,SAAS;QACT3B,OAAO;QACPoC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;EAEA,OAAOrC,OAAO;AAChB", "ignoreList": []}