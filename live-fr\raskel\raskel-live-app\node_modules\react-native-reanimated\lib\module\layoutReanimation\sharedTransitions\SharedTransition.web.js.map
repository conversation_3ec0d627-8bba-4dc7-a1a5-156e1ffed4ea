{"version": 3, "names": ["ReduceMotion", "SharedTransition", "custom", "progressAnimation", "duration", "reduceMotion", "defaultTransitionType", "registerTransition", "unregisterTransition", "getReduceMotion", "System"], "sources": ["SharedTransition.web.ts"], "sourcesContent": ["'use strict';\nimport { ReduceMotion } from '../../commonTypes';\n\nexport class SharedTransition {\n  public custom(): SharedTransition {\n    return this;\n  }\n\n  public progressAnimation(): SharedTransition {\n    return this;\n  }\n\n  public duration(): SharedTransition {\n    return this;\n  }\n\n  public reduceMotion(): this {\n    return this;\n  }\n\n  public defaultTransitionType(): SharedTransition {\n    return this;\n  }\n\n  public registerTransition(): void {\n    // no-op\n  }\n\n  public unregisterTransition(): void {\n    // no-op\n  }\n\n  public getReduceMotion(): ReduceMotion {\n    return ReduceMotion.System;\n  }\n\n  // static builder methods\n\n  public static custom(): SharedTransition {\n    return new SharedTransition();\n  }\n\n  public static duration(): SharedTransition {\n    return new SharedTransition();\n  }\n\n  public static progressAnimation(): SharedTransition {\n    return new SharedTransition();\n  }\n\n  public static defaultTransitionType(): SharedTransition {\n    return new SharedTransition();\n  }\n\n  public static reduceMotion(): SharedTransition {\n    return new SharedTransition();\n  }\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,YAAY,QAAQ,mBAAmB;AAEhD,OAAO,MAAMC,gBAAgB,CAAC;EACrBC,MAAMA,CAAA,EAAqB;IAChC,OAAO,IAAI;EACb;EAEOC,iBAAiBA,CAAA,EAAqB;IAC3C,OAAO,IAAI;EACb;EAEOC,QAAQA,CAAA,EAAqB;IAClC,OAAO,IAAI;EACb;EAEOC,YAAYA,CAAA,EAAS;IAC1B,OAAO,IAAI;EACb;EAEOC,qBAAqBA,CAAA,EAAqB;IAC/C,OAAO,IAAI;EACb;EAEOC,kBAAkBA,CAAA,EAAS;IAChC;EAAA;EAGKC,oBAAoBA,CAAA,EAAS;IAClC;EAAA;EAGKC,eAAeA,CAAA,EAAiB;IACrC,OAAOT,YAAY,CAACU,MAAM;EAC5B;;EAEA;;EAEA,OAAcR,MAAMA,CAAA,EAAqB;IACvC,OAAO,IAAID,gBAAgB,CAAC,CAAC;EAC/B;EAEA,OAAcG,QAAQA,CAAA,EAAqB;IACzC,OAAO,IAAIH,gBAAgB,CAAC,CAAC;EAC/B;EAEA,OAAcE,iBAAiBA,CAAA,EAAqB;IAClD,OAAO,IAAIF,gBAAgB,CAAC,CAAC;EAC/B;EAEA,OAAcK,qBAAqBA,CAAA,EAAqB;IACtD,OAAO,IAAIL,gBAAgB,CAAC,CAAC;EAC/B;EAEA,OAAcI,YAAYA,CAAA,EAAqB;IAC7C,OAAO,IAAIJ,gBAAgB,CAAC,CAAC;EAC/B;AACF", "ignoreList": []}