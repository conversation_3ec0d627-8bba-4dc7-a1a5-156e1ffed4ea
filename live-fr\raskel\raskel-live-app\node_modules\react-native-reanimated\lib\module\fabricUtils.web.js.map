{"version": 3, "names": ["getShadowNodeWrapperFromRef", "Error"], "sources": ["fabricUtils.web.ts"], "sourcesContent": ["'use strict';\nexport function getShadowNodeWrapperFromRef() {\n  throw new Error(\n    '[Reanimated] Trying to call `getShadowNodeWrapperFromRef` on web.'\n  );\n}\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,SAASA,2BAA2BA,CAAA,EAAG;EAC5C,MAAM,IAAIC,KAAK,CACb,mEACF,CAAC;AACH", "ignoreList": []}