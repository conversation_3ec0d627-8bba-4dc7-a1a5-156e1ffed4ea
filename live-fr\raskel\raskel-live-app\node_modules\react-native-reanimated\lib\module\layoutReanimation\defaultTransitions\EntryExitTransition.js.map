{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "BaseAnimationBuilder", "withSequence", "withTiming", "FadeIn", "FadeOut", "EntryExitTransition", "constructor", "args", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "enteringAnimation", "enteringV", "build", "exitingAnimation", "exitingV", "exitingDuration", "getDuration", "values", "enteringValues", "exitingValues", "animations", "transform", "prop", "keys", "Array", "isArray", "for<PERSON>ach", "index", "transformProp", "push", "initialValues", "duration", "sequence", "undefined", "includes", "mergedTransform", "concat", "map", "objectKeys", "length", "console", "error", "current", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "createInstance", "entering", "animation", "instance", "exiting", "combineTransition"], "sources": ["EntryExitTransition.ts"], "sourcesContent": ["'use strict';\nimport type {\n  ILayoutAnimationBuilder,\n  LayoutAnimationsValues,\n  LayoutAnimationFunction,\n  StylePropsWithArrayTransform,\n} from '../animationBuilder/commonTypes';\nimport { BaseAnimationBuilder } from '../animationBuilder';\nimport { withSequence, withTiming } from '../../animation';\nimport { FadeIn, FadeOut } from '../defaultAnimations/Fade';\nimport type {\n  AnimatableValue,\n  AnimationObject,\n  TransformArrayItem,\n} from '../../commonTypes';\n\nexport class EntryExitTransition\n  extends BaseAnimationBuilder\n  implements ILayoutAnimationBuilder\n{\n  static presetName = 'EntryExitTransition';\n\n  enteringV: BaseAnimationBuilder | typeof BaseAnimationBuilder = FadeIn;\n\n  exitingV: BaseAnimationBuilder | typeof BaseAnimationBuilder = FadeOut;\n\n  static createInstance<T extends typeof BaseAnimationBuilder>(\n    this: T\n  ): InstanceType<T> {\n    return new EntryExitTransition() as InstanceType<T>;\n  }\n\n  static entering(\n    animation: BaseAnimationBuilder | typeof BaseAnimationBuilder\n  ): EntryExitTransition {\n    const instance = this.createInstance();\n    return instance.entering(animation);\n  }\n\n  entering(\n    animation: BaseAnimationBuilder | typeof BaseAnimationBuilder\n  ): EntryExitTransition {\n    this.enteringV = animation;\n    return this;\n  }\n\n  static exiting(\n    animation: BaseAnimationBuilder | typeof BaseAnimationBuilder\n  ): EntryExitTransition {\n    const instance = this.createInstance();\n    return instance.exiting(animation);\n  }\n\n  exiting(\n    animation: BaseAnimationBuilder | typeof BaseAnimationBuilder\n  ): EntryExitTransition {\n    this.exitingV = animation;\n    return this;\n  }\n\n  build = (): LayoutAnimationFunction => {\n    const delayFunction = this.getDelayFunction();\n    const callback = this.callbackV;\n    const delay = this.getDelay();\n    // @ts-ignore Calling `.build()` both static and instance methods works fine here, but `this` types are incompatible. They are not used though, so it's fine.\n    const enteringAnimation = this.enteringV.build();\n    // @ts-ignore Calling `.build()` both static and instance methods works fine here, but `this` types are incompatible. They are not used though, so it's fine.\n    const exitingAnimation = this.exitingV.build();\n    const exitingDuration = this.exitingV.getDuration();\n\n    return (values) => {\n      'worklet';\n      const enteringValues = enteringAnimation(values);\n      const exitingValues = exitingAnimation(values);\n      const animations: StylePropsWithArrayTransform = {\n        transform: [],\n      };\n\n      for (const prop of Object.keys(exitingValues.animations)) {\n        if (prop === 'transform') {\n          if (!Array.isArray(exitingValues.animations.transform)) {\n            continue;\n          }\n          exitingValues.animations.transform.forEach((value, index) => {\n            for (const transformProp of Object.keys(value)) {\n              animations.transform!.push({\n                [transformProp]: delayFunction(\n                  delay,\n                  withSequence(\n                    value[transformProp as keyof TransformArrayItem],\n                    withTiming(\n                      exitingValues.initialValues.transform\n                        ? // TODO TYPESCRIPT\n                          // @ts-ignore This line of code fails tragically\n                          // in newer versions of React Native, where they have\n                          // narrowed down the type of `transform` even further.\n                          // Since this piece of code improperly typed anyway\n                          // (e.g. it assumes types from RN Animated here) I'd rather\n                          // fix it in the future when types for animations\n                          // are properly defined.\n                          exitingValues.initialValues.transform[index][\n                            transformProp\n                          ]\n                        : 0,\n                      { duration: 0 }\n                    )\n                  )\n                ),\n              } as TransformArrayItem);\n            }\n          });\n        } else {\n          const sequence =\n            enteringValues.animations[prop] !== undefined\n              ? [\n                  exitingValues.animations[prop],\n                  withTiming(enteringValues.initialValues[prop], {\n                    duration: 0,\n                  }),\n                  enteringValues.animations[prop],\n                ]\n              : [\n                  exitingValues.animations[prop],\n                  withTiming(\n                    Object.keys(values).includes(prop)\n                      ? values[prop as keyof LayoutAnimationsValues]\n                      : exitingValues.initialValues[prop],\n                    { duration: 0 }\n                  ),\n                ];\n\n          animations[prop] = delayFunction(delay, withSequence(...sequence));\n        }\n      }\n      for (const prop of Object.keys(enteringValues.animations)) {\n        if (prop === 'transform') {\n          if (!Array.isArray(enteringValues.animations.transform)) {\n            continue;\n          }\n          enteringValues.animations.transform.forEach((value, index) => {\n            for (const transformProp of Object.keys(value)) {\n              animations.transform!.push({\n                [transformProp]: delayFunction(\n                  delay + exitingDuration,\n                  withSequence(\n                    withTiming(\n                      enteringValues.initialValues.transform\n                        ? ((\n                            enteringValues.initialValues\n                              .transform as TransformArrayItem[]\n                          )[index][\n                            transformProp as keyof TransformArrayItem\n                          ] as AnimatableValue)\n                        : 0,\n                      { duration: exitingDuration }\n                    ),\n                    value[\n                      transformProp as keyof TransformArrayItem\n                    ] as AnimatableValue\n                  )\n                ),\n              } as TransformArrayItem);\n            }\n          });\n        } else if (animations[prop] !== undefined) {\n          // it was already added in the previous loop\n          continue;\n        } else {\n          animations[prop] = delayFunction(\n            delay,\n            withSequence(\n              withTiming(enteringValues.initialValues[prop], { duration: 0 }),\n              enteringValues.animations[prop]\n            )\n          );\n        }\n      }\n\n      const mergedTransform = (\n        Array.isArray(exitingValues.initialValues.transform)\n          ? exitingValues.initialValues.transform\n          : []\n      ).concat(\n        (Array.isArray(enteringValues.animations.transform)\n          ? enteringValues.animations.transform\n          : []\n        ).map((value) => {\n          const objectKeys = Object.keys(value);\n          if (objectKeys?.length < 1) {\n            console.error(\n              `[Reanimated]: \\${value} is not a valid Transform object`\n            );\n            return value;\n          }\n\n          const transformProp = objectKeys[0];\n          const current =\n            // TODO TYPESCRIPT\n            // @ts-ignore Read similar comment above.\n            (value[transformProp] as AnimationObject).current;\n          if (typeof current === 'string') {\n            if (current.includes('deg')) {\n              return {\n                [transformProp]: '0deg',\n              } as unknown as TransformArrayItem;\n            } else {\n              return {\n                [transformProp]: '0',\n              } as unknown as TransformArrayItem;\n            }\n          } else if (transformProp.includes('translate')) {\n            return { [transformProp]: 0 } as unknown as TransformArrayItem;\n          } else {\n            return { [transformProp]: 1 } as unknown as TransformArrayItem;\n          }\n        })\n      );\n\n      return {\n        initialValues: {\n          ...exitingValues.initialValues,\n          originX: values.currentOriginX,\n          originY: values.currentOriginY,\n          width: values.currentWidth,\n          height: values.currentHeight,\n          transform: mergedTransform,\n        },\n        animations: {\n          originX: delayFunction(\n            delay + exitingDuration,\n            withTiming(values.targetOriginX, { duration: exitingDuration })\n          ),\n          originY: delayFunction(\n            delay + exitingDuration,\n            withTiming(values.targetOriginY, { duration: exitingDuration })\n          ),\n          width: delayFunction(\n            delay + exitingDuration,\n            withTiming(values.targetWidth, { duration: exitingDuration })\n          ),\n          height: delayFunction(\n            delay + exitingDuration,\n            withTiming(values.targetHeight, { duration: exitingDuration })\n          ),\n          ...animations,\n        },\n        callback,\n      };\n    };\n  };\n}\n\n/**\n * @deprecated Please use `EntryExitTransition.entering(entering).exiting(exiting)` instead.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/layout-animations/layout-transitions\n */\nexport function combineTransition(\n  exiting: BaseAnimationBuilder | typeof BaseAnimationBuilder,\n  entering: BaseAnimationBuilder | typeof BaseAnimationBuilder\n): EntryExitTransition {\n  return EntryExitTransition.entering(entering).exiting(exiting);\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAOb,SAASW,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAASC,MAAM,EAAEC,OAAO,QAAQ,2BAA2B;AAO3D,OAAO,MAAMC,mBAAmB,SACtBL,oBAAoB,CAE9B;EAAAM,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA5B,eAAA,oBAGkEwB,MAAM;IAAAxB,eAAA,mBAEPyB,OAAO;IAAAzB,eAAA,gBAoC9D,MAA+B;MACrC,MAAM6B,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B;MACA,MAAMC,iBAAiB,GAAG,IAAI,CAACC,SAAS,CAACC,KAAK,CAAC,CAAC;MAChD;MACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,QAAQ,CAACF,KAAK,CAAC,CAAC;MAC9C,MAAMG,eAAe,GAAG,IAAI,CAACD,QAAQ,CAACE,WAAW,CAAC,CAAC;MAEnD,OAAQC,MAAM,IAAK;QACjB,SAAS;;QACT,MAAMC,cAAc,GAAGR,iBAAiB,CAACO,MAAM,CAAC;QAChD,MAAME,aAAa,GAAGN,gBAAgB,CAACI,MAAM,CAAC;QAC9C,MAAMG,UAAwC,GAAG;UAC/CC,SAAS,EAAE;QACb,CAAC;QAED,KAAK,MAAMC,IAAI,IAAI1C,MAAM,CAAC2C,IAAI,CAACJ,aAAa,CAACC,UAAU,CAAC,EAAE;UACxD,IAAIE,IAAI,KAAK,WAAW,EAAE;YACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACN,aAAa,CAACC,UAAU,CAACC,SAAS,CAAC,EAAE;cACtD;YACF;YACAF,aAAa,CAACC,UAAU,CAACC,SAAS,CAACK,OAAO,CAAC,CAAChD,KAAK,EAAEiD,KAAK,KAAK;cAC3D,KAAK,MAAMC,aAAa,IAAIhD,MAAM,CAAC2C,IAAI,CAAC7C,KAAK,CAAC,EAAE;gBAC9C0C,UAAU,CAACC,SAAS,CAAEQ,IAAI,CAAC;kBACzB,CAACD,aAAa,GAAGxB,aAAa,CAC5BI,KAAK,EACLX,YAAY,CACVnB,KAAK,CAACkD,aAAa,CAA6B,EAChD9B,UAAU,CACRqB,aAAa,CAACW,aAAa,CAACT,SAAS;kBACjC;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAF,aAAa,CAACW,aAAa,CAACT,SAAS,CAACM,KAAK,CAAC,CAC1CC,aAAa,CACd,GACD,CAAC,EACL;oBAAEG,QAAQ,EAAE;kBAAE,CAChB,CACF,CACF;gBACF,CAAuB,CAAC;cAC1B;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMC,QAAQ,GACZd,cAAc,CAACE,UAAU,CAACE,IAAI,CAAC,KAAKW,SAAS,GACzC,CACEd,aAAa,CAACC,UAAU,CAACE,IAAI,CAAC,EAC9BxB,UAAU,CAACoB,cAAc,CAACY,aAAa,CAACR,IAAI,CAAC,EAAE;cAC7CS,QAAQ,EAAE;YACZ,CAAC,CAAC,EACFb,cAAc,CAACE,UAAU,CAACE,IAAI,CAAC,CAChC,GACD,CACEH,aAAa,CAACC,UAAU,CAACE,IAAI,CAAC,EAC9BxB,UAAU,CACRlB,MAAM,CAAC2C,IAAI,CAACN,MAAM,CAAC,CAACiB,QAAQ,CAACZ,IAAI,CAAC,GAC9BL,MAAM,CAACK,IAAI,CAAiC,GAC5CH,aAAa,CAACW,aAAa,CAACR,IAAI,CAAC,EACrC;cAAES,QAAQ,EAAE;YAAE,CAChB,CAAC,CACF;YAEPX,UAAU,CAACE,IAAI,CAAC,GAAGlB,aAAa,CAACI,KAAK,EAAEX,YAAY,CAAC,GAAGmC,QAAQ,CAAC,CAAC;UACpE;QACF;QACA,KAAK,MAAMV,IAAI,IAAI1C,MAAM,CAAC2C,IAAI,CAACL,cAAc,CAACE,UAAU,CAAC,EAAE;UACzD,IAAIE,IAAI,KAAK,WAAW,EAAE;YACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACP,cAAc,CAACE,UAAU,CAACC,SAAS,CAAC,EAAE;cACvD;YACF;YACAH,cAAc,CAACE,UAAU,CAACC,SAAS,CAACK,OAAO,CAAC,CAAChD,KAAK,EAAEiD,KAAK,KAAK;cAC5D,KAAK,MAAMC,aAAa,IAAIhD,MAAM,CAAC2C,IAAI,CAAC7C,KAAK,CAAC,EAAE;gBAC9C0C,UAAU,CAACC,SAAS,CAAEQ,IAAI,CAAC;kBACzB,CAACD,aAAa,GAAGxB,aAAa,CAC5BI,KAAK,GAAGO,eAAe,EACvBlB,YAAY,CACVC,UAAU,CACRoB,cAAc,CAACY,aAAa,CAACT,SAAS,GAEhCH,cAAc,CAACY,aAAa,CACzBT,SAAS,CACZM,KAAK,CAAC,CACNC,aAAa,CACd,GACD,CAAC,EACL;oBAAEG,QAAQ,EAAEhB;kBAAgB,CAC9B,CAAC,EACDrC,KAAK,CACHkD,aAAa,CAEjB,CACF;gBACF,CAAuB,CAAC;cAC1B;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIR,UAAU,CAACE,IAAI,CAAC,KAAKW,SAAS,EAAE;YACzC;YACA;UACF,CAAC,MAAM;YACLb,UAAU,CAACE,IAAI,CAAC,GAAGlB,aAAa,CAC9BI,KAAK,EACLX,YAAY,CACVC,UAAU,CAACoB,cAAc,CAACY,aAAa,CAACR,IAAI,CAAC,EAAE;cAAES,QAAQ,EAAE;YAAE,CAAC,CAAC,EAC/Db,cAAc,CAACE,UAAU,CAACE,IAAI,CAChC,CACF,CAAC;UACH;QACF;QAEA,MAAMa,eAAe,GAAG,CACtBX,KAAK,CAACC,OAAO,CAACN,aAAa,CAACW,aAAa,CAACT,SAAS,CAAC,GAChDF,aAAa,CAACW,aAAa,CAACT,SAAS,GACrC,EAAE,EACNe,MAAM,CACN,CAACZ,KAAK,CAACC,OAAO,CAACP,cAAc,CAACE,UAAU,CAACC,SAAS,CAAC,GAC/CH,cAAc,CAACE,UAAU,CAACC,SAAS,GACnC,EAAE,EACJgB,GAAG,CAAE3D,KAAK,IAAK;UACf,MAAM4D,UAAU,GAAG1D,MAAM,CAAC2C,IAAI,CAAC7C,KAAK,CAAC;UACrC,IAAI,CAAA4D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,MAAM,IAAG,CAAC,EAAE;YAC1BC,OAAO,CAACC,KAAK,CACV,yDACH,CAAC;YACD,OAAO/D,KAAK;UACd;UAEA,MAAMkD,aAAa,GAAGU,UAAU,CAAC,CAAC,CAAC;UACnC,MAAMI,OAAO;UACX;UACA;UACChE,KAAK,CAACkD,aAAa,CAAC,CAAqBc,OAAO;UACnD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAIA,OAAO,CAACR,QAAQ,CAAC,KAAK,CAAC,EAAE;cAC3B,OAAO;gBACL,CAACN,aAAa,GAAG;cACnB,CAAC;YACH,CAAC,MAAM;cACL,OAAO;gBACL,CAACA,aAAa,GAAG;cACnB,CAAC;YACH;UACF,CAAC,MAAM,IAAIA,aAAa,CAACM,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC9C,OAAO;cAAE,CAACN,aAAa,GAAG;YAAE,CAAC;UAC/B,CAAC,MAAM;YACL,OAAO;cAAE,CAACA,aAAa,GAAG;YAAE,CAAC;UAC/B;QACF,CAAC,CACH,CAAC;QAED,OAAO;UACLE,aAAa,EAAE;YACb,GAAGX,aAAa,CAACW,aAAa;YAC9Ba,OAAO,EAAE1B,MAAM,CAAC2B,cAAc;YAC9BC,OAAO,EAAE5B,MAAM,CAAC6B,cAAc;YAC9BC,KAAK,EAAE9B,MAAM,CAAC+B,YAAY;YAC1BC,MAAM,EAAEhC,MAAM,CAACiC,aAAa;YAC5B7B,SAAS,EAAEc;UACb,CAAC;UACDf,UAAU,EAAE;YACVuB,OAAO,EAAEvC,aAAa,CACpBI,KAAK,GAAGO,eAAe,EACvBjB,UAAU,CAACmB,MAAM,CAACkC,aAAa,EAAE;cAAEpB,QAAQ,EAAEhB;YAAgB,CAAC,CAChE,CAAC;YACD8B,OAAO,EAAEzC,aAAa,CACpBI,KAAK,GAAGO,eAAe,EACvBjB,UAAU,CAACmB,MAAM,CAACmC,aAAa,EAAE;cAAErB,QAAQ,EAAEhB;YAAgB,CAAC,CAChE,CAAC;YACDgC,KAAK,EAAE3C,aAAa,CAClBI,KAAK,GAAGO,eAAe,EACvBjB,UAAU,CAACmB,MAAM,CAACoC,WAAW,EAAE;cAAEtB,QAAQ,EAAEhB;YAAgB,CAAC,CAC9D,CAAC;YACDkC,MAAM,EAAE7C,aAAa,CACnBI,KAAK,GAAGO,eAAe,EACvBjB,UAAU,CAACmB,MAAM,CAACqC,YAAY,EAAE;cAAEvB,QAAQ,EAAEhB;YAAgB,CAAC,CAC/D,CAAC;YACD,GAAGK;UACL,CAAC;UACDd;QACF,CAAC;MACH,CAAC;IACH,CAAC;EAAA;EA/ND,OAAOiD,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAItD,mBAAmB,CAAC,CAAC;EAClC;EAEA,OAAOuD,QAAQA,CACbC,SAA6D,EACxC;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAACH,cAAc,CAAC,CAAC;IACtC,OAAOG,QAAQ,CAACF,QAAQ,CAACC,SAAS,CAAC;EACrC;EAEAD,QAAQA,CACNC,SAA6D,EACxC;IACrB,IAAI,CAAC9C,SAAS,GAAG8C,SAAS;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOE,OAAOA,CACZF,SAA6D,EACxC;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAACH,cAAc,CAAC,CAAC;IACtC,OAAOG,QAAQ,CAACC,OAAO,CAACF,SAAS,CAAC;EACpC;EAEAE,OAAOA,CACLF,SAA6D,EACxC;IACrB,IAAI,CAAC3C,QAAQ,GAAG2C,SAAS;IACzB,OAAO,IAAI;EACb;AAgMF;;AAEA;AACA;AACA;AACA;AAHAlF,eAAA,CA5Oa0B,mBAAmB,gBAIV,qBAAqB;AA4O3C,OAAO,SAAS2D,iBAAiBA,CAC/BD,OAA2D,EAC3DH,QAA4D,EACvC;EACrB,OAAOvD,mBAAmB,CAACuD,QAAQ,CAACA,QAAQ,CAAC,CAACG,OAAO,CAACA,OAAO,CAAC;AAChE", "ignoreList": []}