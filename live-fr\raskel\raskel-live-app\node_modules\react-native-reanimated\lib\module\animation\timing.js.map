{"version": 3, "names": ["Easing", "assertEasingIsWorklet", "defineAnimation", "getReduceMotionForAnimation", "withTiming", "toValue", "userConfig", "callback", "__DEV__", "easing", "config", "duration", "inOut", "quad", "Object", "keys", "for<PERSON>ach", "key", "timing", "animation", "now", "startTime", "startValue", "runtime", "current", "progress", "onStart", "value", "previousAnimation", "type", "factory", "onFrame", "reduceMotion"], "sources": ["timing.ts"], "sourcesContent": ["'use strict';\nimport type { EasingFunctionFactory } from '../Easing';\nimport { Easing } from '../Easing';\nimport {\n  assertEasingIsWorklet,\n  defineAnimation,\n  getReduceMotionForAnimation,\n} from './util';\nimport type {\n  Animation,\n  AnimationCallback,\n  Timestamp,\n  AnimatableValue,\n  ReduceMotion,\n  EasingFunction,\n} from '../commonTypes';\n\n/**\n * The timing animation configuration.\n *\n * @param duration - Length of the animation (in milliseconds). Defaults to 300.\n * @param easing - An easing function which defines the animation curve. Defaults to `Easing.inOut(Easing.quad)`.\n * @param reduceMotion - Determines how the animation responds to the device's reduced motion accessibility setting. Default to `ReduceMotion.System` - {@link ReduceMotion}.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withTiming#config-\n */\ninterface TimingConfig {\n  duration?: number;\n  reduceMotion?: ReduceMotion;\n  easing?: EasingFunction | EasingFunctionFactory;\n}\n\nexport type WithTimingConfig = TimingConfig;\n\nexport interface TimingAnimation extends Animation<TimingAnimation> {\n  type: string;\n  easing: EasingFunction;\n  startValue: AnimatableValue;\n  startTime: Timestamp;\n  progress: number;\n  toValue: AnimatableValue;\n  current: AnimatableValue;\n}\n\ninterface InnerTimingAnimation\n  extends Omit<TimingAnimation, 'toValue' | 'current'> {\n  toValue: number;\n  current: number;\n}\n\n// TODO TYPESCRIPT This is temporary type put in here to get rid of our .d.ts file\ntype withTimingType = <T extends AnimatableValue>(\n  toValue: T,\n  userConfig?: TimingConfig,\n  callback?: AnimationCallback\n) => T;\n\n/**\n * Lets you create an animation based on duration and easing.\n *\n * @param toValue - The value on which the animation will come at rest - {@link AnimatableValue}.\n * @param config - The timing animation configuration - {@link TimingConfig}.\n * @param callback - A function called on animation complete - {@link AnimationCallback}.\n * @returns An [animation object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object) which holds the current state of the animation.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withTiming\n */\nexport const withTiming = function (\n  toValue: AnimatableValue,\n  userConfig?: TimingConfig,\n  callback?: AnimationCallback\n): Animation<TimingAnimation> {\n  'worklet';\n\n  if (__DEV__ && userConfig?.easing) {\n    assertEasingIsWorklet(userConfig.easing);\n  }\n\n  return defineAnimation<TimingAnimation>(toValue, () => {\n    'worklet';\n    const config: Required<Omit<TimingConfig, 'reduceMotion'>> = {\n      duration: 300,\n      easing: Easing.inOut(Easing.quad),\n    };\n    if (userConfig) {\n      Object.keys(userConfig).forEach(\n        (key) =>\n          ((config as any)[key] = userConfig[key as keyof typeof userConfig])\n      );\n    }\n\n    function timing(animation: InnerTimingAnimation, now: Timestamp): boolean {\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      const { toValue, startTime, startValue } = animation;\n      const runtime = now - startTime;\n\n      if (runtime >= config.duration) {\n        // reset startTime to avoid reusing finished animation config in `start` method\n        animation.startTime = 0;\n        animation.current = toValue;\n        return true;\n      }\n      const progress = animation.easing(runtime / config.duration);\n      animation.current =\n        (startValue as number) + (toValue - (startValue as number)) * progress;\n      return false;\n    }\n\n    function onStart(\n      animation: TimingAnimation,\n      value: number,\n      now: Timestamp,\n      previousAnimation: Animation<TimingAnimation>\n    ): void {\n      if (\n        previousAnimation &&\n        (previousAnimation as TimingAnimation).type === 'timing' &&\n        (previousAnimation as TimingAnimation).toValue === toValue &&\n        (previousAnimation as TimingAnimation).startTime\n      ) {\n        // to maintain continuity of timing animations we check if we are starting\n        // new timing over the old one with the same parameters. If so, we want\n        // to copy animation timeline properties\n        animation.startTime = (previousAnimation as TimingAnimation).startTime;\n        animation.startValue = (\n          previousAnimation as TimingAnimation\n        ).startValue;\n      } else {\n        animation.startTime = now;\n        animation.startValue = value;\n      }\n      animation.current = value;\n      if (typeof config.easing === 'object') {\n        animation.easing = config.easing.factory();\n      } else {\n        animation.easing = config.easing;\n      }\n    }\n\n    return {\n      type: 'timing',\n      onFrame: timing,\n      onStart: onStart as (animation: TimingAnimation, now: number) => boolean,\n      progress: 0,\n      toValue,\n      startValue: 0,\n      startTime: 0,\n      easing: () => 0,\n      current: toValue,\n      callback,\n      reduceMotion: getReduceMotionForAnimation(userConfig?.reduceMotion),\n    } as TimingAnimation;\n  });\n} as withTimingType;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,WAAW;AAClC,SACEC,qBAAqB,EACrBC,eAAe,EACfC,2BAA2B,QACtB,QAAQ;;AAUf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAyBA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAG,SAAAA,CACxBC,OAAwB,EACxBC,UAAyB,EACzBC,QAA4B,EACA;EAC5B,SAAS;;EAET,IAAIC,OAAO,IAAIF,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEG,MAAM,EAAE;IACjCR,qBAAqB,CAACK,UAAU,CAACG,MAAM,CAAC;EAC1C;EAEA,OAAOP,eAAe,CAAkBG,OAAO,EAAE,MAAM;IACrD,SAAS;;IACT,MAAMK,MAAoD,GAAG;MAC3DC,QAAQ,EAAE,GAAG;MACbF,MAAM,EAAET,MAAM,CAACY,KAAK,CAACZ,MAAM,CAACa,IAAI;IAClC,CAAC;IACD,IAAIP,UAAU,EAAE;MACdQ,MAAM,CAACC,IAAI,CAACT,UAAU,CAAC,CAACU,OAAO,CAC5BC,GAAG,IACAP,MAAM,CAASO,GAAG,CAAC,GAAGX,UAAU,CAACW,GAAG,CAC1C,CAAC;IACH;IAEA,SAASC,MAAMA,CAACC,SAA+B,EAAEC,GAAc,EAAW;MACxE;MACA,MAAM;QAAEf,OAAO;QAAEgB,SAAS;QAAEC;MAAW,CAAC,GAAGH,SAAS;MACpD,MAAMI,OAAO,GAAGH,GAAG,GAAGC,SAAS;MAE/B,IAAIE,OAAO,IAAIb,MAAM,CAACC,QAAQ,EAAE;QAC9B;QACAQ,SAAS,CAACE,SAAS,GAAG,CAAC;QACvBF,SAAS,CAACK,OAAO,GAAGnB,OAAO;QAC3B,OAAO,IAAI;MACb;MACA,MAAMoB,QAAQ,GAAGN,SAAS,CAACV,MAAM,CAACc,OAAO,GAAGb,MAAM,CAACC,QAAQ,CAAC;MAC5DQ,SAAS,CAACK,OAAO,GACdF,UAAU,GAAc,CAACjB,OAAO,GAAIiB,UAAqB,IAAIG,QAAQ;MACxE,OAAO,KAAK;IACd;IAEA,SAASC,OAAOA,CACdP,SAA0B,EAC1BQ,KAAa,EACbP,GAAc,EACdQ,iBAA6C,EACvC;MACN,IACEA,iBAAiB,IAChBA,iBAAiB,CAAqBC,IAAI,KAAK,QAAQ,IACvDD,iBAAiB,CAAqBvB,OAAO,KAAKA,OAAO,IACzDuB,iBAAiB,CAAqBP,SAAS,EAChD;QACA;QACA;QACA;QACAF,SAAS,CAACE,SAAS,GAAIO,iBAAiB,CAAqBP,SAAS;QACtEF,SAAS,CAACG,UAAU,GAClBM,iBAAiB,CACjBN,UAAU;MACd,CAAC,MAAM;QACLH,SAAS,CAACE,SAAS,GAAGD,GAAG;QACzBD,SAAS,CAACG,UAAU,GAAGK,KAAK;MAC9B;MACAR,SAAS,CAACK,OAAO,GAAGG,KAAK;MACzB,IAAI,OAAOjB,MAAM,CAACD,MAAM,KAAK,QAAQ,EAAE;QACrCU,SAAS,CAACV,MAAM,GAAGC,MAAM,CAACD,MAAM,CAACqB,OAAO,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLX,SAAS,CAACV,MAAM,GAAGC,MAAM,CAACD,MAAM;MAClC;IACF;IAEA,OAAO;MACLoB,IAAI,EAAE,QAAQ;MACdE,OAAO,EAAEb,MAAM;MACfQ,OAAO,EAAEA,OAA+D;MACxED,QAAQ,EAAE,CAAC;MACXpB,OAAO;MACPiB,UAAU,EAAE,CAAC;MACbD,SAAS,EAAE,CAAC;MACZZ,MAAM,EAAEA,CAAA,KAAM,CAAC;MACfe,OAAO,EAAEnB,OAAO;MAChBE,QAAQ;MACRyB,YAAY,EAAE7B,2BAA2B,CAACG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0B,YAAY;IACpE,CAAC;EACH,CAAC,CAAC;AACJ,CAAmB", "ignoreList": []}