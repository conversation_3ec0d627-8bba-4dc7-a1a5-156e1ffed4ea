{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "recognizePrefixSuffix", "withClamp", "config", "_animationToClamp", "animationToClamp", "strippedMin", "min", "undefined", "strippedValue", "strippedMax", "max", "clampOnFrame", "animation", "now", "finished", "onFrame", "current", "console", "warn", "prefix", "suffix", "newValue", "onStart", "value", "previousAnimation", "animationBeforeClamped", "callback", "isHigherOrder", "reduceMotion"], "sources": ["clamp.ts"], "sourcesContent": ["'use strict';\nimport {\n  defineAnimation,\n  getReduceMotionForAnimation,\n  recognizePrefixSuffix,\n} from './util';\nimport type {\n  Animation,\n  Timestamp,\n  AnimatableValue,\n  AnimationObject,\n  ReduceMotion,\n} from '../commonTypes';\nimport type { ClampAnimation } from './commonTypes';\n\ntype withClampType = <T extends number | string>(\n  config: {\n    min?: T;\n    max?: T;\n  },\n  clampedAnimation: T\n) => T;\n\nexport const withClamp = function <T extends number | string>(\n  config: { min?: T; max?: T; reduceMotion?: ReduceMotion },\n  _animationToClamp: AnimationObject<T> | (() => AnimationObject<T>)\n): Animation<ClampAnimation> {\n  'worklet';\n  return defineAnimation<ClampAnimation, AnimationObject<T>>(\n    _animationToClamp,\n    (): ClampAnimation => {\n      'worklet';\n      const animationToClamp =\n        typeof _animationToClamp === 'function'\n          ? _animationToClamp()\n          : _animationToClamp;\n\n      const strippedMin =\n        config.min === undefined\n          ? undefined\n          : recognizePrefixSuffix(config.min).strippedValue;\n\n      const strippedMax =\n        config.max === undefined\n          ? undefined\n          : recognizePrefixSuffix(config.max).strippedValue;\n\n      function clampOnFrame(\n        animation: ClampAnimation,\n        now: Timestamp\n      ): boolean {\n        const finished = animationToClamp.onFrame(animationToClamp, now);\n\n        if (animationToClamp.current === undefined) {\n          console.warn(\n            \"[Reanimated] Error inside 'withClamp' animation, the inner animation has invalid current value\"\n          );\n          return true;\n        } else {\n          const { prefix, strippedValue, suffix } = recognizePrefixSuffix(\n            animationToClamp.current\n          );\n\n          let newValue;\n\n          if (strippedMax !== undefined && strippedMax < strippedValue) {\n            newValue = strippedMax;\n          } else if (strippedMin !== undefined && strippedMin > strippedValue) {\n            newValue = strippedMin;\n          } else {\n            newValue = strippedValue;\n          }\n\n          animation.current =\n            typeof animationToClamp.current === 'number'\n              ? newValue\n              : `${prefix === undefined ? '' : prefix}${newValue}${\n                  suffix === undefined ? '' : suffix\n                }`;\n        }\n\n        return finished;\n      }\n\n      function onStart(\n        animation: Animation<any>,\n        value: AnimatableValue,\n        now: Timestamp,\n        previousAnimation: Animation<any> | null\n      ): void {\n        animation.current = value;\n        animation.previousAnimation = animationToClamp;\n        const animationBeforeClamped = previousAnimation?.previousAnimation;\n        if (\n          config.max !== undefined &&\n          config.min !== undefined &&\n          config.max < config.min\n        ) {\n          console.warn(\n            '[Reanimated] Wrong config was provided to withClamp. Min value is bigger than max'\n          );\n        }\n\n        animationToClamp.onStart(\n          animationToClamp,\n          /** provide the current value of the previous animation of the clamped animation \n          so we can animate from the original \"un-truncated\" value\n          */\n          animationBeforeClamped?.current || value,\n          now,\n          animationBeforeClamped\n        );\n      }\n\n      const callback = (finished?: boolean): void => {\n        if (animationToClamp.callback) {\n          animationToClamp.callback(finished);\n        }\n      };\n\n      return {\n        isHigherOrder: true,\n        onFrame: clampOnFrame,\n        onStart,\n        current: animationToClamp.current!,\n        callback,\n        previousAnimation: null,\n        reduceMotion: getReduceMotionForAnimation(config.reduceMotion),\n      };\n    }\n  );\n} as withClampType;\n"], "mappings": "AAAA,YAAY;;AACZ,SACEA,eAAe,EACfC,2BAA2B,EAC3BC,qBAAqB,QAChB,QAAQ;AAkBf,OAAO,MAAMC,SAAS,GAAG,SAAAA,CACvBC,MAAyD,EACzDC,iBAAkE,EACvC;EAC3B,SAAS;;EACT,OAAOL,eAAe,CACpBK,iBAAiB,EACjB,MAAsB;IACpB,SAAS;;IACT,MAAMC,gBAAgB,GACpB,OAAOD,iBAAiB,KAAK,UAAU,GACnCA,iBAAiB,CAAC,CAAC,GACnBA,iBAAiB;IAEvB,MAAME,WAAW,GACfH,MAAM,CAACI,GAAG,KAAKC,SAAS,GACpBA,SAAS,GACTP,qBAAqB,CAACE,MAAM,CAACI,GAAG,CAAC,CAACE,aAAa;IAErD,MAAMC,WAAW,GACfP,MAAM,CAACQ,GAAG,KAAKH,SAAS,GACpBA,SAAS,GACTP,qBAAqB,CAACE,MAAM,CAACQ,GAAG,CAAC,CAACF,aAAa;IAErD,SAASG,YAAYA,CACnBC,SAAyB,EACzBC,GAAc,EACL;MACT,MAAMC,QAAQ,GAAGV,gBAAgB,CAACW,OAAO,CAACX,gBAAgB,EAAES,GAAG,CAAC;MAEhE,IAAIT,gBAAgB,CAACY,OAAO,KAAKT,SAAS,EAAE;QAC1CU,OAAO,CAACC,IAAI,CACV,gGACF,CAAC;QACD,OAAO,IAAI;MACb,CAAC,MAAM;QACL,MAAM;UAAEC,MAAM;UAAEX,aAAa;UAAEY;QAAO,CAAC,GAAGpB,qBAAqB,CAC7DI,gBAAgB,CAACY,OACnB,CAAC;QAED,IAAIK,QAAQ;QAEZ,IAAIZ,WAAW,KAAKF,SAAS,IAAIE,WAAW,GAAGD,aAAa,EAAE;UAC5Da,QAAQ,GAAGZ,WAAW;QACxB,CAAC,MAAM,IAAIJ,WAAW,KAAKE,SAAS,IAAIF,WAAW,GAAGG,aAAa,EAAE;UACnEa,QAAQ,GAAGhB,WAAW;QACxB,CAAC,MAAM;UACLgB,QAAQ,GAAGb,aAAa;QAC1B;QAEAI,SAAS,CAACI,OAAO,GACf,OAAOZ,gBAAgB,CAACY,OAAO,KAAK,QAAQ,GACxCK,QAAQ,GACP,GAAEF,MAAM,KAAKZ,SAAS,GAAG,EAAE,GAAGY,MAAO,GAAEE,QAAS,GAC/CD,MAAM,KAAKb,SAAS,GAAG,EAAE,GAAGa,MAC7B,EAAC;MACV;MAEA,OAAON,QAAQ;IACjB;IAEA,SAASQ,OAAOA,CACdV,SAAyB,EACzBW,KAAsB,EACtBV,GAAc,EACdW,iBAAwC,EAClC;MACNZ,SAAS,CAACI,OAAO,GAAGO,KAAK;MACzBX,SAAS,CAACY,iBAAiB,GAAGpB,gBAAgB;MAC9C,MAAMqB,sBAAsB,GAAGD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEA,iBAAiB;MACnE,IACEtB,MAAM,CAACQ,GAAG,KAAKH,SAAS,IACxBL,MAAM,CAACI,GAAG,KAAKC,SAAS,IACxBL,MAAM,CAACQ,GAAG,GAAGR,MAAM,CAACI,GAAG,EACvB;QACAW,OAAO,CAACC,IAAI,CACV,mFACF,CAAC;MACH;MAEAd,gBAAgB,CAACkB,OAAO,CACtBlB,gBAAgB;MAChB;AACV;AACA;MACU,CAAAqB,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAET,OAAO,KAAIO,KAAK,EACxCV,GAAG,EACHY,sBACF,CAAC;IACH;IAEA,MAAMC,QAAQ,GAAIZ,QAAkB,IAAW;MAC7C,IAAIV,gBAAgB,CAACsB,QAAQ,EAAE;QAC7BtB,gBAAgB,CAACsB,QAAQ,CAACZ,QAAQ,CAAC;MACrC;IACF,CAAC;IAED,OAAO;MACLa,aAAa,EAAE,IAAI;MACnBZ,OAAO,EAAEJ,YAAY;MACrBW,OAAO;MACPN,OAAO,EAAEZ,gBAAgB,CAACY,OAAQ;MAClCU,QAAQ;MACRF,iBAAiB,EAAE,IAAI;MACvBI,YAAY,EAAE7B,2BAA2B,CAACG,MAAM,CAAC0B,YAAY;IAC/D,CAAC;EACH,CACF,CAAC;AACH,CAAkB", "ignoreList": []}