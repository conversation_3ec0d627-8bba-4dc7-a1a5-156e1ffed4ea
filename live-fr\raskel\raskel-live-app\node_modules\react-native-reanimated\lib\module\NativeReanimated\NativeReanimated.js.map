{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "checkCppVersion", "jsVersion", "getValueUnpackerCode", "isF<PERSON><PERSON>", "getShadowNodeWrapperFromRef", "ReanimatedModule", "assertSingleReanimatedInstance", "global", "_REANIMATED_VERSION_JS", "undefined", "Error", "NativeReanimated", "constructor", "__DEV__", "__reanimatedModuleProxy", "valueUnpackerCode", "installTurboModule", "InnerNativeModule", "makeShareableClone", "shouldPersistRemote", "nativeStateSource", "scheduleOnUI", "shareable", "executeOnUIRuntimeSync", "createWorkletRuntime", "name", "initializer", "scheduleOnRuntime", "workletRuntime", "shareableWorklet", "registerSensor", "sensorType", "interval", "iosReferenceFrame", "handler", "unregisterSensor", "sensorId", "registerEventHandler", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "emitterReactTag", "unregisterEventHandler", "id", "getViewProp", "viewTag", "propName", "component", "callback", "shadowNodeWrapper", "configureLayoutAnimationBatch", "layoutAnimationsBatch", "setShouldAnimateExitingForTag", "shouldAnimate", "enableLayoutAnimations", "flag", "configureProps", "uiProps", "nativeProps", "subscribeForKeyboardEvents", "isStatusBarTranslucent", "unsubscribeFromKeyboardEvents", "listenerId"], "sources": ["NativeReanimated.ts"], "sourcesContent": ["'use strict';\nimport type {\n  ShadowNodeWrapper,\n  Value3D,\n  ValueRotation,\n  ShareableRef,\n} from '../commonTypes';\nimport { checkCppVersion } from '../platform-specific/checkCppVersion';\nimport { jsVersion } from '../platform-specific/jsVersion';\nimport type { WorkletRuntime } from '../runtimes';\nimport { getValueUnpackerCode } from '../valueUnpacker';\nimport { isFabric } from '../PlatformChecker';\nimport type React from 'react';\nimport { getShadowNodeWrapperFromRef } from '../fabricUtils';\nimport type { LayoutAnimationBatchItem } from '../layoutReanimation/animationBuilder/commonTypes';\nimport ReanimatedModule from '../specs/NativeReanimatedModule';\n\n// this is the type of `__reanimatedModuleProxy` which is injected using JSI\nexport interface NativeReanimatedModule {\n  makeShareableClone<T>(\n    value: T,\n    shouldPersistRemote: boolean,\n    nativeStateSource?: object\n  ): ShareableRef<T>;\n  scheduleOnUI<T>(shareable: ShareableRef<T>): void;\n  executeOnUIRuntimeSync<T, R>(shareable: ShareableRef<T>): R;\n  createWorkletRuntime(\n    name: string,\n    initializer: ShareableRef<() => void>\n  ): WorkletRuntime;\n  scheduleOnRuntime<T>(\n    workletRuntime: WorkletRuntime,\n    worklet: ShareableRef<T>\n  ): void;\n  registerEventHandler<T>(\n    eventHandler: ShareableRef<T>,\n    eventName: string,\n    emitterReactTag: number\n  ): number;\n  unregisterEventHandler(id: number): void;\n  getViewProp<T>(\n    viewTagOrShadowNodeWrapper: number | ShadowNodeWrapper,\n    propName: string,\n    callback?: (result: T) => void\n  ): Promise<T>;\n  enableLayoutAnimations(flag: boolean): void;\n  registerSensor(\n    sensorType: number,\n    interval: number,\n    iosReferenceFrame: number,\n    handler: ShareableRef<(data: Value3D | ValueRotation) => void>\n  ): number;\n  unregisterSensor(sensorId: number): void;\n  configureProps(uiProps: string[], nativeProps: string[]): void;\n  subscribeForKeyboardEvents(\n    handler: ShareableRef<number>,\n    isStatusBarTranslucent: boolean\n  ): number;\n  unsubscribeFromKeyboardEvents(listenerId: number): void;\n  configureLayoutAnimationBatch(\n    layoutAnimationsBatch: LayoutAnimationBatchItem[]\n  ): void;\n  setShouldAnimateExitingForTag(viewTag: number, shouldAnimate: boolean): void;\n}\n\nfunction assertSingleReanimatedInstance() {\n  if (\n    global._REANIMATED_VERSION_JS !== undefined &&\n    global._REANIMATED_VERSION_JS !== jsVersion\n  ) {\n    throw new Error(\n      `[Reanimated] Another instance of Reanimated was detected.\nSee \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#another-instance-of-reanimated-was-detected\\` for more details. Previous: ${global._REANIMATED_VERSION_JS}, current: ${jsVersion}.`\n    );\n  }\n}\n\nexport class NativeReanimated {\n  private InnerNativeModule: NativeReanimatedModule;\n\n  constructor() {\n    // These checks have to split since version checking depend on the execution order\n    if (__DEV__) {\n      assertSingleReanimatedInstance();\n    }\n    global._REANIMATED_VERSION_JS = jsVersion;\n    if (global.__reanimatedModuleProxy === undefined) {\n      const valueUnpackerCode = getValueUnpackerCode();\n      ReanimatedModule?.installTurboModule(valueUnpackerCode);\n    }\n    if (global.__reanimatedModuleProxy === undefined) {\n      throw new Error(\n        `[Reanimated] Native part of Reanimated doesn't seem to be initialized.\nSee https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#native-part-of-reanimated-doesnt-seem-to-be-initialized for more details.`\n      );\n    }\n    if (__DEV__) {\n      checkCppVersion();\n    }\n    this.InnerNativeModule = global.__reanimatedModuleProxy;\n  }\n\n  makeShareableClone<T>(\n    value: T,\n    shouldPersistRemote: boolean,\n    nativeStateSource?: object\n  ) {\n    return this.InnerNativeModule.makeShareableClone(\n      value,\n      shouldPersistRemote,\n      nativeStateSource\n    );\n  }\n\n  scheduleOnUI<T>(shareable: ShareableRef<T>) {\n    return this.InnerNativeModule.scheduleOnUI(shareable);\n  }\n\n  executeOnUIRuntimeSync<T, R>(shareable: ShareableRef<T>): R {\n    return this.InnerNativeModule.executeOnUIRuntimeSync(shareable);\n  }\n\n  createWorkletRuntime(name: string, initializer: ShareableRef<() => void>) {\n    return this.InnerNativeModule.createWorkletRuntime(name, initializer);\n  }\n\n  scheduleOnRuntime<T>(\n    workletRuntime: WorkletRuntime,\n    shareableWorklet: ShareableRef<T>\n  ) {\n    return this.InnerNativeModule.scheduleOnRuntime(\n      workletRuntime,\n      shareableWorklet\n    );\n  }\n\n  registerSensor(\n    sensorType: number,\n    interval: number,\n    iosReferenceFrame: number,\n    handler: ShareableRef<(data: Value3D | ValueRotation) => void>\n  ) {\n    return this.InnerNativeModule.registerSensor(\n      sensorType,\n      interval,\n      iosReferenceFrame,\n      handler\n    );\n  }\n\n  unregisterSensor(sensorId: number) {\n    return this.InnerNativeModule.unregisterSensor(sensorId);\n  }\n\n  registerEventHandler<T>(\n    eventHandler: ShareableRef<T>,\n    eventName: string,\n    emitterReactTag: number\n  ) {\n    return this.InnerNativeModule.registerEventHandler(\n      eventHandler,\n      eventName,\n      emitterReactTag\n    );\n  }\n\n  unregisterEventHandler(id: number) {\n    return this.InnerNativeModule.unregisterEventHandler(id);\n  }\n\n  getViewProp<T>(\n    viewTag: number,\n    propName: string,\n    component: React.Component | undefined, // required on Fabric\n    callback?: (result: T) => void\n  ) {\n    let shadowNodeWrapper;\n    if (isFabric()) {\n      shadowNodeWrapper = getShadowNodeWrapperFromRef(\n        component as React.Component\n      );\n      return this.InnerNativeModule.getViewProp(\n        shadowNodeWrapper,\n        propName,\n        callback\n      );\n    }\n\n    return this.InnerNativeModule.getViewProp(viewTag, propName, callback);\n  }\n\n  configureLayoutAnimationBatch(\n    layoutAnimationsBatch: LayoutAnimationBatchItem[]\n  ) {\n    this.InnerNativeModule.configureLayoutAnimationBatch(layoutAnimationsBatch);\n  }\n\n  setShouldAnimateExitingForTag(viewTag: number, shouldAnimate: boolean) {\n    this.InnerNativeModule.setShouldAnimateExitingForTag(\n      viewTag,\n      shouldAnimate\n    );\n  }\n\n  enableLayoutAnimations(flag: boolean) {\n    this.InnerNativeModule.enableLayoutAnimations(flag);\n  }\n\n  configureProps(uiProps: string[], nativeProps: string[]) {\n    this.InnerNativeModule.configureProps(uiProps, nativeProps);\n  }\n\n  subscribeForKeyboardEvents(\n    handler: ShareableRef<number>,\n    isStatusBarTranslucent: boolean\n  ) {\n    return this.InnerNativeModule.subscribeForKeyboardEvents(\n      handler,\n      isStatusBarTranslucent\n    );\n  }\n\n  unsubscribeFromKeyboardEvents(listenerId: number) {\n    this.InnerNativeModule.unsubscribeFromKeyboardEvents(listenerId);\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AAOb,SAASW,eAAe,QAAQ,sCAAsC;AACtE,SAASC,SAAS,QAAQ,gCAAgC;AAE1D,SAASC,oBAAoB,QAAQ,kBAAkB;AACvD,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,2BAA2B,QAAQ,gBAAgB;AAE5D,OAAOC,gBAAgB,MAAM,iCAAiC;;AAE9D;;AAgDA,SAASC,8BAA8BA,CAAA,EAAG;EACxC,IACEC,MAAM,CAACC,sBAAsB,KAAKC,SAAS,IAC3CF,MAAM,CAACC,sBAAsB,KAAKP,SAAS,EAC3C;IACA,MAAM,IAAIS,KAAK,CACZ;AACP,iKAAiKH,MAAM,CAACC,sBAAuB,cAAaP,SAAU,GAClN,CAAC;EACH;AACF;AAEA,OAAO,MAAMU,gBAAgB,CAAC;EAG5BC,WAAWA,CAAA,EAAG;IAAAjC,eAAA;IACZ;IACA,IAAIkC,OAAO,EAAE;MACXP,8BAA8B,CAAC,CAAC;IAClC;IACAC,MAAM,CAACC,sBAAsB,GAAGP,SAAS;IACzC,IAAIM,MAAM,CAACO,uBAAuB,KAAKL,SAAS,EAAE;MAChD,MAAMM,iBAAiB,GAAGb,oBAAoB,CAAC,CAAC;MAChDG,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEW,kBAAkB,CAACD,iBAAiB,CAAC;IACzD;IACA,IAAIR,MAAM,CAACO,uBAAuB,KAAKL,SAAS,EAAE;MAChD,MAAM,IAAIC,KAAK,CACZ;AACT,6JACM,CAAC;IACH;IACA,IAAIG,OAAO,EAAE;MACXb,eAAe,CAAC,CAAC;IACnB;IACA,IAAI,CAACiB,iBAAiB,GAAGV,MAAM,CAACO,uBAAuB;EACzD;EAEAI,kBAAkBA,CAChBpC,KAAQ,EACRqC,mBAA4B,EAC5BC,iBAA0B,EAC1B;IACA,OAAO,IAAI,CAACH,iBAAiB,CAACC,kBAAkB,CAC9CpC,KAAK,EACLqC,mBAAmB,EACnBC,iBACF,CAAC;EACH;EAEAC,YAAYA,CAAIC,SAA0B,EAAE;IAC1C,OAAO,IAAI,CAACL,iBAAiB,CAACI,YAAY,CAACC,SAAS,CAAC;EACvD;EAEAC,sBAAsBA,CAAOD,SAA0B,EAAK;IAC1D,OAAO,IAAI,CAACL,iBAAiB,CAACM,sBAAsB,CAACD,SAAS,CAAC;EACjE;EAEAE,oBAAoBA,CAACC,IAAY,EAAEC,WAAqC,EAAE;IACxE,OAAO,IAAI,CAACT,iBAAiB,CAACO,oBAAoB,CAACC,IAAI,EAAEC,WAAW,CAAC;EACvE;EAEAC,iBAAiBA,CACfC,cAA8B,EAC9BC,gBAAiC,EACjC;IACA,OAAO,IAAI,CAACZ,iBAAiB,CAACU,iBAAiB,CAC7CC,cAAc,EACdC,gBACF,CAAC;EACH;EAEAC,cAAcA,CACZC,UAAkB,EAClBC,QAAgB,EAChBC,iBAAyB,EACzBC,OAA8D,EAC9D;IACA,OAAO,IAAI,CAACjB,iBAAiB,CAACa,cAAc,CAC1CC,UAAU,EACVC,QAAQ,EACRC,iBAAiB,EACjBC,OACF,CAAC;EACH;EAEAC,gBAAgBA,CAACC,QAAgB,EAAE;IACjC,OAAO,IAAI,CAACnB,iBAAiB,CAACkB,gBAAgB,CAACC,QAAQ,CAAC;EAC1D;EAEAC,oBAAoBA,CAClBC,YAA6B,EAC7BC,SAAiB,EACjBC,eAAuB,EACvB;IACA,OAAO,IAAI,CAACvB,iBAAiB,CAACoB,oBAAoB,CAChDC,YAAY,EACZC,SAAS,EACTC,eACF,CAAC;EACH;EAEAC,sBAAsBA,CAACC,EAAU,EAAE;IACjC,OAAO,IAAI,CAACzB,iBAAiB,CAACwB,sBAAsB,CAACC,EAAE,CAAC;EAC1D;EAEAC,WAAWA,CACTC,OAAe,EACfC,QAAgB,EAChBC,SAAsC;EAAE;EACxCC,QAA8B,EAC9B;IACA,IAAIC,iBAAiB;IACrB,IAAI7C,QAAQ,CAAC,CAAC,EAAE;MACd6C,iBAAiB,GAAG5C,2BAA2B,CAC7C0C,SACF,CAAC;MACD,OAAO,IAAI,CAAC7B,iBAAiB,CAAC0B,WAAW,CACvCK,iBAAiB,EACjBH,QAAQ,EACRE,QACF,CAAC;IACH;IAEA,OAAO,IAAI,CAAC9B,iBAAiB,CAAC0B,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEE,QAAQ,CAAC;EACxE;EAEAE,6BAA6BA,CAC3BC,qBAAiD,EACjD;IACA,IAAI,CAACjC,iBAAiB,CAACgC,6BAA6B,CAACC,qBAAqB,CAAC;EAC7E;EAEAC,6BAA6BA,CAACP,OAAe,EAAEQ,aAAsB,EAAE;IACrE,IAAI,CAACnC,iBAAiB,CAACkC,6BAA6B,CAClDP,OAAO,EACPQ,aACF,CAAC;EACH;EAEAC,sBAAsBA,CAACC,IAAa,EAAE;IACpC,IAAI,CAACrC,iBAAiB,CAACoC,sBAAsB,CAACC,IAAI,CAAC;EACrD;EAEAC,cAAcA,CAACC,OAAiB,EAAEC,WAAqB,EAAE;IACvD,IAAI,CAACxC,iBAAiB,CAACsC,cAAc,CAACC,OAAO,EAAEC,WAAW,CAAC;EAC7D;EAEAC,0BAA0BA,CACxBxB,OAA6B,EAC7ByB,sBAA+B,EAC/B;IACA,OAAO,IAAI,CAAC1C,iBAAiB,CAACyC,0BAA0B,CACtDxB,OAAO,EACPyB,sBACF,CAAC;EACH;EAEAC,6BAA6BA,CAACC,UAAkB,EAAE;IAChD,IAAI,CAAC5C,iBAAiB,CAAC2C,6BAA6B,CAACC,UAAU,CAAC;EAClE;AACF", "ignoreList": []}