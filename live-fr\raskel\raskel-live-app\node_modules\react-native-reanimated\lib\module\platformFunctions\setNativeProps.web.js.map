{"version": 3, "names": ["_updatePropsJS", "setNativeProps", "animatedRef", "updates", "component"], "sources": ["setNativeProps.web.ts"], "sourcesContent": ["'use strict';\nimport type { ReanimatedHTMLElement } from '../js-reanimated';\nimport { _updatePropsJS } from '../js-reanimated';\nimport type { StyleProps } from '../commonTypes';\nimport type { AnimatedRef } from '../hook/commonTypes';\nimport type { Component } from 'react';\n\nexport function setNativeProps<T extends Component>(\n  animatedRef: AnimatedRef<T>,\n  updates: StyleProps\n) {\n  const component = animatedRef() as ReanimatedHTMLElement;\n  _updatePropsJS(updates, component);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,kBAAkB;AAKjD,OAAO,SAASC,cAAcA,CAC5BC,WAA2B,EAC3BC,OAAmB,EACnB;EACA,MAAMC,SAAS,GAAGF,WAAW,CAAC,CAA0B;EACxDF,cAAc,CAACG,OAAO,EAAEC,SAAS,CAAC;AACpC", "ignoreList": []}