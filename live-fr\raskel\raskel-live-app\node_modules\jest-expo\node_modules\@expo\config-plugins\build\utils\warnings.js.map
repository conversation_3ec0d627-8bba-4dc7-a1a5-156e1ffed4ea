{"version": 3, "file": "warnings.js", "names": ["_chalk", "data", "_interopRequireDefault", "require", "e", "__esModule", "default", "addWarningAndroid", "property", "text", "link", "console", "warn", "formatWarning", "addWarningIOS", "addWarningForPlatform", "platform", "warning", "chalk", "yellow", "bold", "gray"], "sources": ["../../src/utils/warnings.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { ModPlatform } from '../Plugin.types';\n\n/**\n * Log a warning that doesn't disrupt the spinners.\n *\n * ```sh\n * » android: android.package: property is invalid https://expo.fyi/android-package\n * ```\n *\n * @param property Name of the config property that triggered the warning (best-effort)\n * @param text Main warning message\n * @param link Useful link to resources related to the warning\n */\nexport function addWarningAndroid(property: string, text: string, link?: string) {\n  console.warn(formatWarning('android', property, text, link));\n}\n\n/**\n * Log a warning that doesn't disrupt the spinners.\n *\n * ```sh\n * » ios: ios.bundleIdentifier: property is invalid https://expo.fyi/bundle-identifier\n * ```\n *\n * @param property Name of the config property that triggered the warning (best-effort)\n * @param text Main warning message\n * @param link Useful link to resources related to the warning\n */\nexport function addWarningIOS(property: string, text: string, link?: string) {\n  console.warn(formatWarning('ios', property, text, link));\n}\n\nexport function addWarningForPlatform(\n  platform: ModPlatform & string,\n  property: string,\n  text: string,\n  link?: string\n) {\n  console.warn(formatWarning(platform, property, text, link));\n}\n\nfunction formatWarning(platform: string, property: string, warning: string, link?: string) {\n  return chalk.yellow`${'» ' + chalk.bold(platform)}: ${property}: ${warning}${\n    link ? chalk.gray(' ' + link) : ''\n  }`;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0B,SAAAC,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAI1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,iBAAiBA,CAACC,QAAgB,EAAEC,IAAY,EAAEC,IAAa,EAAE;EAC/EC,OAAO,CAACC,IAAI,CAACC,aAAa,CAAC,SAAS,EAAEL,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,aAAaA,CAACN,QAAgB,EAAEC,IAAY,EAAEC,IAAa,EAAE;EAC3EC,OAAO,CAACC,IAAI,CAACC,aAAa,CAAC,KAAK,EAAEL,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC;AAC1D;AAEO,SAASK,qBAAqBA,CACnCC,QAA8B,EAC9BR,QAAgB,EAChBC,IAAY,EACZC,IAAa,EACb;EACAC,OAAO,CAACC,IAAI,CAACC,aAAa,CAACG,QAAQ,EAAER,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC;AAC7D;AAEA,SAASG,aAAaA,CAACG,QAAgB,EAAER,QAAgB,EAAES,OAAe,EAAEP,IAAa,EAAE;EACzF,OAAOQ,gBAAK,CAACC,MAAM,GAAG,IAAI,GAAGD,gBAAK,CAACE,IAAI,CAACJ,QAAQ,CAAC,KAAKR,QAAQ,KAAKS,OAAO,GACxEP,IAAI,GAAGQ,gBAAK,CAACG,IAAI,CAAC,GAAG,GAAGX,IAAI,CAAC,GAAG,EAAE,EAClC;AACJ", "ignoreList": []}