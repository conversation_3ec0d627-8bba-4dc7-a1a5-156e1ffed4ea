{"version": 3, "names": ["reportFatalErrorOnJS", "isChromeDebugger", "isJest", "shouldBeUseWeb", "runOnJS", "setupMicrotasks", "callMicrotasks", "runOnUIImmediately", "mockedRequestAnimationFrame", "IS_JEST", "SHOULD_BE_USE_WEB", "IS_CHROME_DEBUGGER", "callGuardDEV", "fn", "args", "e", "global", "__E<PERSON><PERSON><PERSON><PERSON>s", "reportFatalError", "setupCallGuard", "__callGuardDEV", "error", "message", "stack", "createMemorySafeCapturableConsole", "consoleCopy", "Object", "fromEntries", "entries", "console", "map", "methodName", "method", "methodWrapper", "name", "defineProperty", "value", "writable", "capturableConsole", "setupConsole", "assert", "debug", "log", "warn", "info", "setupRequestAnimationFrame", "nativeRequestAnimationFrame", "requestAnimationFrame", "animationFrameCallbacks", "flushRequested", "__flushAnimationFrame", "frameTimestamp", "currentCallbacks", "for<PERSON>ach", "f", "callback", "push", "timestamp", "__frameTimestamp", "undefined", "initializeUIRuntime", "globalThis"], "sources": ["initializers.ts"], "sourcesContent": ["'use strict';\nimport { reportFatalErrorOnJS } from './errors';\nimport { isChromeDebugger, isJest, shouldBeUseWeb } from './PlatformChecker';\nimport {\n  runOnJS,\n  setupMicrotasks,\n  callMicrotasks,\n  runOnUIImmediately,\n} from './threads';\nimport { mockedRequestAnimationFrame } from './mockedRequestAnimationFrame';\n\nconst IS_JEST = isJest();\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\nconst IS_CHROME_DEBUGGER = isChromeDebugger();\n\n// callGuard is only used with debug builds\nexport function callGuardDEV<Args extends unknown[], ReturnValue>(\n  fn: (...args: Args) => ReturnValue,\n  ...args: Args\n): ReturnValue | void {\n  'worklet';\n  try {\n    return fn(...args);\n  } catch (e) {\n    if (global.__ErrorUtils) {\n      global.__ErrorUtils.reportFatalError(e as Error);\n    } else {\n      throw e;\n    }\n  }\n}\n\nexport function setupCallGuard() {\n  'worklet';\n  global.__callGuardDEV = callGuardDEV;\n  global.__ErrorUtils = {\n    reportFatalError: (error: Error) => {\n      runOnJS(reportFatalErrorOnJS)({\n        message: error.message,\n        stack: error.stack,\n      });\n    },\n  };\n}\n\n/**\n * Currently there seems to be a bug in the JSI layer which causes a crash when\n * we try to copy some of the console methods, i.e. `clear` or `dirxml`.\n *\n * The crash happens only in React Native 0.75. It's not reproducible in neither\n * 0.76 nor 0.74. It also happens only in the configuration of a debug app and\n * production bundle.\n *\n * I haven't yet discovered what exactly causes the crash. It's tied to the\n * console methods sometimes being `HostFunction`s. Therefore, as a workaround\n * we don't copy the methods as they are in the original console object, we copy\n * JavaScript wrappers instead.\n */\nfunction createMemorySafeCapturableConsole(): typeof console {\n  const consoleCopy = Object.fromEntries(\n    Object.entries(console).map(([methodName, method]) => {\n      const methodWrapper = function methodWrapper(...args: unknown[]) {\n        return method(...args);\n      };\n      if (method.name) {\n        /**\n         * Set the original method name as the wrapper name if available.\n         *\n         * It might be unnecessary but if we want to fully mimic the console\n         * object we should take into the account the fact some code might rely\n         * on the method name.\n         */\n        Object.defineProperty(methodWrapper, 'name', {\n          value: method.name,\n          writable: false,\n        });\n      }\n      return [methodName, methodWrapper];\n    })\n  );\n\n  return consoleCopy as unknown as typeof console;\n}\n\n// We really have to create a copy of console here. Function runOnJS we use on elements inside\n// this object makes it not configurable\nconst capturableConsole = createMemorySafeCapturableConsole();\n\nexport function setupConsole() {\n  'worklet';\n  if (!IS_CHROME_DEBUGGER) {\n    // @ts-ignore TypeScript doesn't like that there are missing methods in console object, but we don't provide all the methods for the UI runtime console version\n    global.console = {\n      /* eslint-disable @typescript-eslint/unbound-method */\n      assert: runOnJS(capturableConsole.assert),\n      debug: runOnJS(capturableConsole.debug),\n      log: runOnJS(capturableConsole.log),\n      warn: runOnJS(capturableConsole.warn),\n      error: runOnJS(capturableConsole.error),\n      info: runOnJS(capturableConsole.info),\n      /* eslint-enable @typescript-eslint/unbound-method */\n    };\n  }\n}\n\nfunction setupRequestAnimationFrame() {\n  'worklet';\n\n  // Jest mocks requestAnimationFrame API and it does not like if that mock gets overridden\n  // so we avoid doing requestAnimationFrame batching in Jest environment.\n  const nativeRequestAnimationFrame = global.requestAnimationFrame;\n\n  let animationFrameCallbacks: Array<(timestamp: number) => void> = [];\n  let flushRequested = false;\n\n  global.__flushAnimationFrame = (frameTimestamp: number) => {\n    const currentCallbacks = animationFrameCallbacks;\n    animationFrameCallbacks = [];\n    currentCallbacks.forEach((f) => f(frameTimestamp));\n    callMicrotasks();\n  };\n\n  global.requestAnimationFrame = (\n    callback: (timestamp: number) => void\n  ): number => {\n    animationFrameCallbacks.push(callback);\n    if (!flushRequested) {\n      flushRequested = true;\n      nativeRequestAnimationFrame((timestamp) => {\n        flushRequested = false;\n        global.__frameTimestamp = timestamp;\n        global.__flushAnimationFrame(timestamp);\n        global.__frameTimestamp = undefined;\n      });\n    }\n    // Reanimated currently does not support cancelling callbacks requested with\n    // requestAnimationFrame. We return -1 as identifier which isn't in line\n    // with the spec but it should give users better clue in case they actually\n    // attempt to store the value returned from rAF and use it for cancelling.\n    return -1;\n  };\n}\n\nexport function initializeUIRuntime() {\n  if (IS_JEST) {\n    // requestAnimationFrame react-native jest's setup is incorrect as it polyfills\n    // the method directly using setTimeout, therefore the callback doesn't get the\n    // expected timestamp as the only argument: https://github.com/facebook/react-native/blob/main/packages/react-native/jest/setup.js#L28\n    // We override this setup here to make sure that callbacks get the proper timestamps\n    // when executed. For non-jest environments we define requestAnimationFrame in setupRequestAnimationFrame\n    // @ts-ignore TypeScript uses Node definition for rAF, setTimeout, etc which returns a Timeout object rather than a number\n    globalThis.requestAnimationFrame = mockedRequestAnimationFrame;\n  }\n\n  runOnUIImmediately(() => {\n    'worklet';\n    setupCallGuard();\n    setupConsole();\n    if (!SHOULD_BE_USE_WEB) {\n      setupMicrotasks();\n      setupRequestAnimationFrame();\n    }\n  })();\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,oBAAoB,QAAQ,UAAU;AAC/C,SAASC,gBAAgB,EAAEC,MAAM,EAAEC,cAAc,QAAQ,mBAAmB;AAC5E,SACEC,OAAO,EACPC,eAAe,EACfC,cAAc,EACdC,kBAAkB,QACb,WAAW;AAClB,SAASC,2BAA2B,QAAQ,+BAA+B;AAE3E,MAAMC,OAAO,GAAGP,MAAM,CAAC,CAAC;AACxB,MAAMQ,iBAAiB,GAAGP,cAAc,CAAC,CAAC;AAC1C,MAAMQ,kBAAkB,GAAGV,gBAAgB,CAAC,CAAC;;AAE7C;AACA,OAAO,SAASW,YAAYA,CAC1BC,EAAkC,EAClC,GAAGC,IAAU,EACO;EACpB,SAAS;;EACT,IAAI;IACF,OAAOD,EAAE,CAAC,GAAGC,IAAI,CAAC;EACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAIC,MAAM,CAACC,YAAY,EAAE;MACvBD,MAAM,CAACC,YAAY,CAACC,gBAAgB,CAACH,CAAU,CAAC;IAClD,CAAC,MAAM;MACL,MAAMA,CAAC;IACT;EACF;AACF;AAEA,OAAO,SAASI,cAAcA,CAAA,EAAG;EAC/B,SAAS;;EACTH,MAAM,CAACI,cAAc,GAAGR,YAAY;EACpCI,MAAM,CAACC,YAAY,GAAG;IACpBC,gBAAgB,EAAGG,KAAY,IAAK;MAClCjB,OAAO,CAACJ,oBAAoB,CAAC,CAAC;QAC5BsB,OAAO,EAAED,KAAK,CAACC,OAAO;QACtBC,KAAK,EAAEF,KAAK,CAACE;MACf,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAAA,EAAmB;EAC3D,MAAMC,WAAW,GAAGC,MAAM,CAACC,WAAW,CACpCD,MAAM,CAACE,OAAO,CAACC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,UAAU,EAAEC,MAAM,CAAC,KAAK;IACpD,MAAMC,aAAa,GAAG,SAASA,aAAaA,CAAC,GAAGnB,IAAe,EAAE;MAC/D,OAAOkB,MAAM,CAAC,GAAGlB,IAAI,CAAC;IACxB,CAAC;IACD,IAAIkB,MAAM,CAACE,IAAI,EAAE;MACf;AACR;AACA;AACA;AACA;AACA;AACA;MACQR,MAAM,CAACS,cAAc,CAACF,aAAa,EAAE,MAAM,EAAE;QAC3CG,KAAK,EAAEJ,MAAM,CAACE,IAAI;QAClBG,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,CAACN,UAAU,EAAEE,aAAa,CAAC;EACpC,CAAC,CACH,CAAC;EAED,OAAOR,WAAW;AACpB;;AAEA;AACA;AACA,MAAMa,iBAAiB,GAAGd,iCAAiC,CAAC,CAAC;AAE7D,OAAO,SAASe,YAAYA,CAAA,EAAG;EAC7B,SAAS;;EACT,IAAI,CAAC5B,kBAAkB,EAAE;IACvB;IACAK,MAAM,CAACa,OAAO,GAAG;MACf;MACAW,MAAM,EAAEpC,OAAO,CAACkC,iBAAiB,CAACE,MAAM,CAAC;MACzCC,KAAK,EAAErC,OAAO,CAACkC,iBAAiB,CAACG,KAAK,CAAC;MACvCC,GAAG,EAAEtC,OAAO,CAACkC,iBAAiB,CAACI,GAAG,CAAC;MACnCC,IAAI,EAAEvC,OAAO,CAACkC,iBAAiB,CAACK,IAAI,CAAC;MACrCtB,KAAK,EAAEjB,OAAO,CAACkC,iBAAiB,CAACjB,KAAK,CAAC;MACvCuB,IAAI,EAAExC,OAAO,CAACkC,iBAAiB,CAACM,IAAI;MACpC;IACF,CAAC;EACH;AACF;AAEA,SAASC,0BAA0BA,CAAA,EAAG;EACpC,SAAS;;EAET;EACA;EACA,MAAMC,2BAA2B,GAAG9B,MAAM,CAAC+B,qBAAqB;EAEhE,IAAIC,uBAA2D,GAAG,EAAE;EACpE,IAAIC,cAAc,GAAG,KAAK;EAE1BjC,MAAM,CAACkC,qBAAqB,GAAIC,cAAsB,IAAK;IACzD,MAAMC,gBAAgB,GAAGJ,uBAAuB;IAChDA,uBAAuB,GAAG,EAAE;IAC5BI,gBAAgB,CAACC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACH,cAAc,CAAC,CAAC;IAClD7C,cAAc,CAAC,CAAC;EAClB,CAAC;EAEDU,MAAM,CAAC+B,qBAAqB,GAC1BQ,QAAqC,IAC1B;IACXP,uBAAuB,CAACQ,IAAI,CAACD,QAAQ,CAAC;IACtC,IAAI,CAACN,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MACrBH,2BAA2B,CAAEW,SAAS,IAAK;QACzCR,cAAc,GAAG,KAAK;QACtBjC,MAAM,CAAC0C,gBAAgB,GAAGD,SAAS;QACnCzC,MAAM,CAACkC,qBAAqB,CAACO,SAAS,CAAC;QACvCzC,MAAM,CAAC0C,gBAAgB,GAAGC,SAAS;MACrC,CAAC,CAAC;IACJ;IACA;IACA;IACA;IACA;IACA,OAAO,CAAC,CAAC;EACX,CAAC;AACH;AAEA,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,IAAInD,OAAO,EAAE;IACX;IACA;IACA;IACA;IACA;IACA;IACAoD,UAAU,CAACd,qBAAqB,GAAGvC,2BAA2B;EAChE;EAEAD,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTY,cAAc,CAAC,CAAC;IAChBoB,YAAY,CAAC,CAAC;IACd,IAAI,CAAC7B,iBAAiB,EAAE;MACtBL,eAAe,CAAC,CAAC;MACjBwC,0BAA0B,CAAC,CAAC;IAC9B;EACF,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}