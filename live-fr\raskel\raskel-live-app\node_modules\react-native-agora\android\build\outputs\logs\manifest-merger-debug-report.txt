-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:1:1-9:12
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:1:1-9:12
	package
		INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml
	xmlns:android
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:2:3-65
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:2:20-62
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:3:3-63
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:3:20-60
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:4:3-69
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:4:20-66
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:5:3-78
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:5:20-75
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:6:3-74
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:6:20-71
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:7:3-77
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:7:20-74
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:8:3-66
	android:name
		ADDED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml:8:20-63
uses-sdk
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml
INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\fr\live-fr\raskel\raskel-live-app\node_modules\react-native-agora\android\src\main\AndroidManifestNew.xml
