{"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineProperty", "enumerable", "configurable", "writable", "t", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "findNodeHandle", "Platform", "invariant", "adaptViewConfig", "<PERSON><PERSON><PERSON><PERSON>", "enableLayoutAnimations", "SharedTransition", "LayoutAnimationType", "getShadowNodeWrapperFromRef", "removeFromPropsRegistry", "getReduceMotionFromConfig", "maybeBuild", "SkipEnteringContext", "JSPropsUpdater", "flattenArray", "setAndForwardRef", "isF<PERSON><PERSON>", "isJest", "isWeb", "shouldBeUseWeb", "InlinePropManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startWebLayoutAnimation", "tryActivateLayoutTransition", "configureWebLayoutAnimations", "getReducedMotionFromConfig", "saveSnapshot", "updateLayoutAnimations", "addHTMLMutationObserver", "getViewInfo", "NativeEventsManager", "IS_WEB", "onlyAnimatedStyles", "styles", "filter", "style", "viewDescriptors", "id", "createAnimatedComponent", "Component", "options", "isReactComponent", "name", "AnimatedComponent", "constructor", "props", "getForwardedRef", "forwardedRef", "setLocalRef", "ref", "tag", "_componentViewTag", "layout", "entering", "exiting", "sharedTransitionTag", "_this$context", "_configureSharedTransition", "reduceMotionInExiting", "getReduceMotion", "_this$props", "EXITING", "displayName", "skipEntering", "context", "current", "_this$props2", "ENTERING", "_component", "jestAnimatedStyle", "_this$props3", "reanimatedID", "componentDidMount", "_this$_NativeEventsMa", "_getComponentViewTag", "_NativeEventsManager", "attachEvents", "_jsPropsUpdater", "addOnJSPropsChangeListener", "_attachAnimatedStyles", "_InlinePropManager", "attachInlineProps", "_getViewInfo", "_configureLayoutTransition", "_isFirstRender", "componentWillUnmount", "_this$_NativeEventsMa2", "_this$_sharedElementT", "detachEvents", "removeOnJSPropsChangeListener", "_detachStyles", "detachInlineProps", "_sharedElementTransition", "unregisterTransition", "_this$props4", "viewTag", "_styles", "_this$props$animatedP", "remove", "animatedProps", "_updateFromNative", "setNativeProps", "_this$_component", "_this$_component$setN", "_this$_component2", "_getAnimatableRef", "_ref", "_viewInfo", "undefined", "viewName", "shadowNodeWrapper", "viewConfig", "component", "getAnimatableRef", "hostInstance", "findHostInstance_DEPRECATED", "Error", "viewInfo", "_this$props$animatedP2", "_this$props$animatedP3", "prevStyles", "prevAnimatedProps", "_animatedProps", "hasReanimated2Props", "hasOneSameStyle", "prevStyle", "isPresent", "some", "for<PERSON>ach", "add", "initial", "componentDidUpdate", "prevProps", "_prevState", "snapshot", "_this$_NativeEventsMa3", "oldLayout", "updateEvents", "LAYOUT", "isUnmounting", "_this$_sharedElementT2", "sharedElementTransition", "sharedTransitionStyle", "registerTransition", "getSnapshotBeforeUpdate", "_this$_component3", "getBoundingClientRect", "render", "_this$context2", "filteredProps", "_Props<PERSON>ilter", "filterNonAnimatedProps", "visibility", "platformProps", "select", "web", "default", "collapsable", "nativeID", "createElement", "_setComponentRef", "forwardRef"], "sources": ["createAnimatedComponent.tsx"], "sourcesContent": ["'use strict';\nimport type {\n  Component,\n  ComponentClass,\n  ComponentType,\n  FunctionComponent,\n  MutableRefObject,\n} from 'react';\nimport React from 'react';\nimport { findNodeHandle, Platform } from 'react-native';\nimport '../layoutReanimation/animationsManager';\nimport invariant from 'invariant';\nimport { adaptViewConfig } from '../ConfigHelper';\nimport { RNRenderer } from '../platform-specific/RNRenderer';\nimport { enableLayoutAnimations } from '../core';\nimport { SharedTransition, LayoutAnimationType } from '../layoutReanimation';\nimport type { StyleProps, ShadowNodeWrapper } from '../commonTypes';\nimport { getShadowNodeWrapperFromRef } from '../fabricUtils';\nimport { removeFromPropsRegistry } from '../PropsRegistry';\nimport { getReduceMotionFromConfig } from '../animation/util';\nimport { maybeBuild } from '../animationBuilder';\nimport { SkipEnteringContext } from '../component/LayoutAnimationConfig';\nimport type { AnimateProps } from '../helperTypes';\nimport JSPropsUpdater from './JSPropsUpdater';\nimport type {\n  AnimatedComponentProps,\n  AnimatedProps,\n  InitialComponentProps,\n  AnimatedComponentRef,\n  IAnimatedComponentInternal,\n  ViewInfo,\n  INativeEventsManager,\n} from './commonTypes';\nimport { flattenArray } from './utils';\nimport setAndForwardRef from './setAndForwardRef';\nimport { isFabric, isJest, isWeb, shouldBeUseWeb } from '../PlatformChecker';\nimport { InlinePropManager } from './InlinePropManager';\nimport { PropsFilter } from './PropsFilter';\nimport {\n  startWebLayoutAnimation,\n  tryActivateLayoutTransition,\n  configureWebLayoutAnimations,\n  getReducedMotionFromConfig,\n  saveSnapshot,\n} from '../layoutReanimation/web';\nimport { updateLayoutAnimations } from '../UpdateLayoutAnimations';\nimport type { CustomConfig } from '../layoutReanimation/web/config';\nimport type { FlatList, FlatListProps } from 'react-native';\nimport { addHTMLMutationObserver } from '../layoutReanimation/web/domUtils';\nimport { getViewInfo } from './getViewInfo';\nimport { NativeEventsManager } from './NativeEventsManager';\nimport type { ReanimatedHTMLElement } from '../js-reanimated';\n\nconst IS_WEB = isWeb();\n\nif (IS_WEB) {\n  configureWebLayoutAnimations();\n}\n\nfunction onlyAnimatedStyles(styles: StyleProps[]): StyleProps[] {\n  return styles.filter((style) => style?.viewDescriptors);\n}\n\ntype Options<P> = {\n  setNativeProps: (ref: AnimatedComponentRef, props: P) => void;\n};\n\n/**\n * Lets you create an Animated version of any React Native component.\n *\n * @param component - The component you want to make animatable.\n * @returns A component that Reanimated is capable of animating.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/core/createAnimatedComponent\n */\n\n// Don't change the order of overloads, since such a change breaks current behavior\nexport function createAnimatedComponent<P extends object>(\n  component: FunctionComponent<P>,\n  options?: Options<P>\n): FunctionComponent<AnimateProps<P>>;\n\nexport function createAnimatedComponent<P extends object>(\n  component: ComponentClass<P>,\n  options?: Options<P>\n): ComponentClass<AnimateProps<P>>;\n\nexport function createAnimatedComponent<P extends object>(\n  // Actually ComponentType<P = {}> = ComponentClass<P> | FunctionComponent<P> but we need this overload too\n  // since some external components (like FastImage) are typed just as ComponentType\n  component: ComponentType<P>,\n  options?: Options<P>\n): FunctionComponent<AnimateProps<P>> | ComponentClass<AnimateProps<P>>;\n\n/**\n * @deprecated Please use `Animated.FlatList` component instead of calling `Animated.createAnimatedComponent(FlatList)` manually.\n */\n// @ts-ignore This is required to create this overload, since type of createAnimatedComponent is incorrect and doesn't include typeof FlatList\nexport function createAnimatedComponent(\n  component: typeof FlatList<unknown>,\n  options?: Options<any>\n): ComponentClass<AnimateProps<FlatListProps<unknown>>>;\n\nlet id = 0;\n\nexport function createAnimatedComponent(\n  Component: ComponentType<InitialComponentProps>,\n  options?: Options<InitialComponentProps>\n): any {\n  invariant(\n    typeof Component !== 'function' ||\n      (Component.prototype && Component.prototype.isReactComponent),\n    `Looks like you're passing a function component \\`${Component.name}\\` to \\`createAnimatedComponent\\` function which supports only class components. Please wrap your function component with \\`React.forwardRef()\\` or use a class component instead.`\n  );\n\n  class AnimatedComponent\n    extends React.Component<AnimatedComponentProps<InitialComponentProps>>\n    implements IAnimatedComponentInternal\n  {\n    _styles: StyleProps[] | null = null;\n    _animatedProps?: Partial<AnimatedComponentProps<AnimatedProps>>;\n    _componentViewTag = -1;\n    _isFirstRender = true;\n    jestAnimatedStyle: { value: StyleProps } = { value: {} };\n    _component: AnimatedComponentRef | HTMLElement | null = null;\n    _sharedElementTransition: SharedTransition | null = null;\n    _jsPropsUpdater = new JSPropsUpdater();\n    _InlinePropManager = new InlinePropManager();\n    _PropsFilter = new PropsFilter();\n    _NativeEventsManager?: INativeEventsManager;\n    _viewInfo?: ViewInfo;\n    static displayName: string;\n    static contextType = SkipEnteringContext;\n    context!: React.ContextType<typeof SkipEnteringContext>;\n    reanimatedID = id++;\n\n    constructor(props: AnimatedComponentProps<InitialComponentProps>) {\n      super(props);\n      if (isJest()) {\n        this.jestAnimatedStyle = { value: {} };\n      }\n      const entering = this.props.entering;\n      if (entering && isFabric()) {\n        updateLayoutAnimations(\n          this.reanimatedID,\n          LayoutAnimationType.ENTERING,\n          maybeBuild(entering, this.props?.style, AnimatedComponent.displayName)\n        );\n      }\n    }\n\n    componentDidMount() {\n      this._componentViewTag = this._getComponentViewTag();\n      if (!IS_WEB) {\n        // It exists only on native platforms. We initialize it here because the ref to the animated component is available only post-mount\n        this._NativeEventsManager = new NativeEventsManager(this, options);\n      }\n      this._NativeEventsManager?.attachEvents();\n      this._jsPropsUpdater.addOnJSPropsChangeListener(this);\n      this._attachAnimatedStyles();\n      this._InlinePropManager.attachInlineProps(this, this._getViewInfo());\n\n      const layout = this.props.layout;\n      if (layout) {\n        this._configureLayoutTransition();\n      }\n\n      if (IS_WEB) {\n        if (this.props.exiting) {\n          saveSnapshot(this._component as HTMLElement);\n        }\n\n        if (\n          !this.props.entering ||\n          getReducedMotionFromConfig(this.props.entering as CustomConfig)\n        ) {\n          this._isFirstRender = false;\n          return;\n        }\n\n        startWebLayoutAnimation(\n          this.props,\n          this._component as ReanimatedHTMLElement,\n          LayoutAnimationType.ENTERING\n        );\n      }\n\n      this._isFirstRender = false;\n    }\n\n    componentWillUnmount() {\n      this._NativeEventsManager?.detachEvents();\n      this._jsPropsUpdater.removeOnJSPropsChangeListener(this);\n      this._detachStyles();\n      this._InlinePropManager.detachInlineProps();\n      if (this.props.sharedTransitionTag) {\n        this._configureSharedTransition(true);\n      }\n      this._sharedElementTransition?.unregisterTransition(\n        this._componentViewTag,\n        true\n      );\n\n      const exiting = this.props.exiting;\n\n      if (\n        IS_WEB &&\n        this._component &&\n        exiting &&\n        !getReducedMotionFromConfig(exiting as CustomConfig)\n      ) {\n        addHTMLMutationObserver();\n\n        startWebLayoutAnimation(\n          this.props,\n          this._component as ReanimatedHTMLElement,\n          LayoutAnimationType.EXITING\n        );\n      } else if (exiting && !IS_WEB && !isFabric()) {\n        const reduceMotionInExiting =\n          'getReduceMotion' in exiting &&\n          typeof exiting.getReduceMotion === 'function'\n            ? getReduceMotionFromConfig(exiting.getReduceMotion())\n            : getReduceMotionFromConfig();\n        if (!reduceMotionInExiting) {\n          updateLayoutAnimations(\n            this._componentViewTag,\n            LayoutAnimationType.EXITING,\n            maybeBuild(\n              exiting,\n              this.props?.style,\n              AnimatedComponent.displayName\n            )\n          );\n        }\n      }\n    }\n\n    _getComponentViewTag() {\n      return this._getViewInfo().viewTag as number;\n    }\n\n    _detachStyles() {\n      if (this._componentViewTag !== -1 && this._styles !== null) {\n        for (const style of this._styles) {\n          style.viewDescriptors.remove(this._componentViewTag);\n        }\n        if (this.props.animatedProps?.viewDescriptors) {\n          this.props.animatedProps.viewDescriptors.remove(\n            this._componentViewTag\n          );\n        }\n        if (isFabric()) {\n          removeFromPropsRegistry(this._componentViewTag);\n        }\n      }\n    }\n\n    _updateFromNative(props: StyleProps) {\n      if (options?.setNativeProps) {\n        options.setNativeProps(this._component as AnimatedComponentRef, props);\n      } else {\n        (this._component as AnimatedComponentRef)?.setNativeProps?.(props);\n      }\n    }\n\n    _getViewInfo(): ViewInfo {\n      if (this._viewInfo !== undefined) {\n        return this._viewInfo;\n      }\n\n      let viewTag: number | HTMLElement | null;\n      let viewName: string | null;\n      let shadowNodeWrapper: ShadowNodeWrapper | null = null;\n      let viewConfig;\n      // Component can specify ref which should be animated when animated version of the component is created.\n      // Otherwise, we animate the component itself.\n      const component = (this._component as AnimatedComponentRef)\n        ?.getAnimatableRef\n        ? (this._component as AnimatedComponentRef).getAnimatableRef?.()\n        : this;\n\n      if (IS_WEB) {\n        // At this point I assume that `_setComponentRef` was already called and `_component` is set.\n        // `this._component` on web represents HTMLElement of our component, that's why we use casting\n        viewTag = this._component as HTMLElement;\n        viewName = null;\n        shadowNodeWrapper = null;\n        viewConfig = null;\n      } else {\n        // hostInstance can be null for a component that doesn't render anything (render function returns null). Example: svg Stop: https://github.com/react-native-svg/react-native-svg/blob/develop/src/elements/Stop.tsx\n        const hostInstance = RNRenderer.findHostInstance_DEPRECATED(component);\n        if (!hostInstance) {\n          throw new Error(\n            '[Reanimated] Cannot find host instance for this component. Maybe it renders nothing?'\n          );\n        }\n\n        const viewInfo = getViewInfo(hostInstance);\n        viewTag = viewInfo.viewTag;\n        viewName = viewInfo.viewName;\n        viewConfig = viewInfo.viewConfig;\n        shadowNodeWrapper = isFabric()\n          ? getShadowNodeWrapperFromRef(this)\n          : null;\n      }\n      this._viewInfo = { viewTag, viewName, shadowNodeWrapper, viewConfig };\n      return this._viewInfo;\n    }\n\n    _attachAnimatedStyles() {\n      const styles = this.props.style\n        ? onlyAnimatedStyles(flattenArray<StyleProps>(this.props.style))\n        : [];\n      const prevStyles = this._styles;\n      this._styles = styles;\n\n      const prevAnimatedProps = this._animatedProps;\n      this._animatedProps = this.props.animatedProps;\n\n      const { viewTag, viewName, shadowNodeWrapper, viewConfig } =\n        this._getViewInfo();\n\n      // update UI props whitelist for this view\n      const hasReanimated2Props =\n        this.props.animatedProps?.viewDescriptors || styles.length;\n      if (hasReanimated2Props && viewConfig) {\n        adaptViewConfig(viewConfig);\n      }\n\n      this._componentViewTag = viewTag as number;\n\n      // remove old styles\n      if (prevStyles) {\n        // in most of the cases, views have only a single animated style and it remains unchanged\n        const hasOneSameStyle =\n          styles.length === 1 &&\n          prevStyles.length === 1 &&\n          styles[0] === prevStyles[0];\n\n        if (!hasOneSameStyle) {\n          // otherwise, remove each style that is not present in new styles\n          for (const prevStyle of prevStyles) {\n            const isPresent = styles.some((style) => style === prevStyle);\n            if (!isPresent) {\n              prevStyle.viewDescriptors.remove(viewTag);\n            }\n          }\n        }\n      }\n\n      styles.forEach((style) => {\n        style.viewDescriptors.add({\n          tag: viewTag,\n          name: viewName,\n          shadowNodeWrapper,\n        });\n        if (isJest()) {\n          /**\n           * We need to connect Jest's TestObject instance whose contains just props object\n           * with the updateProps() function where we update the properties of the component.\n           * We can't update props object directly because TestObject contains a copy of props - look at render function:\n           * const props = this._filterNonAnimatedProps(this.props);\n           */\n          this.jestAnimatedStyle.value = {\n            ...this.jestAnimatedStyle.value,\n            ...style.initial.value,\n          };\n          style.jestAnimatedStyle.current = this.jestAnimatedStyle;\n        }\n      });\n\n      // detach old animatedProps\n      if (prevAnimatedProps && prevAnimatedProps !== this.props.animatedProps) {\n        prevAnimatedProps.viewDescriptors!.remove(viewTag as number);\n      }\n\n      // attach animatedProps property\n      if (this.props.animatedProps?.viewDescriptors) {\n        this.props.animatedProps.viewDescriptors.add({\n          tag: viewTag as number,\n          name: viewName!,\n          shadowNodeWrapper: shadowNodeWrapper!,\n        });\n      }\n    }\n\n    componentDidUpdate(\n      prevProps: AnimatedComponentProps<InitialComponentProps>,\n      _prevState: Readonly<unknown>,\n      // This type comes straight from React\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      snapshot: DOMRect | null\n    ) {\n      const layout = this.props.layout;\n      const oldLayout = prevProps.layout;\n      if (layout !== oldLayout) {\n        this._configureLayoutTransition();\n      }\n      if (\n        this.props.sharedTransitionTag !== undefined ||\n        prevProps.sharedTransitionTag !== undefined\n      ) {\n        this._configureSharedTransition();\n      }\n      this._NativeEventsManager?.updateEvents(prevProps);\n      this._attachAnimatedStyles();\n      this._InlinePropManager.attachInlineProps(this, this._getViewInfo());\n\n      if (IS_WEB && this.props.exiting) {\n        saveSnapshot(this._component as HTMLElement);\n      }\n\n      // Snapshot won't be undefined because it comes from getSnapshotBeforeUpdate method\n      if (\n        IS_WEB &&\n        snapshot !== null &&\n        this.props.layout &&\n        !getReducedMotionFromConfig(this.props.layout as CustomConfig)\n      ) {\n        tryActivateLayoutTransition(\n          this.props,\n          this._component as ReanimatedHTMLElement,\n          snapshot\n        );\n      }\n    }\n\n    _configureLayoutTransition() {\n      if (IS_WEB) {\n        return;\n      }\n\n      const layout = this.props.layout\n        ? maybeBuild(\n            this.props.layout,\n            undefined /* We don't have to warn user if style has common properties with animation for LAYOUT */,\n            AnimatedComponent.displayName\n          )\n        : undefined;\n      updateLayoutAnimations(\n        this._componentViewTag,\n        LayoutAnimationType.LAYOUT,\n        layout\n      );\n    }\n\n    _configureSharedTransition(isUnmounting = false) {\n      if (IS_WEB) {\n        return;\n      }\n\n      const { sharedTransitionTag } = this.props;\n      if (!sharedTransitionTag) {\n        this._sharedElementTransition?.unregisterTransition(\n          this._componentViewTag,\n          isUnmounting\n        );\n        this._sharedElementTransition = null;\n        return;\n      }\n      const sharedElementTransition =\n        this.props.sharedTransitionStyle ??\n        this._sharedElementTransition ??\n        new SharedTransition();\n      sharedElementTransition.registerTransition(\n        this._componentViewTag,\n        sharedTransitionTag,\n        isUnmounting\n      );\n      this._sharedElementTransition = sharedElementTransition;\n    }\n\n    _setComponentRef = setAndForwardRef<Component | HTMLElement>({\n      getForwardedRef: () =>\n        this.props.forwardedRef as MutableRefObject<\n          Component<Record<string, unknown>, Record<string, unknown>, unknown>\n        >,\n      setLocalRef: (ref) => {\n        // TODO update config\n\n        const tag = IS_WEB\n          ? (ref as HTMLElement)\n          : findNodeHandle(ref as Component);\n\n        this._componentViewTag = tag as number;\n\n        const { layout, entering, exiting, sharedTransitionTag } = this.props;\n        if (\n          (layout || entering || exiting || sharedTransitionTag) &&\n          tag != null\n        ) {\n          if (!shouldBeUseWeb()) {\n            enableLayoutAnimations(true, false);\n          }\n\n          if (sharedTransitionTag) {\n            this._configureSharedTransition();\n          }\n          if (exiting && isFabric()) {\n            const reduceMotionInExiting =\n              'getReduceMotion' in exiting &&\n              typeof exiting.getReduceMotion === 'function'\n                ? getReduceMotionFromConfig(exiting.getReduceMotion())\n                : getReduceMotionFromConfig();\n            if (!reduceMotionInExiting) {\n              updateLayoutAnimations(\n                tag as number,\n                LayoutAnimationType.EXITING,\n                maybeBuild(\n                  exiting,\n                  this.props?.style,\n                  AnimatedComponent.displayName\n                )\n              );\n            }\n          }\n\n          const skipEntering = this.context?.current;\n          if (entering && !skipEntering && !IS_WEB) {\n            updateLayoutAnimations(\n              tag as number,\n              LayoutAnimationType.ENTERING,\n              maybeBuild(\n                entering,\n                this.props?.style,\n                AnimatedComponent.displayName\n              )\n            );\n          }\n        }\n\n        if (ref !== this._component) {\n          this._component = ref;\n        }\n      },\n    });\n\n    // This is a component lifecycle method from React, therefore we are not calling it directly.\n    // It is called before the component gets rerendered. This way we can access components' position before it changed\n    // and later on, in componentDidUpdate, calculate translation for layout transition.\n    getSnapshotBeforeUpdate() {\n      if (\n        IS_WEB &&\n        (this._component as HTMLElement)?.getBoundingClientRect !== undefined\n      ) {\n        return (this._component as HTMLElement).getBoundingClientRect();\n      }\n\n      return null;\n    }\n\n    render() {\n      const filteredProps = this._PropsFilter.filterNonAnimatedProps(this);\n\n      if (isJest()) {\n        filteredProps.jestAnimatedStyle = this.jestAnimatedStyle;\n      }\n\n      // Layout animations on web are set inside `componentDidMount` method, which is called after first render.\n      // Because of that we can encounter a situation in which component is visible for a short amount of time, and later on animation triggers.\n      // I've tested that on various browsers and devices and it did not happen to me. To be sure that it won't happen to someone else,\n      // I've decided to hide component at first render. Its visibility is reset in `componentDidMount`.\n      if (\n        this._isFirstRender &&\n        IS_WEB &&\n        filteredProps.entering &&\n        !getReducedMotionFromConfig(filteredProps.entering as CustomConfig)\n      ) {\n        filteredProps.style = {\n          ...(filteredProps.style ?? {}),\n          visibility: 'hidden', // Hide component until `componentDidMount` triggers\n        };\n      }\n\n      const platformProps = Platform.select({\n        web: {},\n        default: { collapsable: false },\n      });\n\n      const skipEntering = this.context?.current;\n      const nativeID =\n        skipEntering || !isFabric() ? undefined : `${this.reanimatedID}`;\n\n      return (\n        <Component\n          nativeID={nativeID}\n          {...filteredProps}\n          // Casting is used here, because ref can be null - in that case it cannot be assigned to HTMLElement.\n          // After spending some time trying to figure out what to do with this problem, we decided to leave it this way\n          ref={this._setComponentRef as (ref: Component) => void}\n          {...platformProps}\n        />\n      );\n    }\n  }\n\n  AnimatedComponent.displayName = `AnimatedComponent(${\n    Component.displayName || Component.name || 'Component'\n  })`;\n\n  return React.forwardRef<Component>((props, ref) => {\n    return (\n      <AnimatedComponent\n        {...props}\n        {...(ref === null ? null : { forwardedRef: ref })}\n      />\n    );\n  });\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAAA,SAAAQ,gBAAAC,GAAA,EAAAN,GAAA,EAAAO,KAAA,IAAAP,GAAA,GAAAQ,cAAA,CAAAR,GAAA,OAAAA,GAAA,IAAAM,GAAA,IAAAd,MAAA,CAAAiB,cAAA,CAAAH,GAAA,EAAAN,GAAA,IAAAO,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAN,GAAA,CAAAN,GAAA,IAAAO,KAAA,WAAAD,GAAA;AAAA,SAAAE,eAAAK,CAAA,QAAAjB,CAAA,GAAAkB,YAAA,CAAAD,CAAA,uCAAAjB,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAkB,aAAAD,CAAA,EAAAE,CAAA,2BAAAF,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAG,CAAA,GAAAH,CAAA,CAAAI,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAApB,CAAA,GAAAoB,CAAA,CAAAb,IAAA,CAAAU,CAAA,EAAAE,CAAA,uCAAAnB,CAAA,SAAAA,CAAA,YAAAuB,SAAA,yEAAAJ,CAAA,GAAAK,MAAA,GAAAC,MAAA,EAAAR,CAAA;AAQb,OAAOS,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,EAAEC,QAAQ,QAAQ,cAAc;AACvD,OAAO,wCAAwC;AAC/C,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,sBAAsB,QAAQ,SAAS;AAChD,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAE5E,SAASC,2BAA2B,QAAQ,gBAAgB;AAC5D,SAASC,uBAAuB,QAAQ,kBAAkB;AAC1D,SAASC,yBAAyB,QAAQ,mBAAmB;AAC7D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,OAAOC,cAAc,MAAM,kBAAkB;AAU7C,SAASC,YAAY,QAAQ,SAAS;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,cAAc,QAAQ,oBAAoB;AAC5E,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,YAAY,QACP,0BAA0B;AACjC,SAASC,sBAAsB,QAAQ,2BAA2B;AAGlE,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,QAAQ,uBAAuB;AAG3D,MAAMC,MAAM,GAAGb,KAAK,CAAC,CAAC;AAEtB,IAAIa,MAAM,EAAE;EACVP,4BAA4B,CAAC,CAAC;AAChC;AAEA,SAASQ,kBAAkBA,CAACC,MAAoB,EAAgB;EAC9D,OAAOA,MAAM,CAACC,MAAM,CAAEC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,eAAe,CAAC;AACzD;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAkBA;AACA;AACA;AACA;;AAMA,IAAIC,EAAE,GAAG,CAAC;AAEV,OAAO,SAASC,uBAAuBA,CACrCC,SAA+C,EAC/CC,OAAwC,EACnC;EACLtC,SAAS,CACP,OAAOqC,SAAS,KAAK,UAAU,IAC5BA,SAAS,CAAC7D,SAAS,IAAI6D,SAAS,CAAC7D,SAAS,CAAC+D,gBAAiB,EAC9D,oDAAmDF,SAAS,CAACG,IAAK,oLACrE,CAAC;EAED,MAAMC,iBAAiB,SACb5C,KAAK,CAACwC,SAAS,CAEzB;IAkBEK,WAAWA,CAACC,KAAoD,EAAE;MAChE,KAAK,CAACA,KAAK,CAAC;MAAC/D,eAAA,kBAlBgB,IAAI;MAAAA,eAAA;MAAAA,eAAA,4BAEf,CAAC,CAAC;MAAAA,eAAA,yBACL,IAAI;MAAAA,eAAA,4BACsB;QAAEE,KAAK,EAAE,CAAC;MAAE,CAAC;MAAAF,eAAA,qBACA,IAAI;MAAAA,eAAA,mCACR,IAAI;MAAAA,eAAA,0BACtC,IAAI+B,cAAc,CAAC,CAAC;MAAA/B,eAAA,6BACjB,IAAIsC,iBAAiB,CAAC,CAAC;MAAAtC,eAAA,uBAC7B,IAAIuC,WAAW,CAAC,CAAC;MAAAvC,eAAA;MAAAA,eAAA;MAAAA,eAAA;MAAAA,eAAA,uBAMjBuD,EAAE,EAAE;MAAAvD,eAAA,2BAmVAiC,gBAAgB,CAA0B;QAC3D+B,eAAe,EAAEA,CAAA,KACf,IAAI,CAACD,KAAK,CAACE,YAEV;QACHC,WAAW,EAAGC,GAAG,IAAK;UACpB;;UAEA,MAAMC,GAAG,GAAGnB,MAAM,GACbkB,GAAG,GACJjD,cAAc,CAACiD,GAAgB,CAAC;UAEpC,IAAI,CAACE,iBAAiB,GAAGD,GAAa;UAEtC,MAAM;YAAEE,MAAM;YAAEC,QAAQ;YAAEC,OAAO;YAAEC;UAAoB,CAAC,GAAG,IAAI,CAACV,KAAK;UACrE,IACE,CAACO,MAAM,IAAIC,QAAQ,IAAIC,OAAO,IAAIC,mBAAmB,KACrDL,GAAG,IAAI,IAAI,EACX;YAAA,IAAAM,aAAA;YACA,IAAI,CAACrC,cAAc,CAAC,CAAC,EAAE;cACrBd,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC;YACrC;YAEA,IAAIkD,mBAAmB,EAAE;cACvB,IAAI,CAACE,0BAA0B,CAAC,CAAC;YACnC;YACA,IAAIH,OAAO,IAAItC,QAAQ,CAAC,CAAC,EAAE;cACzB,MAAM0C,qBAAqB,GACzB,iBAAiB,IAAIJ,OAAO,IAC5B,OAAOA,OAAO,CAACK,eAAe,KAAK,UAAU,GACzCjD,yBAAyB,CAAC4C,OAAO,CAACK,eAAe,CAAC,CAAC,CAAC,GACpDjD,yBAAyB,CAAC,CAAC;cACjC,IAAI,CAACgD,qBAAqB,EAAE;gBAAA,IAAAE,WAAA;gBAC1BjC,sBAAsB,CACpBuB,GAAG,EACH3C,mBAAmB,CAACsD,OAAO,EAC3BlD,UAAU,CACR2C,OAAO,GAAAM,WAAA,GACP,IAAI,CAACf,KAAK,cAAAe,WAAA,uBAAVA,WAAA,CAAYzB,KAAK,EACjBQ,iBAAiB,CAACmB,WACpB,CACF,CAAC;cACH;YACF;YAEA,MAAMC,YAAY,IAAAP,aAAA,GAAG,IAAI,CAACQ,OAAO,cAAAR,aAAA,uBAAZA,aAAA,CAAcS,OAAO;YAC1C,IAAIZ,QAAQ,IAAI,CAACU,YAAY,IAAI,CAAChC,MAAM,EAAE;cAAA,IAAAmC,YAAA;cACxCvC,sBAAsB,CACpBuB,GAAG,EACH3C,mBAAmB,CAAC4D,QAAQ,EAC5BxD,UAAU,CACR0C,QAAQ,GAAAa,YAAA,GACR,IAAI,CAACrB,KAAK,cAAAqB,YAAA,uBAAVA,YAAA,CAAY/B,KAAK,EACjBQ,iBAAiB,CAACmB,WACpB,CACF,CAAC;YACH;UACF;UAEA,IAAIb,GAAG,KAAK,IAAI,CAACmB,UAAU,EAAE;YAC3B,IAAI,CAACA,UAAU,GAAGnB,GAAG;UACvB;QACF;MACF,CAAC,CAAC;MA9YA,IAAIhC,MAAM,CAAC,CAAC,EAAE;QACZ,IAAI,CAACoD,iBAAiB,GAAG;UAAErF,KAAK,EAAE,CAAC;QAAE,CAAC;MACxC;MACA,MAAMqE,SAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;MACpC,IAAIA,SAAQ,IAAIrC,QAAQ,CAAC,CAAC,EAAE;QAAA,IAAAsD,YAAA;QAC1B3C,sBAAsB,CACpB,IAAI,CAAC4C,YAAY,EACjBhE,mBAAmB,CAAC4D,QAAQ,EAC5BxD,UAAU,CAAC0C,SAAQ,GAAAiB,YAAA,GAAE,IAAI,CAACzB,KAAK,cAAAyB,YAAA,uBAAVA,YAAA,CAAYnC,KAAK,EAAEQ,iBAAiB,CAACmB,WAAW,CACvE,CAAC;MACH;IACF;IAEAU,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MAClB,IAAI,CAACtB,iBAAiB,GAAG,IAAI,CAACuB,oBAAoB,CAAC,CAAC;MACpD,IAAI,CAAC3C,MAAM,EAAE;QACX;QACA,IAAI,CAAC4C,oBAAoB,GAAG,IAAI7C,mBAAmB,CAAC,IAAI,EAAEU,OAAO,CAAC;MACpE;MACA,CAAAiC,qBAAA,OAAI,CAACE,oBAAoB,cAAAF,qBAAA,eAAzBA,qBAAA,CAA2BG,YAAY,CAAC,CAAC;MACzC,IAAI,CAACC,eAAe,CAACC,0BAA0B,CAAC,IAAI,CAAC;MACrD,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,kBAAkB,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;MAEpE,MAAM9B,MAAM,GAAG,IAAI,CAACP,KAAK,CAACO,MAAM;MAChC,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC+B,0BAA0B,CAAC,CAAC;MACnC;MAEA,IAAIpD,MAAM,EAAE;QACV,IAAI,IAAI,CAACc,KAAK,CAACS,OAAO,EAAE;UACtB5B,YAAY,CAAC,IAAI,CAAC0C,UAAyB,CAAC;QAC9C;QAEA,IACE,CAAC,IAAI,CAACvB,KAAK,CAACQ,QAAQ,IACpB5B,0BAA0B,CAAC,IAAI,CAACoB,KAAK,CAACQ,QAAwB,CAAC,EAC/D;UACA,IAAI,CAAC+B,cAAc,GAAG,KAAK;UAC3B;QACF;QAEA9D,uBAAuB,CACrB,IAAI,CAACuB,KAAK,EACV,IAAI,CAACuB,UAAU,EACf7D,mBAAmB,CAAC4D,QACtB,CAAC;MACH;MAEA,IAAI,CAACiB,cAAc,GAAG,KAAK;IAC7B;IAEAC,oBAAoBA,CAAA,EAAG;MAAA,IAAAC,sBAAA,EAAAC,qBAAA;MACrB,CAAAD,sBAAA,OAAI,CAACX,oBAAoB,cAAAW,sBAAA,eAAzBA,sBAAA,CAA2BE,YAAY,CAAC,CAAC;MACzC,IAAI,CAACX,eAAe,CAACY,6BAA6B,CAAC,IAAI,CAAC;MACxD,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACV,kBAAkB,CAACW,iBAAiB,CAAC,CAAC;MAC3C,IAAI,IAAI,CAAC9C,KAAK,CAACU,mBAAmB,EAAE;QAClC,IAAI,CAACE,0BAA0B,CAAC,IAAI,CAAC;MACvC;MACA,CAAA8B,qBAAA,OAAI,CAACK,wBAAwB,cAAAL,qBAAA,eAA7BA,qBAAA,CAA+BM,oBAAoB,CACjD,IAAI,CAAC1C,iBAAiB,EACtB,IACF,CAAC;MAED,MAAMG,OAAO,GAAG,IAAI,CAACT,KAAK,CAACS,OAAO;MAElC,IACEvB,MAAM,IACN,IAAI,CAACqC,UAAU,IACfd,OAAO,IACP,CAAC7B,0BAA0B,CAAC6B,OAAuB,CAAC,EACpD;QACA1B,uBAAuB,CAAC,CAAC;QAEzBN,uBAAuB,CACrB,IAAI,CAACuB,KAAK,EACV,IAAI,CAACuB,UAAU,EACf7D,mBAAmB,CAACsD,OACtB,CAAC;MACH,CAAC,MAAM,IAAIP,OAAO,IAAI,CAACvB,MAAM,IAAI,CAACf,QAAQ,CAAC,CAAC,EAAE;QAC5C,MAAM0C,qBAAqB,GACzB,iBAAiB,IAAIJ,OAAO,IAC5B,OAAOA,OAAO,CAACK,eAAe,KAAK,UAAU,GACzCjD,yBAAyB,CAAC4C,OAAO,CAACK,eAAe,CAAC,CAAC,CAAC,GACpDjD,yBAAyB,CAAC,CAAC;QACjC,IAAI,CAACgD,qBAAqB,EAAE;UAAA,IAAAoC,YAAA;UAC1BnE,sBAAsB,CACpB,IAAI,CAACwB,iBAAiB,EACtB5C,mBAAmB,CAACsD,OAAO,EAC3BlD,UAAU,CACR2C,OAAO,GAAAwC,YAAA,GACP,IAAI,CAACjD,KAAK,cAAAiD,YAAA,uBAAVA,YAAA,CAAY3D,KAAK,EACjBQ,iBAAiB,CAACmB,WACpB,CACF,CAAC;QACH;MACF;IACF;IAEAY,oBAAoBA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACQ,YAAY,CAAC,CAAC,CAACa,OAAO;IACpC;IAEAL,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAACvC,iBAAiB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC6C,OAAO,KAAK,IAAI,EAAE;QAAA,IAAAC,qBAAA;QAC1D,KAAK,MAAM9D,KAAK,IAAI,IAAI,CAAC6D,OAAO,EAAE;UAChC7D,KAAK,CAACC,eAAe,CAAC8D,MAAM,CAAC,IAAI,CAAC/C,iBAAiB,CAAC;QACtD;QACA,KAAA8C,qBAAA,GAAI,IAAI,CAACpD,KAAK,CAACsD,aAAa,cAAAF,qBAAA,eAAxBA,qBAAA,CAA0B7D,eAAe,EAAE;UAC7C,IAAI,CAACS,KAAK,CAACsD,aAAa,CAAC/D,eAAe,CAAC8D,MAAM,CAC7C,IAAI,CAAC/C,iBACP,CAAC;QACH;QACA,IAAInC,QAAQ,CAAC,CAAC,EAAE;UACdP,uBAAuB,CAAC,IAAI,CAAC0C,iBAAiB,CAAC;QACjD;MACF;IACF;IAEAiD,iBAAiBA,CAACvD,KAAiB,EAAE;MACnC,IAAIL,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE6D,cAAc,EAAE;QAC3B7D,OAAO,CAAC6D,cAAc,CAAC,IAAI,CAACjC,UAAU,EAA0BvB,KAAK,CAAC;MACxE,CAAC,MAAM;QAAA,IAAAyD,gBAAA,EAAAC,qBAAA;QACL,CAAAD,gBAAA,GAAC,IAAI,CAAClC,UAAU,cAAAkC,gBAAA,gBAAAC,qBAAA,GAAhBD,gBAAA,CAA2CD,cAAc,cAAAE,qBAAA,eAAzDA,qBAAA,CAAA3H,IAAA,CAAA0H,gBAAA,EAA4DzD,KAAK,CAAC;MACpE;IACF;IAEAqC,YAAYA,CAAA,EAAa;MAAA,IAAAsB,iBAAA,EAAAC,iBAAA,EAAAC,IAAA;MACvB,IAAI,IAAI,CAACC,SAAS,KAAKC,SAAS,EAAE;QAChC,OAAO,IAAI,CAACD,SAAS;MACvB;MAEA,IAAIZ,OAAoC;MACxC,IAAIc,QAAuB;MAC3B,IAAIC,iBAA2C,GAAG,IAAI;MACtD,IAAIC,UAAU;MACd;MACA;MACA,MAAMC,SAAS,GAAG,CAAAR,iBAAA,GAAC,IAAI,CAACpC,UAAU,cAAAoC,iBAAA,eAAhBA,iBAAA,CACdS,gBAAgB,IAAAR,iBAAA,GAChB,CAAAC,IAAA,GAAC,IAAI,CAACtC,UAAU,EAA0B6C,gBAAgB,cAAAR,iBAAA,uBAA1DA,iBAAA,CAAA7H,IAAA,CAAA8H,IAA6D,CAAC,GAC9D,IAAI;MAER,IAAI3E,MAAM,EAAE;QACV;QACA;QACAgE,OAAO,GAAG,IAAI,CAAC3B,UAAyB;QACxCyC,QAAQ,GAAG,IAAI;QACfC,iBAAiB,GAAG,IAAI;QACxBC,UAAU,GAAG,IAAI;MACnB,CAAC,MAAM;QACL;QACA,MAAMG,YAAY,GAAG9G,UAAU,CAAC+G,2BAA2B,CAACH,SAAS,CAAC;QACtE,IAAI,CAACE,YAAY,EAAE;UACjB,MAAM,IAAIE,KAAK,CACb,sFACF,CAAC;QACH;QAEA,MAAMC,QAAQ,GAAGxF,WAAW,CAACqF,YAAY,CAAC;QAC1CnB,OAAO,GAAGsB,QAAQ,CAACtB,OAAO;QAC1Bc,QAAQ,GAAGQ,QAAQ,CAACR,QAAQ;QAC5BE,UAAU,GAAGM,QAAQ,CAACN,UAAU;QAChCD,iBAAiB,GAAG9F,QAAQ,CAAC,CAAC,GAC1BR,2BAA2B,CAAC,IAAI,CAAC,GACjC,IAAI;MACV;MACA,IAAI,CAACmG,SAAS,GAAG;QAAEZ,OAAO;QAAEc,QAAQ;QAAEC,iBAAiB;QAAEC;MAAW,CAAC;MACrE,OAAO,IAAI,CAACJ,SAAS;IACvB;IAEA5B,qBAAqBA,CAAA,EAAG;MAAA,IAAAuC,sBAAA,EAAAC,sBAAA;MACtB,MAAMtF,MAAM,GAAG,IAAI,CAACY,KAAK,CAACV,KAAK,GAC3BH,kBAAkB,CAAClB,YAAY,CAAa,IAAI,CAAC+B,KAAK,CAACV,KAAK,CAAC,CAAC,GAC9D,EAAE;MACN,MAAMqF,UAAU,GAAG,IAAI,CAACxB,OAAO;MAC/B,IAAI,CAACA,OAAO,GAAG/D,MAAM;MAErB,MAAMwF,iBAAiB,GAAG,IAAI,CAACC,cAAc;MAC7C,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC7E,KAAK,CAACsD,aAAa;MAE9C,MAAM;QAAEJ,OAAO;QAAEc,QAAQ;QAAEC,iBAAiB;QAAEC;MAAW,CAAC,GACxD,IAAI,CAAC7B,YAAY,CAAC,CAAC;;MAErB;MACA,MAAMyC,mBAAmB,GACvB,EAAAL,sBAAA,OAAI,CAACzE,KAAK,CAACsD,aAAa,cAAAmB,sBAAA,uBAAxBA,sBAAA,CAA0BlF,eAAe,KAAIH,MAAM,CAAC1D,MAAM;MAC5D,IAAIoJ,mBAAmB,IAAIZ,UAAU,EAAE;QACrC5G,eAAe,CAAC4G,UAAU,CAAC;MAC7B;MAEA,IAAI,CAAC5D,iBAAiB,GAAG4C,OAAiB;;MAE1C;MACA,IAAIyB,UAAU,EAAE;QACd;QACA,MAAMI,eAAe,GACnB3F,MAAM,CAAC1D,MAAM,KAAK,CAAC,IACnBiJ,UAAU,CAACjJ,MAAM,KAAK,CAAC,IACvB0D,MAAM,CAAC,CAAC,CAAC,KAAKuF,UAAU,CAAC,CAAC,CAAC;QAE7B,IAAI,CAACI,eAAe,EAAE;UACpB;UACA,KAAK,MAAMC,SAAS,IAAIL,UAAU,EAAE;YAClC,MAAMM,SAAS,GAAG7F,MAAM,CAAC8F,IAAI,CAAE5F,KAAK,IAAKA,KAAK,KAAK0F,SAAS,CAAC;YAC7D,IAAI,CAACC,SAAS,EAAE;cACdD,SAAS,CAACzF,eAAe,CAAC8D,MAAM,CAACH,OAAO,CAAC;YAC3C;UACF;QACF;MACF;MAEA9D,MAAM,CAAC+F,OAAO,CAAE7F,KAAK,IAAK;QACxBA,KAAK,CAACC,eAAe,CAAC6F,GAAG,CAAC;UACxB/E,GAAG,EAAE6C,OAAO;UACZrD,IAAI,EAAEmE,QAAQ;UACdC;QACF,CAAC,CAAC;QACF,IAAI7F,MAAM,CAAC,CAAC,EAAE;UACZ;AACV;AACA;AACA;AACA;AACA;UACU,IAAI,CAACoD,iBAAiB,CAACrF,KAAK,GAAG;YAC7B,GAAG,IAAI,CAACqF,iBAAiB,CAACrF,KAAK;YAC/B,GAAGmD,KAAK,CAAC+F,OAAO,CAAClJ;UACnB,CAAC;UACDmD,KAAK,CAACkC,iBAAiB,CAACJ,OAAO,GAAG,IAAI,CAACI,iBAAiB;QAC1D;MACF,CAAC,CAAC;;MAEF;MACA,IAAIoD,iBAAiB,IAAIA,iBAAiB,KAAK,IAAI,CAAC5E,KAAK,CAACsD,aAAa,EAAE;QACvEsB,iBAAiB,CAACrF,eAAe,CAAE8D,MAAM,CAACH,OAAiB,CAAC;MAC9D;;MAEA;MACA,KAAAwB,sBAAA,GAAI,IAAI,CAAC1E,KAAK,CAACsD,aAAa,cAAAoB,sBAAA,eAAxBA,sBAAA,CAA0BnF,eAAe,EAAE;QAC7C,IAAI,CAACS,KAAK,CAACsD,aAAa,CAAC/D,eAAe,CAAC6F,GAAG,CAAC;UAC3C/E,GAAG,EAAE6C,OAAiB;UACtBrD,IAAI,EAAEmE,QAAS;UACfC,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;IACF;IAEAqB,kBAAkBA,CAChBC,SAAwD,EACxDC,UAA6B;IAC7B;IACA;IACAC,QAAwB,EACxB;MAAA,IAAAC,sBAAA;MACA,MAAMnF,MAAM,GAAG,IAAI,CAACP,KAAK,CAACO,MAAM;MAChC,MAAMoF,SAAS,GAAGJ,SAAS,CAAChF,MAAM;MAClC,IAAIA,MAAM,KAAKoF,SAAS,EAAE;QACxB,IAAI,CAACrD,0BAA0B,CAAC,CAAC;MACnC;MACA,IACE,IAAI,CAACtC,KAAK,CAACU,mBAAmB,KAAKqD,SAAS,IAC5CwB,SAAS,CAAC7E,mBAAmB,KAAKqD,SAAS,EAC3C;QACA,IAAI,CAACnD,0BAA0B,CAAC,CAAC;MACnC;MACA,CAAA8E,sBAAA,OAAI,CAAC5D,oBAAoB,cAAA4D,sBAAA,eAAzBA,sBAAA,CAA2BE,YAAY,CAACL,SAAS,CAAC;MAClD,IAAI,CAACrD,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACC,kBAAkB,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;MAEpE,IAAInD,MAAM,IAAI,IAAI,CAACc,KAAK,CAACS,OAAO,EAAE;QAChC5B,YAAY,CAAC,IAAI,CAAC0C,UAAyB,CAAC;MAC9C;;MAEA;MACA,IACErC,MAAM,IACNuG,QAAQ,KAAK,IAAI,IACjB,IAAI,CAACzF,KAAK,CAACO,MAAM,IACjB,CAAC3B,0BAA0B,CAAC,IAAI,CAACoB,KAAK,CAACO,MAAsB,CAAC,EAC9D;QACA7B,2BAA2B,CACzB,IAAI,CAACsB,KAAK,EACV,IAAI,CAACuB,UAAU,EACfkE,QACF,CAAC;MACH;IACF;IAEAnD,0BAA0BA,CAAA,EAAG;MAC3B,IAAIpD,MAAM,EAAE;QACV;MACF;MAEA,MAAMqB,MAAM,GAAG,IAAI,CAACP,KAAK,CAACO,MAAM,GAC5BzC,UAAU,CACR,IAAI,CAACkC,KAAK,CAACO,MAAM,EACjBwD,SAAS,CAAC,2FACVjE,iBAAiB,CAACmB,WACpB,CAAC,GACD8C,SAAS;MACbjF,sBAAsB,CACpB,IAAI,CAACwB,iBAAiB,EACtB5C,mBAAmB,CAACmI,MAAM,EAC1BtF,MACF,CAAC;IACH;IAEAK,0BAA0BA,CAACkF,YAAY,GAAG,KAAK,EAAE;MAC/C,IAAI5G,MAAM,EAAE;QACV;MACF;MAEA,MAAM;QAAEwB;MAAoB,CAAC,GAAG,IAAI,CAACV,KAAK;MAC1C,IAAI,CAACU,mBAAmB,EAAE;QAAA,IAAAqF,sBAAA;QACxB,CAAAA,sBAAA,OAAI,CAAChD,wBAAwB,cAAAgD,sBAAA,eAA7BA,sBAAA,CAA+B/C,oBAAoB,CACjD,IAAI,CAAC1C,iBAAiB,EACtBwF,YACF,CAAC;QACD,IAAI,CAAC/C,wBAAwB,GAAG,IAAI;QACpC;MACF;MACA,MAAMiD,uBAAuB,GAC3B,IAAI,CAAChG,KAAK,CAACiG,qBAAqB,IAChC,IAAI,CAAClD,wBAAwB,IAC7B,IAAItF,gBAAgB,CAAC,CAAC;MACxBuI,uBAAuB,CAACE,kBAAkB,CACxC,IAAI,CAAC5F,iBAAiB,EACtBI,mBAAmB,EACnBoF,YACF,CAAC;MACD,IAAI,CAAC/C,wBAAwB,GAAGiD,uBAAuB;IACzD;IAmEA;IACA;IACA;IACAG,uBAAuBA,CAAA,EAAG;MAAA,IAAAC,iBAAA;MACxB,IACElH,MAAM,IACN,EAAAkH,iBAAA,GAAC,IAAI,CAAC7E,UAAU,cAAA6E,iBAAA,uBAAhBA,iBAAA,CAAkCC,qBAAqB,MAAKtC,SAAS,EACrE;QACA,OAAQ,IAAI,CAACxC,UAAU,CAAiB8E,qBAAqB,CAAC,CAAC;MACjE;MAEA,OAAO,IAAI;IACb;IAEAC,MAAMA,CAAA,EAAG;MAAA,IAAAC,cAAA;MACP,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAACC,sBAAsB,CAAC,IAAI,CAAC;MAEpE,IAAItI,MAAM,CAAC,CAAC,EAAE;QACZoI,aAAa,CAAChF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC1D;;MAEA;MACA;MACA;MACA;MACA,IACE,IAAI,CAACe,cAAc,IACnBrD,MAAM,IACNsH,aAAa,CAAChG,QAAQ,IACtB,CAAC5B,0BAA0B,CAAC4H,aAAa,CAAChG,QAAwB,CAAC,EACnE;QACAgG,aAAa,CAAClH,KAAK,GAAG;UACpB,IAAIkH,aAAa,CAAClH,KAAK,IAAI,CAAC,CAAC,CAAC;UAC9BqH,UAAU,EAAE,QAAQ,CAAE;QACxB,CAAC;MACH;MAEA,MAAMC,aAAa,GAAGxJ,QAAQ,CAACyJ,MAAM,CAAC;QACpCC,GAAG,EAAE,CAAC,CAAC;QACPC,OAAO,EAAE;UAAEC,WAAW,EAAE;QAAM;MAChC,CAAC,CAAC;MAEF,MAAM9F,YAAY,IAAAqF,cAAA,GAAG,IAAI,CAACpF,OAAO,cAAAoF,cAAA,uBAAZA,cAAA,CAAcnF,OAAO;MAC1C,MAAM6F,QAAQ,GACZ/F,YAAY,IAAI,CAAC/C,QAAQ,CAAC,CAAC,GAAG4F,SAAS,GAAI,GAAE,IAAI,CAACrC,YAAa,EAAC;MAElE,oBACExE,KAAA,CAAAgK,aAAA,CAACxH,SAAS,EAAAvE,QAAA;QACR8L,QAAQ,EAAEA;MAAS,GACfT,aAAa;QACjB;QACA;QACApG,GAAG,EAAE,IAAI,CAAC+G;MAA6C,GACnDP,aAAa,CAClB,CAAC;IAEN;EACF;EAAC3K,eAAA,CAheK6D,iBAAiB;EAAA7D,eAAA,CAAjB6D,iBAAiB,iBAiBA/B,mBAAmB;EAid1C+B,iBAAiB,CAACmB,WAAW,GAAI,qBAC/BvB,SAAS,CAACuB,WAAW,IAAIvB,SAAS,CAACG,IAAI,IAAI,WAC5C,GAAE;EAEH,oBAAO3C,KAAK,CAACkK,UAAU,CAAY,CAACpH,KAAK,EAAEI,GAAG,KAAK;IACjD,oBACElD,KAAA,CAAAgK,aAAA,CAACpH,iBAAiB,EAAA3E,QAAA,KACZ6E,KAAK,EACJI,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG;MAAEF,YAAY,EAAEE;IAAI,CAAC,CACjD,CAAC;EAEN,CAAC,CAAC;AACJ", "ignoreList": []}