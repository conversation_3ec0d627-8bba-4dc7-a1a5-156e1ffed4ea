{"version": 3, "names": ["useCallback", "useWorkletCallback", "worklet", "deps"], "sources": ["useWorkletCallback.ts"], "sourcesContent": ["'use strict';\nimport { useCallback } from 'react';\nimport type { DependencyList } from './commonTypes';\n\n/**\n * @deprecated use React.useCallback instead\n */\nexport function useWorkletCallback<Args extends unknown[], ReturnValue>(\n  worklet: (...args: Args) => ReturnValue,\n  deps?: DependencyList\n) {\n  return useCallback(worklet, deps ?? []);\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,WAAW,QAAQ,OAAO;AAGnC;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAChCC,OAAuC,EACvCC,IAAqB,EACrB;EACA,OAAOH,WAAW,CAACE,OAAO,EAAEC,IAAI,IAAI,EAAE,CAAC;AACzC", "ignoreList": []}