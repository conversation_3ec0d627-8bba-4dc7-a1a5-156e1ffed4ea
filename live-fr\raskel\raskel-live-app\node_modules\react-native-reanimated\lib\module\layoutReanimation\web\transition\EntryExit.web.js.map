{"version": 3, "names": ["AnimationsData", "ExitingFinalStep", "EnteringStartStep", "addTransformToKeepPosition", "keyframeStyleData", "animationStyle", "transformData", "isExiting", "timestamp", "styles", "Object", "entries", "transform", "undefined", "unshift", "newTimestamp", "parseInt", "index", "Math", "min", "hideComponentBetweenAnimations", "opacityInStep", "Map", "opacity", "set", "step", "EntryExitTransition", "name", "transitionData", "exitingAnimationData", "structuredClone", "exiting", "enteringAnimationData", "entering", "additionalExitingData", "translateX", "translateY", "scale", "scaleX", "scaleY", "additionalEnteringData", "keyframeData", "style", "duration"], "sources": ["EntryExit.web.ts"], "sourcesContent": ["'use strict';\nimport type {\n  AnimationData,\n  AnimationStyle,\n  TransitionData,\n} from '../animationParser';\nimport { AnimationsData } from '../config';\n\nconst ExitingFinalStep = 49;\nconst EnteringStartStep = 50;\n\ntype TransformData = {\n  translateX: string;\n  translateY: string;\n  scale: string;\n};\n\n// Layout transitions on web work in \"reverse order\". It means that the element is rendered at its destination and then, at the beginning of the animation,\n// we move it back to its starting point.\n// This function is responsible for adding transition data into beginning of each keyframe step.\n// Doing so will ensure that the element will perform animation from correct position.\nfunction addTransformToKeepPosition(\n  keyframeStyleData: Record<number, AnimationStyle>,\n  animationStyle: Record<number, AnimationStyle>,\n  transformData: TransformData,\n  isExiting: boolean\n) {\n  for (const [timestamp, styles] of Object.entries(animationStyle)) {\n    if (styles.transform !== undefined) {\n      // If transform was defined, we want to put transform from transition at the beginning, hence we use `unshift`\n      styles.transform.unshift(transformData);\n    } else {\n      // If transform was undefined, we simply add transform from transition\n      styles.transform = [transformData];\n    }\n\n    const newTimestamp = parseInt(timestamp) / 2;\n    const index = isExiting\n      ? Math.min(newTimestamp, ExitingFinalStep) // We want to squeeze exiting animation from range 0-100 into range 0-49\n      : newTimestamp + EnteringStartStep; // Entering animation will start from 50 and go up to 100\n\n    keyframeStyleData[`${index}`] = styles;\n  }\n}\n\n// EntryExit transition consists of two animations - exiting and entering.\n// In Keyframes one cannot simply specify animation for given frame. Switching from one animation\n// to the other one between steps 49 and 50 may lead to flickers, since browser tries to interpolate\n// one step into the other. To avoid that, we set components' `opacity` to 0 right before switching animation\n// and set it again to 1 when component is in right position. Hiding component between animations\n// prevents flickers.\nfunction hideComponentBetweenAnimations(\n  keyframeStyleData: Record<number, AnimationStyle>\n) {\n  // We have to take into account that some animations have already defined `opacity`.\n  // In that case, we don't want to override it.\n  const opacityInStep = new Map<number, number>();\n\n  if (keyframeStyleData[0].opacity === undefined) {\n    opacityInStep.set(48, 1);\n    opacityInStep.set(49, 0);\n  }\n\n  if (keyframeStyleData[50].opacity === undefined) {\n    opacityInStep.set(50, 0);\n    opacityInStep.set(51, 1);\n  }\n\n  for (const [step, opacity] of opacityInStep) {\n    keyframeStyleData[step] = {\n      ...keyframeStyleData[step],\n      opacity,\n    };\n  }\n}\n\nexport function EntryExitTransition(\n  name: string,\n  transitionData: TransitionData\n) {\n  const exitingAnimationData = structuredClone(\n    AnimationsData[transitionData.exiting]\n  );\n  const enteringAnimationData = structuredClone(\n    AnimationsData[transitionData.entering]\n  );\n\n  const additionalExitingData: TransformData = {\n    translateX: `${transitionData.translateX}px`,\n    translateY: `${transitionData.translateY}px`,\n    scale: `${transitionData.scaleX},${transitionData.scaleY}`,\n  };\n\n  const additionalEnteringData: TransformData = {\n    translateX: `0px`,\n    translateY: `0px`,\n    scale: `1,1`,\n  };\n\n  const keyframeData: AnimationData = {\n    name,\n    style: {},\n    duration: 300,\n  };\n\n  addTransformToKeepPosition(\n    keyframeData.style,\n    exitingAnimationData.style,\n    additionalExitingData,\n    true\n  );\n\n  addTransformToKeepPosition(\n    keyframeData.style,\n    enteringAnimationData.style,\n    additionalEnteringData,\n    false\n  );\n\n  hideComponentBetweenAnimations(keyframeData.style);\n\n  return keyframeData;\n}\n"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,cAAc,QAAQ,WAAW;AAE1C,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,iBAAiB,GAAG,EAAE;AAQ5B;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CACjCC,iBAAiD,EACjDC,cAA8C,EAC9CC,aAA4B,EAC5BC,SAAkB,EAClB;EACA,KAAK,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,cAAc,CAAC,EAAE;IAChE,IAAII,MAAM,CAACG,SAAS,KAAKC,SAAS,EAAE;MAClC;MACAJ,MAAM,CAACG,SAAS,CAACE,OAAO,CAACR,aAAa,CAAC;IACzC,CAAC,MAAM;MACL;MACAG,MAAM,CAACG,SAAS,GAAG,CAACN,aAAa,CAAC;IACpC;IAEA,MAAMS,YAAY,GAAGC,QAAQ,CAACR,SAAS,CAAC,GAAG,CAAC;IAC5C,MAAMS,KAAK,GAAGV,SAAS,GACnBW,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEd,gBAAgB,CAAC,CAAC;IAAA,EACzCc,YAAY,GAAGb,iBAAiB,CAAC,CAAC;;IAEtCE,iBAAiB,CAAE,GAAEa,KAAM,EAAC,CAAC,GAAGR,MAAM;EACxC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,8BAA8BA,CACrChB,iBAAiD,EACjD;EACA;EACA;EACA,MAAMiB,aAAa,GAAG,IAAIC,GAAG,CAAiB,CAAC;EAE/C,IAAIlB,iBAAiB,CAAC,CAAC,CAAC,CAACmB,OAAO,KAAKV,SAAS,EAAE;IAC9CQ,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxBH,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1B;EAEA,IAAIpB,iBAAiB,CAAC,EAAE,CAAC,CAACmB,OAAO,KAAKV,SAAS,EAAE;IAC/CQ,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxBH,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1B;EAEA,KAAK,MAAM,CAACC,IAAI,EAAEF,OAAO,CAAC,IAAIF,aAAa,EAAE;IAC3CjB,iBAAiB,CAACqB,IAAI,CAAC,GAAG;MACxB,GAAGrB,iBAAiB,CAACqB,IAAI,CAAC;MAC1BF;IACF,CAAC;EACH;AACF;AAEA,OAAO,SAASG,mBAAmBA,CACjCC,IAAY,EACZC,cAA8B,EAC9B;EACA,MAAMC,oBAAoB,GAAGC,eAAe,CAC1C9B,cAAc,CAAC4B,cAAc,CAACG,OAAO,CACvC,CAAC;EACD,MAAMC,qBAAqB,GAAGF,eAAe,CAC3C9B,cAAc,CAAC4B,cAAc,CAACK,QAAQ,CACxC,CAAC;EAED,MAAMC,qBAAoC,GAAG;IAC3CC,UAAU,EAAG,GAAEP,cAAc,CAACO,UAAW,IAAG;IAC5CC,UAAU,EAAG,GAAER,cAAc,CAACQ,UAAW,IAAG;IAC5CC,KAAK,EAAG,GAAET,cAAc,CAACU,MAAO,IAAGV,cAAc,CAACW,MAAO;EAC3D,CAAC;EAED,MAAMC,sBAAqC,GAAG;IAC5CL,UAAU,EAAG,KAAI;IACjBC,UAAU,EAAG,KAAI;IACjBC,KAAK,EAAG;EACV,CAAC;EAED,MAAMI,YAA2B,GAAG;IAClCd,IAAI;IACJe,KAAK,EAAE,CAAC,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EAEDxC,0BAA0B,CACxBsC,YAAY,CAACC,KAAK,EAClBb,oBAAoB,CAACa,KAAK,EAC1BR,qBAAqB,EACrB,IACF,CAAC;EAED/B,0BAA0B,CACxBsC,YAAY,CAACC,KAAK,EAClBV,qBAAqB,CAACU,KAAK,EAC3BF,sBAAsB,EACtB,KACF,CAAC;EAEDpB,8BAA8B,CAACqB,YAAY,CAACC,KAAK,CAAC;EAElD,OAAOD,YAAY;AACrB", "ignoreList": []}