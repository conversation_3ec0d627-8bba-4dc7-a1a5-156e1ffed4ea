{"version": 3, "names": [], "sources": ["commonTypes.ts"], "sourcesContent": ["'use strict';\n\nimport type {\n  MeasuredDimensions,\n  ShadowNodeWrapper,\n  SharedValue,\n} from '../commonTypes';\n\nexport type PanGestureHandlerEventPayload = {\n  x: number;\n  y: number;\n  absoluteX: number;\n  absoluteY: number;\n  translationX: number;\n  translationY: number;\n  velocityX: number;\n  velocityY: number;\n};\n\nexport type AnimatedScreenTransition = {\n  topScreenStyle: (\n    event: PanGestureHandlerEventPayload,\n    screenDimensions: MeasuredDimensions\n  ) => Record<string, unknown>;\n  belowTopScreenStyle: (\n    event: PanGestureHandlerEventPayload,\n    screenDimensions: MeasuredDimensions\n  ) => Record<string, unknown>;\n};\n\nexport type GoBackGesture =\n  | 'swipeRight'\n  | 'swipeLeft'\n  | 'swipeUp'\n  | 'swipeDown'\n  | 'verticalSwipe'\n  | 'horizontalSwipe'\n  | 'twoDimensionalSwipe';\n\nexport type ScreenTransitionConfig = {\n  stackTag: number;\n  belowTopScreenId: number | ShadowNodeWrapper;\n  topScreenId: number | ShadowNodeWrapper;\n  screenTransition: AnimatedScreenTransition;\n  sharedEvent: SharedValue<PanGestureHandlerEventPayload>;\n  startingGesturePosition: SharedValue<PanGestureHandlerEventPayload>;\n  onFinishAnimation?: () => void;\n  isTransitionCanceled: boolean;\n  goBackGesture: GoBackGesture;\n  screenDimensions: MeasuredDimensions;\n};\n\nexport type RNScreensTurboModuleType = {\n  startTransition: (stackTag: number) => {\n    topScreenId: number | ShadowNodeWrapper;\n    belowTopScreenId: number | ShadowNodeWrapper;\n    canStartTransition: boolean;\n  };\n  updateTransition: (stackTag: number, progress: number) => void;\n  finishTransition: (stackTag: number, isCanceled: boolean) => void;\n};\n\nexport type LockAxis = 'x' | 'y' | undefined;\n"], "mappings": "AAAA,YAAY;;AAAC", "ignoreList": []}