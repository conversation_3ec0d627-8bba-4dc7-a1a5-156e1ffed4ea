{"version": 3, "names": ["NEWTON_ITERATIONS", "NEWTON_MIN_SLOPE", "SUBDIVISION_PRECISION", "SUBDIVISION_MAX_ITERATIONS", "kSplineTableSize", "kSampleStepSize", "A", "aA1", "aA2", "B", "C", "calcBezier", "aT", "getSlope", "binarySubdivide", "aX", "aA", "aB", "mX1", "mX2", "currentX", "currentT", "i", "Math", "abs", "newtonRaphsonIterate", "aGuessT", "currentSlope", "<PERSON><PERSON>", "mY1", "mY2", "LinearEasing", "x", "Error", "sampleValues", "Array", "getTForX", "intervalStart", "currentSample", "lastSample", "dist", "guessForT", "initialSlope", "BezierEasing"], "sources": ["Bezier.ts"], "sourcesContent": ["'use strict';\n/**\n * https://github.com/gre/bezier-easing\n * BezierEasing - use bezier curve for transition easing function\n * by <PERSON><PERSON><PERSON><PERSON> 2014 - 2015 – MIT License\n */\n\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\n\nconst NEWTON_ITERATIONS = 4;\nconst NEWTON_MIN_SLOPE = 0.001;\nconst SUBDIVISION_PRECISION = 0.0000001;\nconst SUBDIVISION_MAX_ITERATIONS = 10;\n\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\n\nfunction A(aA1: number, aA2: number): number {\n  'worklet';\n  return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n}\nfunction B(aA1: number, aA2: number): number {\n  'worklet';\n  return 3.0 * aA2 - 6.0 * aA1;\n}\nfunction C(aA1: number) {\n  'worklet';\n  return 3.0 * aA1;\n}\n\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier(aT: number, aA1: number, aA2: number): number {\n  'worklet';\n  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n}\n\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope(aT: number, aA1: number, aA2: number): number {\n  'worklet';\n  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n}\n\nfunction binarySubdivide(\n  aX: number,\n  aA: number,\n  aB: number,\n  mX1: number,\n  mX2: number\n): number {\n  'worklet';\n  let currentX;\n  let currentT;\n  let i = 0;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (\n    Math.abs(currentX) > SUBDIVISION_PRECISION &&\n    ++i < SUBDIVISION_MAX_ITERATIONS\n  );\n  return currentT;\n}\n\nfunction newtonRaphsonIterate(\n  aX: number,\n  aGuessT: number,\n  mX1: number,\n  mX2: number\n): number {\n  'worklet';\n  for (let i = 0; i < NEWTON_ITERATIONS; ++i) {\n    const currentSlope = getSlope(aGuessT, mX1, mX2);\n    if (currentSlope === 0.0) {\n      return aGuessT;\n    }\n    const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n    aGuessT -= currentX / currentSlope;\n  }\n  return aGuessT;\n}\n\nexport function Bezier(\n  mX1: number,\n  mY1: number,\n  mX2: number,\n  mY2: number\n): (x: number) => number {\n  'worklet';\n\n  function LinearEasing(x: number): number {\n    'worklet';\n    return x;\n  }\n\n  if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {\n    throw new Error('[Reanimated] Bezier x values must be in [0, 1] range.');\n  }\n\n  if (mX1 === mY1 && mX2 === mY2) {\n    return LinearEasing;\n  }\n\n  const sampleValues = new Array(kSplineTableSize);\n\n  // Precompute samples table\n  for (let i = 0; i < kSplineTableSize; ++i) {\n    sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n  }\n\n  function getTForX(aX: number): number {\n    'worklet';\n    let intervalStart = 0.0;\n    let currentSample = 1;\n    const lastSample = kSplineTableSize - 1;\n\n    for (\n      ;\n      currentSample !== lastSample && sampleValues[currentSample] <= aX;\n      ++currentSample\n    ) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n\n    // Interpolate to provide an initial guess for t\n    const dist =\n      (aX - sampleValues[currentSample]) /\n      (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    const guessForT = intervalStart + dist * kSampleStepSize;\n\n    const initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(\n        aX,\n        intervalStart,\n        intervalStart + kSampleStepSize,\n        mX1,\n        mX2\n      );\n    }\n  }\n\n  return function BezierEasing(x) {\n    'worklet';\n    if (mX1 === mY1 && mX2 === mY2) {\n      return x; // linear\n    }\n    // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n}\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;;AAEA;AAEA,MAAMA,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,gBAAgB,GAAG,KAAK;AAC9B,MAAMC,qBAAqB,GAAG,SAAS;AACvC,MAAMC,0BAA0B,GAAG,EAAE;AAErC,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,eAAe,GAAG,GAAG,IAAID,gBAAgB,GAAG,GAAG,CAAC;AAEtD,SAASE,CAACA,CAACC,GAAW,EAAEC,GAAW,EAAU;EAC3C,SAAS;;EACT,OAAO,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AACpC;AACA,SAASE,CAACA,CAACF,GAAW,EAAEC,GAAW,EAAU;EAC3C,SAAS;;EACT,OAAO,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AAC9B;AACA,SAASG,CAACA,CAACH,GAAW,EAAE;EACtB,SAAS;;EACT,OAAO,GAAG,GAAGA,GAAG;AAClB;;AAEA;AACA,SAASI,UAAUA,CAACC,EAAU,EAAEL,GAAW,EAAEC,GAAW,EAAU;EAChE,SAAS;;EACT,OAAO,CAAC,CAACF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,IAAII,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC,IAAIK,EAAE;AAC9D;;AAEA;AACA,SAASC,QAAQA,CAACD,EAAU,EAAEL,GAAW,EAAEC,GAAW,EAAU;EAC9D,SAAS;;EACT,OAAO,GAAG,GAAGF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGA,EAAE,GAAG,GAAG,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC;AACtE;AAEA,SAASO,eAAeA,CACtBC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,GAAW,EACXC,GAAW,EACH;EACR,SAAS;;EACT,IAAIC,QAAQ;EACZ,IAAIC,QAAQ;EACZ,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACDD,QAAQ,GAAGL,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,GAAG;IAC/BI,QAAQ,GAAGT,UAAU,CAACU,QAAQ,EAAEH,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IAC9C,IAAIK,QAAQ,GAAG,GAAG,EAAE;MAClBH,EAAE,GAAGI,QAAQ;IACf,CAAC,MAAM;MACLL,EAAE,GAAGK,QAAQ;IACf;EACF,CAAC,QACCE,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,GAAGlB,qBAAqB,IAC1C,EAAEoB,CAAC,GAAGnB,0BAA0B;EAElC,OAAOkB,QAAQ;AACjB;AAEA,SAASI,oBAAoBA,CAC3BV,EAAU,EACVW,OAAe,EACfR,GAAW,EACXC,GAAW,EACH;EACR,SAAS;;EACT,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,iBAAiB,EAAE,EAAEsB,CAAC,EAAE;IAC1C,MAAMK,YAAY,GAAGd,QAAQ,CAACa,OAAO,EAAER,GAAG,EAAEC,GAAG,CAAC;IAChD,IAAIQ,YAAY,KAAK,GAAG,EAAE;MACxB,OAAOD,OAAO;IAChB;IACA,MAAMN,QAAQ,GAAGT,UAAU,CAACe,OAAO,EAAER,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IACnDW,OAAO,IAAIN,QAAQ,GAAGO,YAAY;EACpC;EACA,OAAOD,OAAO;AAChB;AAEA,OAAO,SAASE,MAAMA,CACpBV,GAAW,EACXW,GAAW,EACXV,GAAW,EACXW,GAAW,EACY;EACvB,SAAS;;EAET,SAASC,YAAYA,CAACC,CAAS,EAAU;IACvC,SAAS;;IACT,OAAOA,CAAC;EACV;EAEA,IAAI,EAAEd,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC,EAAE;IACnD,MAAM,IAAIc,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAIf,GAAG,KAAKW,GAAG,IAAIV,GAAG,KAAKW,GAAG,EAAE;IAC9B,OAAOC,YAAY;EACrB;EAEA,MAAMG,YAAY,GAAG,IAAIC,KAAK,CAAC/B,gBAAgB,CAAC;;EAEhD;EACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,gBAAgB,EAAE,EAAEkB,CAAC,EAAE;IACzCY,YAAY,CAACZ,CAAC,CAAC,GAAGX,UAAU,CAACW,CAAC,GAAGjB,eAAe,EAAEa,GAAG,EAAEC,GAAG,CAAC;EAC7D;EAEA,SAASiB,QAAQA,CAACrB,EAAU,EAAU;IACpC,SAAS;;IACT,IAAIsB,aAAa,GAAG,GAAG;IACvB,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,UAAU,GAAGnC,gBAAgB,GAAG,CAAC;IAEvC,OAEEkC,aAAa,KAAKC,UAAU,IAAIL,YAAY,CAACI,aAAa,CAAC,IAAIvB,EAAE,EACjE,EAAEuB,aAAa,EACf;MACAD,aAAa,IAAIhC,eAAe;IAClC;IACA,EAAEiC,aAAa;;IAEf;IACA,MAAME,IAAI,GACR,CAACzB,EAAE,GAAGmB,YAAY,CAACI,aAAa,CAAC,KAChCJ,YAAY,CAACI,aAAa,GAAG,CAAC,CAAC,GAAGJ,YAAY,CAACI,aAAa,CAAC,CAAC;IACjE,MAAMG,SAAS,GAAGJ,aAAa,GAAGG,IAAI,GAAGnC,eAAe;IAExD,MAAMqC,YAAY,GAAG7B,QAAQ,CAAC4B,SAAS,EAAEvB,GAAG,EAAEC,GAAG,CAAC;IAClD,IAAIuB,YAAY,IAAIzC,gBAAgB,EAAE;MACpC,OAAOwB,oBAAoB,CAACV,EAAE,EAAE0B,SAAS,EAAEvB,GAAG,EAAEC,GAAG,CAAC;IACtD,CAAC,MAAM,IAAIuB,YAAY,KAAK,GAAG,EAAE;MAC/B,OAAOD,SAAS;IAClB,CAAC,MAAM;MACL,OAAO3B,eAAe,CACpBC,EAAE,EACFsB,aAAa,EACbA,aAAa,GAAGhC,eAAe,EAC/Ba,GAAG,EACHC,GACF,CAAC;IACH;EACF;EAEA,OAAO,SAASwB,YAAYA,CAACX,CAAC,EAAE;IAC9B,SAAS;;IACT,IAAId,GAAG,KAAKW,GAAG,IAAIV,GAAG,KAAKW,GAAG,EAAE;MAC9B,OAAOE,CAAC,CAAC,CAAC;IACZ;IACA;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA,OAAOrB,UAAU,CAACyB,QAAQ,CAACJ,CAAC,CAAC,EAAEH,GAAG,EAAEC,GAAG,CAAC;EAC1C,CAAC;AACH", "ignoreList": []}