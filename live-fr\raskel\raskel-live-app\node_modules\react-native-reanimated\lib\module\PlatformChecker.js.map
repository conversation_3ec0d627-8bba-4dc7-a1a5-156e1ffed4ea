{"version": 3, "names": ["Platform", "isJest", "process", "env", "JEST_WORKER_ID", "isChromeDebugger", "global", "nativeCallSyncHook", "__REMOTEDEV__", "RN$Bridgeless", "isWeb", "OS", "isAndroid", "isWindows", "shouldBeUseWeb", "isF<PERSON><PERSON>", "_IS_FABRIC", "isWindowAvailable", "window"], "sources": ["PlatformChecker.ts"], "sourcesContent": ["'use strict';\nimport { Platform } from 'react-native';\n\n// This type is necessary since some libraries tend to do a lib check\n// and this file causes type errors on `global` access.\ntype localGlobal = typeof global & Record<string, unknown>;\n\nexport function isJest(): boolean {\n  return !!process.env.JEST_WORKER_ID;\n}\n\n// `isChromeDebugger` also returns true in Jest environment, so `isJest()` check should always be performed first\nexport function isChromeDebugger(): boolean {\n  return (\n    (!(global as localGlobal).nativeCallSyncHook ||\n      !!(global as localGlobal).__REMOTEDEV__) &&\n    !(global as localGlobal).RN$Bridgeless\n  );\n}\n\nexport function isWeb(): boolean {\n  return Platform.OS === 'web';\n}\n\nexport function isAndroid(): boolean {\n  return Platform.OS === 'android';\n}\n\nfunction isWindows(): boolean {\n  return Platform.OS === 'windows';\n}\n\nexport function shouldBeUseWeb() {\n  return isJest() || isChromeDebugger() || isWeb() || isWindows();\n}\n\nexport function isFabric() {\n  return !!(global as localGlobal)._IS_FABRIC;\n}\n\nexport function isWindowAvailable() {\n  // the window object is unavailable when building the server portion of a site that uses SSG\n  // this function shouldn't be used to conditionally render components\n  // https://www.joshwcomeau.com/react/the-perils-of-rehydration/\n  // @ts-ignore Fallback if `window` is undefined.\n  return typeof window !== 'undefined';\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,QAAQ,cAAc;;AAEvC;AACA;;AAGA,OAAO,SAASC,MAAMA,CAAA,EAAY;EAChC,OAAO,CAAC,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc;AACrC;;AAEA;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAY;EAC1C,OACE,CAAC,CAAEC,MAAM,CAAiBC,kBAAkB,IAC1C,CAAC,CAAED,MAAM,CAAiBE,aAAa,KACzC,CAAEF,MAAM,CAAiBG,aAAa;AAE1C;AAEA,OAAO,SAASC,KAAKA,CAAA,EAAY;EAC/B,OAAOV,QAAQ,CAACW,EAAE,KAAK,KAAK;AAC9B;AAEA,OAAO,SAASC,SAASA,CAAA,EAAY;EACnC,OAAOZ,QAAQ,CAACW,EAAE,KAAK,SAAS;AAClC;AAEA,SAASE,SAASA,CAAA,EAAY;EAC5B,OAAOb,QAAQ,CAACW,EAAE,KAAK,SAAS;AAClC;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAG;EAC/B,OAAOb,MAAM,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC,IAAIK,KAAK,CAAC,CAAC,IAAIG,SAAS,CAAC,CAAC;AACjE;AAEA,OAAO,SAASE,QAAQA,CAAA,EAAG;EACzB,OAAO,CAAC,CAAET,MAAM,CAAiBU,UAAU;AAC7C;AAEA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC;EACA;EACA;EACA;EACA,OAAO,OAAOC,MAAM,KAAK,WAAW;AACtC", "ignoreList": []}