{"version": 3, "names": ["default", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["RNRenderer.ts"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/ban-ts-comment\n// @ts-nocheck\n'use strict';\nexport { default as <PERSON><PERSON><PERSON><PERSON> } from 'react-native/Libraries/Renderer/shims/ReactNative';\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AACZ,SAASA,OAAO,IAAIC,UAAU,QAAQ,mDAAmD", "ignoreList": []}