{"version": 3, "names": ["NativeReanimatedModule", "isWeb", "shouldBeUseWeb", "isF<PERSON><PERSON>", "makeShareableCloneRecursive", "initializeUIRuntime", "SensorContainer", "startMapper", "stopMapper", "runOnJS", "runOnUI", "executeOnUIRuntimeSync", "createWorkletRuntime", "runOnRuntime", "makeShareable", "makeMutable", "SHOULD_BE_USE_WEB", "isReanimated3", "isConfigured", "global", "_WORKLET", "_log", "console", "log", "_getAnimationTimestamp", "performance", "now", "getViewProp", "viewTag", "propName", "component", "Error", "Promise", "resolve", "reject", "result", "substr", "getSensorContainer", "__sensorContainer", "registerEventHandler", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "emitterReactTag", "handleAndFlushAnimationFrame", "eventTimestamp", "event", "__frameTimestamp", "__flushAnimationFrame", "undefined", "unregisterEventHandler", "id", "subscribeForKeyboardEvents", "options", "state", "height", "isStatusBarTranslucentAndroid", "unsubscribeFromKeyboardEvents", "listenerId", "registerSensor", "sensorType", "config", "sensorContainer", "initializeSensor", "unregisterSensor", "sensorId", "featuresConfig", "enableLayoutAnimations", "setByUser", "flag", "isCallByUser", "configureLayoutAnimationBatch", "layoutAnimationsBatch", "setShouldAnimateExitingForTag", "shouldAnimate", "jsiConfigureProps", "uiProps", "nativeProps", "configureProps"], "sources": ["core.ts"], "sourcesContent": ["'use strict';\nimport NativeReanimatedModule from './NativeReanimated';\nimport { isWeb, shouldBeUseWeb, isFabric } from './PlatformChecker';\nimport type {\n  AnimatedKeyboardOptions,\n  SensorConfig,\n  SensorType,\n  SharedValue,\n  Value3D,\n  ValueRotation,\n} from './commonTypes';\nimport { makeShareableCloneRecursive } from './shareables';\nimport { initializeUIRuntime } from './initializers';\nimport type { LayoutAnimationBatchItem } from './layoutReanimation/animationBuilder/commonTypes';\nimport { SensorContainer } from './SensorContainer';\n\nexport { startMapper, stopMapper } from './mappers';\nexport { runOnJS, runOnUI, executeOnUIRuntimeSync } from './threads';\nexport { createWorkletRuntime, runOnRuntime } from './runtimes';\nexport type { WorkletRuntime } from './runtimes';\nexport { makeShareable, makeShareableCloneRecursive } from './shareables';\nexport { makeMutable } from './mutables';\n\nconst SHOULD_BE_USE_WEB = shouldBeUseWeb();\n\n/**\n * @returns `true` in Reanimated 3, doesn't exist in Reanimated 2 or 1\n */\nexport const isReanimated3 = () => true;\n\n// Superseded by check in `/src/threads.ts`.\n// Used by `react-navigation` to detect if using Reanimated 2 or 3.\n/**\n * @deprecated This function was superseded by other checks.\n * We keep it here for backward compatibility reasons.\n * If you need to check if you are using Reanimated 3 or Reanimated 2\n * please use `isReanimated3` function instead.\n * @returns `true` in Reanimated 3, doesn't exist in Reanimated 2\n */\nexport const isConfigured = isReanimated3;\n\n// this is for web implementation\nif (SHOULD_BE_USE_WEB) {\n  global._WORKLET = false;\n  global._log = console.log;\n  global._getAnimationTimestamp = () => performance.now();\n}\n\nexport function getViewProp<T>(\n  viewTag: number,\n  propName: string,\n  component?: React.Component // required on Fabric\n): Promise<T> {\n  if (isFabric() && !component) {\n    throw new Error(\n      '[Reanimated] Function `getViewProp` requires a component to be passed as an argument on Fabric.'\n    );\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-misused-promises\n  return new Promise((resolve, reject) => {\n    return NativeReanimatedModule.getViewProp(\n      viewTag,\n      propName,\n      component,\n      (result: T) => {\n        if (typeof result === 'string' && result.substr(0, 6) === 'error:') {\n          reject(result);\n        } else {\n          resolve(result);\n        }\n      }\n    );\n  });\n}\n\nfunction getSensorContainer(): SensorContainer {\n  if (!global.__sensorContainer) {\n    global.__sensorContainer = new SensorContainer();\n  }\n  return global.__sensorContainer;\n}\n\nexport function registerEventHandler<T>(\n  eventHandler: (event: T) => void,\n  eventName: string,\n  emitterReactTag = -1\n): number {\n  function handleAndFlushAnimationFrame(eventTimestamp: number, event: T) {\n    'worklet';\n    global.__frameTimestamp = eventTimestamp;\n    eventHandler(event);\n    global.__flushAnimationFrame(eventTimestamp);\n    global.__frameTimestamp = undefined;\n  }\n  return NativeReanimatedModule.registerEventHandler(\n    makeShareableCloneRecursive(handleAndFlushAnimationFrame),\n    eventName,\n    emitterReactTag\n  );\n}\n\nexport function unregisterEventHandler(id: number): void {\n  return NativeReanimatedModule.unregisterEventHandler(id);\n}\n\nexport function subscribeForKeyboardEvents(\n  eventHandler: (state: number, height: number) => void,\n  options: AnimatedKeyboardOptions\n): number {\n  // TODO: this should really go with the same code path as other events, that is\n  // via registerEventHandler. For now we are copying the code from there.\n  function handleAndFlushAnimationFrame(state: number, height: number) {\n    'worklet';\n    const now = global._getAnimationTimestamp();\n    global.__frameTimestamp = now;\n    eventHandler(state, height);\n    global.__flushAnimationFrame(now);\n    global.__frameTimestamp = undefined;\n  }\n  return NativeReanimatedModule.subscribeForKeyboardEvents(\n    makeShareableCloneRecursive(handleAndFlushAnimationFrame),\n    options.isStatusBarTranslucentAndroid ?? false\n  );\n}\n\nexport function unsubscribeFromKeyboardEvents(listenerId: number): void {\n  return NativeReanimatedModule.unsubscribeFromKeyboardEvents(listenerId);\n}\n\nexport function registerSensor(\n  sensorType: SensorType,\n  config: SensorConfig,\n  eventHandler: (\n    data: Value3D | ValueRotation,\n    orientationDegrees: number\n  ) => void\n): number {\n  const sensorContainer = getSensorContainer();\n  return sensorContainer.registerSensor(\n    sensorType,\n    config,\n    makeShareableCloneRecursive(eventHandler)\n  );\n}\n\nexport function initializeSensor(\n  sensorType: SensorType,\n  config: SensorConfig\n): SharedValue<Value3D | ValueRotation> {\n  const sensorContainer = getSensorContainer();\n  return sensorContainer.initializeSensor(sensorType, config);\n}\n\nexport function unregisterSensor(sensorId: number): void {\n  const sensorContainer = getSensorContainer();\n  return sensorContainer.unregisterSensor(sensorId);\n}\n\nif (!isWeb()) {\n  initializeUIRuntime();\n}\n\ntype FeaturesConfig = {\n  enableLayoutAnimations: boolean;\n  setByUser: boolean;\n};\n\nlet featuresConfig: FeaturesConfig = {\n  enableLayoutAnimations: false,\n  setByUser: false,\n};\n\nexport function enableLayoutAnimations(\n  flag: boolean,\n  isCallByUser = true\n): void {\n  if (isCallByUser) {\n    featuresConfig = {\n      enableLayoutAnimations: flag,\n      setByUser: true,\n    };\n    NativeReanimatedModule.enableLayoutAnimations(flag);\n  } else if (\n    !featuresConfig.setByUser &&\n    featuresConfig.enableLayoutAnimations !== flag\n  ) {\n    featuresConfig.enableLayoutAnimations = flag;\n    NativeReanimatedModule.enableLayoutAnimations(flag);\n  }\n}\n\nexport function configureLayoutAnimationBatch(\n  layoutAnimationsBatch: LayoutAnimationBatchItem[]\n): void {\n  NativeReanimatedModule.configureLayoutAnimationBatch(layoutAnimationsBatch);\n}\n\nexport function setShouldAnimateExitingForTag(\n  viewTag: number | HTMLElement,\n  shouldAnimate: boolean\n) {\n  NativeReanimatedModule.setShouldAnimateExitingForTag(\n    viewTag as number,\n    shouldAnimate\n  );\n}\n\nexport function jsiConfigureProps(\n  uiProps: string[],\n  nativeProps: string[]\n): void {\n  if (!SHOULD_BE_USE_WEB) {\n    NativeReanimatedModule.configureProps(uiProps, nativeProps);\n  }\n}\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,mBAAmB;AASnE,SAASC,2BAA2B,QAAQ,cAAc;AAC1D,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,eAAe,QAAQ,mBAAmB;AAEnD,SAASC,WAAW,EAAEC,UAAU,QAAQ,WAAW;AACnD,SAASC,OAAO,EAAEC,OAAO,EAAEC,sBAAsB,QAAQ,WAAW;AACpE,SAASC,oBAAoB,EAAEC,YAAY,QAAQ,YAAY;AAE/D,SAASC,aAAa,EAAEV,2BAA2B,QAAQ,cAAc;AACzE,SAASW,WAAW,QAAQ,YAAY;AAExC,MAAMC,iBAAiB,GAAGd,cAAc,CAAC,CAAC;;AAE1C;AACA;AACA;AACA,OAAO,MAAMe,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGD,aAAa;;AAEzC;AACA,IAAID,iBAAiB,EAAE;EACrBG,MAAM,CAACC,QAAQ,GAAG,KAAK;EACvBD,MAAM,CAACE,IAAI,GAAGC,OAAO,CAACC,GAAG;EACzBJ,MAAM,CAACK,sBAAsB,GAAG,MAAMC,WAAW,CAACC,GAAG,CAAC,CAAC;AACzD;AAEA,OAAO,SAASC,WAAWA,CACzBC,OAAe,EACfC,QAAgB,EAChBC,SAA2B,EACf;EACZ,IAAI3B,QAAQ,CAAC,CAAC,IAAI,CAAC2B,SAAS,EAAE;IAC5B,MAAM,IAAIC,KAAK,CACb,iGACF,CAAC;EACH;;EAEA;EACA,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,OAAOlC,sBAAsB,CAAC2B,WAAW,CACvCC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACRK,MAAS,IAAK;MACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;QAClEF,MAAM,CAACC,MAAM,CAAC;MAChB,CAAC,MAAM;QACLF,OAAO,CAACE,MAAM,CAAC;MACjB;IACF,CACF,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASE,kBAAkBA,CAAA,EAAoB;EAC7C,IAAI,CAAClB,MAAM,CAACmB,iBAAiB,EAAE;IAC7BnB,MAAM,CAACmB,iBAAiB,GAAG,IAAIhC,eAAe,CAAC,CAAC;EAClD;EACA,OAAOa,MAAM,CAACmB,iBAAiB;AACjC;AAEA,OAAO,SAASC,oBAAoBA,CAClCC,YAAgC,EAChCC,SAAiB,EACjBC,eAAe,GAAG,CAAC,CAAC,EACZ;EACR,SAASC,4BAA4BA,CAACC,cAAsB,EAAEC,KAAQ,EAAE;IACtE,SAAS;;IACT1B,MAAM,CAAC2B,gBAAgB,GAAGF,cAAc;IACxCJ,YAAY,CAACK,KAAK,CAAC;IACnB1B,MAAM,CAAC4B,qBAAqB,CAACH,cAAc,CAAC;IAC5CzB,MAAM,CAAC2B,gBAAgB,GAAGE,SAAS;EACrC;EACA,OAAOhD,sBAAsB,CAACuC,oBAAoB,CAChDnC,2BAA2B,CAACuC,4BAA4B,CAAC,EACzDF,SAAS,EACTC,eACF,CAAC;AACH;AAEA,OAAO,SAASO,sBAAsBA,CAACC,EAAU,EAAQ;EACvD,OAAOlD,sBAAsB,CAACiD,sBAAsB,CAACC,EAAE,CAAC;AAC1D;AAEA,OAAO,SAASC,0BAA0BA,CACxCX,YAAqD,EACrDY,OAAgC,EACxB;EACR;EACA;EACA,SAAST,4BAA4BA,CAACU,KAAa,EAAEC,MAAc,EAAE;IACnE,SAAS;;IACT,MAAM5B,GAAG,GAAGP,MAAM,CAACK,sBAAsB,CAAC,CAAC;IAC3CL,MAAM,CAAC2B,gBAAgB,GAAGpB,GAAG;IAC7Bc,YAAY,CAACa,KAAK,EAAEC,MAAM,CAAC;IAC3BnC,MAAM,CAAC4B,qBAAqB,CAACrB,GAAG,CAAC;IACjCP,MAAM,CAAC2B,gBAAgB,GAAGE,SAAS;EACrC;EACA,OAAOhD,sBAAsB,CAACmD,0BAA0B,CACtD/C,2BAA2B,CAACuC,4BAA4B,CAAC,EACzDS,OAAO,CAACG,6BAA6B,IAAI,KAC3C,CAAC;AACH;AAEA,OAAO,SAASC,6BAA6BA,CAACC,UAAkB,EAAQ;EACtE,OAAOzD,sBAAsB,CAACwD,6BAA6B,CAACC,UAAU,CAAC;AACzE;AAEA,OAAO,SAASC,cAAcA,CAC5BC,UAAsB,EACtBC,MAAoB,EACpBpB,YAGS,EACD;EACR,MAAMqB,eAAe,GAAGxB,kBAAkB,CAAC,CAAC;EAC5C,OAAOwB,eAAe,CAACH,cAAc,CACnCC,UAAU,EACVC,MAAM,EACNxD,2BAA2B,CAACoC,YAAY,CAC1C,CAAC;AACH;AAEA,OAAO,SAASsB,gBAAgBA,CAC9BH,UAAsB,EACtBC,MAAoB,EACkB;EACtC,MAAMC,eAAe,GAAGxB,kBAAkB,CAAC,CAAC;EAC5C,OAAOwB,eAAe,CAACC,gBAAgB,CAACH,UAAU,EAAEC,MAAM,CAAC;AAC7D;AAEA,OAAO,SAASG,gBAAgBA,CAACC,QAAgB,EAAQ;EACvD,MAAMH,eAAe,GAAGxB,kBAAkB,CAAC,CAAC;EAC5C,OAAOwB,eAAe,CAACE,gBAAgB,CAACC,QAAQ,CAAC;AACnD;AAEA,IAAI,CAAC/D,KAAK,CAAC,CAAC,EAAE;EACZI,mBAAmB,CAAC,CAAC;AACvB;AAOA,IAAI4D,cAA8B,GAAG;EACnCC,sBAAsB,EAAE,KAAK;EAC7BC,SAAS,EAAE;AACb,CAAC;AAED,OAAO,SAASD,sBAAsBA,CACpCE,IAAa,EACbC,YAAY,GAAG,IAAI,EACb;EACN,IAAIA,YAAY,EAAE;IAChBJ,cAAc,GAAG;MACfC,sBAAsB,EAAEE,IAAI;MAC5BD,SAAS,EAAE;IACb,CAAC;IACDnE,sBAAsB,CAACkE,sBAAsB,CAACE,IAAI,CAAC;EACrD,CAAC,MAAM,IACL,CAACH,cAAc,CAACE,SAAS,IACzBF,cAAc,CAACC,sBAAsB,KAAKE,IAAI,EAC9C;IACAH,cAAc,CAACC,sBAAsB,GAAGE,IAAI;IAC5CpE,sBAAsB,CAACkE,sBAAsB,CAACE,IAAI,CAAC;EACrD;AACF;AAEA,OAAO,SAASE,6BAA6BA,CAC3CC,qBAAiD,EAC3C;EACNvE,sBAAsB,CAACsE,6BAA6B,CAACC,qBAAqB,CAAC;AAC7E;AAEA,OAAO,SAASC,6BAA6BA,CAC3C5C,OAA6B,EAC7B6C,aAAsB,EACtB;EACAzE,sBAAsB,CAACwE,6BAA6B,CAClD5C,OAAO,EACP6C,aACF,CAAC;AACH;AAEA,OAAO,SAASC,iBAAiBA,CAC/BC,OAAiB,EACjBC,WAAqB,EACf;EACN,IAAI,CAAC5D,iBAAiB,EAAE;IACtBhB,sBAAsB,CAAC6E,cAAc,CAACF,OAAO,EAAEC,WAAW,CAAC;EAC7D;AACF", "ignoreList": []}