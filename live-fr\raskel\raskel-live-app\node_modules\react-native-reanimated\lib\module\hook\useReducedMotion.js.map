{"version": 3, "names": ["isReducedMotionEnabledInSystem", "IS_REDUCED_MOTION_ENABLED_IN_SYSTEM", "useReducedMotion"], "sources": ["useReducedMotion.ts"], "sourcesContent": ["'use strict';\nimport { isReducedMotionEnabledInSystem } from '../ReducedMotion';\n\nconst IS_REDUCED_MOTION_ENABLED_IN_SYSTEM = isReducedMotionEnabledInSystem();\n\n/**\n * Lets you query the reduced motion system setting.\n *\n * Changing the reduced motion system setting doesn't cause your components to rerender.\n *\n * @returns A boolean indicating whether the reduced motion setting was enabled when the app started.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useReducedMotion\n */\nexport function useReducedMotion() {\n  return IS_REDUCED_MOTION_ENABLED_IN_SYSTEM;\n}\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,8BAA8B,QAAQ,kBAAkB;AAEjE,MAAMC,mCAAmC,GAAGD,8BAA8B,CAAC,CAAC;;AAE5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAAA,EAAG;EACjC,OAAOD,mCAAmC;AAC5C", "ignoreList": []}