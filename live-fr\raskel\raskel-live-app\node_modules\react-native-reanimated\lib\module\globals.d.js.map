{"version": 3, "names": [], "sources": ["globals.d.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable no-var */\n'use strict';\nimport type {\n  StyleProps,\n  MeasuredDimensions,\n  MapperRegistry,\n  ShareableRef,\n  ShadowNodeWrapper,\n  FlatShareableRef,\n} from './commonTypes';\nimport type { AnimatedStyle } from './helperTypes';\nimport type { FrameCallbackRegistryUI } from './frameCallback/FrameCallbackRegistryUI';\nimport type { NativeReanimatedModule } from './NativeReanimated/NativeReanimated';\nimport type { SensorContainer } from './SensorContainer';\nimport type { LayoutAnimationsManager } from './layoutReanimation/animationsManager';\nimport type { ProgressTransitionRegister } from './layoutReanimation/sharedTransitions';\nimport type { UpdatePropsManager } from './UpdateProps';\nimport type { callGuardDEV } from './initializers';\nimport type { WorkletRuntime } from './runtimes';\nimport type { RNScreensTurboModuleType } from './screenTransition/commonTypes';\n\ndeclare global {\n  var _REANIMATED_IS_REDUCED_MOTION: boolean | undefined;\n  var _IS_FABRIC: boolean | undefined;\n  var _REANIMATED_VERSION_CPP: string | undefined;\n  var _REANIMATED_VERSION_JS: string | undefined;\n  var __reanimatedModuleProxy: NativeReanimatedModule | undefined;\n  var __callGuardDEV: typeof callGuardDEV | undefined;\n  var evalWithSourceMap:\n    | ((js: string, sourceURL: string, sourceMap: string) => any)\n    | undefined;\n  var evalWithSourceUrl: ((js: string, sourceURL: string) => any) | undefined;\n  var _log: (value: unknown) => void;\n  var _toString: (value: unknown) => string;\n  var _notifyAboutProgress: (\n    tag: number,\n    value: Record<string, unknown>,\n    isSharedTransition: boolean\n  ) => void;\n  var _notifyAboutEnd: (tag: number, removeView: boolean) => void;\n  var _setGestureState: (handlerTag: number, newState: number) => void;\n  var _makeShareableClone: <T>(\n    value: T,\n    nativeStateSource?: object\n  ) => FlatShareableRef<T>;\n  var _scheduleOnJS: (fun: (...args: A) => R, args?: A) => void;\n  var _scheduleOnRuntime: (\n    runtime: WorkletRuntime,\n    worklet: ShareableRef<() => void>\n  ) => void;\n  var _updatePropsPaper:\n    | ((\n        operations: {\n          tag: number;\n          name: string | null;\n          // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n          updates: StyleProps | AnimatedStyle<any>;\n        }[]\n      ) => void)\n    | undefined;\n  var _updatePropsFabric:\n    | ((\n        operations: {\n          shadowNodeWrapper: ShadowNodeWrapper;\n          // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n          updates: StyleProps | AnimatedStyle<any>;\n        }[]\n      ) => void)\n    | undefined;\n  var _removeFromPropsRegistry: (viewTags: number[]) => void | undefined;\n  var _measurePaper:\n    | ((viewTag: number | null) => MeasuredDimensions)\n    | undefined;\n  var _measureFabric:\n    | ((shadowNodeWrapper: ShadowNodeWrapper | null) => MeasuredDimensions)\n    | undefined;\n  var _scrollToPaper:\n    | ((viewTag: number, x: number, y: number, animated: boolean) => void)\n    | undefined;\n  var _dispatchCommandPaper:\n    | ((viewTag: number, commandName: string, args: Array<unknown>) => void)\n    | undefined;\n  var _dispatchCommandFabric:\n    | ((\n        shadowNodeWrapper: ShadowNodeWrapper,\n        commandName: string,\n        args: Array<unknown>\n      ) => void)\n    | undefined;\n  var _getAnimationTimestamp: () => number;\n  var __ErrorUtils: {\n    reportFatalError: (error: Error) => void;\n  };\n  var _frameCallbackRegistry: FrameCallbackRegistryUI;\n  var console: Console;\n  var __frameTimestamp: number | undefined;\n  var __flushAnimationFrame: (timestamp: number) => void;\n  var __workletsCache: Map<string, any>;\n  var __handleCache: WeakMap<object, any>;\n  var __callMicrotasks: () => void;\n  var __mapperRegistry: MapperRegistry;\n  var __sensorContainer: SensorContainer;\n  var _maybeFlushUIUpdatesQueue: () => void;\n  var LayoutAnimationsManager: LayoutAnimationsManager;\n  var UpdatePropsManager: UpdatePropsManager;\n  var ProgressTransitionRegister: ProgressTransitionRegister;\n  var updateJSProps: (viewTag: number, props: Record<string, unknown>) => void;\n  var RNScreensTurboModule: RNScreensTurboModuleType | undefined;\n  var _obtainPropPaper: (viewTag: number, propName: string) => string;\n  var _obtainPropFabric: (\n    shadowNodeWrapper: ShadowNodeWrapper,\n    propName: string\n  ) => string;\n}\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAAC", "ignoreList": []}