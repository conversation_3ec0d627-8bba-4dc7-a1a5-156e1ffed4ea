{"version": 3, "names": ["useEffect", "useRef", "useCallback", "useEvent", "useSharedValue", "isWeb", "IS_WEB", "useScrollViewOffset", "useScrollViewOffsetWeb", "useScrollViewOffsetNative", "animatedRef", "providedOffset", "internalOffset", "offset", "current", "<PERSON><PERSON><PERSON><PERSON>", "element", "getWebScrollableElement", "value", "scrollLeft", "scrollTop", "addEventListener", "removeEventListener", "event", "contentOffset", "x", "y", "scrollNativeEventNames", "elementTag", "getTag", "workletEventHandler", "registerForEvents", "unregisterFromEvents", "scrollComponent", "getScrollableNode"], "sources": ["useScrollViewOffset.ts"], "sourcesContent": ["'use strict';\nimport { useEffect, useRef, useCallback } from 'react';\nimport type { SharedValue } from '../commonTypes';\nimport type { EventHandlerInternal } from './useEvent';\nimport { useEvent } from './useEvent';\nimport { useSharedValue } from './useSharedValue';\nimport type { AnimatedScrollView } from '../component/ScrollView';\nimport type {\n  AnimatedRef,\n  RNNativeScrollEvent,\n  ReanimatedScrollEvent,\n} from './commonTypes';\nimport { isWeb } from '../PlatformChecker';\n\nconst IS_WEB = isWeb();\n\n/**\n * Lets you synchronously get the current offset of a `ScrollView`.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef) attached to an Animated.ScrollView component.\n * @returns A shared value which holds the current offset of the `ScrollView`.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/useScrollViewOffset\n */\nexport const useScrollViewOffset = IS_WEB\n  ? useScrollViewOffsetWeb\n  : useScrollViewOffsetNative;\n\nfunction useScrollViewOffsetWeb(\n  animatedRef: AnimatedRef<AnimatedScrollView> | null,\n  providedOffset?: SharedValue<number>\n): SharedValue<number> {\n  const internalOffset = useSharedValue(0);\n  const offset = useRef(providedOffset ?? internalOffset).current;\n\n  const eventHandler = useCallback(() => {\n    'worklet';\n    if (animatedRef) {\n      const element = getWebScrollableElement(animatedRef.current);\n      // scrollLeft is the X axis scrolled offset, works properly also with RTL layout\n      offset.value =\n        element.scrollLeft === 0 ? element.scrollTop : element.scrollLeft;\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animatedRef, animatedRef?.current]);\n\n  useEffect(() => {\n    const element = animatedRef?.current\n      ? getWebScrollableElement(animatedRef.current)\n      : null;\n\n    if (element) {\n      element.addEventListener('scroll', eventHandler);\n    }\n    return () => {\n      if (element) {\n        element.removeEventListener('scroll', eventHandler);\n      }\n    };\n    // React here has a problem with `animatedRef.current` since a Ref .current\n    // field shouldn't be used as a dependency. However, in this case we have\n    // to do it this way.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animatedRef, animatedRef?.current, eventHandler]);\n\n  return offset;\n}\n\nfunction useScrollViewOffsetNative(\n  animatedRef: AnimatedRef<AnimatedScrollView> | null,\n  providedOffset?: SharedValue<number>\n): SharedValue<number> {\n  const internalOffset = useSharedValue(0);\n  const offset = useRef(providedOffset ?? internalOffset).current;\n\n  const eventHandler = useEvent<RNNativeScrollEvent>(\n    (event: ReanimatedScrollEvent) => {\n      'worklet';\n      offset.value =\n        event.contentOffset.x === 0\n          ? event.contentOffset.y\n          : event.contentOffset.x;\n    },\n    scrollNativeEventNames\n    // Read https://github.com/software-mansion/react-native-reanimated/pull/5056\n    // for more information about this cast.\n  ) as unknown as EventHandlerInternal<ReanimatedScrollEvent>;\n\n  useEffect(() => {\n    const elementTag = animatedRef?.getTag() ?? null;\n\n    if (elementTag) {\n      eventHandler.workletEventHandler.registerForEvents(elementTag);\n    }\n    return () => {\n      if (elementTag) {\n        eventHandler.workletEventHandler.unregisterFromEvents(elementTag);\n      }\n    };\n    // React here has a problem with `animatedRef.current` since a Ref .current\n    // field shouldn't be used as a dependency. However, in this case we have\n    // to do it this way.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animatedRef, animatedRef?.current, eventHandler]);\n\n  return offset;\n}\n\nfunction getWebScrollableElement(\n  scrollComponent: AnimatedScrollView | null\n): HTMLElement {\n  return (\n    (scrollComponent?.getScrollableNode() as unknown as HTMLElement) ??\n    scrollComponent\n  );\n}\n\nconst scrollNativeEventNames = [\n  'onScroll',\n  'onScrollBeginDrag',\n  'onScrollEndDrag',\n  'onMomentumScrollBegin',\n  'onMomentumScrollEnd',\n];\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAGtD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,cAAc,QAAQ,kBAAkB;AAOjD,SAASC,KAAK,QAAQ,oBAAoB;AAE1C,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,mBAAmB,GAAGD,MAAM,GACrCE,sBAAsB,GACtBC,yBAAyB;AAE7B,SAASD,sBAAsBA,CAC7BE,WAAmD,EACnDC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGR,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMS,MAAM,GAAGZ,MAAM,CAACU,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAE/D,MAAMC,YAAY,GAAGb,WAAW,CAAC,MAAM;IACrC,SAAS;;IACT,IAAIQ,WAAW,EAAE;MACf,MAAMM,OAAO,GAAGC,uBAAuB,CAACP,WAAW,CAACI,OAAO,CAAC;MAC5D;MACAD,MAAM,CAACK,KAAK,GACVF,OAAO,CAACG,UAAU,KAAK,CAAC,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACG,UAAU;IACrE;IACA;EACF,CAAC,EAAE,CAACT,WAAW,EAAEA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,OAAO,CAAC,CAAC;EAEvCd,SAAS,CAAC,MAAM;IACd,MAAMgB,OAAO,GAAGN,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEI,OAAO,GAChCG,uBAAuB,CAACP,WAAW,CAACI,OAAO,CAAC,GAC5C,IAAI;IAER,IAAIE,OAAO,EAAE;MACXA,OAAO,CAACK,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAClD;IACA,OAAO,MAAM;MACX,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACM,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MACrD;IACF,CAAC;IACD;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACL,WAAW,EAAEA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,OAAO,EAAEC,YAAY,CAAC,CAAC;EAErD,OAAOF,MAAM;AACf;AAEA,SAASJ,yBAAyBA,CAChCC,WAAmD,EACnDC,cAAoC,EACf;EACrB,MAAMC,cAAc,GAAGR,cAAc,CAAC,CAAC,CAAC;EACxC,MAAMS,MAAM,GAAGZ,MAAM,CAACU,cAAc,IAAIC,cAAc,CAAC,CAACE,OAAO;EAE/D,MAAMC,YAAY,GAAGZ,QAAQ,CAC1BoB,KAA4B,IAAK;IAChC,SAAS;;IACTV,MAAM,CAACK,KAAK,GACVK,KAAK,CAACC,aAAa,CAACC,CAAC,KAAK,CAAC,GACvBF,KAAK,CAACC,aAAa,CAACE,CAAC,GACrBH,KAAK,CAACC,aAAa,CAACC,CAAC;EAC7B,CAAC,EACDE;EACA;EACA;EACF,CAA2D;EAE3D3B,SAAS,CAAC,MAAM;IACd,MAAM4B,UAAU,GAAG,CAAAlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,MAAM,CAAC,CAAC,KAAI,IAAI;IAEhD,IAAID,UAAU,EAAE;MACdb,YAAY,CAACe,mBAAmB,CAACC,iBAAiB,CAACH,UAAU,CAAC;IAChE;IACA,OAAO,MAAM;MACX,IAAIA,UAAU,EAAE;QACdb,YAAY,CAACe,mBAAmB,CAACE,oBAAoB,CAACJ,UAAU,CAAC;MACnE;IACF,CAAC;IACD;IACA;IACA;IACA;EACF,CAAC,EAAE,CAAClB,WAAW,EAAEA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,OAAO,EAAEC,YAAY,CAAC,CAAC;EAErD,OAAOF,MAAM;AACf;AAEA,SAASI,uBAAuBA,CAC9BgB,eAA0C,EAC7B;EACb,OACE,CAACA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEC,iBAAiB,CAAC,CAAC,KACrCD,eAAe;AAEnB;AAEA,MAAMN,sBAAsB,GAAG,CAC7B,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,CACtB", "ignoreList": []}