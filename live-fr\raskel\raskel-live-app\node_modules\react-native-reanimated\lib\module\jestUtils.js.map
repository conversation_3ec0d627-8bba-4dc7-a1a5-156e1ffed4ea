{"version": 3, "names": ["isJest", "defaultFramerateConfig", "fps", "getCurrentStyle", "component", "styleObject", "props", "style", "currentStyle", "Array", "isArray", "for<PERSON>ach", "_component$props$jest", "jestAnimatedStyle", "value", "checkEqual", "current", "expected", "length", "i", "property", "findStyleDiff", "shouldMatchAllProps", "diffs", "isEqual", "push", "expect", "Object", "keys", "undefined", "compareStyle", "expectedStyle", "config", "message", "pass", "currentStyleStr", "JSON", "stringify", "expectedStyleStr", "differences", "map", "diff", "join", "frameTime", "Math", "round", "beforeTest", "jest", "useFakeTimers", "afterTest", "runOnlyPendingTimers", "useRealTimers", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animationTest", "console", "warn", "advanceAnimationByTime", "time", "advanceTimersByTime", "advanceAnimationByFrame", "count", "requireFunction", "require", "Error", "setUpTests", "userFramerateConfig", "global", "expectModule", "jestGlobals", "extend", "default", "framerateConfig", "toHaveAnimatedStyle", "getAnimatedStyle"], "sources": ["jestUtils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-namespace */\n'use strict';\n\nimport type { ReactTestInstance } from 'react-test-renderer';\nimport type {\n  AnimatedComponentProps,\n  IAnimatedComponentInternal,\n  InitialComponentProps,\n} from './createAnimatedComponent/commonTypes';\nimport { isJest } from './PlatformChecker';\nimport type { DefaultStyle } from './hook/commonTypes';\n\ndeclare global {\n  namespace jest {\n    interface Matchers<R> {\n      toHaveAnimatedStyle(\n        style: Record<string, unknown>[] | Record<string, unknown>,\n        config?: {\n          shouldMatchAllProps?: boolean;\n        }\n      ): R;\n    }\n  }\n}\n\nconst defaultFramerateConfig = {\n  fps: 60,\n};\n\nconst getCurrentStyle = (component: TestComponent): DefaultStyle => {\n  const styleObject = component.props.style;\n  let currentStyle = {};\n  if (Array.isArray(styleObject)) {\n    styleObject.forEach((style) => {\n      currentStyle = {\n        ...currentStyle,\n        ...style,\n      };\n    });\n  } else {\n    currentStyle = {\n      ...styleObject,\n      ...component.props.jestAnimatedStyle?.value,\n    };\n  }\n  return currentStyle;\n};\n\nconst checkEqual = <Value>(current: Value, expected: Value) => {\n  if (Array.isArray(expected)) {\n    if (!Array.isArray(current) || expected.length !== current.length) {\n      return false;\n    }\n    for (let i = 0; i < current.length; i++) {\n      if (!checkEqual(current[i], expected[i])) {\n        return false;\n      }\n    }\n  } else if (typeof current === 'object' && current) {\n    if (typeof expected !== 'object' || !expected) {\n      return false;\n    }\n    for (const property in expected) {\n      if (!checkEqual(current[property], expected[property])) {\n        return false;\n      }\n    }\n  } else {\n    return current === expected;\n  }\n  return true;\n};\n\nconst findStyleDiff = (\n  current: DefaultStyle,\n  expected: DefaultStyle,\n  shouldMatchAllProps?: boolean\n) => {\n  const diffs = [];\n  let isEqual = true;\n  let property: keyof DefaultStyle;\n  for (property in expected) {\n    if (!checkEqual(current[property], expected[property])) {\n      isEqual = false;\n      diffs.push({\n        property,\n        current: current[property],\n        expect: expected[property],\n      });\n    }\n  }\n\n  if (\n    shouldMatchAllProps &&\n    Object.keys(current).length !== Object.keys(expected).length\n  ) {\n    isEqual = false;\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    let property: keyof DefaultStyle;\n    for (property in current) {\n      if (expected[property] === undefined) {\n        diffs.push({\n          property,\n          current: current[property],\n          expect: expected[property],\n        });\n      }\n    }\n  }\n\n  return { isEqual, diffs };\n};\n\nconst compareStyle = (\n  component: TestComponent,\n  expectedStyle: DefaultStyle,\n  config: ToHaveAnimatedStyleConfig\n) => {\n  if (!component.props.style) {\n    return { message: () => `Component doesn't have a style.`, pass: false };\n  }\n  const { shouldMatchAllProps } = config;\n  const currentStyle = getCurrentStyle(component);\n  const { isEqual, diffs } = findStyleDiff(\n    currentStyle,\n    expectedStyle,\n    shouldMatchAllProps\n  );\n\n  if (isEqual) {\n    return { message: () => 'ok', pass: true };\n  }\n\n  const currentStyleStr = JSON.stringify(currentStyle);\n  const expectedStyleStr = JSON.stringify(expectedStyle);\n  const differences = diffs\n    .map(\n      (diff) =>\n        `- '${diff.property}' should be ${JSON.stringify(\n          diff.expect\n        )}, but is ${JSON.stringify(diff.current)}`\n    )\n    .join('\\n');\n\n  return {\n    message: () =>\n      `Expected: ${expectedStyleStr}\\nReceived: ${currentStyleStr}\\n\\nDifferences:\\n${differences}`,\n    pass: false,\n  };\n};\n\nlet frameTime = Math.round(1000 / defaultFramerateConfig.fps);\n\nconst beforeTest = () => {\n  jest.useFakeTimers();\n};\n\nconst afterTest = () => {\n  jest.runOnlyPendingTimers();\n  jest.useRealTimers();\n};\n\nexport const withReanimatedTimer = (animationTest: () => void) => {\n  console.warn(\n    'This method is deprecated, you should define your own before and after test hooks to enable jest.useFakeTimers(). Check out the documentation for details on testing'\n  );\n  beforeTest();\n  animationTest();\n  afterTest();\n};\n\nexport const advanceAnimationByTime = (time = frameTime) => {\n  console.warn(\n    'This method is deprecated, use jest.advanceTimersByTime directly'\n  );\n  jest.advanceTimersByTime(time);\n  jest.runOnlyPendingTimers();\n};\n\nexport const advanceAnimationByFrame = (count: number) => {\n  console.warn(\n    'This method is deprecated, use jest.advanceTimersByTime directly'\n  );\n  jest.advanceTimersByTime(count * frameTime);\n  jest.runOnlyPendingTimers();\n};\n\nconst requireFunction = isJest()\n  ? require\n  : () => {\n      throw new Error(\n        '[Reanimated] `setUpTests` is available only in Jest environment.'\n      );\n    };\n\ntype ToHaveAnimatedStyleConfig = {\n  shouldMatchAllProps?: boolean;\n};\n\nexport const setUpTests = (userFramerateConfig = {}) => {\n  let expect: jest.Expect = (global as typeof global & { expect: jest.Expect })\n    .expect;\n  if (expect === undefined) {\n    const expectModule = requireFunction('expect');\n    expect = expectModule;\n    // Starting from Jest 28, \"expect\" package uses named exports instead of default export.\n    // So, requiring \"expect\" package doesn't give direct access to \"expect\" function anymore.\n    // It gives access to the module object instead.\n    // We use this info to detect if the project uses Jest 28 or higher.\n    if (typeof expect === 'object') {\n      const jestGlobals = requireFunction('@jest/globals');\n      expect = jestGlobals.expect;\n    }\n    if (expect === undefined || expect.extend === undefined) {\n      expect = expectModule.default;\n    }\n  }\n\n  const framerateConfig = {\n    ...defaultFramerateConfig,\n    ...userFramerateConfig,\n  };\n  frameTime = Math.round(1000 / framerateConfig.fps);\n\n  expect.extend({\n    toHaveAnimatedStyle(\n      component: React.Component<\n        AnimatedComponentProps<InitialComponentProps>\n      > &\n        IAnimatedComponentInternal,\n      expectedStyle: DefaultStyle,\n      config: ToHaveAnimatedStyleConfig = {}\n    ) {\n      return compareStyle(component, expectedStyle, config);\n    },\n  });\n};\n\ntype TestComponent = React.Component<\n  AnimatedComponentProps<InitialComponentProps> & {\n    jestAnimatedStyle?: { value: DefaultStyle };\n  }\n>;\n\nexport const getAnimatedStyle = (component: ReactTestInstance) => {\n  return getCurrentStyle(\n    // This type assertion is needed to get type checking in the following\n    // functions since `ReactTestInstance` has its `props` defined as `any`.\n    component as unknown as TestComponent\n  );\n};\n"], "mappings": "AAAA;AACA,YAAY;;AAQZ,SAASA,MAAM,QAAQ,mBAAmB;AAgB1C,MAAMC,sBAAsB,GAAG;EAC7BC,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,eAAe,GAAIC,SAAwB,IAAmB;EAClE,MAAMC,WAAW,GAAGD,SAAS,CAACE,KAAK,CAACC,KAAK;EACzC,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,KAAK,CAACC,OAAO,CAACL,WAAW,CAAC,EAAE;IAC9BA,WAAW,CAACM,OAAO,CAAEJ,KAAK,IAAK;MAC7BC,YAAY,GAAG;QACb,GAAGA,YAAY;QACf,GAAGD;MACL,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IAAA,IAAAK,qBAAA;IACLJ,YAAY,GAAG;MACb,GAAGH,WAAW;MACd,KAAAO,qBAAA,GAAGR,SAAS,CAACE,KAAK,CAACO,iBAAiB,cAAAD,qBAAA,uBAAjCA,qBAAA,CAAmCE,KAAK;IAC7C,CAAC;EACH;EACA,OAAON,YAAY;AACrB,CAAC;AAED,MAAMO,UAAU,GAAGA,CAAQC,OAAc,EAAEC,QAAe,KAAK;EAC7D,IAAIR,KAAK,CAACC,OAAO,CAACO,QAAQ,CAAC,EAAE;IAC3B,IAAI,CAACR,KAAK,CAACC,OAAO,CAACM,OAAO,CAAC,IAAIC,QAAQ,CAACC,MAAM,KAAKF,OAAO,CAACE,MAAM,EAAE;MACjE,OAAO,KAAK;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;MACvC,IAAI,CAACJ,UAAU,CAACC,OAAO,CAACG,CAAC,CAAC,EAAEF,QAAQ,CAACE,CAAC,CAAC,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;IACF;EACF,CAAC,MAAM,IAAI,OAAOH,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;IACjD,IAAI,OAAOC,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;MAC7C,OAAO,KAAK;IACd;IACA,KAAK,MAAMG,QAAQ,IAAIH,QAAQ,EAAE;MAC/B,IAAI,CAACF,UAAU,CAACC,OAAO,CAACI,QAAQ,CAAC,EAAEH,QAAQ,CAACG,QAAQ,CAAC,CAAC,EAAE;QACtD,OAAO,KAAK;MACd;IACF;EACF,CAAC,MAAM;IACL,OAAOJ,OAAO,KAAKC,QAAQ;EAC7B;EACA,OAAO,IAAI;AACb,CAAC;AAED,MAAMI,aAAa,GAAGA,CACpBL,OAAqB,EACrBC,QAAsB,EACtBK,mBAA6B,KAC1B;EACH,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIJ,QAA4B;EAChC,KAAKA,QAAQ,IAAIH,QAAQ,EAAE;IACzB,IAAI,CAACF,UAAU,CAACC,OAAO,CAACI,QAAQ,CAAC,EAAEH,QAAQ,CAACG,QAAQ,CAAC,CAAC,EAAE;MACtDI,OAAO,GAAG,KAAK;MACfD,KAAK,CAACE,IAAI,CAAC;QACTL,QAAQ;QACRJ,OAAO,EAAEA,OAAO,CAACI,QAAQ,CAAC;QAC1BM,MAAM,EAAET,QAAQ,CAACG,QAAQ;MAC3B,CAAC,CAAC;IACJ;EACF;EAEA,IACEE,mBAAmB,IACnBK,MAAM,CAACC,IAAI,CAACZ,OAAO,CAAC,CAACE,MAAM,KAAKS,MAAM,CAACC,IAAI,CAACX,QAAQ,CAAC,CAACC,MAAM,EAC5D;IACAM,OAAO,GAAG,KAAK;IACf;IACA,IAAIJ,QAA4B;IAChC,KAAKA,QAAQ,IAAIJ,OAAO,EAAE;MACxB,IAAIC,QAAQ,CAACG,QAAQ,CAAC,KAAKS,SAAS,EAAE;QACpCN,KAAK,CAACE,IAAI,CAAC;UACTL,QAAQ;UACRJ,OAAO,EAAEA,OAAO,CAACI,QAAQ,CAAC;UAC1BM,MAAM,EAAET,QAAQ,CAACG,QAAQ;QAC3B,CAAC,CAAC;MACJ;IACF;EACF;EAEA,OAAO;IAAEI,OAAO;IAAED;EAAM,CAAC;AAC3B,CAAC;AAED,MAAMO,YAAY,GAAGA,CACnB1B,SAAwB,EACxB2B,aAA2B,EAC3BC,MAAiC,KAC9B;EACH,IAAI,CAAC5B,SAAS,CAACE,KAAK,CAACC,KAAK,EAAE;IAC1B,OAAO;MAAE0B,OAAO,EAAEA,CAAA,KAAO,iCAAgC;MAAEC,IAAI,EAAE;IAAM,CAAC;EAC1E;EACA,MAAM;IAAEZ;EAAoB,CAAC,GAAGU,MAAM;EACtC,MAAMxB,YAAY,GAAGL,eAAe,CAACC,SAAS,CAAC;EAC/C,MAAM;IAAEoB,OAAO;IAAED;EAAM,CAAC,GAAGF,aAAa,CACtCb,YAAY,EACZuB,aAAa,EACbT,mBACF,CAAC;EAED,IAAIE,OAAO,EAAE;IACX,OAAO;MAAES,OAAO,EAAEA,CAAA,KAAM,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC;EAC5C;EAEA,MAAMC,eAAe,GAAGC,IAAI,CAACC,SAAS,CAAC7B,YAAY,CAAC;EACpD,MAAM8B,gBAAgB,GAAGF,IAAI,CAACC,SAAS,CAACN,aAAa,CAAC;EACtD,MAAMQ,WAAW,GAAGhB,KAAK,CACtBiB,GAAG,CACDC,IAAI,IACF,MAAKA,IAAI,CAACrB,QAAS,eAAcgB,IAAI,CAACC,SAAS,CAC9CI,IAAI,CAACf,MACP,CAAE,YAAWU,IAAI,CAACC,SAAS,CAACI,IAAI,CAACzB,OAAO,CAAE,EAC9C,CAAC,CACA0B,IAAI,CAAC,IAAI,CAAC;EAEb,OAAO;IACLT,OAAO,EAAEA,CAAA,KACN,aAAYK,gBAAiB,eAAcH,eAAgB,qBAAoBI,WAAY,EAAC;IAC/FL,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAED,IAAIS,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAG5C,sBAAsB,CAACC,GAAG,CAAC;AAE7D,MAAM4C,UAAU,GAAGA,CAAA,KAAM;EACvBC,IAAI,CAACC,aAAa,CAAC,CAAC;AACtB,CAAC;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtBF,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC3BH,IAAI,CAACI,aAAa,CAAC,CAAC;AACtB,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAIC,aAAyB,IAAK;EAChEC,OAAO,CAACC,IAAI,CACV,sKACF,CAAC;EACDT,UAAU,CAAC,CAAC;EACZO,aAAa,CAAC,CAAC;EACfJ,SAAS,CAAC,CAAC;AACb,CAAC;AAED,OAAO,MAAMO,sBAAsB,GAAGA,CAACC,IAAI,GAAGd,SAAS,KAAK;EAC1DW,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACDR,IAAI,CAACW,mBAAmB,CAACD,IAAI,CAAC;EAC9BV,IAAI,CAACG,oBAAoB,CAAC,CAAC;AAC7B,CAAC;AAED,OAAO,MAAMS,uBAAuB,GAAIC,KAAa,IAAK;EACxDN,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACDR,IAAI,CAACW,mBAAmB,CAACE,KAAK,GAAGjB,SAAS,CAAC;EAC3CI,IAAI,CAACG,oBAAoB,CAAC,CAAC;AAC7B,CAAC;AAED,MAAMW,eAAe,GAAG7D,MAAM,CAAC,CAAC,GAC5B8D,OAAO,GACP,MAAM;EACJ,MAAM,IAAIC,KAAK,CACb,kEACF,CAAC;AACH,CAAC;AAML,OAAO,MAAMC,UAAU,GAAGA,CAACC,mBAAmB,GAAG,CAAC,CAAC,KAAK;EACtD,IAAIvC,MAAmB,GAAIwC,MAAM,CAC9BxC,MAAM;EACT,IAAIA,MAAM,KAAKG,SAAS,EAAE;IACxB,MAAMsC,YAAY,GAAGN,eAAe,CAAC,QAAQ,CAAC;IAC9CnC,MAAM,GAAGyC,YAAY;IACrB;IACA;IACA;IACA;IACA,IAAI,OAAOzC,MAAM,KAAK,QAAQ,EAAE;MAC9B,MAAM0C,WAAW,GAAGP,eAAe,CAAC,eAAe,CAAC;MACpDnC,MAAM,GAAG0C,WAAW,CAAC1C,MAAM;IAC7B;IACA,IAAIA,MAAM,KAAKG,SAAS,IAAIH,MAAM,CAAC2C,MAAM,KAAKxC,SAAS,EAAE;MACvDH,MAAM,GAAGyC,YAAY,CAACG,OAAO;IAC/B;EACF;EAEA,MAAMC,eAAe,GAAG;IACtB,GAAGtE,sBAAsB;IACzB,GAAGgE;EACL,CAAC;EACDtB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAG0B,eAAe,CAACrE,GAAG,CAAC;EAElDwB,MAAM,CAAC2C,MAAM,CAAC;IACZG,mBAAmBA,CACjBpE,SAG4B,EAC5B2B,aAA2B,EAC3BC,MAAiC,GAAG,CAAC,CAAC,EACtC;MACA,OAAOF,YAAY,CAAC1B,SAAS,EAAE2B,aAAa,EAAEC,MAAM,CAAC;IACvD;EACF,CAAC,CAAC;AACJ,CAAC;AAQD,OAAO,MAAMyC,gBAAgB,GAAIrE,SAA4B,IAAK;EAChE,OAAOD,eAAe;EACpB;EACA;EACAC,SACF,CAAC;AACH,CAAC", "ignoreList": []}