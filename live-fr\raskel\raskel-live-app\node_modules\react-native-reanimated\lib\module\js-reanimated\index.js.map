{"version": 3, "names": ["JSReanimated", "createReactDOMStyle", "createTransformValue", "createTextShadowValue", "PropsAllowlists", "reanimatedJS", "global", "_makeShareableClone", "Error", "_scheduleOnJS", "_scheduleOnRuntime", "_updatePropsJS", "updates", "viewRef", "isAnimatedProps", "component", "getAnimatableRef", "rawStyles", "Object", "keys", "reduce", "acc", "key", "value", "index", "setNativeProps", "undefined", "style", "updatePropsDOM", "props", "length", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "replace", "m", "toLowerCase", "_touchableNode", "setAttribute", "componentName", "className", "console", "warn", "newProps", "_component$setNativeP2", "_component$setNativeP", "uiProps", "isNativeProp", "call", "previousStyle", "currentStyle", "domStyle", "Array", "isArray", "transform", "textShadowColor", "textShadowRadius", "textShadowOffset", "textShadow", "propName", "NATIVE_THREAD_PROPS_WHITELIST"], "sources": ["index.ts"], "sourcesContent": ["'use strict';\nimport JSReanimated from './JSReanimated';\nimport type { StyleProps, AnimatedStyle } from '../commonTypes';\nimport {\n  createReactDOMStyle,\n  createTransformValue,\n  createTextShadowValue,\n} from './webUtils';\nimport { PropsAllowlists } from '../propsAllowlists';\n\nconst reanimatedJS = new JSReanimated();\n\nglobal._makeShareableClone = () => {\n  throw new Error(\n    '[Reanimated] _makeShareableClone should never be called in JSReanimated.'\n  );\n};\n\nglobal._scheduleOnJS = () => {\n  throw new Error(\n    '[Reanimated] _scheduleOnJS should never be called in JSReanimated.'\n  );\n};\n\nglobal._scheduleOnRuntime = () => {\n  throw new Error(\n    '[Reanimated] _scheduleOnRuntime should never be called in JSReanimated.'\n  );\n};\n\ninterface JSReanimatedComponent {\n  previousStyle: StyleProps;\n  setNativeProps?: (style: StyleProps) => void;\n  style?: StyleProps;\n  props: Record<string, string | number>;\n  _touchableNode: {\n    setAttribute: (key: string, props: unknown) => void;\n  };\n}\n\nexport interface ReanimatedHTMLElement extends HTMLElement {\n  previousStyle: StyleProps;\n  setNativeProps?: (style: StyleProps) => void;\n  props: Record<string, string | number>;\n  _touchableNode: {\n    setAttribute: (key: string, props: unknown) => void;\n  };\n  reanimatedDummy?: boolean;\n  removedAfterAnimation?: boolean;\n}\n\nexport const _updatePropsJS = (\n  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n  updates: StyleProps | AnimatedStyle<any>,\n  viewRef: (JSReanimatedComponent | ReanimatedHTMLElement) & {\n    getAnimatableRef?: () => JSReanimatedComponent | ReanimatedHTMLElement;\n  },\n  isAnimatedProps?: boolean\n): void => {\n  if (viewRef) {\n    const component = viewRef.getAnimatableRef\n      ? viewRef.getAnimatableRef()\n      : viewRef;\n    const [rawStyles] = Object.keys(updates).reduce(\n      (acc: [StyleProps, AnimatedStyle<any>], key) => {\n        const value = updates[key];\n        const index = typeof value === 'function' ? 1 : 0;\n        acc[index][key] = value;\n        return acc;\n      },\n      [{}, {}]\n    );\n\n    if (typeof component.setNativeProps === 'function') {\n      // This is the legacy way to update props on React Native Web <= 0.18.\n      // Also, some components (e.g. from react-native-svg) don't have styles\n      // and always provide setNativeProps function instead (even on React Native Web 0.19+).\n      setNativeProps(component, rawStyles, isAnimatedProps);\n    } else if (\n      createReactDOMStyle !== undefined &&\n      component.style !== undefined\n    ) {\n      // React Native Web 0.19+ no longer provides setNativeProps function,\n      // so we need to update DOM nodes directly.\n      updatePropsDOM(component, rawStyles, isAnimatedProps);\n    } else if (Object.keys(component.props).length > 0) {\n      Object.keys(component.props).forEach((key) => {\n        if (!rawStyles[key]) {\n          return;\n        }\n        const dashedKey = key.replace(/[A-Z]/g, (m) => '-' + m.toLowerCase());\n        component._touchableNode.setAttribute(dashedKey, rawStyles[key]);\n      });\n    } else {\n      const componentName =\n        'className' in component ? component?.className : '';\n      console.warn(\n        `[Reanimated] It's not possible to manipulate the component ${componentName}`\n      );\n    }\n  }\n};\n\nconst setNativeProps = (\n  component: JSReanimatedComponent | ReanimatedHTMLElement,\n  newProps: StyleProps,\n  isAnimatedProps?: boolean\n): void => {\n  if (isAnimatedProps) {\n    const uiProps: Record<string, unknown> = {};\n    for (const key in newProps) {\n      if (isNativeProp(key)) {\n        uiProps[key] = newProps[key];\n      }\n    }\n    // Only update UI props directly on the component,\n    // other props can be updated as standard style props.\n    component.setNativeProps?.(uiProps);\n  }\n\n  const previousStyle = component.previousStyle ? component.previousStyle : {};\n  const currentStyle = { ...previousStyle, ...newProps };\n  component.previousStyle = currentStyle;\n\n  component.setNativeProps?.({ style: currentStyle });\n};\n\nconst updatePropsDOM = (\n  component: JSReanimatedComponent | HTMLElement,\n  style: StyleProps,\n  isAnimatedProps?: boolean\n): void => {\n  const previousStyle = (component as JSReanimatedComponent).previousStyle\n    ? (component as JSReanimatedComponent).previousStyle\n    : {};\n  const currentStyle = { ...previousStyle, ...style };\n  (component as JSReanimatedComponent).previousStyle = currentStyle;\n\n  const domStyle = createReactDOMStyle(currentStyle);\n  if (Array.isArray(domStyle.transform) && createTransformValue !== undefined) {\n    domStyle.transform = createTransformValue(domStyle.transform);\n  }\n\n  if (\n    createTextShadowValue !== undefined &&\n    (domStyle.textShadowColor ||\n      domStyle.textShadowRadius ||\n      domStyle.textShadowOffset)\n  ) {\n    domStyle.textShadow = createTextShadowValue({\n      textShadowColor: domStyle.textShadowColor,\n      textShadowOffset: domStyle.textShadowOffset,\n      textShadowRadius: domStyle.textShadowRadius,\n    });\n  }\n\n  for (const key in domStyle) {\n    if (isAnimatedProps) {\n      (component as HTMLElement).setAttribute(key, domStyle[key]);\n    } else {\n      (component.style as StyleProps)[key] = domStyle[key];\n    }\n  }\n};\n\nfunction isNativeProp(propName: string): boolean {\n  return !!PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST[propName];\n}\n\nexport default reanimatedJS;\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,YAAY,MAAM,gBAAgB;AAEzC,SACEC,mBAAmB,EACnBC,oBAAoB,EACpBC,qBAAqB,QAChB,YAAY;AACnB,SAASC,eAAe,QAAQ,oBAAoB;AAEpD,MAAMC,YAAY,GAAG,IAAIL,YAAY,CAAC,CAAC;AAEvCM,MAAM,CAACC,mBAAmB,GAAG,MAAM;EACjC,MAAM,IAAIC,KAAK,CACb,0EACF,CAAC;AACH,CAAC;AAEDF,MAAM,CAACG,aAAa,GAAG,MAAM;EAC3B,MAAM,IAAID,KAAK,CACb,oEACF,CAAC;AACH,CAAC;AAEDF,MAAM,CAACI,kBAAkB,GAAG,MAAM;EAChC,MAAM,IAAIF,KAAK,CACb,yEACF,CAAC;AACH,CAAC;AAuBD,OAAO,MAAMG,cAAc,GAAGA,CAE5BC,OAAwC,EACxCC,OAEC,EACDC,eAAyB,KAChB;EACT,IAAID,OAAO,EAAE;IACX,MAAME,SAAS,GAAGF,OAAO,CAACG,gBAAgB,GACtCH,OAAO,CAACG,gBAAgB,CAAC,CAAC,GAC1BH,OAAO;IACX,MAAM,CAACI,SAAS,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAACQ,MAAM,CAC7C,CAACC,GAAqC,EAAEC,GAAG,KAAK;MAC9C,MAAMC,KAAK,GAAGX,OAAO,CAACU,GAAG,CAAC;MAC1B,MAAME,KAAK,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC;MACjDF,GAAG,CAACG,KAAK,CAAC,CAACF,GAAG,CAAC,GAAGC,KAAK;MACvB,OAAOF,GAAG;IACZ,CAAC,EACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACT,CAAC;IAED,IAAI,OAAON,SAAS,CAACU,cAAc,KAAK,UAAU,EAAE;MAClD;MACA;MACA;MACAA,cAAc,CAACV,SAAS,EAAEE,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IACLb,mBAAmB,KAAKyB,SAAS,IACjCX,SAAS,CAACY,KAAK,KAAKD,SAAS,EAC7B;MACA;MACA;MACAE,cAAc,CAACb,SAAS,EAAEE,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IAAII,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACc,KAAK,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAClDZ,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACc,KAAK,CAAC,CAACE,OAAO,CAAET,GAAG,IAAK;QAC5C,IAAI,CAACL,SAAS,CAACK,GAAG,CAAC,EAAE;UACnB;QACF;QACA,MAAMU,SAAS,GAAGV,GAAG,CAACW,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;QACrEpB,SAAS,CAACqB,cAAc,CAACC,YAAY,CAACL,SAAS,EAAEf,SAAS,CAACK,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMgB,aAAa,GACjB,WAAW,IAAIvB,SAAS,GAAGA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwB,SAAS,GAAG,EAAE;MACtDC,OAAO,CAACC,IAAI,CACT,8DAA6DH,aAAc,EAC9E,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMb,cAAc,GAAGA,CACrBV,SAAwD,EACxD2B,QAAoB,EACpB5B,eAAyB,KAChB;EAAA,IAAA6B,sBAAA;EACT,IAAI7B,eAAe,EAAE;IAAA,IAAA8B,qBAAA;IACnB,MAAMC,OAAgC,GAAG,CAAC,CAAC;IAC3C,KAAK,MAAMvB,GAAG,IAAIoB,QAAQ,EAAE;MAC1B,IAAII,YAAY,CAACxB,GAAG,CAAC,EAAE;QACrBuB,OAAO,CAACvB,GAAG,CAAC,GAAGoB,QAAQ,CAACpB,GAAG,CAAC;MAC9B;IACF;IACA;IACA;IACA,CAAAsB,qBAAA,GAAA7B,SAAS,CAACU,cAAc,cAAAmB,qBAAA,eAAxBA,qBAAA,CAAAG,IAAA,CAAAhC,SAAS,EAAkB8B,OAAO,CAAC;EACrC;EAEA,MAAMG,aAAa,GAAGjC,SAAS,CAACiC,aAAa,GAAGjC,SAAS,CAACiC,aAAa,GAAG,CAAC,CAAC;EAC5E,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGN;EAAS,CAAC;EACtD3B,SAAS,CAACiC,aAAa,GAAGC,YAAY;EAEtC,CAAAN,sBAAA,GAAA5B,SAAS,CAACU,cAAc,cAAAkB,sBAAA,eAAxBA,sBAAA,CAAAI,IAAA,CAAAhC,SAAS,EAAkB;IAAEY,KAAK,EAAEsB;EAAa,CAAC,CAAC;AACrD,CAAC;AAED,MAAMrB,cAAc,GAAGA,CACrBb,SAA8C,EAC9CY,KAAiB,EACjBb,eAAyB,KAChB;EACT,MAAMkC,aAAa,GAAIjC,SAAS,CAA2BiC,aAAa,GACnEjC,SAAS,CAA2BiC,aAAa,GAClD,CAAC,CAAC;EACN,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGrB;EAAM,CAAC;EAClDZ,SAAS,CAA2BiC,aAAa,GAAGC,YAAY;EAEjE,MAAMC,QAAQ,GAAGjD,mBAAmB,CAACgD,YAAY,CAAC;EAClD,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACG,SAAS,CAAC,IAAInD,oBAAoB,KAAKwB,SAAS,EAAE;IAC3EwB,QAAQ,CAACG,SAAS,GAAGnD,oBAAoB,CAACgD,QAAQ,CAACG,SAAS,CAAC;EAC/D;EAEA,IACElD,qBAAqB,KAAKuB,SAAS,KAClCwB,QAAQ,CAACI,eAAe,IACvBJ,QAAQ,CAACK,gBAAgB,IACzBL,QAAQ,CAACM,gBAAgB,CAAC,EAC5B;IACAN,QAAQ,CAACO,UAAU,GAAGtD,qBAAqB,CAAC;MAC1CmD,eAAe,EAAEJ,QAAQ,CAACI,eAAe;MACzCE,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;MAC3CD,gBAAgB,EAAEL,QAAQ,CAACK;IAC7B,CAAC,CAAC;EACJ;EAEA,KAAK,MAAMjC,GAAG,IAAI4B,QAAQ,EAAE;IAC1B,IAAIpC,eAAe,EAAE;MAClBC,SAAS,CAAiBsB,YAAY,CAACf,GAAG,EAAE4B,QAAQ,CAAC5B,GAAG,CAAC,CAAC;IAC7D,CAAC,MAAM;MACJP,SAAS,CAACY,KAAK,CAAgBL,GAAG,CAAC,GAAG4B,QAAQ,CAAC5B,GAAG,CAAC;IACtD;EACF;AACF,CAAC;AAED,SAASwB,YAAYA,CAACY,QAAgB,EAAW;EAC/C,OAAO,CAAC,CAACtD,eAAe,CAACuD,6BAA6B,CAACD,QAAQ,CAAC;AAClE;AAEA,eAAerD,YAAY", "ignoreList": []}