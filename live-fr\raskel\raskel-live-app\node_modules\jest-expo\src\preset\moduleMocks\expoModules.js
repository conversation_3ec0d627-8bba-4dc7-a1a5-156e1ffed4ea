module.exports = {
  NativeUnimoduleProxy: {
    callMethod: { type: 'function', functionType: 'promise' },
    exportedMethods: {
      type: 'object',
      mock: {
        EASClient: [],
        ExpoAppleAuthentication: [
          { name: 'formatFullName', argumentsCount: 2, key: 'formatFullName' },
          { name: 'getCredentialStateAsync', argumentsCount: 1, key: 'getCredentialStateAsync' },
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'requestAsync', argumentsCount: 1, key: 'requestAsync' },
        ],
        ExpoApplication: [
          {
            name: 'getApplicationReleaseTypeAsync',
            argumentsCount: 0,
            key: 'getApplicationReleaseTypeAsync',
          },
          { name: 'getInstallationTimeAsync', argumentsCount: 0, key: 'getInstallationTimeAsync' },
          { name: 'getIosIdForVendorAsync', argumentsCount: 0, key: 'getIosIdForVendorAsync' },
          {
            name: 'getPushNotificationServiceEnvironmentAsync',
            argumentsCount: 0,
            key: 'getPushNotificationServiceEnvironmentAsync',
          },
        ],
        ExpoAsset: [{ name: 'downloadAsync', argumentsCount: 3, key: 'downloadAsync' }],
        ExpoAudio: [
          {
            name: 'getRecordingPermissionsAsync',
            argumentsCount: 0,
            key: 'getRecordingPermissionsAsync',
          },
          {
            name: 'requestRecordingPermissionsAsync',
            argumentsCount: 0,
            key: 'requestRecordingPermissionsAsync',
          },
          { name: 'setAudioModeAsync', argumentsCount: 1, key: 'setAudioModeAsync' },
          { name: 'setIsAudioActiveAsync', argumentsCount: 1, key: 'setIsAudioActiveAsync' },
        ],
        ExpoBackgroundFetch: [
          { name: 'getStatusAsync', argumentsCount: 0, key: 'getStatusAsync' },
          { name: 'registerTaskAsync', argumentsCount: 2, key: 'registerTaskAsync' },
          { name: 'setMinimumIntervalAsync', argumentsCount: 1, key: 'setMinimumIntervalAsync' },
          { name: 'unregisterTaskAsync', argumentsCount: 1, key: 'unregisterTaskAsync' },
        ],
        ExpoBackgroundNotificationTasksModule: [
          { name: 'registerTaskAsync', argumentsCount: 1, key: 'registerTaskAsync' },
          { name: 'unregisterTaskAsync', argumentsCount: 1, key: 'unregisterTaskAsync' },
        ],
        ExpoBackgroundTask: [
          { name: 'getStatusAsync', argumentsCount: 0, key: 'getStatusAsync' },
          { name: 'registerTaskAsync', argumentsCount: 2, key: 'registerTaskAsync' },
          {
            name: 'triggerTaskWorkerForTestingAsync',
            argumentsCount: 0,
            key: 'triggerTaskWorkerForTestingAsync',
          },
          { name: 'unregisterTaskAsync', argumentsCount: 1, key: 'unregisterTaskAsync' },
        ],
        ExpoBadgeModule: [
          { name: 'getBadgeCountAsync', argumentsCount: 0, key: 'getBadgeCountAsync' },
          { name: 'setBadgeCountAsync', argumentsCount: 1, key: 'setBadgeCountAsync' },
        ],
        ExpoBarometer: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'setUpdateInterval', argumentsCount: 1, key: 'setUpdateInterval' },
        ],
        ExpoBattery: [
          { name: 'getBatteryLevelAsync', argumentsCount: 0, key: 'getBatteryLevelAsync' },
          { name: 'getBatteryStateAsync', argumentsCount: 0, key: 'getBatteryStateAsync' },
          {
            name: 'isLowPowerModeEnabledAsync',
            argumentsCount: 0,
            key: 'isLowPowerModeEnabledAsync',
          },
        ],
        ExpoBlurView: [],
        ExpoBrightness: [
          { name: 'getBrightnessAsync', argumentsCount: 0, key: 'getBrightnessAsync' },
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'getSystemBrightnessAsync', argumentsCount: 0, key: 'getSystemBrightnessAsync' },
          {
            name: 'getSystemBrightnessModeAsync',
            argumentsCount: 0,
            key: 'getSystemBrightnessModeAsync',
          },
          {
            name: 'isUsingSystemBrightnessAsync',
            argumentsCount: 0,
            key: 'isUsingSystemBrightnessAsync',
          },
          { name: 'requestPermissionsAsync', argumentsCount: 0, key: 'requestPermissionsAsync' },
          { name: 'setBrightnessAsync', argumentsCount: 1, key: 'setBrightnessAsync' },
          { name: 'setSystemBrightnessAsync', argumentsCount: 0, key: 'setSystemBrightnessAsync' },
          {
            name: 'setSystemBrightnessModeAsync',
            argumentsCount: 0,
            key: 'setSystemBrightnessModeAsync',
          },
          { name: 'useSystemBrightnessAsync', argumentsCount: 0, key: 'useSystemBrightnessAsync' },
        ],
        ExpoCalendar: [
          {
            name: 'createEventInCalendarAsync',
            argumentsCount: 1,
            key: 'createEventInCalendarAsync',
          },
          { name: 'deleteCalendarAsync', argumentsCount: 1, key: 'deleteCalendarAsync' },
          { name: 'deleteEventAsync', argumentsCount: 2, key: 'deleteEventAsync' },
          { name: 'deleteReminderAsync', argumentsCount: 1, key: 'deleteReminderAsync' },
          { name: 'editEventInCalendarAsync', argumentsCount: 1, key: 'editEventInCalendarAsync' },
          {
            name: 'getAttendeesForEventAsync',
            argumentsCount: 1,
            key: 'getAttendeesForEventAsync',
          },
          {
            name: 'getCalendarPermissionsAsync',
            argumentsCount: 0,
            key: 'getCalendarPermissionsAsync',
          },
          { name: 'getCalendarsAsync', argumentsCount: 1, key: 'getCalendarsAsync' },
          { name: 'getDefaultCalendarAsync', argumentsCount: 0, key: 'getDefaultCalendarAsync' },
          { name: 'getEventByIdAsync', argumentsCount: 2, key: 'getEventByIdAsync' },
          { name: 'getEventsAsync', argumentsCount: 3, key: 'getEventsAsync' },
          { name: 'getReminderByIdAsync', argumentsCount: 1, key: 'getReminderByIdAsync' },
          { name: 'getRemindersAsync', argumentsCount: 4, key: 'getRemindersAsync' },
          {
            name: 'getRemindersPermissionsAsync',
            argumentsCount: 0,
            key: 'getRemindersPermissionsAsync',
          },
          { name: 'getSourceByIdAsync', argumentsCount: 1, key: 'getSourceByIdAsync' },
          { name: 'getSourcesAsync', argumentsCount: 0, key: 'getSourcesAsync' },
          { name: 'openEventInCalendarAsync', argumentsCount: 1, key: 'openEventInCalendarAsync' },
          {
            name: 'requestCalendarPermissionsAsync',
            argumentsCount: 0,
            key: 'requestCalendarPermissionsAsync',
          },
          {
            name: 'requestRemindersPermissionsAsync',
            argumentsCount: 0,
            key: 'requestRemindersPermissionsAsync',
          },
          { name: 'saveCalendarAsync', argumentsCount: 1, key: 'saveCalendarAsync' },
          { name: 'saveEventAsync', argumentsCount: 2, key: 'saveEventAsync' },
          { name: 'saveReminderAsync', argumentsCount: 1, key: 'saveReminderAsync' },
        ],
        ExpoCamera: [
          { name: 'dismissScanner', argumentsCount: 0, key: 'dismissScanner' },
          {
            name: 'getAvailableVideoCodecsAsync',
            argumentsCount: 0,
            key: 'getAvailableVideoCodecsAsync',
          },
          {
            name: 'getCameraPermissionsAsync',
            argumentsCount: 0,
            key: 'getCameraPermissionsAsync',
          },
          {
            name: 'getMicrophonePermissionsAsync',
            argumentsCount: 0,
            key: 'getMicrophonePermissionsAsync',
          },
          { name: 'launchScanner', argumentsCount: 1, key: 'launchScanner' },
          {
            name: 'requestCameraPermissionsAsync',
            argumentsCount: 0,
            key: 'requestCameraPermissionsAsync',
          },
          {
            name: 'requestMicrophonePermissionsAsync',
            argumentsCount: 0,
            key: 'requestMicrophonePermissionsAsync',
          },
          { name: 'scanFromURLAsync', argumentsCount: 2, key: 'scanFromURLAsync' },
        ],
        ExpoCellular: [
          { name: 'allowsVoipAsync', argumentsCount: 0, key: 'allowsVoipAsync' },
          { name: 'getCarrierNameAsync', argumentsCount: 0, key: 'getCarrierNameAsync' },
          {
            name: 'getCellularGenerationAsync',
            argumentsCount: 0,
            key: 'getCellularGenerationAsync',
          },
          { name: 'getIsoCountryCodeAsync', argumentsCount: 0, key: 'getIsoCountryCodeAsync' },
          {
            name: 'getMobileCountryCodeAsync',
            argumentsCount: 0,
            key: 'getMobileCountryCodeAsync',
          },
          {
            name: 'getMobileNetworkCodeAsync',
            argumentsCount: 0,
            key: 'getMobileNetworkCodeAsync',
          },
        ],
        ExpoContactAccessButton: [],
        ExpoContacts: [
          { name: 'addContactAsync', argumentsCount: 2, key: 'addContactAsync' },
          {
            name: 'addExistingContactToGroupAsync',
            argumentsCount: 2,
            key: 'addExistingContactToGroupAsync',
          },
          {
            name: 'addExistingGroupToContainerAsync',
            argumentsCount: 2,
            key: 'addExistingGroupToContainerAsync',
          },
          { name: 'createGroupAsync', argumentsCount: 2, key: 'createGroupAsync' },
          { name: 'dismissFormAsync', argumentsCount: 0, key: 'dismissFormAsync' },
          { name: 'getContactsAsync', argumentsCount: 1, key: 'getContactsAsync' },
          { name: 'getContainersAsync', argumentsCount: 1, key: 'getContainersAsync' },
          {
            name: 'getDefaultContainerIdentifierAsync',
            argumentsCount: 0,
            key: 'getDefaultContainerIdentifierAsync',
          },
          { name: 'getGroupsAsync', argumentsCount: 1, key: 'getGroupsAsync' },
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'presentAccessPickerAsync', argumentsCount: 0, key: 'presentAccessPickerAsync' },
          {
            name: 'presentContactPickerAsync',
            argumentsCount: 0,
            key: 'presentContactPickerAsync',
          },
          { name: 'presentFormAsync', argumentsCount: 3, key: 'presentFormAsync' },
          { name: 'removeContactAsync', argumentsCount: 1, key: 'removeContactAsync' },
          {
            name: 'removeContactFromGroupAsync',
            argumentsCount: 2,
            key: 'removeContactFromGroupAsync',
          },
          { name: 'removeGroupAsync', argumentsCount: 1, key: 'removeGroupAsync' },
          { name: 'requestPermissionsAsync', argumentsCount: 0, key: 'requestPermissionsAsync' },
          { name: 'updateContactAsync', argumentsCount: 1, key: 'updateContactAsync' },
          { name: 'updateGroupNameAsync', argumentsCount: 2, key: 'updateGroupNameAsync' },
          { name: 'writeContactToFileAsync', argumentsCount: 1, key: 'writeContactToFileAsync' },
        ],
        ExpoDevice: [
          { name: 'getDeviceTypeAsync', argumentsCount: 0, key: 'getDeviceTypeAsync' },
          { name: 'getUptimeAsync', argumentsCount: 0, key: 'getUptimeAsync' },
          {
            name: 'isRootedExperimentalAsync',
            argumentsCount: 0,
            key: 'isRootedExperimentalAsync',
          },
        ],
        ExpoDocumentPicker: [
          { name: 'getDocumentAsync', argumentsCount: 1, key: 'getDocumentAsync' },
        ],
        ExpoDomWebViewModule: [
          { name: 'evalJsForWebViewAsync', argumentsCount: 2, key: 'evalJsForWebViewAsync' },
        ],
        ExpoFetchModule: [],
        ExpoFontLoader: [
          { name: 'getLoadedFonts', argumentsCount: 0, key: 'getLoadedFonts' },
          { name: 'loadAsync', argumentsCount: 2, key: 'loadAsync' },
        ],
        ExpoFontUtils: [
          { name: 'renderToImageAsync', argumentsCount: 2, key: 'renderToImageAsync' },
        ],
        ExpoGo: [{ name: 'getModulesSchema', argumentsCount: 0, key: 'getModulesSchema' }],
        ExpoHaptics: [
          { name: 'impactAsync', argumentsCount: 1, key: 'impactAsync' },
          { name: 'notificationAsync', argumentsCount: 1, key: 'notificationAsync' },
          { name: 'selectionAsync', argumentsCount: 0, key: 'selectionAsync' },
        ],
        ExpoHead: [
          { name: 'clearActivitiesAsync', argumentsCount: 1, key: 'clearActivitiesAsync' },
          { name: 'createActivity', argumentsCount: 1, key: 'createActivity' },
          { name: 'getLaunchActivity', argumentsCount: 0, key: 'getLaunchActivity' },
          { name: 'revokeActivity', argumentsCount: 1, key: 'revokeActivity' },
          { name: 'suspendActivity', argumentsCount: 1, key: 'suspendActivity' },
        ],
        ExpoImage: [
          { name: 'clearDiskCache', argumentsCount: 0, key: 'clearDiskCache' },
          { name: 'clearMemoryCache', argumentsCount: 0, key: 'clearMemoryCache' },
          { name: 'generateBlurhashAsync', argumentsCount: 2, key: 'generateBlurhashAsync' },
          { name: 'generateThumbhashAsync', argumentsCount: 1, key: 'generateThumbhashAsync' },
          { name: 'getCachePathAsync', argumentsCount: 1, key: 'getCachePathAsync' },
          { name: 'loadAsync', argumentsCount: 2, key: 'loadAsync' },
          { name: 'prefetch', argumentsCount: 3, key: 'prefetch' },
        ],
        ExpoImageManipulator: [{ name: 'manipulate', argumentsCount: 1, key: 'manipulate' }],
        ExpoKeepAwake: [
          { name: 'activate', argumentsCount: 1, key: 'activate' },
          { name: 'deactivate', argumentsCount: 1, key: 'deactivate' },
          { name: 'isActivated', argumentsCount: 0, key: 'isActivated' },
        ],
        ExpoLinearGradient: [],
        ExpoLivePhoto: [],
        ExpoLocalAuthentication: [
          { name: 'authenticateAsync', argumentsCount: 1, key: 'authenticateAsync' },
          { name: 'getEnrolledLevelAsync', argumentsCount: 0, key: 'getEnrolledLevelAsync' },
          { name: 'hasHardwareAsync', argumentsCount: 0, key: 'hasHardwareAsync' },
          { name: 'isEnrolledAsync', argumentsCount: 0, key: 'isEnrolledAsync' },
          {
            name: 'supportedAuthenticationTypesAsync',
            argumentsCount: 0,
            key: 'supportedAuthenticationTypesAsync',
          },
        ],
        ExpoLocation: [
          { name: 'geocodeAsync', argumentsCount: 1, key: 'geocodeAsync' },
          {
            name: 'getBackgroundPermissionsAsync',
            argumentsCount: 0,
            key: 'getBackgroundPermissionsAsync',
          },
          { name: 'getCurrentPositionAsync', argumentsCount: 1, key: 'getCurrentPositionAsync' },
          {
            name: 'getForegroundPermissionsAsync',
            argumentsCount: 0,
            key: 'getForegroundPermissionsAsync',
          },
          {
            name: 'getLastKnownPositionAsync',
            argumentsCount: 1,
            key: 'getLastKnownPositionAsync',
          },
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'getProviderStatusAsync', argumentsCount: 0, key: 'getProviderStatusAsync' },
          { name: 'hasServicesEnabledAsync', argumentsCount: 0, key: 'hasServicesEnabledAsync' },
          {
            name: 'hasStartedGeofencingAsync',
            argumentsCount: 1,
            key: 'hasStartedGeofencingAsync',
          },
          {
            name: 'hasStartedLocationUpdatesAsync',
            argumentsCount: 1,
            key: 'hasStartedLocationUpdatesAsync',
          },
          { name: 'removeWatchAsync', argumentsCount: 1, key: 'removeWatchAsync' },
          {
            name: 'requestBackgroundPermissionsAsync',
            argumentsCount: 0,
            key: 'requestBackgroundPermissionsAsync',
          },
          {
            name: 'requestForegroundPermissionsAsync',
            argumentsCount: 0,
            key: 'requestForegroundPermissionsAsync',
          },
          { name: 'requestPermissionsAsync', argumentsCount: 0, key: 'requestPermissionsAsync' },
          { name: 'reverseGeocodeAsync', argumentsCount: 1, key: 'reverseGeocodeAsync' },
          { name: 'startGeofencingAsync', argumentsCount: 2, key: 'startGeofencingAsync' },
          {
            name: 'startLocationUpdatesAsync',
            argumentsCount: 2,
            key: 'startLocationUpdatesAsync',
          },
          { name: 'stopGeofencingAsync', argumentsCount: 1, key: 'stopGeofencingAsync' },
          { name: 'stopLocationUpdatesAsync', argumentsCount: 1, key: 'stopLocationUpdatesAsync' },
          { name: 'watchDeviceHeading', argumentsCount: 1, key: 'watchDeviceHeading' },
          { name: 'watchPositionImplAsync', argumentsCount: 2, key: 'watchPositionImplAsync' },
        ],
        ExpoMailComposer: [
          { name: 'composeAsync', argumentsCount: 1, key: 'composeAsync' },
          { name: 'getClients', argumentsCount: 0, key: 'getClients' },
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
        ],
        ExpoMediaLibrary: [
          { name: 'addAssetsToAlbumAsync', argumentsCount: 2, key: 'addAssetsToAlbumAsync' },
          { name: 'createAlbumAsync', argumentsCount: 3, key: 'createAlbumAsync' },
          { name: 'createAssetAsync', argumentsCount: 2, key: 'createAssetAsync' },
          { name: 'deleteAlbumsAsync', argumentsCount: 2, key: 'deleteAlbumsAsync' },
          { name: 'deleteAssetsAsync', argumentsCount: 1, key: 'deleteAssetsAsync' },
          { name: 'getAlbumAsync', argumentsCount: 1, key: 'getAlbumAsync' },
          { name: 'getAlbumsAsync', argumentsCount: 1, key: 'getAlbumsAsync' },
          { name: 'getAssetInfoAsync', argumentsCount: 2, key: 'getAssetInfoAsync' },
          { name: 'getAssetsAsync', argumentsCount: 1, key: 'getAssetsAsync' },
          { name: 'getMomentsAsync', argumentsCount: 0, key: 'getMomentsAsync' },
          { name: 'getPermissionsAsync', argumentsCount: 1, key: 'getPermissionsAsync' },
          {
            name: 'presentPermissionsPickerAsync',
            argumentsCount: 0,
            key: 'presentPermissionsPickerAsync',
          },
          {
            name: 'removeAssetsFromAlbumAsync',
            argumentsCount: 2,
            key: 'removeAssetsFromAlbumAsync',
          },
          { name: 'requestPermissionsAsync', argumentsCount: 1, key: 'requestPermissionsAsync' },
          { name: 'saveToLibraryAsync', argumentsCount: 1, key: 'saveToLibraryAsync' },
        ],
        ExpoMeshGradient: [],
        ExponentAccelerometer: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'setUpdateInterval', argumentsCount: 1, key: 'setUpdateInterval' },
        ],
        ExponentConstants: [
          { name: 'getWebViewUserAgentAsync', argumentsCount: 0, key: 'getWebViewUserAgentAsync' },
        ],
        ExponentDeviceMotion: [
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'requestPermissionsAsync', argumentsCount: 0, key: 'requestPermissionsAsync' },
          { name: 'setUpdateInterval', argumentsCount: 1, key: 'setUpdateInterval' },
        ],
        ExponentFileSystem: [
          { name: 'copyAsync', argumentsCount: 1, key: 'copyAsync' },
          { name: 'deleteAsync', argumentsCount: 2, key: 'deleteAsync' },
          { name: 'downloadAsync', argumentsCount: 3, key: 'downloadAsync' },
          {
            name: 'downloadResumablePauseAsync',
            argumentsCount: 1,
            key: 'downloadResumablePauseAsync',
          },
          {
            name: 'downloadResumableStartAsync',
            argumentsCount: 5,
            key: 'downloadResumableStartAsync',
          },
          { name: 'getFreeDiskStorageAsync', argumentsCount: 0, key: 'getFreeDiskStorageAsync' },
          { name: 'getInfoAsync', argumentsCount: 2, key: 'getInfoAsync' },
          {
            name: 'getTotalDiskCapacityAsync',
            argumentsCount: 0,
            key: 'getTotalDiskCapacityAsync',
          },
          { name: 'makeDirectoryAsync', argumentsCount: 2, key: 'makeDirectoryAsync' },
          { name: 'moveAsync', argumentsCount: 1, key: 'moveAsync' },
          { name: 'networkTaskCancelAsync', argumentsCount: 1, key: 'networkTaskCancelAsync' },
          { name: 'readAsStringAsync', argumentsCount: 2, key: 'readAsStringAsync' },
          { name: 'readDirectoryAsync', argumentsCount: 1, key: 'readDirectoryAsync' },
          { name: 'uploadAsync', argumentsCount: 3, key: 'uploadAsync' },
          { name: 'uploadTaskStartAsync', argumentsCount: 4, key: 'uploadTaskStartAsync' },
          { name: 'writeAsStringAsync', argumentsCount: 3, key: 'writeAsStringAsync' },
        ],
        ExponentGLView: [],
        ExponentGyroscope: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'setUpdateInterval', argumentsCount: 1, key: 'setUpdateInterval' },
        ],
        ExponentImagePicker: [
          {
            name: 'getCameraPermissionsAsync',
            argumentsCount: 0,
            key: 'getCameraPermissionsAsync',
          },
          {
            name: 'getMediaLibraryPermissionsAsync',
            argumentsCount: 1,
            key: 'getMediaLibraryPermissionsAsync',
          },
          { name: 'launchCameraAsync', argumentsCount: 1, key: 'launchCameraAsync' },
          { name: 'launchImageLibraryAsync', argumentsCount: 1, key: 'launchImageLibraryAsync' },
          {
            name: 'requestCameraPermissionsAsync',
            argumentsCount: 0,
            key: 'requestCameraPermissionsAsync',
          },
          {
            name: 'requestMediaLibraryPermissionsAsync',
            argumentsCount: 1,
            key: 'requestMediaLibraryPermissionsAsync',
          },
        ],
        ExponentMagnetometer: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'setUpdateInterval', argumentsCount: 1, key: 'setUpdateInterval' },
        ],
        ExponentMagnetometerUncalibrated: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'setUpdateInterval', argumentsCount: 1, key: 'setUpdateInterval' },
        ],
        ExponentPedometer: [
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'getStepCountAsync', argumentsCount: 2, key: 'getStepCountAsync' },
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'requestPermissionsAsync', argumentsCount: 0, key: 'requestPermissionsAsync' },
        ],
        ExpoNetwork: [
          { name: 'getIpAddressAsync', argumentsCount: 0, key: 'getIpAddressAsync' },
          { name: 'getNetworkStateAsync', argumentsCount: 0, key: 'getNetworkStateAsync' },
        ],
        ExpoNotificationCategoriesModule: [
          {
            name: 'deleteNotificationCategoryAsync',
            argumentsCount: 1,
            key: 'deleteNotificationCategoryAsync',
          },
          {
            name: 'getNotificationCategoriesAsync',
            argumentsCount: 0,
            key: 'getNotificationCategoriesAsync',
          },
          {
            name: 'setNotificationCategoryAsync',
            argumentsCount: 3,
            key: 'setNotificationCategoryAsync',
          },
        ],
        ExpoNotificationPermissionsModule: [
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'requestPermissionsAsync', argumentsCount: 1, key: 'requestPermissionsAsync' },
        ],
        ExpoNotificationPresenter: [
          {
            name: 'dismissAllNotificationsAsync',
            argumentsCount: 0,
            key: 'dismissAllNotificationsAsync',
          },
          { name: 'dismissNotificationAsync', argumentsCount: 1, key: 'dismissNotificationAsync' },
          {
            name: 'getPresentedNotificationsAsync',
            argumentsCount: 0,
            key: 'getPresentedNotificationsAsync',
          },
        ],
        ExpoNotificationScheduler: [
          {
            name: 'cancelAllScheduledNotificationsAsync',
            argumentsCount: 0,
            key: 'cancelAllScheduledNotificationsAsync',
          },
          {
            name: 'cancelScheduledNotificationAsync',
            argumentsCount: 1,
            key: 'cancelScheduledNotificationAsync',
          },
          {
            name: 'getAllScheduledNotificationsAsync',
            argumentsCount: 0,
            key: 'getAllScheduledNotificationsAsync',
          },
          { name: 'getNextTriggerDateAsync', argumentsCount: 1, key: 'getNextTriggerDateAsync' },
          {
            name: 'scheduleNotificationAsync',
            argumentsCount: 3,
            key: 'scheduleNotificationAsync',
          },
        ],
        ExpoNotificationsEmitter: [
          {
            name: 'clearLastNotificationResponse',
            argumentsCount: 0,
            key: 'clearLastNotificationResponse',
          },
          {
            name: 'getLastNotificationResponse',
            argumentsCount: 0,
            key: 'getLastNotificationResponse',
          },
        ],
        ExpoNotificationsHandlerModule: [
          { name: 'handleNotificationAsync', argumentsCount: 2, key: 'handleNotificationAsync' },
        ],
        ExpoPrint: [
          { name: 'print', argumentsCount: 1, key: 'print' },
          { name: 'printToFileAsync', argumentsCount: 1, key: 'printToFileAsync' },
          { name: 'selectPrinter', argumentsCount: 0, key: 'selectPrinter' },
        ],
        ExpoPushTokenManager: [
          { name: 'getDevicePushTokenAsync', argumentsCount: 0, key: 'getDevicePushTokenAsync' },
          {
            name: 'unregisterForNotificationsAsync',
            argumentsCount: 0,
            key: 'unregisterForNotificationsAsync',
          },
        ],
        ExpoRouterNativeLinkPreview: [],
        ExpoScreenCapture: [
          { name: 'allowScreenCapture', argumentsCount: 0, key: 'allowScreenCapture' },
          {
            name: 'disableAppSwitcherProtection',
            argumentsCount: 0,
            key: 'disableAppSwitcherProtection',
          },
          {
            name: 'enableAppSwitcherProtection',
            argumentsCount: 1,
            key: 'enableAppSwitcherProtection',
          },
          { name: 'preventScreenCapture', argumentsCount: 0, key: 'preventScreenCapture' },
        ],
        ExpoScreenOrientation: [
          { name: 'getOrientationAsync', argumentsCount: 0, key: 'getOrientationAsync' },
          { name: 'getOrientationLockAsync', argumentsCount: 0, key: 'getOrientationLockAsync' },
          {
            name: 'getPlatformOrientationLockAsync',
            argumentsCount: 0,
            key: 'getPlatformOrientationLockAsync',
          },
          { name: 'lockAsync', argumentsCount: 1, key: 'lockAsync' },
          { name: 'lockPlatformAsync', argumentsCount: 1, key: 'lockPlatformAsync' },
          {
            name: 'supportsOrientationLockAsync',
            argumentsCount: 1,
            key: 'supportsOrientationLockAsync',
          },
        ],
        ExpoSecureStore: [
          {
            name: 'canUseBiometricAuthentication',
            argumentsCount: 0,
            key: 'canUseBiometricAuthentication',
          },
          { name: 'deleteValueWithKeyAsync', argumentsCount: 2, key: 'deleteValueWithKeyAsync' },
          { name: 'getValueWithKeyAsync', argumentsCount: 2, key: 'getValueWithKeyAsync' },
          { name: 'getValueWithKeySync', argumentsCount: 2, key: 'getValueWithKeySync' },
          { name: 'setValueWithKeyAsync', argumentsCount: 3, key: 'setValueWithKeyAsync' },
          { name: 'setValueWithKeySync', argumentsCount: 3, key: 'setValueWithKeySync' },
        ],
        ExpoSharing: [{ name: 'shareAsync', argumentsCount: 2, key: 'shareAsync' }],
        ExpoSMS: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'sendSMSAsync', argumentsCount: 3, key: 'sendSMSAsync' },
        ],
        ExpoSpeech: [
          { name: 'getVoices', argumentsCount: 0, key: 'getVoices' },
          { name: 'isSpeaking', argumentsCount: 0, key: 'isSpeaking' },
          { name: 'pause', argumentsCount: 0, key: 'pause' },
          { name: 'resume', argumentsCount: 0, key: 'resume' },
          { name: 'speak', argumentsCount: 3, key: 'speak' },
          { name: 'stop', argumentsCount: 0, key: 'stop' },
        ],
        ExpoSQLite: [
          { name: 'backupDatabaseAsync', argumentsCount: 4, key: 'backupDatabaseAsync' },
          { name: 'backupDatabaseSync', argumentsCount: 4, key: 'backupDatabaseSync' },
          { name: 'deleteDatabaseAsync', argumentsCount: 1, key: 'deleteDatabaseAsync' },
          { name: 'deleteDatabaseSync', argumentsCount: 1, key: 'deleteDatabaseSync' },
          {
            name: 'ensureDatabasePathExistsAsync',
            argumentsCount: 1,
            key: 'ensureDatabasePathExistsAsync',
          },
          {
            name: 'ensureDatabasePathExistsSync',
            argumentsCount: 1,
            key: 'ensureDatabasePathExistsSync',
          },
          { name: 'importAssetDatabaseAsync', argumentsCount: 3, key: 'importAssetDatabaseAsync' },
        ],
        ExpoStoreReview: [
          { name: 'isAvailableAsync', argumentsCount: 0, key: 'isAvailableAsync' },
          { name: 'requestReview', argumentsCount: 0, key: 'requestReview' },
        ],
        ExpoSystemUI: [
          { name: 'getBackgroundColorAsync', argumentsCount: 0, key: 'getBackgroundColorAsync' },
          { name: 'setBackgroundColorAsync', argumentsCount: 1, key: 'setBackgroundColorAsync' },
        ],
        ExpoTrackingTransparency: [
          { name: 'getAdvertisingId', argumentsCount: 0, key: 'getAdvertisingId' },
          { name: 'getPermissionsAsync', argumentsCount: 0, key: 'getPermissionsAsync' },
          { name: 'requestPermissionsAsync', argumentsCount: 0, key: 'requestPermissionsAsync' },
        ],
        ExpoUpdates: [
          { name: 'checkForUpdateAsync', argumentsCount: 0, key: 'checkForUpdateAsync' },
          { name: 'clearLogEntriesAsync', argumentsCount: 0, key: 'clearLogEntriesAsync' },
          { name: 'fetchUpdateAsync', argumentsCount: 0, key: 'fetchUpdateAsync' },
          { name: 'getExtraParamsAsync', argumentsCount: 0, key: 'getExtraParamsAsync' },
          { name: 'readLogEntriesAsync', argumentsCount: 1, key: 'readLogEntriesAsync' },
          { name: 'reload', argumentsCount: 0, key: 'reload' },
          { name: 'setExtraParamAsync', argumentsCount: 2, key: 'setExtraParamAsync' },
        ],
        ExpoVideo: [
          { name: 'clearVideoCacheAsync', argumentsCount: 0, key: 'clearVideoCacheAsync' },
          { name: 'getCurrentVideoCacheSize', argumentsCount: 0, key: 'getCurrentVideoCacheSize' },
          {
            name: 'isPictureInPictureSupported',
            argumentsCount: 0,
            key: 'isPictureInPictureSupported',
          },
          { name: 'setVideoCacheSizeAsync', argumentsCount: 1, key: 'setVideoCacheSizeAsync' },
        ],
        ExpoVideoThumbnails: [{ name: 'getThumbnail', argumentsCount: 2, key: 'getThumbnail' }],
        ExpoVideoView: [{ name: 'setFullscreen', argumentsCount: 2, key: 'setFullscreen' }],
        ExpoWebBrowser: [
          { name: 'coolDownAsync', argumentsCount: 0, key: 'coolDownAsync' },
          { name: 'dismissAuthSession', argumentsCount: 0, key: 'dismissAuthSession' },
          { name: 'dismissBrowser', argumentsCount: 0, key: 'dismissBrowser' },
          {
            name: 'getCustomTabsSupportingBrowsers',
            argumentsCount: 0,
            key: 'getCustomTabsSupportingBrowsers',
          },
          { name: 'mayInitWithUrlAsync', argumentsCount: 0, key: 'mayInitWithUrlAsync' },
          { name: 'openAuthSessionAsync', argumentsCount: 3, key: 'openAuthSessionAsync' },
          { name: 'openBrowserAsync', argumentsCount: 2, key: 'openBrowserAsync' },
          { name: 'warmUpAsync', argumentsCount: 0, key: 'warmUpAsync' },
        ],
        FileSystem: [
          { name: 'downloadFileAsync', argumentsCount: 3, key: 'downloadFileAsync' },
          { name: 'info', argumentsCount: 1, key: 'info' },
        ],
        NotificationsServerRegistrationModule: [
          { name: 'getInstallationIdAsync', argumentsCount: 0, key: 'getInstallationIdAsync' },
          { name: 'getRegistrationInfoAsync', argumentsCount: 0, key: 'getRegistrationInfoAsync' },
          { name: 'setRegistrationInfoAsync', argumentsCount: 1, key: 'setRegistrationInfoAsync' },
        ],
        SymbolModule: [],
      },
    },
    getConstants: { type: 'function' },
    modulesConstants: {
      type: 'mock',
      mockDefinition: {
        EASClient: {
          addListener: { type: 'function' },
          clientID: { type: 'string' },
          removeListeners: { type: 'function' },
        },
        ExpoAppleAuthentication: {
          addListener: { type: 'function' },
          formatFullName: { type: 'function' },
          getCredentialStateAsync: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestAsync: { type: 'function' },
        },
        ExpoApplication: {
          addListener: { type: 'function' },
          applicationId: { type: 'string' },
          applicationName: { type: 'string' },
          getApplicationReleaseTypeAsync: { type: 'function' },
          getInstallationTimeAsync: { type: 'function' },
          getIosIdForVendorAsync: { type: 'function' },
          getPushNotificationServiceEnvironmentAsync: { type: 'function' },
          nativeApplicationVersion: { type: 'string' },
          nativeBuildVersion: { type: 'string' },
          removeListeners: { type: 'function' },
        },
        ExpoAsset: {
          addListener: { type: 'function' },
          downloadAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoAudio: {
          addListener: { type: 'function' },
          getRecordingPermissionsAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestRecordingPermissionsAsync: { type: 'function' },
          setAudioModeAsync: { type: 'function' },
          setIsAudioActiveAsync: { type: 'function' },
        },
        ExpoBackgroundFetch: {
          addListener: { type: 'function' },
          getStatusAsync: { type: 'function' },
          registerTaskAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setMinimumIntervalAsync: { type: 'function' },
          unregisterTaskAsync: { type: 'function' },
        },
        ExpoBackgroundNotificationTasksModule: {
          addListener: { type: 'function' },
          registerTaskAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          unregisterTaskAsync: { type: 'function' },
        },
        ExpoBackgroundTask: {
          addListener: { type: 'function' },
          getStatusAsync: { type: 'function' },
          registerTaskAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          triggerTaskWorkerForTestingAsync: { type: 'function' },
          unregisterTaskAsync: { type: 'function' },
        },
        ExpoBadgeModule: {
          addListener: { type: 'function' },
          getBadgeCountAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setBadgeCountAsync: { type: 'function' },
        },
        ExpoBarometer: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setUpdateInterval: { type: 'function' },
        },
        ExpoBattery: {
          addListener: { type: 'function' },
          getBatteryLevelAsync: { type: 'function' },
          getBatteryStateAsync: { type: 'function' },
          isLowPowerModeEnabledAsync: { type: 'function' },
          isSupported: { type: 'boolean', mock: false },
          removeListeners: { type: 'function' },
        },
        ExpoBlurView: { addListener: { type: 'function' }, removeListeners: { type: 'function' } },
        ExpoBrightness: {
          addListener: { type: 'function' },
          getBrightnessAsync: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          getSystemBrightnessAsync: { type: 'function' },
          getSystemBrightnessModeAsync: { type: 'function' },
          isUsingSystemBrightnessAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
          setBrightnessAsync: { type: 'function' },
          setSystemBrightnessAsync: { type: 'function' },
          setSystemBrightnessModeAsync: { type: 'function' },
          useSystemBrightnessAsync: { type: 'function' },
        },
        ExpoCalendar: {
          addListener: { type: 'function' },
          createEventInCalendarAsync: { type: 'function' },
          deleteCalendarAsync: { type: 'function' },
          deleteEventAsync: { type: 'function' },
          deleteReminderAsync: { type: 'function' },
          editEventInCalendarAsync: { type: 'function' },
          getAttendeesForEventAsync: { type: 'function' },
          getCalendarPermissionsAsync: { type: 'function' },
          getCalendarsAsync: { type: 'function' },
          getDefaultCalendarAsync: { type: 'function' },
          getEventByIdAsync: { type: 'function' },
          getEventsAsync: { type: 'function' },
          getReminderByIdAsync: { type: 'function' },
          getRemindersAsync: { type: 'function' },
          getRemindersPermissionsAsync: { type: 'function' },
          getSourceByIdAsync: { type: 'function' },
          getSourcesAsync: { type: 'function' },
          openEventInCalendarAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestCalendarPermissionsAsync: { type: 'function' },
          requestRemindersPermissionsAsync: { type: 'function' },
          saveCalendarAsync: { type: 'function' },
          saveEventAsync: { type: 'function' },
          saveReminderAsync: { type: 'function' },
        },
        ExpoCamera: {
          addListener: { type: 'function' },
          dismissScanner: { type: 'function' },
          getAvailableVideoCodecsAsync: { type: 'function' },
          getCameraPermissionsAsync: { type: 'function' },
          getMicrophonePermissionsAsync: { type: 'function' },
          isModernBarcodeScannerAvailable: { type: 'property' },
          launchScanner: { type: 'function' },
          removeListeners: { type: 'function' },
          requestCameraPermissionsAsync: { type: 'function' },
          requestMicrophonePermissionsAsync: { type: 'function' },
          scanFromURLAsync: { type: 'function' },
          toggleRecordingAsyncAvailable: { type: 'property' },
        },
        ExpoCellular: {
          addListener: { type: 'function' },
          allowsVoip: { type: 'null' },
          allowsVoipAsync: { type: 'function' },
          carrier: { type: 'null' },
          generation: { type: 'number', mock: 0 },
          getCarrierNameAsync: { type: 'function' },
          getCellularGenerationAsync: { type: 'function' },
          getIsoCountryCodeAsync: { type: 'function' },
          getMobileCountryCodeAsync: { type: 'function' },
          getMobileNetworkCodeAsync: { type: 'function' },
          isoCountryCode: { type: 'null' },
          mobileCountryCode: { type: 'null' },
          mobileNetworkCode: { type: 'null' },
          removeListeners: { type: 'function' },
        },
        ExpoContactAccessButton: {
          addListener: { type: 'function' },
          isAvailable: { type: 'property' },
          removeListeners: { type: 'function' },
        },
        ExpoContacts: {
          addContactAsync: { type: 'function' },
          addExistingContactToGroupAsync: { type: 'function' },
          addExistingGroupToContainerAsync: { type: 'function' },
          addListener: { type: 'function' },
          createGroupAsync: { type: 'function' },
          dismissFormAsync: { type: 'function' },
          getContactsAsync: { type: 'function' },
          getContainersAsync: { type: 'function' },
          getDefaultContainerIdentifierAsync: { type: 'function' },
          getGroupsAsync: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          presentAccessPickerAsync: { type: 'function' },
          presentContactPickerAsync: { type: 'function' },
          presentFormAsync: { type: 'function' },
          removeContactAsync: { type: 'function' },
          removeContactFromGroupAsync: { type: 'function' },
          removeGroupAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
          updateContactAsync: { type: 'function' },
          updateGroupNameAsync: { type: 'function' },
          writeContactToFileAsync: { type: 'function' },
        },
        ExpoDevice: {
          addListener: { type: 'function' },
          brand: { type: 'string' },
          deviceName: { type: 'string' },
          deviceType: { type: 'number', mock: 1 },
          deviceYearClass: { type: 'number', mock: 2025 },
          getDeviceTypeAsync: { type: 'function' },
          getUptimeAsync: { type: 'function' },
          isDevice: { type: 'boolean', mock: false },
          isRootedExperimentalAsync: { type: 'function' },
          manufacturer: { type: 'string' },
          modelId: { type: 'string' },
          modelName: { type: 'string' },
          osBuildId: { type: 'string' },
          osInternalBuildId: { type: 'string' },
          osName: { type: 'string' },
          osVersion: { type: 'string' },
          removeListeners: { type: 'function' },
          supportedCpuArchitectures: { type: 'array' },
          totalMemory: { type: 'unknown' },
        },
        ExpoDocumentPicker: {
          addListener: { type: 'function' },
          getDocumentAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoDomWebViewModule: {
          addListener: { type: 'function' },
          evalJsForWebViewAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoFetchModule: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoFontLoader: {
          addListener: { type: 'function' },
          getLoadedFonts: { type: 'function' },
          loadAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoFontUtils: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
          renderToImageAsync: { type: 'function' },
        },
        ExpoGo: {
          addListener: { type: 'function' },
          expoVersion: { type: 'string' },
          getModulesSchema: { type: 'function' },
          projectConfig: { type: 'object' },
          removeListeners: { type: 'function' },
        },
        ExpoHaptics: {
          addListener: { type: 'function' },
          impactAsync: { type: 'function' },
          notificationAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          selectionAsync: { type: 'function' },
        },
        ExpoHead: {
          activities: { type: 'object' },
          addListener: { type: 'function' },
          clearActivitiesAsync: { type: 'function' },
          createActivity: { type: 'function' },
          getLaunchActivity: { type: 'function' },
          removeListeners: { type: 'function' },
          revokeActivity: { type: 'function' },
          suspendActivity: { type: 'function' },
        },
        ExpoImage: {
          addListener: { type: 'function' },
          clearDiskCache: { type: 'function' },
          clearMemoryCache: { type: 'function' },
          generateBlurhashAsync: { type: 'function' },
          generateThumbhashAsync: { type: 'function' },
          getCachePathAsync: { type: 'function' },
          loadAsync: { type: 'function' },
          prefetch: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoImageManipulator: {
          addListener: { type: 'function' },
          manipulate: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoKeepAwake: {
          activate: { type: 'function' },
          addListener: { type: 'function' },
          deactivate: { type: 'function' },
          isActivated: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoLinearGradient: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoGlassEffect: {
          isLiquidGlassAvailable: { type: 'string' },
        },
        ExpoLivePhoto: { addListener: { type: 'function' }, removeListeners: { type: 'function' } },
        ExpoLocalAuthentication: {
          addListener: { type: 'function' },
          authenticateAsync: { type: 'function' },
          getEnrolledLevelAsync: { type: 'function' },
          hasHardwareAsync: { type: 'function' },
          isEnrolledAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          supportedAuthenticationTypesAsync: { type: 'function' },
        },
        ExpoLocation: {
          addListener: { type: 'function' },
          geocodeAsync: { type: 'function' },
          getBackgroundPermissionsAsync: { type: 'function' },
          getCurrentPositionAsync: { type: 'function' },
          getForegroundPermissionsAsync: { type: 'function' },
          getLastKnownPositionAsync: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          getProviderStatusAsync: { type: 'function' },
          hasServicesEnabledAsync: { type: 'function' },
          hasStartedGeofencingAsync: { type: 'function' },
          hasStartedLocationUpdatesAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          removeWatchAsync: { type: 'function' },
          requestBackgroundPermissionsAsync: { type: 'function' },
          requestForegroundPermissionsAsync: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
          reverseGeocodeAsync: { type: 'function' },
          startGeofencingAsync: { type: 'function' },
          startLocationUpdatesAsync: { type: 'function' },
          stopGeofencingAsync: { type: 'function' },
          stopLocationUpdatesAsync: { type: 'function' },
          watchDeviceHeading: { type: 'function' },
          watchPositionImplAsync: { type: 'function' },
        },
        ExpoMailComposer: {
          addListener: { type: 'function' },
          composeAsync: { type: 'function' },
          getClients: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoMediaLibrary: {
          addAssetsToAlbumAsync: { type: 'function' },
          addListener: { type: 'function' },
          CHANGE_LISTENER_NAME: { type: 'string' },
          createAlbumAsync: { type: 'function' },
          createAssetAsync: { type: 'function' },
          deleteAlbumsAsync: { type: 'function' },
          deleteAssetsAsync: { type: 'function' },
          getAlbumAsync: { type: 'function' },
          getAlbumsAsync: { type: 'function' },
          getAssetInfoAsync: { type: 'function' },
          getAssetsAsync: { type: 'function' },
          getMomentsAsync: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          MediaType: { type: 'object' },
          presentPermissionsPickerAsync: { type: 'function' },
          removeAssetsFromAlbumAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
          saveToLibraryAsync: { type: 'function' },
          SortBy: { type: 'object' },
        },
        ExpoMeshGradient: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExponentAccelerometer: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setUpdateInterval: { type: 'function' },
        },
        ExponentConstants: {
          addListener: { type: 'function' },
          appOwnership: { type: 'string' },
          debugMode: { type: 'boolean', mock: true },
          deviceName: { type: 'string' },
          executionEnvironment: { type: 'string' },
          experienceUrl: { type: 'string' },
          expoRuntimeVersion: { type: 'string' },
          expoVersion: { type: 'string' },
          getWebViewUserAgentAsync: { type: 'function' },
          isDetached: { type: 'boolean', mock: false },
          isHeadless: { type: 'boolean', mock: false },
          linkingUri: { type: 'string' },
          manifest: { type: 'object' },
          platform: { type: 'object' },
          removeListeners: { type: 'function' },
          sessionId: { type: 'string' },
          statusBarHeight: { type: 'number', mock: 54 },
          supportedExpoSdks: { type: 'array' },
          systemFonts: { type: 'array' },
        },
        ExponentDeviceMotion: {
          addListener: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          Gravity: { type: 'number', mock: 9.80665 },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
          setUpdateInterval: { type: 'function' },
        },
        ExponentFileSystem: {
          addListener: { type: 'function' },
          bundleDirectory: { type: 'string' },
          cacheDirectory: { type: 'string' },
          copyAsync: { type: 'function' },
          deleteAsync: { type: 'function' },
          documentDirectory: { type: 'string' },
          downloadAsync: { type: 'function' },
          downloadResumablePauseAsync: { type: 'function' },
          downloadResumableStartAsync: { type: 'function' },
          getFreeDiskStorageAsync: { type: 'function' },
          getInfoAsync: { type: 'function' },
          getTotalDiskCapacityAsync: { type: 'function' },
          makeDirectoryAsync: { type: 'function' },
          moveAsync: { type: 'function' },
          networkTaskCancelAsync: { type: 'function' },
          readAsStringAsync: { type: 'function' },
          readDirectoryAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          uploadAsync: { type: 'function' },
          uploadTaskStartAsync: { type: 'function' },
          writeAsStringAsync: { type: 'function' },
        },
        ExponentGLView: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExponentGyroscope: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setUpdateInterval: { type: 'function' },
        },
        ExponentImagePicker: {
          addListener: { type: 'function' },
          getCameraPermissionsAsync: { type: 'function' },
          getMediaLibraryPermissionsAsync: { type: 'function' },
          launchCameraAsync: { type: 'function' },
          launchImageLibraryAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestCameraPermissionsAsync: { type: 'function' },
          requestMediaLibraryPermissionsAsync: { type: 'function' },
        },
        ExponentMagnetometer: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setUpdateInterval: { type: 'function' },
        },
        ExponentMagnetometerUncalibrated: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setUpdateInterval: { type: 'function' },
        },
        ExponentPedometer: {
          addListener: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          getStepCountAsync: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
        },
        ExpoNetwork: {
          addListener: { type: 'function' },
          getIpAddressAsync: { type: 'function' },
          getNetworkStateAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoNotificationCategoriesModule: {
          addListener: { type: 'function' },
          deleteNotificationCategoryAsync: { type: 'function' },
          getNotificationCategoriesAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setNotificationCategoryAsync: { type: 'function' },
        },
        ExpoNotificationPermissionsModule: {
          addListener: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
        },
        ExpoNotificationPresenter: {
          addListener: { type: 'function' },
          dismissAllNotificationsAsync: { type: 'function' },
          dismissNotificationAsync: { type: 'function' },
          getPresentedNotificationsAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoNotificationScheduler: {
          addListener: { type: 'function' },
          cancelAllScheduledNotificationsAsync: { type: 'function' },
          cancelScheduledNotificationAsync: { type: 'function' },
          getAllScheduledNotificationsAsync: { type: 'function' },
          getNextTriggerDateAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          scheduleNotificationAsync: { type: 'function' },
        },
        ExpoNotificationsEmitter: {
          addListener: { type: 'function' },
          clearLastNotificationResponse: { type: 'function' },
          getLastNotificationResponse: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoNotificationsHandlerModule: {
          addListener: { type: 'function' },
          handleNotificationAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoPrint: {
          addListener: { type: 'function' },
          Orientation: { type: 'object' },
          print: { type: 'function' },
          printToFileAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          selectPrinter: { type: 'function' },
        },
        ExpoPushTokenManager: {
          addListener: { type: 'function' },
          getDevicePushTokenAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          unregisterForNotificationsAsync: { type: 'function' },
        },
        ExpoRouterNativeLinkPreview: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoScreenCapture: {
          addListener: { type: 'function' },
          allowScreenCapture: { type: 'function' },
          disableAppSwitcherProtection: { type: 'function' },
          enableAppSwitcherProtection: { type: 'function' },
          preventScreenCapture: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoScreenOrientation: {
          addListener: { type: 'function' },
          getOrientationAsync: { type: 'function' },
          getOrientationLockAsync: { type: 'function' },
          getPlatformOrientationLockAsync: { type: 'function' },
          lockAsync: { type: 'function' },
          lockPlatformAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          supportsOrientationLockAsync: { type: 'function' },
        },
        ExpoSecureStore: {
          addListener: { type: 'function' },
          AFTER_FIRST_UNLOCK: { type: 'number', mock: 0 },
          AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY: { type: 'number', mock: 1 },
          ALWAYS: { type: 'number', mock: 2 },
          ALWAYS_THIS_DEVICE_ONLY: { type: 'number', mock: 4 },
          canUseBiometricAuthentication: { type: 'function' },
          deleteValueWithKeyAsync: { type: 'function' },
          getValueWithKeyAsync: { type: 'function' },
          getValueWithKeySync: { type: 'function' },
          removeListeners: { type: 'function' },
          setValueWithKeyAsync: { type: 'function' },
          setValueWithKeySync: { type: 'function' },
          WHEN_PASSCODE_SET_THIS_DEVICE_ONLY: { type: 'number', mock: 3 },
          WHEN_UNLOCKED: { type: 'number', mock: 5 },
          WHEN_UNLOCKED_THIS_DEVICE_ONLY: { type: 'number', mock: 6 },
        },
        ExpoSharing: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
          shareAsync: { type: 'function' },
        },
        ExpoSMS: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          sendSMSAsync: { type: 'function' },
        },
        ExpoSpeech: {
          addListener: { type: 'function' },
          getVoices: { type: 'function' },
          isSpeaking: { type: 'function' },
          pause: { type: 'function' },
          removeListeners: { type: 'function' },
          resume: { type: 'function' },
          speak: { type: 'function' },
          stop: { type: 'function' },
        },
        ExpoSQLite: {
          addListener: { type: 'function' },
          backupDatabaseAsync: { type: 'function' },
          backupDatabaseSync: { type: 'function' },
          defaultDatabaseDirectory: { type: 'string' },
          deleteDatabaseAsync: { type: 'function' },
          deleteDatabaseSync: { type: 'function' },
          ensureDatabasePathExistsAsync: { type: 'function' },
          ensureDatabasePathExistsSync: { type: 'function' },
          importAssetDatabaseAsync: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoStoreReview: {
          addListener: { type: 'function' },
          isAvailableAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestReview: { type: 'function' },
        },
        ExpoSystemUI: {
          addListener: { type: 'function' },
          getBackgroundColorAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setBackgroundColorAsync: { type: 'function' },
        },
        ExpoTrackingTransparency: {
          addListener: { type: 'function' },
          getAdvertisingId: { type: 'function' },
          getPermissionsAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          requestPermissionsAsync: { type: 'function' },
        },
        ExpoUpdates: {
          addListener: { type: 'function' },
          channel: { type: 'string' },
          checkAutomatically: { type: 'string' },
          checkForUpdateAsync: { type: 'function' },
          clearLogEntriesAsync: { type: 'function' },
          emergencyLaunchReason: { type: 'null' },
          fetchUpdateAsync: { type: 'function' },
          getExtraParamsAsync: { type: 'function' },
          initialContext: { type: 'object' },
          isEmbeddedLaunch: { type: 'boolean', mock: false },
          isEmergencyLaunch: { type: 'boolean', mock: false },
          isEnabled: { type: 'boolean', mock: true },
          isUsingEmbeddedAssets: { type: 'boolean', mock: false },
          launchDuration: { type: 'number', mock: 455.30903339385986 },
          readLogEntriesAsync: { type: 'function' },
          reload: { type: 'function' },
          removeListeners: { type: 'function' },
          runtimeVersion: { type: 'string' },
          setExtraParamAsync: { type: 'function' },
          shouldDeferToNativeForAPIMethodAvailabilityInDevelopment: { type: 'boolean', mock: true },
        },
        ExpoVideo: {
          addListener: { type: 'function' },
          clearVideoCacheAsync: { type: 'function' },
          getCurrentVideoCacheSize: { type: 'function' },
          isPictureInPictureSupported: { type: 'function' },
          removeListeners: { type: 'function' },
          setVideoCacheSizeAsync: { type: 'function' },
        },
        ExpoVideoThumbnails: {
          addListener: { type: 'function' },
          getThumbnail: { type: 'function' },
          removeListeners: { type: 'function' },
        },
        ExpoVideoView: {
          addListener: { type: 'function' },
          removeListeners: { type: 'function' },
          ScaleAspectFill: { type: 'unknown' },
          ScaleAspectFit: { type: 'unknown' },
          ScaleNone: { type: 'unknown' },
          ScaleToFill: { type: 'unknown' },
          setFullscreen: { type: 'function' },
        },
        ExpoWebBrowser: {
          addListener: { type: 'function' },
          coolDownAsync: { type: 'function' },
          dismissAuthSession: { type: 'function' },
          dismissBrowser: { type: 'function' },
          getCustomTabsSupportingBrowsers: { type: 'function' },
          mayInitWithUrlAsync: { type: 'function' },
          openAuthSessionAsync: { type: 'function' },
          openBrowserAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          warmUpAsync: { type: 'function' },
        },
        FileSystem: {
          addListener: { type: 'function' },
          appleSharedContainers: { type: 'object' },
          availableDiskSpace: { type: 'property' },
          bundleDirectory: { type: 'string' },
          cacheDirectory: { type: 'string' },
          documentDirectory: { type: 'string' },
          downloadFileAsync: { type: 'function' },
          info: { type: 'function' },
          removeListeners: { type: 'function' },
          totalDiskSpace: { type: 'property' },
        },
        NotificationsServerRegistrationModule: {
          addListener: { type: 'function' },
          getInstallationIdAsync: { type: 'function' },
          getRegistrationInfoAsync: { type: 'function' },
          removeListeners: { type: 'function' },
          setRegistrationInfoAsync: { type: 'function' },
        },
        SymbolModule: { addListener: { type: 'function' }, removeListeners: { type: 'function' } },
      },
    },
    viewManagersMetadata: {
      type: 'object',
      mock: {
        ExpoAppleAuthentication: { propNames: ['buttonStyle', 'buttonType', 'cornerRadius'] },
        ExpoBlurView: { propNames: ['intensity', 'tint'] },
        ExpoCamera: {
          propNames: [
            'active',
            'animateShutter',
            'autoFocus',
            'barcodeScannerEnabled',
            'barcodeScannerSettings',
            'enableTorch',
            'facing',
            'flashMode',
            'mirror',
            'mode',
            'mute',
            'pictureSize',
            'responsiveOrientationWhenOrientationLocked',
            'selectedLens',
            'videoBitrate',
            'videoQuality',
            'zoom',
          ],
        },
        ExpoDomWebViewModule: {
          propNames: [
            'automaticallyAdjustsScrollIndicatorInsets',
            'bounces',
            'contentInset',
            'contentInsetAdjustmentBehavior',
            'decelerationRate',
            'directionalLockEnabled',
            'injectedJavaScriptBeforeContentLoaded',
            'pagingEnabled',
            'scrollEnabled',
            'showsHorizontalScrollIndicator',
            'showsVerticalScrollIndicator',
            'source',
            'webviewDebuggingEnabled',
          ],
        },
        ExpoImage: {
          propNames: [
            'accessibilityLabel',
            'accessible',
            'allowDownscaling',
            'autoplay',
            'blurRadius',
            'cachePolicy',
            'contentFit',
            'contentPosition',
            'enableLiveTextInteraction',
            'enforceEarlyResizing',
            'placeholder',
            'placeholderContentFit',
            'priority',
            'recyclingKey',
            'source',
            'tintColor',
            'transition',
            'useAppleWebpCodec',
          ],
        },
        ExpoLinearGradient: { propNames: ['colors', 'endPoint', 'locations', 'startPoint'] },
        ExpoLivePhoto: {
          propNames: ['contentFit', 'isMuted', 'source', 'useDefaultGestureRecognizer'],
        },
        ExponentGLView: { propNames: ['enableExperimentalWorkletSupport', 'msaaSamples'] },
        ExpoRouterNativeLinkPreview: {
          propNames: [
            'destructive',
            'disabled',
            'displayAsPalette',
            'displayInline',
            'icon',
            'isOn',
            'keepPresented',
            'nextScreenId',
            'preferredContentSize',
            'singleSelection',
            'tabPath',
            'title',
          ],
        },
        ExpoVideo: {
          propNames: [
            'activeTint',
            'allowsFullscreen',
            'allowsPictureInPicture',
            'allowsVideoFrameAnalysis',
            'contentFit',
            'contentPosition',
            'fullscreenOptions',
            'nativeControls',
            'player',
            'prioritizeVideoDevices',
            'requiresLinearPlayback',
            'showsTimecodes',
            'startsPictureInPictureAutomatically',
            'tint',
          ],
        },
        ExpoVideoView: { propNames: ['resizeMode', 'source', 'status', 'useNativeControls'] },
        SymbolModule: {
          propNames: [
            'animated',
            'animationSpec',
            'colors',
            'name',
            'resizeMode',
            'scale',
            'tintColor',
            'type',
            'weight',
          ],
        },
      },
    },
  },
};
