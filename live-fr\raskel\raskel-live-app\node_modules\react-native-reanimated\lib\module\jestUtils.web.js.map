{"version": 3, "names": ["with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advanceAnimationByTime", "advanceAnimationByFrame", "setUpTests", "getAnimatedStyle"], "sources": ["jestUtils.web.ts"], "sourcesContent": ["'use strict';\n/*\n * Stubbed for web, where we don't use this file;\n */\n\nexport function withReanimatedTimer() {\n  // NOOP\n}\n\nexport function advanceAnimationByTime() {\n  // NOOP\n}\n\nexport function advanceAnimationByFrame() {\n  // NOOP\n}\n\nexport function setUpTests() {\n  // NOOP\n}\n\nexport function getAnimatedStyle() {\n  // NOOP\n}\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AAEA,OAAO,SAASA,mBAAmBA,CAAA,EAAG;EACpC;AAAA;AAGF,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACvC;AAAA;AAGF,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC;AAAA;AAGF,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B;AAAA;AAGF,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC;AAAA", "ignoreList": []}