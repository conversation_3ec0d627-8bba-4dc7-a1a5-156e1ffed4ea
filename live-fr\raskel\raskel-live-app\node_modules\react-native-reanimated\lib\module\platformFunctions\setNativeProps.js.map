{"version": 3, "names": ["isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "processColorsInProps", "setNativeProps", "setNativePropsFabric", "animatedRef", "updates", "_WORKLET", "console", "warn", "shadowNodeWrapper", "global", "_updatePropsFabric", "setNativePropsPaper", "tag", "name", "viewName", "value", "_updatePropsPaper", "setNativePropsJest", "setNativePropsChromeDebugger", "setNativePropsDefault"], "sources": ["setNativeProps.ts"], "sourcesContent": ["'use strict';\nimport type { <PERSON><PERSON><PERSON><PERSON>rapper, StyleProps } from '../commonTypes';\nimport {\n  isChromeDebugger,\n  isFabric,\n  isJest,\n  shouldBeUseWeb,\n} from '../PlatformChecker';\nimport type {\n  AnimatedRef,\n  AnimatedRefOnJS,\n  AnimatedRefOnUI,\n} from '../hook/commonTypes';\nimport type { Component } from 'react';\nimport { processColorsInProps } from '../Colors';\n\ntype SetNativeProps = <T extends Component>(\n  animatedRef: AnimatedRef<T>,\n  updates: StyleProps\n) => void;\n/**\n * Lets you imperatively update component properties. You should always reach for [useAnimatedStyle](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedStyle) and [useAnimatedProps](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedProps) first when animating styles or properties.\n *\n * @param animatedRef - An [animated ref](https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef#returns) connected to the component you'd want to update.\n * @param updates - An object with properties you want to update.\n * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/setNativeProps\n */\nexport let setNativeProps: SetNativeProps;\n\nfunction setNativePropsFabric(\n  animatedRef: AnimatedRefOnJS | AnimatedRefOnUI,\n  updates: StyleProps\n) {\n  'worklet';\n  if (!_WORKLET) {\n    console.warn(\n      '[Reanimated] setNativeProps() can only be used on the UI runtime.'\n    );\n    return;\n  }\n  const shadowNodeWrapper = animatedRef() as ShadowNodeWrapper;\n  processColorsInProps(updates);\n  global._updatePropsFabric!([{ shadowNodeWrapper, updates }]);\n}\n\nfunction setNativePropsPaper(\n  animatedRef: AnimatedRefOnJS | AnimatedRefOnUI,\n  updates: StyleProps\n) {\n  'worklet';\n  if (!_WORKLET) {\n    console.warn(\n      '[Reanimated] setNativeProps() can only be used on the UI runtime.'\n    );\n    return;\n  }\n  const tag = animatedRef() as number;\n  const name = (animatedRef as AnimatedRefOnUI).viewName.value;\n  processColorsInProps(updates);\n  global._updatePropsPaper!([{ tag, name, updates }]);\n}\n\nfunction setNativePropsJest() {\n  console.warn('[Reanimated] setNativeProps() is not supported with Jest.');\n}\n\nfunction setNativePropsChromeDebugger() {\n  console.warn(\n    '[Reanimated] setNativeProps() is not supported with Chrome Debugger.'\n  );\n}\n\nfunction setNativePropsDefault() {\n  console.warn(\n    '[Reanimated] setNativeProps() is not supported on this configuration.'\n  );\n}\n\nif (!shouldBeUseWeb()) {\n  // Those assertions are actually correct since on Native platforms `AnimatedRef` is\n  // mapped as a different function in `shareableMappingCache` and\n  // TypeScript is not able to infer that.\n  if (isFabric()) {\n    setNativeProps = setNativePropsFabric as unknown as SetNativeProps;\n  } else {\n    setNativeProps = setNativePropsPaper as unknown as SetNativeProps;\n  }\n} else if (isJest()) {\n  setNativeProps = setNativePropsJest;\n} else if (isChromeDebugger()) {\n  setNativeProps = setNativePropsChromeDebugger;\n} else {\n  setNativeProps = setNativePropsDefault;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SACEA,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,oBAAoB;AAO3B,SAASC,oBAAoB,QAAQ,WAAW;AAMhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAA8B;AAEzC,SAASC,oBAAoBA,CAC3BC,WAA8C,EAC9CC,OAAmB,EACnB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbC,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACD;EACF;EACA,MAAMC,iBAAiB,GAAGL,WAAW,CAAC,CAAsB;EAC5DH,oBAAoB,CAACI,OAAO,CAAC;EAC7BK,MAAM,CAACC,kBAAkB,CAAE,CAAC;IAAEF,iBAAiB;IAAEJ;EAAQ,CAAC,CAAC,CAAC;AAC9D;AAEA,SAASO,mBAAmBA,CAC1BR,WAA8C,EAC9CC,OAAmB,EACnB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbC,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACD;EACF;EACA,MAAMK,GAAG,GAAGT,WAAW,CAAC,CAAW;EACnC,MAAMU,IAAI,GAAIV,WAAW,CAAqBW,QAAQ,CAACC,KAAK;EAC5Df,oBAAoB,CAACI,OAAO,CAAC;EAC7BK,MAAM,CAACO,iBAAiB,CAAE,CAAC;IAAEJ,GAAG;IAAEC,IAAI;IAAET;EAAQ,CAAC,CAAC,CAAC;AACrD;AAEA,SAASa,kBAAkBA,CAAA,EAAG;EAC5BX,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;AAC3E;AAEA,SAASW,4BAA4BA,CAAA,EAAG;EACtCZ,OAAO,CAACC,IAAI,CACV,sEACF,CAAC;AACH;AAEA,SAASY,qBAAqBA,CAAA,EAAG;EAC/Bb,OAAO,CAACC,IAAI,CACV,uEACF,CAAC;AACH;AAEA,IAAI,CAACR,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdI,cAAc,GAAGC,oBAAiD;EACpE,CAAC,MAAM;IACLD,cAAc,GAAGU,mBAAgD;EACnE;AACF,CAAC,MAAM,IAAIb,MAAM,CAAC,CAAC,EAAE;EACnBG,cAAc,GAAGgB,kBAAkB;AACrC,CAAC,MAAM,IAAIrB,gBAAgB,CAAC,CAAC,EAAE;EAC7BK,cAAc,GAAGiB,4BAA4B;AAC/C,CAAC,MAAM;EACLjB,cAAc,GAAGkB,qBAAqB;AACxC", "ignoreList": []}