{"version": 3, "names": ["LayoutAnimationType", "getEasingByName", "resetStyle", "component", "style", "animationName", "position", "top", "left", "margin", "width", "height", "showChildren", "parent", "childrenDisplayProperty", "shouldShow", "i", "children", "length", "child", "display", "get", "set", "prepareParent", "element", "dummy", "animationConfig", "transitionData", "easing", "easingX", "Map", "originalBackgroundColor", "backgroundColor", "onFinalize", "contains", "<PERSON><PERSON><PERSON><PERSON>", "animationCancelCallback", "removeEventListener", "animationEndCallback", "addEventListener", "append<PERSON><PERSON><PERSON>", "prepareDummy", "dummyTransitionKeyframeName", "dummyAnimationConfig", "animationType", "LAYOUT", "duration", "delay", "easingY", "callback", "reversed", "cloneNode", "prepareCurvedTransition", "CurvedTransition", "keyframeXName", "keyframeYName", "keyframeXObj", "name", "transform", "translateX", "scale", "scaleX", "scaleY", "keyframeYObj", "translateY", "firstKeyframeObj", "secondKeyframeObj"], "sources": ["Curved.web.ts"], "sourcesContent": ["'use strict';\nimport type { ReanimatedHTMLElement } from '../../../js-reanimated';\nimport { LayoutAnimationType } from '../..';\nimport type { WebEasingsNames } from '../Easing.web';\nimport { getEasingByName } from '../Easing.web';\nimport type { TransitionData } from '../animationParser';\nimport type { AnimationConfig } from '../config';\n\nfunction resetStyle(component: HTMLElement) {\n  component.style.animationName = ''; // This line prevents unwanted entering animation\n  component.style.position = 'absolute';\n  component.style.top = '0px';\n  component.style.left = '0px';\n  component.style.margin = '0px';\n  component.style.width = '100%';\n  component.style.height = '100%';\n}\n\nfunction showChildren(\n  parent: HTMLElement,\n  childrenDisplayProperty: Map<HTMLElement, string>,\n  shouldShow: boolean\n) {\n  for (let i = 0; i < parent.children.length; ++i) {\n    const child = parent.children[i] as HTMLElement;\n\n    if (shouldShow) {\n      child.style.display = childrenDisplayProperty.get(child)!;\n    } else {\n      childrenDisplayProperty.set(child, child.style.display);\n      child.style.display = 'none';\n    }\n  }\n}\n\nfunction prepareParent(\n  element: ReanimatedHTMLElement,\n  dummy: ReanimatedHTMLElement,\n  animationConfig: AnimationConfig,\n  transitionData: TransitionData\n) {\n  // Adjust configs for `CurvedTransition` and create config object for dummy\n  animationConfig.easing = getEasingByName(\n    transitionData.easingX as WebEasingsNames\n  );\n\n  const childrenDisplayProperty = new Map<HTMLElement, string>();\n  showChildren(element, childrenDisplayProperty, false);\n\n  const originalBackgroundColor = element.style.backgroundColor;\n  element.style.backgroundColor = 'transparent';\n\n  const onFinalize = () => {\n    if (element.contains(dummy)) {\n      element.removeChild(dummy);\n    }\n\n    showChildren(element, childrenDisplayProperty, true);\n\n    element.style.backgroundColor = originalBackgroundColor;\n  };\n\n  const animationCancelCallback = () => {\n    onFinalize();\n    element.removeEventListener('animationcancel', animationCancelCallback);\n  };\n\n  const animationEndCallback = () => {\n    onFinalize();\n    element.removeEventListener('animationend', animationEndCallback);\n  };\n\n  element.addEventListener('animationend', animationEndCallback);\n  element.addEventListener('animationcancel', animationCancelCallback);\n\n  element.appendChild(dummy);\n}\n\nfunction prepareDummy(\n  element: ReanimatedHTMLElement,\n  animationConfig: AnimationConfig,\n  transitionData: TransitionData,\n  dummyTransitionKeyframeName: string\n) {\n  const dummyAnimationConfig: AnimationConfig = {\n    animationName: dummyTransitionKeyframeName,\n    animationType: LayoutAnimationType.LAYOUT,\n    duration: animationConfig.duration,\n    delay: animationConfig.delay,\n    easing: getEasingByName(transitionData.easingY as WebEasingsNames),\n    callback: null,\n    reversed: false,\n  };\n\n  const dummy = element.cloneNode(true) as ReanimatedHTMLElement;\n  resetStyle(dummy);\n\n  return { dummy, dummyAnimationConfig };\n}\n\nexport function prepareCurvedTransition(\n  element: ReanimatedHTMLElement,\n  animationConfig: AnimationConfig,\n  transitionData: TransitionData,\n  dummyTransitionKeyframeName: string\n) {\n  const { dummy, dummyAnimationConfig } = prepareDummy(\n    element,\n    animationConfig,\n    transitionData,\n    dummyTransitionKeyframeName\n  );\n\n  prepareParent(element, dummy, animationConfig, transitionData);\n\n  return { dummy, dummyAnimationConfig };\n}\n\nexport function CurvedTransition(\n  keyframeXName: string,\n  keyframeYName: string,\n  transitionData: TransitionData\n) {\n  const keyframeXObj = {\n    name: keyframeXName,\n    style: {\n      0: {\n        transform: [\n          {\n            translateX: `${transitionData.translateX}px`,\n            scale: `${transitionData.scaleX},${transitionData.scaleY}`,\n          },\n        ],\n      },\n    },\n    duration: 300,\n  };\n\n  const keyframeYObj = {\n    name: keyframeYName,\n    style: {\n      0: {\n        transform: [\n          {\n            translateY: `${transitionData.translateY}px`,\n            scale: `${transitionData.scaleX},${transitionData.scaleY}`,\n          },\n        ],\n      },\n    },\n    duration: 300,\n  };\n\n  return {\n    firstKeyframeObj: keyframeXObj,\n    secondKeyframeObj: keyframeYObj,\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,mBAAmB,QAAQ,OAAO;AAE3C,SAASC,eAAe,QAAQ,eAAe;AAI/C,SAASC,UAAUA,CAACC,SAAsB,EAAE;EAC1CA,SAAS,CAACC,KAAK,CAACC,aAAa,GAAG,EAAE,CAAC,CAAC;EACpCF,SAAS,CAACC,KAAK,CAACE,QAAQ,GAAG,UAAU;EACrCH,SAAS,CAACC,KAAK,CAACG,GAAG,GAAG,KAAK;EAC3BJ,SAAS,CAACC,KAAK,CAACI,IAAI,GAAG,KAAK;EAC5BL,SAAS,CAACC,KAAK,CAACK,MAAM,GAAG,KAAK;EAC9BN,SAAS,CAACC,KAAK,CAACM,KAAK,GAAG,MAAM;EAC9BP,SAAS,CAACC,KAAK,CAACO,MAAM,GAAG,MAAM;AACjC;AAEA,SAASC,YAAYA,CACnBC,MAAmB,EACnBC,uBAAiD,EACjDC,UAAmB,EACnB;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,QAAQ,CAACC,MAAM,EAAE,EAAEF,CAAC,EAAE;IAC/C,MAAMG,KAAK,GAAGN,MAAM,CAACI,QAAQ,CAACD,CAAC,CAAgB;IAE/C,IAAID,UAAU,EAAE;MACdI,KAAK,CAACf,KAAK,CAACgB,OAAO,GAAGN,uBAAuB,CAACO,GAAG,CAACF,KAAK,CAAE;IAC3D,CAAC,MAAM;MACLL,uBAAuB,CAACQ,GAAG,CAACH,KAAK,EAAEA,KAAK,CAACf,KAAK,CAACgB,OAAO,CAAC;MACvDD,KAAK,CAACf,KAAK,CAACgB,OAAO,GAAG,MAAM;IAC9B;EACF;AACF;AAEA,SAASG,aAAaA,CACpBC,OAA8B,EAC9BC,KAA4B,EAC5BC,eAAgC,EAChCC,cAA8B,EAC9B;EACA;EACAD,eAAe,CAACE,MAAM,GAAG3B,eAAe,CACtC0B,cAAc,CAACE,OACjB,CAAC;EAED,MAAMf,uBAAuB,GAAG,IAAIgB,GAAG,CAAsB,CAAC;EAC9DlB,YAAY,CAACY,OAAO,EAAEV,uBAAuB,EAAE,KAAK,CAAC;EAErD,MAAMiB,uBAAuB,GAAGP,OAAO,CAACpB,KAAK,CAAC4B,eAAe;EAC7DR,OAAO,CAACpB,KAAK,CAAC4B,eAAe,GAAG,aAAa;EAE7C,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIT,OAAO,CAACU,QAAQ,CAACT,KAAK,CAAC,EAAE;MAC3BD,OAAO,CAACW,WAAW,CAACV,KAAK,CAAC;IAC5B;IAEAb,YAAY,CAACY,OAAO,EAAEV,uBAAuB,EAAE,IAAI,CAAC;IAEpDU,OAAO,CAACpB,KAAK,CAAC4B,eAAe,GAAGD,uBAAuB;EACzD,CAAC;EAED,MAAMK,uBAAuB,GAAGA,CAAA,KAAM;IACpCH,UAAU,CAAC,CAAC;IACZT,OAAO,CAACa,mBAAmB,CAAC,iBAAiB,EAAED,uBAAuB,CAAC;EACzE,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCL,UAAU,CAAC,CAAC;IACZT,OAAO,CAACa,mBAAmB,CAAC,cAAc,EAAEC,oBAAoB,CAAC;EACnE,CAAC;EAEDd,OAAO,CAACe,gBAAgB,CAAC,cAAc,EAAED,oBAAoB,CAAC;EAC9Dd,OAAO,CAACe,gBAAgB,CAAC,iBAAiB,EAAEH,uBAAuB,CAAC;EAEpEZ,OAAO,CAACgB,WAAW,CAACf,KAAK,CAAC;AAC5B;AAEA,SAASgB,YAAYA,CACnBjB,OAA8B,EAC9BE,eAAgC,EAChCC,cAA8B,EAC9Be,2BAAmC,EACnC;EACA,MAAMC,oBAAqC,GAAG;IAC5CtC,aAAa,EAAEqC,2BAA2B;IAC1CE,aAAa,EAAE5C,mBAAmB,CAAC6C,MAAM;IACzCC,QAAQ,EAAEpB,eAAe,CAACoB,QAAQ;IAClCC,KAAK,EAAErB,eAAe,CAACqB,KAAK;IAC5BnB,MAAM,EAAE3B,eAAe,CAAC0B,cAAc,CAACqB,OAA0B,CAAC;IAClEC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMzB,KAAK,GAAGD,OAAO,CAAC2B,SAAS,CAAC,IAAI,CAA0B;EAC9DjD,UAAU,CAACuB,KAAK,CAAC;EAEjB,OAAO;IAAEA,KAAK;IAAEkB;EAAqB,CAAC;AACxC;AAEA,OAAO,SAASS,uBAAuBA,CACrC5B,OAA8B,EAC9BE,eAAgC,EAChCC,cAA8B,EAC9Be,2BAAmC,EACnC;EACA,MAAM;IAAEjB,KAAK;IAAEkB;EAAqB,CAAC,GAAGF,YAAY,CAClDjB,OAAO,EACPE,eAAe,EACfC,cAAc,EACde,2BACF,CAAC;EAEDnB,aAAa,CAACC,OAAO,EAAEC,KAAK,EAAEC,eAAe,EAAEC,cAAc,CAAC;EAE9D,OAAO;IAAEF,KAAK;IAAEkB;EAAqB,CAAC;AACxC;AAEA,OAAO,SAASU,gBAAgBA,CAC9BC,aAAqB,EACrBC,aAAqB,EACrB5B,cAA8B,EAC9B;EACA,MAAM6B,YAAY,GAAG;IACnBC,IAAI,EAAEH,aAAa;IACnBlD,KAAK,EAAE;MACL,CAAC,EAAE;QACDsD,SAAS,EAAE,CACT;UACEC,UAAU,EAAG,GAAEhC,cAAc,CAACgC,UAAW,IAAG;UAC5CC,KAAK,EAAG,GAAEjC,cAAc,CAACkC,MAAO,IAAGlC,cAAc,CAACmC,MAAO;QAC3D,CAAC;MAEL;IACF,CAAC;IACDhB,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMiB,YAAY,GAAG;IACnBN,IAAI,EAAEF,aAAa;IACnBnD,KAAK,EAAE;MACL,CAAC,EAAE;QACDsD,SAAS,EAAE,CACT;UACEM,UAAU,EAAG,GAAErC,cAAc,CAACqC,UAAW,IAAG;UAC5CJ,KAAK,EAAG,GAAEjC,cAAc,CAACkC,MAAO,IAAGlC,cAAc,CAACmC,MAAO;QAC3D,CAAC;MAEL;IACF,CAAC;IACDhB,QAAQ,EAAE;EACZ,CAAC;EAED,OAAO;IACLmB,gBAAgB,EAAET,YAAY;IAC9BU,iBAAiB,EAAEH;EACrB,CAAC;AACH", "ignoreList": []}