{"logs": [{"outputFile": "com.msrfi.liveapp-mergeDebugResources-76:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\962ce58a92e31b97c141ac06310bb23c\\transformed\\play-services-base-18.1.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5679,5787,5950,6077,6187,6341,6470,6585,6836,7004,7110,7272,7397,7544,7686,7756,7817", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "5782,5945,6072,6182,6336,6465,6580,6685,6999,7105,7267,7392,7539,7681,7751,7812,7900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aecacd41bb3fe9f9b9ff7ee8bbb41880\\transformed\\exoplayer-ui-2.18.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3818,3884,3936,3997,4082,4167", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3813,3879,3931,3992,4077,4162,4217"}, "to": {"startLines": "2,11,17,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,721,8154,8238,8320,8403,8503,8602,8687,8750,8848,8947,9018,9087,9153,9221,9347,9472,9609,9686,9768,9843,9931,10026,10119,10187,10960,11013,11073,11121,11182,11249,11317,11381,11448,11513,11573,11639,11704,11770,11822,11883,11968,12053", "endLines": "10,16,22,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "377,716,1046,8233,8315,8398,8498,8597,8682,8745,8843,8942,9013,9082,9148,9216,9342,9467,9604,9681,9763,9838,9926,10021,10114,10182,10267,11008,11068,11116,11177,11244,11312,11376,11443,11508,11568,11634,11699,11765,11817,11878,11963,12048,12103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1559,1621,1688,1746,1817,1876,1930,2044,2104,2167,2221,2294,2413,2499,2575,2666,2747,2830,2969,3054,3141,3274,3362,3440,3497,3548,3614,3686,3762,3833,3916,3989,4066,4148,4222,4331,4421,4500,4591,4687,4761,4842,4937,4991,5073,5139,5226,5312,5374,5438,5501,5574,5681,5791,5889,5995,6056,6111,6193,6278,6354", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1554,1616,1683,1741,1812,1871,1925,2039,2099,2162,2216,2289,2408,2494,2570,2661,2742,2825,2964,3049,3136,3269,3357,3435,3492,3543,3609,3681,3757,3828,3911,3984,4061,4143,4217,4326,4416,4495,4586,4682,4756,4837,4932,4986,5068,5134,5221,5307,5369,5433,5496,5569,5676,5786,5884,5990,6051,6106,6188,6273,6349,6426"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,92,93,145,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,222,223,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1051,4128,4206,4284,4372,4480,5298,5394,5510,8015,8087,12108,12608,12836,12971,13059,13121,13188,13246,13317,13376,13430,13544,13604,13667,13721,13794,14135,14221,14297,14388,14469,14552,14691,14776,14863,14996,15084,15162,15219,15270,15336,15408,15484,15555,15638,15711,15788,15870,15944,16053,16143,16222,16313,16409,16483,16564,16659,16713,16795,16861,16948,17034,17096,17160,17223,17296,17403,17513,17611,17717,17778,17833,18324,18409,18485", "endLines": "28,57,58,59,60,61,69,70,71,92,93,145,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,222,223,224", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "1320,4201,4279,4367,4475,4566,5389,5505,5588,8082,8149,12194,12669,12894,13054,13116,13183,13241,13312,13371,13425,13539,13599,13662,13716,13789,13908,14216,14292,14383,14464,14547,14686,14771,14858,14991,15079,15157,15214,15265,15331,15403,15479,15550,15633,15706,15783,15865,15939,16048,16138,16217,16308,16404,16478,16559,16654,16708,16790,16856,16943,17029,17091,17155,17218,17291,17398,17508,17606,17712,17773,17828,17910,18404,18480,18557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1434,1536,1644,1730,1835,1953,2036,2118,2209,2302,2397,2491,2591,2684,2779,2874,2965,3056,3155,3261,3367,3465,3572,3679,3784,3954,18242", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "1429,1531,1639,1725,1830,1948,2031,2113,2204,2297,2392,2486,2586,2679,2774,2869,2960,3051,3150,3256,3362,3460,3567,3674,3779,3949,4049,18319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "62,63,64,65,66,67,68,233", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4571,4671,4773,4874,4975,5080,5185,19196", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "4666,4768,4869,4970,5075,5180,5293,19292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,289,363,451,523,590,666,745,833,919,991,1072,1157,1233,1315,1398,1475,1548,1621,1706,1780,1860,1930", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,210,284,358,446,518,585,661,740,828,914,986,1067,1152,1228,1310,1393,1470,1543,1616,1701,1775,1855,1925,2010"}, "to": {"startLines": "56,72,149,151,152,154,168,169,170,217,218,219,220,225,226,227,228,229,230,231,232,234,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4054,5593,12534,12674,12748,12899,13913,13980,14056,17915,18003,18089,18161,18562,18647,18723,18805,18888,18965,19038,19111,19297,19371,19451,19521", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "4123,5674,12603,12743,12831,12966,13975,14051,14130,17998,18084,18156,18237,18642,18718,18800,18883,18960,19033,19106,19191,19366,19446,19516,19601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\107af08ab037182b56f33201f0856ef3\\transformed\\play-services-basement-18.1.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "6690", "endColumns": "145", "endOffsets": "6831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1fa8e62cff35f0924d9735e7c1c52928\\transformed\\exoplayer-core-2.18.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10272,10346,10411,10479,10550,10630,10703,10796,10885", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "10341,10406,10474,10545,10625,10698,10791,10880,10955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "91,146,147,148", "startColumns": "4,4,4,4", "startOffsets": "7905,12199,12306,12426", "endColumns": "109,106,119,107", "endOffsets": "8010,12301,12421,12529"}}]}]}