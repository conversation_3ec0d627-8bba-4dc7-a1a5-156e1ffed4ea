{"version": 3, "names": ["isSharedValue", "value", "_isReanimatedSharedValue"], "sources": ["isSharedValue.ts"], "sourcesContent": ["'use strict';\nimport type { SharedValue } from './commonTypes';\n\nexport function isSharedValue<T = unknown>(\n  value: unknown\n): value is SharedValue<T> {\n  'worklet';\n  // We cannot use `in` operator here because `value` could be a HostObject and therefore we cast.\n  return (value as Record<string, unknown>)?._isReanimatedSharedValue === true;\n}\n"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,aAAaA,CAC3BC,KAAc,EACW;EACzB,SAAS;;EACT;EACA,OAAO,CAACA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAA8BC,wBAAwB,MAAK,IAAI;AAC9E", "ignoreList": []}