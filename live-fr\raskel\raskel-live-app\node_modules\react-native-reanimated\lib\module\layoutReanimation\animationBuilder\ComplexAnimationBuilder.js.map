{"version": 3, "names": ["_defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable", "t", "i", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "withTiming", "with<PERSON><PERSON><PERSON>", "BaseAnimationBuilder", "assertEasingIsWorklet", "ComplexAnimationBuilder", "constructor", "args", "easing", "easingFunction", "instance", "createInstance", "__DEV__", "easingV", "rotate", "degree", "rotateV", "springify", "duration", "durationV", "type", "dampingRatio", "dampingRatioV", "damping", "dampingV", "mass", "massV", "stiffness", "stiffnessV", "overshootClamping", "overshootClampingV", "restDisplacementThreshold", "restDisplacementThresholdV", "restSpeedThreshold", "restSpeedThresholdV", "withInitialValues", "values", "initialValues", "getAnimationAndConfig", "animation", "config", "maybeSetConfigValue", "variableName", "for<PERSON>ach"], "sources": ["ComplexAnimationBuilder.ts"], "sourcesContent": ["'use strict';\nimport { withTiming, withSpring } from '../../animation';\nimport type {\n  AnimationFunction,\n  BaseBuilderAnimationConfig,\n  LayoutAnimationAndConfig,\n} from './commonTypes';\nimport { BaseAnimationBuilder } from './BaseAnimationBuilder';\nimport type { StyleProps, EasingFunction } from '../../commonTypes';\nimport { assertEasingIsWorklet } from '../../animation/util';\n\nexport class ComplexAnimationBuilder extends BaseAnimationBuilder {\n  easingV?: EasingFunction;\n  rotateV?: string;\n  type?: AnimationFunction;\n  dampingV?: number;\n  dampingRatioV?: number;\n  massV?: number;\n  stiffnessV?: number;\n  overshootClampingV?: number;\n  restDisplacementThresholdV?: number;\n  restSpeedThresholdV?: number;\n  initialValues?: StyleProps;\n\n  static createInstance: <T extends typeof BaseAnimationBuilder>(\n    this: T\n  ) => InstanceType<T>;\n\n  /**\n   * Lets you change the easing curve of the animation. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param easingFunction - An easing function which defines the animation curve.\n   */\n  static easing<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    easingFunction: EasingFunction\n  ) {\n    const instance = this.createInstance();\n    return instance.easing(easingFunction);\n  }\n\n  easing(easingFunction: EasingFunction): this {\n    if (__DEV__) {\n      assertEasingIsWorklet(easingFunction);\n    }\n    this.easingV = easingFunction;\n    return this;\n  }\n\n  /**\n   * Lets you rotate the element. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param degree - The rotation degree.\n   */\n  static rotate<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    degree: string\n  ) {\n    const instance = this.createInstance();\n    return instance.rotate(degree);\n  }\n\n  rotate(degree: string): this {\n    this.rotateV = degree;\n    return this;\n  }\n\n  /**\n   * Enables the spring-based animation configuration. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param duration - An optional duration of the spring animation (in milliseconds).\n   */\n  static springify<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    duration?: number\n  ): ComplexAnimationBuilder {\n    const instance = this.createInstance();\n    return instance.springify(duration);\n  }\n\n  springify(duration?: number): this {\n    this.durationV = duration;\n    this.type = withSpring as AnimationFunction;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the spring animation damping ratio. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param dampingRatio - How damped the spring is.\n   */\n  static dampingRatio<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    dampingRatio: number\n  ) {\n    const instance = this.createInstance();\n    return instance.dampingRatio(dampingRatio);\n  }\n\n  dampingRatio(value: number): this {\n    this.dampingRatioV = value;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the spring animation damping. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param value - Decides how quickly a spring stops moving. Higher damping means the spring will come to rest faster.\n   */\n  static damping<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    damping: number\n  ) {\n    const instance = this.createInstance();\n    return instance.damping(damping);\n  }\n\n  damping(damping: number): this {\n    this.dampingV = damping;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the spring animation mass. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param mass - The weight of the spring. Reducing this value makes the animation faster.\n   */\n  static mass<T extends typeof ComplexAnimationBuilder>(this: T, mass: number) {\n    const instance = this.createInstance();\n    return instance.mass(mass);\n  }\n\n  mass(mass: number): this {\n    this.massV = mass;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the stiffness of the spring animation. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param stiffness - How bouncy the spring is.\n   */\n  static stiffness<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    stiffness: number\n  ) {\n    const instance = this.createInstance();\n    return instance.stiffness(stiffness);\n  }\n\n  stiffness(stiffness: number): this {\n    this.stiffnessV = stiffness;\n    return this;\n  }\n\n  /**\n   * Lets you adjust overshoot clamping of the spring. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param overshootClamping - Whether a spring can bounce over the final position.\n   */\n  static overshootClamping<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    overshootClamping: number\n  ) {\n    const instance = this.createInstance();\n    return instance.overshootClamping(overshootClamping);\n  }\n\n  overshootClamping(overshootClamping: number): this {\n    this.overshootClampingV = overshootClamping;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the rest displacement threshold of the spring animation. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param restDisplacementThreshold - The displacement below which the spring will snap to the designated position without further oscillations.\n   */\n  static restDisplacementThreshold<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    restDisplacementThreshold: number\n  ) {\n    const instance = this.createInstance();\n    return instance.restDisplacementThreshold(restDisplacementThreshold);\n  }\n\n  restDisplacementThreshold(restDisplacementThreshold: number) {\n    this.restDisplacementThresholdV = restDisplacementThreshold;\n    return this;\n  }\n\n  /**\n   * Lets you adjust the rest speed threshold of the spring animation. Can be chained alongside other [layout animation modifiers](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#layout-animation-modifier).\n   *\n   * @param restSpeedThreshold - The speed in pixels per second from which the spring will snap to the designated position without further oscillations.\n   */\n  static restSpeedThreshold<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    restSpeedThreshold: number\n  ) {\n    const instance = this.createInstance();\n    return instance.restSpeedThreshold(restSpeedThreshold);\n  }\n\n  restSpeedThreshold(restSpeedThreshold: number): this {\n    this.restSpeedThresholdV = restSpeedThreshold;\n    return this;\n  }\n\n  /**\n   * Lets you override the initial config of the animation\n   *\n   * @param values - An object containing the styles to override.\n   */\n  static withInitialValues<T extends typeof ComplexAnimationBuilder>(\n    this: T,\n    values: StyleProps\n  ) {\n    const instance = this.createInstance();\n    return instance.withInitialValues(values);\n  }\n\n  withInitialValues(values: StyleProps): this {\n    this.initialValues = values;\n    return this;\n  }\n\n  getAnimationAndConfig(): LayoutAnimationAndConfig {\n    const duration = this.durationV;\n    const easing = this.easingV;\n    const rotate = this.rotateV;\n    const type = this.type ? this.type : (withTiming as AnimationFunction);\n    const damping = this.dampingV;\n    const dampingRatio = this.dampingRatioV;\n    const mass = this.massV;\n    const stiffness = this.stiffnessV;\n    const overshootClamping = this.overshootClampingV;\n    const restDisplacementThreshold = this.restDisplacementThresholdV;\n    const restSpeedThreshold = this.restSpeedThresholdV;\n\n    const animation = type;\n\n    const config: BaseBuilderAnimationConfig = {};\n\n    function maybeSetConfigValue<Key extends keyof BaseBuilderAnimationConfig>(\n      value: BaseBuilderAnimationConfig[Key],\n      variableName: Key\n    ) {\n      if (value) {\n        config[variableName] = value;\n      }\n    }\n\n    if (type === withTiming) {\n      maybeSetConfigValue(easing, 'easing');\n    }\n\n    (\n      [\n        { variableName: 'damping', value: damping },\n        { variableName: 'dampingRatio', value: dampingRatio },\n        { variableName: 'mass', value: mass },\n        { variableName: 'stiffness', value: stiffness },\n        { variableName: 'overshootClamping', value: overshootClamping },\n        {\n          variableName: 'restDisplacementThreshold',\n          value: restDisplacementThreshold,\n        },\n        { variableName: 'restSpeedThreshold', value: restSpeedThreshold },\n        { variableName: 'duration', value: duration },\n        { variableName: 'rotate', value: rotate },\n      ] as const\n    ).forEach(({ value, variableName }) =>\n      maybeSetConfigValue(value, variableName)\n    );\n\n    return [animation, config];\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,CAAA,QAAAC,CAAA,GAAAC,YAAA,CAAAF,CAAA,uCAAAC,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAF,CAAA,EAAAG,CAAA,2BAAAH,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAI,CAAA,GAAAJ,CAAA,CAAAK,MAAA,CAAAC,WAAA,kBAAAF,CAAA,QAAAH,CAAA,GAAAG,CAAA,CAAAG,IAAA,CAAAP,CAAA,EAAAG,CAAA,uCAAAF,CAAA,SAAAA,CAAA,YAAAO,SAAA,yEAAAL,CAAA,GAAAM,MAAA,GAAAC,MAAA,EAAAV,CAAA;AACb,SAASW,UAAU,EAAEC,UAAU,QAAQ,iBAAiB;AAMxD,SAASC,oBAAoB,QAAQ,wBAAwB;AAE7D,SAASC,qBAAqB,QAAQ,sBAAsB;AAE5D,OAAO,MAAMC,uBAAuB,SAASF,oBAAoB,CAAC;EAAAG,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA3B,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;EAAA;EAiBhE;AACF;AACA;AACA;AACA;EACE,OAAO4B,MAAMA,CAEXC,cAA8B,EAC9B;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACF,MAAM,CAACC,cAAc,CAAC;EACxC;EAEAD,MAAMA,CAACC,cAA8B,EAAQ;IAC3C,IAAIG,OAAO,EAAE;MACXR,qBAAqB,CAACK,cAAc,CAAC;IACvC;IACA,IAAI,CAACI,OAAO,GAAGJ,cAAc;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOK,MAAMA,CAEXC,MAAc,EACd;IACA,MAAML,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACI,MAAM,CAACC,MAAM,CAAC;EAChC;EAEAD,MAAMA,CAACC,MAAc,EAAQ;IAC3B,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,SAASA,CAEdC,QAAiB,EACQ;IACzB,MAAMR,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACO,SAAS,CAACC,QAAQ,CAAC;EACrC;EAEAD,SAASA,CAACC,QAAiB,EAAQ;IACjC,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,IAAI,CAACE,IAAI,GAAGlB,UAA+B;IAC3C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOmB,YAAYA,CAEjBA,YAAoB,EACpB;IACA,MAAMX,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACW,YAAY,CAACA,YAAY,CAAC;EAC5C;EAEAA,YAAYA,CAACtC,KAAa,EAAQ;IAChC,IAAI,CAACuC,aAAa,GAAGvC,KAAK;IAC1B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOwC,OAAOA,CAEZA,OAAe,EACf;IACA,MAAMb,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACa,OAAO,CAACA,OAAO,CAAC;EAClC;EAEAA,OAAOA,CAACA,OAAe,EAAQ;IAC7B,IAAI,CAACC,QAAQ,GAAGD,OAAO;IACvB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,IAAIA,CAAoDA,IAAY,EAAE;IAC3E,MAAMf,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACe,IAAI,CAACA,IAAI,CAAC;EAC5B;EAEAA,IAAIA,CAACA,IAAY,EAAQ;IACvB,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,SAASA,CAEdA,SAAiB,EACjB;IACA,MAAMjB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACiB,SAAS,CAACA,SAAS,CAAC;EACtC;EAEAA,SAASA,CAACA,SAAiB,EAAQ;IACjC,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,iBAAiBA,CAEtBA,iBAAyB,EACzB;IACA,MAAMnB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACmB,iBAAiB,CAACA,iBAAiB,CAAC;EACtD;EAEAA,iBAAiBA,CAACA,iBAAyB,EAAQ;IACjD,IAAI,CAACC,kBAAkB,GAAGD,iBAAiB;IAC3C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,yBAAyBA,CAE9BA,yBAAiC,EACjC;IACA,MAAMrB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACqB,yBAAyB,CAACA,yBAAyB,CAAC;EACtE;EAEAA,yBAAyBA,CAACA,yBAAiC,EAAE;IAC3D,IAAI,CAACC,0BAA0B,GAAGD,yBAAyB;IAC3D,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,kBAAkBA,CAEvBA,kBAA0B,EAC1B;IACA,MAAMvB,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACuB,kBAAkB,CAACA,kBAAkB,CAAC;EACxD;EAEAA,kBAAkBA,CAACA,kBAA0B,EAAQ;IACnD,IAAI,CAACC,mBAAmB,GAAGD,kBAAkB;IAC7C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,iBAAiBA,CAEtBC,MAAkB,EAClB;IACA,MAAM1B,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACtC,OAAOD,QAAQ,CAACyB,iBAAiB,CAACC,MAAM,CAAC;EAC3C;EAEAD,iBAAiBA,CAACC,MAAkB,EAAQ;IAC1C,IAAI,CAACC,aAAa,GAAGD,MAAM;IAC3B,OAAO,IAAI;EACb;EAEAE,qBAAqBA,CAAA,EAA6B;IAChD,MAAMpB,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMX,MAAM,GAAG,IAAI,CAACK,OAAO;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACE,OAAO;IAC3B,MAAMI,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,GAAInB,UAAgC;IACtE,MAAMsB,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC7B,MAAMH,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,MAAMG,IAAI,GAAG,IAAI,CAACC,KAAK;IACvB,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU;IACjC,MAAMC,iBAAiB,GAAG,IAAI,CAACC,kBAAkB;IACjD,MAAMC,yBAAyB,GAAG,IAAI,CAACC,0BAA0B;IACjE,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IAEnD,MAAMK,SAAS,GAAGnB,IAAI;IAEtB,MAAMoB,MAAkC,GAAG,CAAC,CAAC;IAE7C,SAASC,mBAAmBA,CAC1B1D,KAAsC,EACtC2D,YAAiB,EACjB;MACA,IAAI3D,KAAK,EAAE;QACTyD,MAAM,CAACE,YAAY,CAAC,GAAG3D,KAAK;MAC9B;IACF;IAEA,IAAIqC,IAAI,KAAKnB,UAAU,EAAE;MACvBwC,mBAAmB,CAACjC,MAAM,EAAE,QAAQ,CAAC;IACvC;IAGE,CACE;MAAEkC,YAAY,EAAE,SAAS;MAAE3D,KAAK,EAAEwC;IAAQ,CAAC,EAC3C;MAAEmB,YAAY,EAAE,cAAc;MAAE3D,KAAK,EAAEsC;IAAa,CAAC,EACrD;MAAEqB,YAAY,EAAE,MAAM;MAAE3D,KAAK,EAAE0C;IAAK,CAAC,EACrC;MAAEiB,YAAY,EAAE,WAAW;MAAE3D,KAAK,EAAE4C;IAAU,CAAC,EAC/C;MAAEe,YAAY,EAAE,mBAAmB;MAAE3D,KAAK,EAAE8C;IAAkB,CAAC,EAC/D;MACEa,YAAY,EAAE,2BAA2B;MACzC3D,KAAK,EAAEgD;IACT,CAAC,EACD;MAAEW,YAAY,EAAE,oBAAoB;MAAE3D,KAAK,EAAEkD;IAAmB,CAAC,EACjE;MAAES,YAAY,EAAE,UAAU;MAAE3D,KAAK,EAAEmC;IAAS,CAAC,EAC7C;MAAEwB,YAAY,EAAE,QAAQ;MAAE3D,KAAK,EAAE+B;IAAO,CAAC,CAC1C,CACD6B,OAAO,CAAC,CAAC;MAAE5D,KAAK;MAAE2D;IAAa,CAAC,KAChCD,mBAAmB,CAAC1D,KAAK,EAAE2D,YAAY,CACzC,CAAC;IAED,OAAO,CAACH,SAAS,EAAEC,MAAM,CAAC;EAC5B;AACF;AAAC5D,eAAA,CA3QYyB,uBAAuB", "ignoreList": []}